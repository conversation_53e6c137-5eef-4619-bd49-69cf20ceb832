<?php


// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);

global $wpdb;

$project_id = $_POST['project_id'];
$lead_id = $_POST['lead_id'];
$product_id = $_POST['product_id'];
$table_name = $wpdb->prefix . 'audit_logs';
$milestone_stage_log = $wpdb->prefix . 'milestone_stage_log';
$milestones_table = $wpdb->prefix . 'milestones';
$stage_table = $wpdb->prefix . 'milestone_stages';
$projects_table = $wpdb->prefix . 'projects';
$audit_log_table = $wpdb->prefix . 'erc_audit_log';
$mapping_table = $wpdb->prefix . 'erc_iris_mapping_table';


$is_document_enabled = false;
if (in_array($product_id, array(935, 937, 936))) {
	$is_document_enabled = true;
}


if ($is_document_enabled == true) {
	$products = array(
		"935" => array(
			"ERC" => array(
				1 => "Company Documents",
				2 => "Payroll Documents",
				3 => "ERC Documents",
				4 => "Other Documents"
			)
		),
		"937" => array(
			"STC" => array(
				5 => "Impacted Days",
				6 => "Required Documents"
			)
		),
		"936" => array(
			"Tax Amendment" => array(
				7 => "Required Documents",
				8 => "Additional Documents"
			)
		),
	);

	$form_id = get_form_id($product_id, $products);

	if ($form_id == "") {
		$form_id = 0;
	}
}



// $lead_id = $wpdb->get_var("SELECT lead_id FROM $projects_table WHERE project_id=$project_id");

$audit_logs = $wpdb->get_results("SELECT $table_name.* FROM $table_name WHERE (FieldID=$project_id AND TableName='eccom_projects' AND FieldName!='milestone_id' AND FieldName!='milestone_stage_id' AND FieldName!='modified_at' AND FieldName!='modified_by') OR (FieldID=$lead_id AND TableName='eccom_erc_bank_info') OR (FieldID=$lead_id AND TableName='eccom_erc_erc_intake') OR (FieldID=$lead_id AND TableName='eccom_erc_erc_fees') OR (FieldID=$lead_id AND TableName='eccom_erc_business_info') OR (FieldID=$project_id AND TableName='eccom_collaborators') ORDER BY LogID DESC");

$milestone_data = $wpdb->get_results("SELECT $milestone_stage_log.* FROM $milestone_stage_log WHERE project_id=$project_id ORDER BY id DESC");

$service_head_table = $wpdb->prefix . "product_service_head";

$service_head_query = "SELECT * FROM $service_head_table";

$service_head_results = $wpdb->get_results($service_head_query, ARRAY_A);

$invoice_log_query = "SELECT 
						inv.id,
						inv.invoice_no,
						inv.customer_id,
						inv.customer_invoice_no,
						inv_al.*,
						inv_user.display_name
					FROM {$wpdb->prefix}invoices inv
					LEFT JOIN 
						{$wpdb->prefix}invoice_audit_logs inv_al
					ON 
						inv.id = inv_al.action_id
					LEFT JOIN 
						{$wpdb->prefix}users inv_user
					ON 
						inv_al.CreatedBy = inv_user.ID
					WHERE 
						inv.lead_id = $lead_id AND inv.parent_product = $product_id AND ( inv_al.FieldName IN ('customer_invoice_no', 'invoice_date', 'due_date', 'total_amount') OR inv_al.FieldName LIKE 'productdata.%' )";

$invoice_change_log = $wpdb->get_results($invoice_log_query);

if ($is_document_enabled == true) {

	$document_audit_log_query = "
		SELECT 
			ldn.comments,
			ldn_mapping.doc_label,
			ldn.update_datetime,
			inv_user.display_name,
			ldn_mapping.form_id
		FROM {$wpdb->prefix}leads_document_notes ldn
		LEFT JOIN
			{$wpdb->prefix}users inv_user
		ON 
			ldn.user_id = inv_user.ID
		LEFT JOIN 
			{$wpdb->prefix}leads_document_upload ldn_upload
		ON 
			ldn.lead_id = ldn_upload.lead_id 
		LEFT JOIN 
			{$wpdb->prefix}leads_document_mapping ldn_mapping
		ON
			ldn_mapping.doc_type_id = ldn.doc_type_id
		WHERE 
			ldn.lead_id = $lead_id AND ldn_mapping.form_id IN ($form_id)
		GROUP BY
			ldn.update_datetime
		ORDER BY 
			ldn.update_datetime DESC";

	$document_audit_logs = $wpdb->get_results($document_audit_log_query);

}


$log_history = '';
$log_history .= '<div class="row mb-4"><div class="col-sm-12">
                		<h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;font-weight: bold;">Project Fields</h5>
            		</div>';
$log_history .= '<table id="project_audit_log_table"><thead><tr><th>No.</th><th>Field Name</th><th>Changed From</th><th>Changed To</th><th>Changed Time</th><th>Changed By</th></tr></thead><tbody class="audit_data">';
$no = 1;
foreach ($audit_logs as $key => $value) {
	$fieldname = $value->FieldName;
	$from = $value->BeforeValueString;
	$to = $value->AfterValueString;
	$change_date = $value->DateCreated;
	$changed_by = $value->CreatedBy;
	$user_name = '';
	if ($changed_by) {
		$user_details = get_user_by('id', $changed_by);
		$user_name = $user_details->display_name;
	}
	if(isset($change_date) && $change_date!=''){
		$change_date = date('m/d/Y H:i:s', strtotime($change_date));
	}else{
		$change_date = '';
	}
	
	$to = get_field_meaning($fieldname,$to);
	$from = get_field_meaning($fieldname,$from);

	$fieldname = get_field_name($fieldname);
	$fieldname = str_replace('_', ' ', $fieldname);
	$fieldname = ucfirst($fieldname);
	
	// && ($from == '' && $to!='N/A') && ($from == 'N/A' && $to!='') && ($from == 'NO' && $to!='') && ($from == '' && $to!='NO')
	
	if($from == 'NO' && $to==''){
		$to = 'NO';
		$from = 'NO';
	}else if($from == '' && $to=='NO'){
		$to = 'NO';
		$from = 'NO';
	}else if($from == '' && $to=='N/A'){
		$to = 'NO';
		$from = 'NO';
	}else if($from == 'N/A' && $to==''){
		$to = 'NO';
		$from = 'NO';
	}

 if( (trim($from) !=trim($to)) ){
	$log_history .= "<tr>
			  <td>" . $no . "</td>
              <td>" . $fieldname . "</td>
              <td>" . $from . "</td>
              <td>" . $to . "</td>
              <td>" . $change_date . "</td>
              <td>" . $user_name . "</td>
            </tr>";
	$no++;
  }
}//loop



$log_history .= "</tbody></table>";

$log_history .= '<div class="col-sm-12" style="margin-top:20px;">
                		<h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;font-weight: bold;">Milestone & Stages</h5>
            		</div>';

// -----------------------------------------------------------------

$log_history .= '<div class="col-sm-12"><table id="project_milestone_log_table"><thead><tr><th>No.</th><th>From Milestone</th><th>To Milestone</th><th>From Stage</th><th>To Stage</th><th>Changed Time</th><th>Changed By</th></tr></thead><tbody class="audit_data">';
$no = 1;
foreach ($milestone_data as $mkey => $mvalue) {
	$updated_milestone_id = $mvalue->updated_milestone_id;
	$previous_milestone_id = $mvalue->previous_milestone_id;
	$updated_stage_id = $mvalue->updated_stage_id;
	$previous_stage_id = $mvalue->previous_stage_id;
	$change_date = $mvalue->changed_date;
	$changed_by = $mvalue->changed_by;
	$user_name = '';
	if ($changed_by) {
		$user_details = get_user_by('id', $changed_by);
		$user_name = $user_details->display_name;
	}

	$fieldname = str_replace('_', ' ', $fieldname);
	$fieldname = ucfirst($fieldname);

	$milestone_name = $wpdb->get_var("SELECT milestone_name FROM $milestones_table WHERE milestone_id=$updated_milestone_id");
	$pre_milestone_name = $wpdb->get_var("SELECT milestone_name FROM $milestones_table WHERE milestone_id=$previous_milestone_id");

	$stage_name = $wpdb->get_var("SELECT stage_name FROM $stage_table WHERE milestone_stage_id = $updated_stage_id");
	$pre_stage_name = $wpdb->get_var("SELECT stage_name FROM $stage_table WHERE milestone_stage_id = $previous_stage_id");
	if(isset($change_date) && $change_date!=''){
		$change_date = date('m/d/Y H:i:s', strtotime($change_date));
	}else{
		$change_date = '';
	}
	$log_history .= "<tr>
			  <td>" . $no . "</td>
              <td>" . $pre_milestone_name . "</td>
              <td>" . $milestone_name . "</td>
              <td>" . $pre_stage_name . "</td>
              <td>" . $stage_name . "</td>
              <td>" . $change_date . "</td>
              <td>" . $user_name . "</td>
            </tr>";
	$no++;
}//loop

$log_history .= "</tbody></table></div>";
// $log_history .= "</div>";

// -----------------------------------------------------------------


$log_history .= '<div class="col-sm-12">
                		<h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;font-weight: bold;">Invoices</h5>
            		</div>';
$log_history .= '<div class="col-sm-12"><table id="project_invoice_log_table"><thead><tr><th>No.</th><th>Invoice No.</th><th>Field Name</th><th>Changed From</th><th>Changed To</th><th>Created/Changed Time</th><th>Created/Changed By</th></tr></thead><tbody class="audit_data">';

$no = 1;
foreach ($invoice_change_log as $mkey => $mvalue) {

	$log_history .= "<tr>
	<td>" . $no . "</td>
	<td>" . $mvalue->customer_invoice_no . "</td>
	<td>" . clear_text_to_show($mvalue->FieldName) . "</td>";

	if (preg_match('/\d+\.product_name+/', $mvalue->FieldName, $matches)) {

		if ($mvalue->BeforeValueString != "") {
			$log_history .= "<td>" . find_product_head_by_qb_product_id($mvalue->BeforeValueString, $service_head_results) . "</td>";
		} else {
			$log_history .= "<td>" . $mvalue->BeforeValueString . "</td>";
		}

		if ($mvalue->AfterValueString != "") {
			$log_history .= "<td>" . find_product_head_by_qb_product_id($mvalue->AfterValueString, $service_head_results) . "</td>";
		} else {
			$log_history .= "<td>" . $mvalue->AfterValueString . "</td>";

		}

	} else if (preg_match('/\d+\.discount_type+/', $mvalue->FieldName, $matches)) {
		$discount_type_sign = "";
		if ($mvalue->BeforeValueString != "") {
			$discount_type_sign = ( $mvalue->BeforeValueString == 1 ) ? "%" : "$" ;
			$log_history .= "<td>" . $discount_type_sign . "</td>";
		} else {
			$log_history .= "<td>" . $mvalue->BeforeValueString . "</td>";
		}

		if ($mvalue->AfterValueString != "") {
			$discount_type_sign = ( $mvalue->AfterValueString == 1 ) ? "%" : "$" ;
			$log_history .= "<td>" . $discount_type_sign . "</td>";
		} else {
			$log_history .= "<td>" . $mvalue->AfterValueString . "</td>";
		}

	} else {
		$log_history .= "<td>" . $mvalue->BeforeValueString . "</td><td>" . $mvalue->AfterValueString . "</td>";
	}

	$log_history .= "<td>" . date_formate_as_asked($mvalue->DateCreated) . "</td>
	<td>" . $mvalue->display_name . "</td>
</tr>";
	$no++;
}//loop

$log_history .= "</tbody></table></div>";


if ($is_document_enabled == true) {

	$log_history .= '<div class="col-sm-12">
	<h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;font-weight: bold;">Documents</h5>
	</div>';
	$log_history .= '<div class="col-sm-12"><table id="project_document_log_table"><thead><tr><th>No.</th><th>Document Type</th><th>Note</th><th>Label</th><th>Changed Time</th><th>Changed By</th></tr></thead><tbody class="audit_data">';

	$no = 1;
	foreach ($document_audit_logs as $mkey => $mvalue) {

		$log_history .= "<tr>
	<td>" . $no . "</td>
	<td> " . get_form_name($mvalue->form_id, $products) . " </td>
	<td>" . $mvalue->comments . "</td>
	<td>" . $mvalue->doc_label . "</td>
	<td>" . date_formate_as_asked($mvalue->update_datetime) . "</td>
	<td>" . $mvalue->display_name . "</td>
	</tr>";
		$no++;
	}

	$log_history .= "</tbody></table></div>";

}

// ------------------- Lead Audit log -------------------

$lead_audit_logs = $wpdb->get_results("SELECT $audit_log_table.*,$mapping_table.iris_label FROM $audit_log_table LEFT JOIN $mapping_table ON $audit_log_table.field_name = $mapping_table.db_field_name WHERE $audit_log_table.lead_id = ".$lead_id." ORDER BY $audit_log_table.change_date DESC");

$data = array();
foreach ($lead_audit_logs as $key => $value) {
    if(($value->from =='' && $value->to =='') || ($value->from =='' && $value->to =='N/A') || ($value->field_name =='')){

    }else{
		$id = $value->id;
    	$data[$id] = $value;
    }	
}

if (!empty($data)) {
	
	$log_history .= '<div class="col-sm-12">
	<h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;margin-top: 20px;font-weight: bold;">Business Audit Log</h5>
	</div>';
	$log_history .= '<div class="col-sm-12"><table id="lead_log_table"><thead><tr><th>No.</th><th>Field Name</th><th>Changed From</th><th>Changed To</th><th>Note</th><th>Changed Time</th><th>Changed By</th></tr></thead><tbody class="audit_data">';
	$c=1;
    foreach ($data as $a_value) {
        $user_data = get_user_by('id', $a_value->change_by);
        if(isset($user_data->data->display_name)){
            $change_by = $user_data->data->display_name;
        }else{
            $change_by = '';
        }

        if($a_value->field_name == 'assign_user'){
           $fieldname = 'Assign user';
       }else if($a_value->field_name == 'unassign_user'){
           $fieldname = 'Unassign user';
       }else if($a_value->field_name == 'category' || $a_value->field_name == 'Category'){
           $fieldname = 'Category';
      }else if($a_value->field_name == 'status' || $a_value->field_name == 'lead_status'){
           $fieldname = 'Status';
      }else if($a_value->note != ''){
        $fieldname = 'Note';
      }else{
        $fieldname = $a_value->iris_label;
      }
      
      if(empty($fieldname)){
  			$fieldname = $a_value->field_name;
      }

        $log_history .= "<tr>
        	  <td>".$c."</td>
              <td>".$fieldname."</td>
              <td>".$a_value->from."</td>
              <td>".$a_value->to."</td>
              <td>".$a_value->note."</td>
              <td>".date_formate_as_asked($a_value->change_date)."</td>
              <td>".$change_by."</td>
            </tr>";
    		$c++;
    } //loop end
}
// ------------------------ Lead log ---------------

	$log_history .= "</tbody></table></div>";
	$log_history .= "</div>";

echo $log_history;


function clear_text_to_show($key)
{

	$key = str_replace('_', ' ', $key);
	if (preg_match('/productdata\.(\d+)/', $key, $matches)) {
		$key = preg_replace('/productdata\.\d+/', 'Service  ' . $matches[1] + 1 . " - ", $key);
	} elseif (strpos($key, 'productdata') !== false) {
		$key = str_replace('productdata', 'Product', $key);
	}
	$key = str_replace('.', ' ', $key);
	$key = str_replace('product', '', $key);
	$key = ucwords($key);

	return $key;
}



function date_formate_as_asked($requested_date)
{
	$formatted_date = date('m/d/Y H:i:s', strtotime($requested_date));
	return $formatted_date;
}

function get_form_id($product_id, $products)
{

	if (isset($products[$product_id])) {
		$category = current($products[$product_id]);
		$form_ids = array_keys($category);
		return implode(', ', $form_ids);
	}

	return '';
}

function get_form_name($form_id, $products)
{

	foreach ($products as $product) {
		foreach ($product as $category) {
			if (isset($category[$form_id])) {
				return $category[$form_id];
			}
		}
	}

	return 'Form ID not found';
}

function find_product_head_by_qb_product_id($product_id, $products)
{
	foreach ($products as $product) {
		if ($product['qb_product_id'] == $product_id) {
			return $product['product_head'];
		}
	}
	return "Product head missing.";
}

function get_field_name($fieldname){

	if($fieldname=='2020_q1_4144'){
		$fieldname = '2020_q1_941';
	}else if($fieldname =='2020_q2_4145'){
		$fieldname = '2020_q2_941';
	}else if($fieldname =='2020_q3_4146'){
		$fieldname = '2020_q3_941';
	}else if($fieldname =='2020_q4_4147'){
		$fieldname = '2020_q4_941';
	}else if($fieldname =='2021_q1_4149'){
		$fieldname = '2021_q1_941';
	}else if($fieldname =='2021_q2_4151'){
		$fieldname = '2021_q2_941';
	}else if($fieldname =='2021_q3_4152'){
		$fieldname = '2021_q3_941';
	}else if($fieldname =='2020_q1_4155'){
		$fieldname = '2020_q1_payroll';
	}else if($fieldname =='2020_q2_4156'){
		$fieldname = '2020_q2_payroll';
	}else if($fieldname =='2020_q3_4157'){
		$fieldname = '2020_q3_payroll';
	}else if($fieldname =='2020_q4_4158'){
		$fieldname = '2020_q4_payroll';
	}else if($fieldname =='2021_q1_4160'){
		$fieldname = '2021_q1_payroll';
	}else if($fieldname =='2021_q2_4161'){
		$fieldname = '2021_q2_payroll';
	}else if($fieldname =='2021_q3_4162'){
		$fieldname = '2021_q3_payroll';
	}else if($fieldname =='filing_date_4267'){
		$fieldname = 'q1_2020_filed_date';
	}else if($fieldname =='amount_filed_4263'){
		$fieldname = 'q1_2020_amount_filed';
	}else if($fieldname =='filing_date_4268'){
		$fieldname = 'q2_2020_filed_date';
	}else if($fieldname =='amount_filed_4269'){
		$fieldname = 'q2_2020_amount_filed';
	}else if($fieldname =='filing_date_4270'){
		$fieldname = 'q3_2020_filed_date';
	}else if($fieldname =='amount_filed_4266'){
		$fieldname = 'q3_2020_amount_filed';
	}else if($fieldname =='filing_date_4272'){
		$fieldname = 'q4_2020_filed_date';
	}else if($fieldname =='amount_filed_4273'){
		$fieldname = 'q4_2020_amount_filed';
	}else if($fieldname =='filing_date_4276'){
		$fieldname = 'q1_2021_filed_date';
	}else if($fieldname =='amount_filed_4277'){
		$fieldname = 'q1_2021_amount_filed';
	}else if($fieldname =='filing_date_4279'){
		$fieldname = 'q2_2021_filed_date';
	}else if($fieldname =='amount_filed_4280'){
		$fieldname = 'q2_2021_amount_filed';
	}else if($fieldname =='filing_date_4282'){
		$fieldname = 'q3_2021_filed_date';
	}else if($fieldname =='amount_filed_4283'){
		$fieldname = 'q3_2021_amount_filed';
	}else if($fieldname =='filing_date_4285'){
		$fieldname = 'q4_2021_filed_date';
	}else if($fieldname =='amount_filed_4286'){
		$fieldname = 'q4_2021_amount_filed';
	}else if($fieldname =='avg_emp_count_2019'){
		$fieldname = 'average_employee_count_2019';
	}else if($fieldname =='bal_retainer_return_reaso'){
		$fieldname = 'balance_retainer_return_reason';
	}

	return $fieldname;

}

function get_field_meaning($fieldname,$value){
			$field_meaning = $value;
			if($fieldname=='business_entity_type'){
				if($value==1){
					$field_meaning = 'N/A';
				}else if($value==2){
					$field_meaning = 'Limited Liability (LLC)';
				}else if($value==3){
					$field_meaning = 'Partnership';
				}else if($value==4){
					$field_meaning = 'Sole Proprietorship';
				}else if($value==5){
					$field_meaning = 'Other';
				}else if($value==6){
					$field_meaning = 'Corporation (S,C,B,etc)';
				}else if($value==7){
					$field_meaning = 'Trust';
				}
			}else if($fieldname=='account_type'){
				if($value==1){
					$field_meaning = 'N/A';
				}else if($value==2){
					$field_meaning = 'Savings';
				}else if($value==3){
					$field_meaning = 'Checking';
				}else if($value==4){
					$field_meaning = 'Other';
				}
			}else if($fieldname=='avg_emp_count_2019'){
				if($value==0){
					$field_meaning = 'N/A';
				}else if($value==1){
					$field_meaning = 'Less Than 100';
				}else if($value==2){
					$field_meaning = 'Between 100-500';
				}else if($value==3){
					$field_meaning = 'More Than 500';
				}
			}else if($fieldname=='retainer_payment_type'){
				if($value==1){
					$field_meaning = 'ACH';
				}else if($value==2){
					$field_meaning = 'CC/DB Card';
				}
			}else if($fieldname=='coi_aoi' || $fieldname=='voided_check' || $fieldname=='2019_tax_return' || $fieldname=='2020_tax_return' || $fieldname=='2021_financials'){
				if($value==1){
					$field_meaning = 'YES';
				}else if($value==2){
					$field_meaning = 'NO';
				}else if($value==3){
					$field_meaning = 'N/A';
				}
			}else if($fieldname=='2020_q1_941' || $fieldname=='2020_q2_941' || $fieldname=='2020_q3_941' || $fieldname=='2020_q4_941' || $fieldname=='2021_q1_941' || $fieldname=='2021_q2_941' ||$fieldname=='2021_q3_941' || $fieldname=='2020_q1_payroll' || $fieldname=='2020_q2_payroll' || $fieldname=='2020_q3_payroll' || $fieldname=='2020_q4_payroll' || $fieldname=='2021_q1_payroll' || $fieldname=='2021_q2_payroll' || $fieldname=='2021_q3_payroll' || $fieldname=='ppp_1_applied' || $fieldname=='ppp_1_forgiveness_applied' || $fieldname=='ppp_2_applied' || $fieldname=='ppp_2_forgiveness_applied' || $fieldname=='2020_q1_4144' || $fieldname =='2020_q2_4145' || $fieldname =='2020_q3_4146' || $fieldname =='2020_q4_4147' || $fieldname =='2021_q1_4149' || $fieldname =='2021_q2_4151' || $fieldname =='2021_q3_4152' || $fieldname =='2020_q1_4155' || $fieldname =='2020_q2_4156' || $fieldname =='2020_q3_4157' || $fieldname =='2020_q4_4158' || $fieldname =='2021_q1_4160' || $fieldname =='2021_q2_4161' || $fieldname =='2021_q3_4162'){
				if($value==1){
					$field_meaning = 'N/A';
				}else if($value==2){
					$field_meaning = 'YES';
				}else if($value==3){
					$field_meaning = 'NO';
				}
			}else if($fieldname=='created_by'){
				$user_details = get_user_by('id', $value);
				$field_meaning = $user_details->display_name;
			}

		return $field_meaning;
}

die();

?>