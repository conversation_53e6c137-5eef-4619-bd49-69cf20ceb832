<?php


// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);

global $wpdb;

$opportunity_id = $_POST['opportunity_id'];
$lead_id = $_POST['lead_id'];
$product_id = $_POST['product_id'];
$log_table = $wpdb->prefix . 'crm_activity_log';
$milestone_stage_log = $wpdb->prefix . 'milestone_stage_log';
$milestones_table = $wpdb->prefix . 'milestones';
$stage_table = $wpdb->prefix . 'milestone_stages';
$opportunity_table = $wpdb->prefix . 'opportunities';
$project_table = $wpdb->prefix . 'projects';
$audit_log_table = $wpdb->prefix . 'erc_audit_log';
$mapping_table = $wpdb->prefix . 'erc_iris_mapping_table';


$audit_logs = $wpdb->get_results("SELECT $log_table.* FROM $log_table WHERE opportunity_id=$opportunity_id AND field_name!='milestone' AND field_name!='stage' ORDER BY id DESC");

$milestone_data = $wpdb->get_results("SELECT $milestone_stage_log.* FROM $milestone_stage_log WHERE opportunity_id=$opportunity_id ORDER BY id DESC");


$log_history = '';
$log_history .= '<div class="row mb-4"><div class="col-sm-12">
                		<h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;font-weight: bold;">Opportunity Fields</h5>
            		</div>';
$log_history .= '<table id="opportunity_audit_log_table"><thead><tr><th>No.</th><th>Field Name</th><th>Changed From</th><th>Changed To</th><th>Note</th><th>Changed Time</th></tr></thead><tbody class="audit_data">';
$no = 1;
foreach ($audit_logs as $key => $value) {
	$fieldname = $value->field_name;
	$from = $value->changed_from;
	$to = $value->changed_to;
	$change_date = $value->created_on;
	$changed_by = $value->changed_by;
	$note = $value->note;

	$fieldname = str_replace('_', ' ', $fieldname);
	$fieldname = ucfirst($fieldname);
	
	if(isset($change_date)){
		$formatted_change_date = date('m/d/Y H:i:s', strtotime($change_date));
	}else{
		$formatted_change_date = $change_date;
	}

	$log_history .= "<tr>
			  <td>" . $no . "</td>
              <td>" . $fieldname . "</td>
              <td>" . $from . "</td>
              <td>" . $to . "</td>
         	  <td>" . $note	."</td>
              <td>" . $formatted_change_date . "</td>
            </tr>";
	$no++;
}//loop


$log_history .= "</tbody></table>";

$log_history .= '<div class="col-sm-12" style="margin-top:20px;">
                		<h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;font-weight: bold;">Milestone & Stages</h5>
            		</div>';

// -----------------------------------------------------------------

$log_history .= '<div class="col-sm-12"><table id="opportunity_milestone_log_table"><thead><tr><th>No.</th><th>From Milestone</th><th>To Milestone</th><th>From Stage</th><th>To Stage</th><th>Changed Time</th><th>Changed By</th></tr></thead><tbody class="audit_data">';
$no = 1;
foreach ($milestone_data as $mkey => $mvalue) {
	$updated_milestone_id = $mvalue->updated_milestone_id;
	$previous_milestone_id = $mvalue->previous_milestone_id;
	$updated_stage_id = $mvalue->updated_stage_id;
	$previous_stage_id = $mvalue->previous_stage_id;
	$change_date = $mvalue->changed_date;
	$changed_by = $mvalue->changed_by;
	$user_name = '';
	if ($changed_by) {
		$user_details = get_user_by('id', $changed_by);
		$user_name = $user_details->display_name;
	}

	$fieldname = str_replace('_', ' ', $fieldname);
	$fieldname = ucfirst($fieldname);

	$milestone_name = $wpdb->get_var("SELECT milestone_name FROM $milestones_table WHERE milestone_id=$updated_milestone_id");
	$pre_milestone_name = $wpdb->get_var("SELECT milestone_name FROM $milestones_table WHERE milestone_id=$previous_milestone_id");

	$stage_name = $wpdb->get_var("SELECT stage_name FROM $stage_table WHERE milestone_stage_id = $updated_stage_id");
	$pre_stage_name = $wpdb->get_var("SELECT stage_name FROM $stage_table WHERE milestone_stage_id = $previous_stage_id");

	if(isset($change_date)){
		$formatted_change_date = date('m/d/Y H:i:s', strtotime($change_date));
	}else{
		$formatted_change_date = $change_date;
	}

	$log_history .= "<tr>
			  <td>" . $no . "</td>
              <td>" . $pre_milestone_name . "</td>
              <td>" . $milestone_name . "</td>
              <td>" . $pre_stage_name . "</td>
              <td>" . $stage_name . "</td>
              <td>" . $formatted_change_date . "</td>
              <td>" . $change_by . "</td>
            </tr>";
	$no++;
}//loop

$log_history .= "</tbody></table></div>";
// $log_history .= "</div>";

// -----------------------------------------------------------------

// ------ lead project --------

	$project_id = $wpdb->get_var("SELECT project_id FROM $project_table WHERE product_id=$product_id AND lead_id=$lead_id");
	
	if(empty($project_id)){
			
			// ------------------- Lead Audit log -------------------
			$lead_audit_logs = $wpdb->get_results("SELECT $audit_log_table.*,$mapping_table.iris_label FROM $audit_log_table LEFT JOIN $mapping_table ON $audit_log_table.field_name = $mapping_table.db_field_name WHERE $audit_log_table.lead_id = ".$lead_id." ORDER BY $audit_log_table.change_date DESC");

			$data = array();
			foreach ($lead_audit_logs as $key => $value) {
			    if(($value->from =='' && $value->to =='') || ($value->from =='' && $value->to =='N/A') || ($value->field_name =='')){

			    }else{
					$id = $value->id;
			    	$data[$id] = $value;
			    }	
			}

			if (!empty($data)) {
				
				$log_history .= '<div class="col-sm-12">
				<h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;margin-top: 20px;font-weight: bold;">Business Audit Log</h5>
				</div>';
				$log_history .= '<div class="col-sm-12"><table id="lead_log_table"><thead><tr><th>No.</th><th>Field Name</th><th>Changed From</th><th>Changed To</th><th>Note</th><th>Changed Time</th><th>Changed By</th></tr></thead><tbody class="audit_data">';
				$c=1;
			    foreach ($data as $a_value) {
			        $user_data = get_user_by('id', $a_value->change_by);
			        if(isset($user_data->data->display_name)){
			            $change_by = $user_data->data->display_name;
			        }else{
			            $change_by = '';
			        }

			        if($a_value->field_name == 'assign_user'){
			           $fieldname = 'Assign user';
			       }else if($a_value->field_name == 'unassign_user'){
			           $fieldname = 'Unassign user';
			       }else if($a_value->field_name == 'category' || $a_value->field_name == 'Category'){
			           $fieldname = 'Category';
			      }else if($a_value->field_name == 'status' || $a_value->field_name == 'lead_status'){
			           $fieldname = 'Status';
			      }else if($a_value->note != ''){
			        $fieldname = 'Note';
			      }else{
			        $fieldname = $a_value->iris_label;
			      }
			      
			      if(empty($fieldname)){
			  			$fieldname = $a_value->field_name;
			      }
					
					if(isset($a_value->change_date)){
						$formatted_change_date_a = date('m/d/Y H:i:s', strtotime($a_value->change_date));
					}else{
						$formatted_change_date_a = $a_value->change_date;
					}
					
			        $log_history .= "<tr>
			        	  <td>".$c."</td>
			              <td>".$fieldname."</td>
			              <td>".$a_value->from."</td>
			              <td>".$a_value->to."</td>
			              <td>".$a_value->note."</td>
			              <td>".$formatted_change_date_a."</td>
			              <td>".$change_by."</td>
			            </tr>";
			    		$c++;
			    } //loop end
			}
			// ------------------------ Lead log ---------------

				$log_history .= "</tbody></table></div>";
				$log_history .= "</div>";

	}// project id empty check

echo $log_history;
die();

?>