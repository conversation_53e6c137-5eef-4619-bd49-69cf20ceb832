<?php
global $wpdb;
$table_name = $wpdb->prefix.'projects';
$data = $request->get_json_params();
$project_id = $data['project_id'];

$project = $wpdb->get_row("SELECT {$wpdb->prefix}projects.lead_id
                                                                    FROM {$wpdb->prefix}projects 
                                                                    WHERE {$wpdb->prefix}projects.deleted_at IS NULL 
                                                                    AND {$wpdb->prefix}projects.project_id = ".$project_id." ");
if(!empty($project)){
	$lead_id = $project->lead_id;
	$ercin_w2_employees_count = "";
	$ercin_initial_retain_fee_amount = "";
	$ercin_w2_ee_difference_count = "";
	$ercin_balance_retainer_fee = "";
	$ercin_total_max_erc_amount = "";
	$ercin_total_estimated_fees = "";
	$ercin_affiliate_referral_fees = "";
	$ercin_avg_emp_count_2019 = "";
	$ercin_fee_type = "";
	$custom_fee = "";
	$ercin_company_folder_link = "";
	$ercin_document_folder_link = "";
	$ercin_eligible_quarters = "";
	$ercin_welcome_email = "";
	$ercin_retainer_payment_date = "";
	$ercin_retainer_payment_cleared = "";
	$ercin_retainer_payment_returned = "";
	$ercin_ret_payment_return_reason = "";
	$ercin_retainer_refund_date = "";
	$ercin_retainer_refund_amount = "";
	$ercin_retainer_payment_amount = "";
	$ercin_retainer_payment_type = "";
	$ercin_bal_retainer_invoice_no = "";
	$ercin_bal_retainer_sent_date = "";
	$ercin_bal_retainer_pay_date = "";
	$ercin_bal_retainer_clear_date = "";
	$ercin_bal_retainer_return_date = "";
	$ercin_bal_retainer_return_reaso = "";
	$ercin_interest_percentage = "";
	$ercin_net_no = "";
	$ercin_coi_aoi = "";
	$ercin_voided_check = "";
	$ercin_2019_tax_return = "";
	$ercin_2020_tax_return = "";
	$ercin_2021_financials = "";
	$ercin_2020_q1 = "";
	$ercin_2020_q2 = "";
	$ercin_2020_q3 = "";
	$ercin_2020_q4 = "";
	$ercin_2021_q1 = "";
	$ercin_payroll_register_2021_q2 = "";
	$ercin_2021_q3 = "";
	$ercin_payroll_register_2020_q1 = "";
	$ercin_payroll_register_2020_q2 = "";
	$ercin_payroll_register_2020_q3 = "";
	$ercin_payroll_register_2020_q4 = "";
	$ercin_payroll_register_2021_q1 = "";
	$ercin_2021_q2 = "";
	$ercin_payroll_register_2021_q3 = "";
	$ercin_ppp_1_applied = "";
	$ercin_ppp_1_date = "";
	$ercin_ppp_1_forgiveness_applied = "";
	$ercin_ppp_1_forgive_app_date = "";
	$ercin_ppp_1_amount = "";
	$ercin_ppp_1_wages_allocated = "";
	$ercin_ppp_2_applied = "";
	$ercin_ppp_2_date = "";
	$ercin_ppp_2_forgiveness_applied = "";
	$ercin_ppp_2_forgive_app_date = "";
	$ercin_ppp_2_amount = "";
	$ercin_ppp_2_wages_allocated = "";
	$ercin_additional_comments = "";
	$ercin_attorney_name = "";
    $ercin_call_date = "";
    $ercin_call_time = "";
    $ercin_memo_received_date = "";
    $ercin_memo_cut_off_date = "";

	// ----------------- Get data from db ------
	global $wpdb;
	$intake_table_name = "{$wpdb->prefix}erc_erc_intake";
	$in_sql = "SELECT * FROM $intake_table_name WHERE `lead_id`=$lead_id";
	$intake_data = $wpdb->get_results($in_sql);

	$data = [];

	foreach ($intake_data as $in_key => $in_value) {
	    $in_value = (array) $in_value;
	    $ercin_w2_employees_count = $in_value["w2_employees_count"];
	    $ercin_initial_retain_fee_amount = $in_value["initial_retain_fee_amount"];
	    $ercin_w2_ee_difference_count = $in_value["w2_ee_difference_count"];
	    $ercin_balance_retainer_fee = $in_value["balance_retainer_fee"];
	    $ercin_total_max_erc_amount = $in_value["total_max_erc_amount"];
	    $ercin_total_estimated_fees = $in_value["total_estimated_fees"];
	    $ercin_affiliate_referral_fees = $in_value["affiliate_referral_fees"];
	    $ercin_avg_emp_count_2019 = $in_value["avg_emp_count_2019"];
	    $ercin_fee_type = $in_value["fee_type"];
	    $custom_fee = $in_value["fee_type"];
	    $ercfee_sdgr = $in_value['sdgr'];
	    $ercin_company_folder_link = $in_value["company_folder_link"];
	    $ercin_document_folder_link = $in_value["document_folder_link"];
	    $ercin_eligible_quarters = $in_value["eligible_quarters"];
	    $ercin_welcome_email = $in_value["welcome_email"];
	    $ercin_retainer_invoice_no = $in_value["retainer_invoice_no"];
	    $ercin_retainer_payment_date = $in_value["retainer_payment_date"];
	    $ercin_retainer_payment_cleared = $in_value["retainer_payment_cleared"];
	    $ercin_retainer_payment_returned = $in_value["retainer_payment_returned"];
	    $ercin_ret_payment_return_reason = $in_value["retpayment_return_reason"];
	    $ercin_retainer_refund_date = $in_value["retainer_refund_date"];
	    $ercin_retainer_refund_amount = $in_value["retainer_refund_amount"];
	    $ercin_retainer_payment_amount = $in_value["retainer_payment_amount"];
	    $ercin_retainer_payment_type = $in_value["retainer_payment_type"];
	    $ercin_bal_retainer_invoice_no = $in_value["bal_retainer_invoice_no"];
	    $ercin_bal_retainer_sent_date = $in_value["bal_retainer_sent_date"];
	    $ercin_bal_retainer_pay_date = $in_value["bal_retainer_pay_date"];
	    $ercin_bal_retainer_clear_date = $in_value["bal_retainer_clear_date"];
	    $ercin_bal_retainer_return_date = $in_value["bal_retainer_return_date"];
	    $ercin_bal_retainer_return_reaso = $in_value["bal_retainer_return_reaso"];
	    $ercin_interest_percentage = $in_value["interest_percentage"];
	    $ercin_net_no = $in_value["net_no"];
	    $ercin_coi_aoi = $in_value["coi_aoi"];
	    $ercin_voided_check = $in_value["voided_check"];
	    $ercin_2019_tax_return = $in_value["2019_tax_return"];
	    $ercin_2020_tax_return = $in_value["2020_tax_return"];
	    $ercin_2021_financials = $in_value["2021_financials"];
	    $ercin_2020_q1 = $in_value["2020_q1_4144"];
	    $ercin_2020_q2 = $in_value["2020_q2_4145"];
	    $ercin_2020_q3 = $in_value["2020_q3_4146"];
	    $ercin_2020_q4 = $in_value["2020_q4_4147"];
	    $ercin_2021_q1 = $in_value["2021_q1_4149"];
	    $ercin_2021_q2 = $in_value["2021_q2_4151"];
	    $ercin_2021_q3 = $in_value["2021_q3_4152"];
	    $ercin_payroll_register_2020_q1 = $in_value["2020_q1_4155"];
	    $ercin_payroll_register_2020_q2 = $in_value["2020_q2_4156"];
	    $ercin_payroll_register_2020_q3 = $in_value["2020_q3_4157"];
	    $ercin_payroll_register_2020_q4 = $in_value["2020_q4_4158"];
	    $ercin_payroll_register_2021_q1 = $in_value["2021_q1_4160"];
	    $ercin_payroll_register_2021_q2 = $in_value["2021_q2_4161"];
	    $ercin_payroll_register_2021_q3 = $in_value["2021_q3_4162"];
	    $f911_status = $in_value["f911_status"];
	    $ercin_ppp_1_applied = $in_value["ppp_1_applied"];
	    $ercin_ppp_1_date = $in_value["ppp_1_date"];
	    $ercin_ppp_1_forgiveness_applied = $in_value["ppp_1_forgiveness_applied"];
	    $ercin_ppp_1_forgive_app_date = $in_value["ppp_1_forgive_app_date"];
	    $ercin_ppp_1_amount = $in_value["ppp_1_amount"];
	    $ercin_ppp_1_wages_allocated = $in_value["ppp_1_wages_allocated"];
	    $ercin_ppp_2_applied = $in_value["ppp_2_applied"];
	    $ercin_ppp_2_date = $in_value["ppp_2_date"];
	    $ercin_ppp_2_forgiveness_applied = $in_value["ppp_2_forgiveness_applied"];
	    $ercin_ppp_2_forgive_app_date = $in_value["ppp_2_forgive_app_date"];
	    $ercin_ppp_2_amount = $in_value["ppp_2_amount"];
	    $ercin_ppp_2_wages_allocated = $in_value["ppp_2_wages_allocated"];
	    $ercin_additional_comments = $in_value["additional_comments"];
	    $opportunity_size = $in_value["opportunity_size"];
	    $opportunity_timeline = $in_value["opportunity_timeline"];
	    $confidance_label = $in_value["confidance_label"];
	    $ercin_attorney_name = $in_value["attorney_name"];
	    $ercin_call_date = $in_value["call_date"];
	    $ercin_call_time = $in_value["call_time"];
	    $ercin_memo_received_date = $in_value["memo_received_date"];
	    $ercin_memo_cut_off_date = $in_value["memo_cut_off_date"];

	}
	if($ercin_avg_emp_count_2019 == 1){
		$avg_emp_count_2019 = 'Less Than 100';
	}
	if($ercin_avg_emp_count_2019 == 2){
		$avg_emp_count_2019 = 'Between 100-500';
	}
	if($ercin_avg_emp_count_2019 == 3){
		$avg_emp_count_2019 = 'More Than 500';
	}
	$results['status'] = 1;
	$results['message'] = 'Intake Detail';
	$results['result'][0]['w2_employees_count'] = $ercin_w2_employees_count;
	$results['result'][0]['initial_retain_fee_amount'] = $ercin_initial_retain_fee_amount;
	$results['result'][0]['w2_ee_difference_count'] = $ercin_w2_ee_difference_count;
	$results['result'][0]['balance_retainer_fee'] = $ercin_balance_retainer_fee;
	$results['result'][0]['total_max_erc_amount'] = $ercin_total_max_erc_amount;
	$results['result'][0]['total_estimated_fees'] = $ercin_total_estimated_fees;
	$results['result'][0]['affiliate_referral_fees'] = $ercin_affiliate_referral_fees;
	$results['result'][0]['sdgr'] = $ercfee_sdgr;
	$results['result'][0]['company_folder_link'] = $ercin_company_folder_link;
	$results['result'][0]['document_folder_link'] = $ercin_document_folder_link;
	$results['result'][0]['avg_emp_count_2019'] = $ercin_avg_emp_count_2019;
	$results['result'][0]['fee_type'] = $ercin_fee_type;
	$results['result'][0]['custom_fee'] = $custom_fee;
	$results['result'][0]['eligible_quarters'] = $ercin_eligible_quarters;
	$results['result'][0]['welcome_email'] = $ercin_welcome_email;
	$results['result'][0]['retainer_invoice_no'] = $ercin_retainer_invoice_no;
	$results['result'][0]['retainer_payment_date'] = $ercin_retainer_payment_date;
	$results['result'][0]['retainer_payment_cleared'] = $ercin_retainer_payment_cleared;
	$results['result'][0]['retainer_payment_returned'] = $ercin_retainer_payment_returned;
	$results['result'][0]['retpayment_return_reason'] = $ercin_ret_payment_return_reason;
	$results['result'][0]['retainer_refund_date'] = $ercin_retainer_refund_date;
	$results['result'][0]['retainer_refund_amount'] = $ercin_retainer_refund_amount;
	$results['result'][0]['retainer_payment_amount'] = $ercin_retainer_payment_amount;
	$results['result'][0]['retainer_payment_type'] = $ercin_retainer_payment_type;
	$results['result'][0]['bal_retainer_invoice_no'] = $ercin_bal_retainer_invoice_no;
	$results['result'][0]['bal_retainer_sent_date'] = $ercin_bal_retainer_sent_date;
	$results['result'][0]['bal_retainer_pay_date'] = $ercin_bal_retainer_pay_date;
	$results['result'][0]['bal_retainer_clear_date'] = $ercin_bal_retainer_clear_date;
	$results['result'][0]['bal_retainer_return_date'] = $ercin_bal_retainer_return_date;
	$results['result'][0]['bal_retainer_return_reaso'] = $ercin_bal_retainer_return_reaso;
	$results['result'][0]['interest_percentage'] = $ercin_interest_percentage;
	$results['result'][0]['net_no'] = $ercin_net_no;
	$results['result'][0]['coi_aoi'] = $ercin_coi_aoi;
	$results['result'][0]['voided_check'] = $ercin_voided_check;
	$results['result'][0]['2019_tax_return'] = $ercin_2019_tax_return;
	$results['result'][0]['2020_tax_return'] = $ercin_2020_tax_return;
	$results['result'][0]['2021_financials'] = $ercin_2021_financials;
	$results['result'][0]['2020_q1_941'] = $ercin_2020_q1;
	$results['result'][0]['2020_q2_941'] = $ercin_2020_q2;
	$results['result'][0]['2020_q3_941'] = $ercin_2020_q3;
	$results['result'][0]['2020_q4_941'] = $ercin_2020_q4;
	$results['result'][0]['2021_q1_941'] = $ercin_2021_q1;
	$results['result'][0]['2021_q2_941'] = $ercin_2021_q2;
	$results['result'][0]['2021_q3_941'] = $ercin_2021_q3;
	$results['result'][0]['2020_q1_payroll'] = $ercin_payroll_register_2020_q1;
	$results['result'][0]['2020_q2_payroll'] = $ercin_payroll_register_2020_q2;
	$results['result'][0]['2020_q3_payroll'] = $ercin_payroll_register_2020_q3;
	$results['result'][0]['2020_q4_payroll'] = $ercin_payroll_register_2020_q4;
	$results['result'][0]['2021_q1_payroll'] = $ercin_payroll_register_2021_q1;
	$results['result'][0]['2021_q2_payroll'] = $ercin_payroll_register_2021_q2;
	$results['result'][0]['2021_q3_payroll'] = $ercin_payroll_register_2021_q3;
	$results['result'][0]['f911_status'] = $f911_status;
	$results['result'][0]['ppp_1_applied'] = $ercin_ppp_1_applied;
	$results['result'][0]['ppp_1_date'] = $ercin_ppp_1_date;
	$results['result'][0]['ppp_1_forgiveness_applied'] = $ercin_ppp_1_forgiveness_applied;
	$results['result'][0]['ppp_1_forgive_app_date'] = $ercin_ppp_1_forgive_app_date;
	$results['result'][0]['ppp_1_amount'] = $ercin_ppp_1_amount;
	$results['result'][0]['ppp_1_wages_allocated'] = $ercin_ppp_1_wages_allocated;
	$results['result'][0]['ppp_2_applied'] = $ercin_ppp_2_applied;
	$results['result'][0]['ppp_2_date'] = $ercin_ppp_2_date;
	$results['result'][0]['ppp_2_forgiveness_applied'] = $ercin_ppp_2_forgiveness_applied;
	$results['result'][0]['ppp_2_forgive_app_date'] = $ercin_ppp_2_forgive_app_date;
	$results['result'][0]['ppp_2_amount'] = $ercin_ppp_2_amount;
	$results['result'][0]['ppp_2_wages_allocated'] = $ercin_ppp_2_wages_allocated;
	$results['result'][0]['additional_comment'] = $ercin_additional_comments;
	$results['result'][0]['attorney_name'] = $ercin_attorney_name;
	$results['result'][0]['call_date'] = $ercin_call_date;
	$results['result'][0]['call_time'] = $ercin_call_time;
	$results['result'][0]['memo_received_date'] = $ercin_memo_received_date;
	$results['result'][0]['memo_cut_off_date'] = $ercin_memo_cut_off_date;
}else{
	$results['status'] = 0;
    $results['message'] = 'No project found.';
}
echo json_encode($results);die;