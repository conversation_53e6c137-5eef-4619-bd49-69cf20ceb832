<?php
/**
 * Create a new table class that will extend the WP_List_Table
 */
class CRM_ERP_Product_List extends WP_List_Table {
    private $userRole; 
    private $limitPerpage;
    function __construct() {
        global $status, $page;
        $this->limitPerpage = 15;
        parent::__construct(array(
            'singular' => 'bulk-delete', //singular name of the listed records
            'plural' => 'bulk-deletes', //plural name of the listed records
            'ajax' => false
                //does this table support ajax?
        ));
    }

    public function column_default($item, $column_name) {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        switch ($column_name) {
            case 'cb':
            return '<input type="checkbox" name="deleteItem" value="'.$item['ProductID'].'"/>';
            case 'ProductID':
                return $item->$column_name;
            case 'Title':
                return ucfirst($item->$column_name);
            case 'SKU':
                return ucfirst($item->$column_name);
            /*case 'UnitPrice':
                return $item->$column_name;
            case 'HourlyPrice':
                return $item->$column_name;*/
            case 'UnitName':
                return ucfirst($item->$column_name);
            case 'OwnerName':
                return ucfirst($item->$column_name);
            case 'ProductType':
                return ucfirst($item->$column_name);
            case 'ProductCategory':
                return ucfirst($item->$column_name);
            case 'LaunchDate':
                if(!empty($item->$column_name) && $item->$column_name != '0000-00-00'){
                    return date("m/d/Y", strtotime($item->$column_name));
                }else{
                    return 'NA';
                }
            case 'ExpiryDate':
                if(!empty($item->$column_name) && $item->$column_name != '0000-00-00'){
                    return date("m/d/Y", strtotime($item->$column_name));
                }else{
                    return 'NA';
                }
            case 'Status':
                return ucfirst($item->$column_name);
            case 'Action':
                return $item->$column_name;
            default:
                return print_r($item, true);
        }
    }
    function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" class="chkbox" name="deleteItem[]" value="'.$item->ProductID.'"/>',

            $this->_args['singular'], //Let's simply repurpose the table's singular label ("plugin")
            /* $2%s */
            $item->ProductID //The value of the checkbox should be the record's id
        );
    }
    function column_ProductID($item){
        //Build row actions
        $actions = array(
            'edit'      => sprintf('<a href="?page=add_edit_product&action=edit&id='.$item->ProductID.'">Edit</a>',$_REQUEST['page'],'edit',$item->ProductID),
            'view'    => sprintf('<a href="?page=view_product&action=view&id='.$item->ProductID.'">View</a>',$_REQUEST['page'],'view',$item->ProductID),
        );
        
        //Return the title contents
        return sprintf('%1$s %2$s',$item->ProductID,$this->row_actions($actions));
    }
    /**
     * Prepare the items for the table to process
     *
     * @return Void
     */
    public function prepare_items() {
        global $wpdb;
        $this->process_bulk_action();  
    ?>
    <form method="get" action="" id="search_all_form">
    <?php 
        $this->search_box_new($_GET); 
    ?>
        <div class="wrap woocommerce">
            <div class="white_card card_height_100 mb_30">
                <div class="white_card_header">
                    <div class="box_header m-0 new_report_header">
                        <div class="title_img">
                            <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/products-icon.png" class="page-title-img" alt="">
                            <h4>Products List</h4>
                        </div>
                        <div class="invoice_exports">
                            <a href="admin.php?page=add_edit_product&action=add" class="add-opp-custom-icon"><i class="fa-solid fa-plus"></i> New Product</a>
                            <a href="javascript:void(0);" class="status_btn_export export_invoice_excel export_products">Export</a>
                        </div>
                    </div>
                </div>
                <div class="loader_box" id="loader_box" style="display: none;">
                    <div class="loading">
                        <p class="loading__text">Please Wait. Deleting Product.</p>
                        <div class="loading__bar"></div>
                    </div>
                </div>
                <div class="white_card_body custom-crm-erp-product-report p-15" id="echeck_report_table_wrap">
            <?php
            $columns = $this->get_columns();
            $hidden = $this->get_hidden_columns();
            $sortable = $this->get_sortable_columns();
            $this->_column_headers = array(
                $columns,
                $hidden,
                $sortable
            );
            //$this->process_bulk_action();
            $search = $_GET;
            $data = $this->table_data($search);
            usort($data, array($this, 'sort_data'));
            $perPage = $this->limitPerpage;
            $currentPage = $this->get_pagenum();
            $totalItems = count($data);
            $this->set_pagination_args(array(
                'total_items' => $totalItems,
                'per_page' => $perPage
            ));
            $data = array_slice($data, (($currentPage - 1) * $perPage), $perPage);
            $this->items = $data;
            $this->display();
            

            ?>
                </div>
            </div>
        </div>
    </form>
            <script type="text/javascript">
                $(document).ready(function() {
                    // Handler for the change event of #agreeCheckbox
                    $(document).on('change', '#agreeCheckbox', function() {
                        // Check if #agreeCheckbox is checked
                        if ($(this).prop('checked')) {
                            $(".swal-button--confirm").css("pointer-events", "auto");
                        } else {
                            $(".swal-button--confirm").css("pointer-events", "none");
                        }
                    });

                    $(document).on('click', '.swal-button-container:has(.swal-button--confirm)', function() {
                        if ($('#agreeCheckbox').prop('checked')) {
                            return true;
                        } else {
                            // Check if error message already exists
                            if (!$('.swal-content + p.error-message').length) {
                                // Append the error message if it doesn't exist
                                $('.swal-content').after('<p class="error-message" style="color: red; margin: 40px;">Please agree to delete the product!</p>');
                            }
                        }
                    });
                });

                jQuery(document).on('click', '.delete_product', function () {
                    swal({
                        title: "Are you sure?",
                        text: "Yes, I want to delete this Product. Related Milestones and Stages will delete and Opportunities will no longer be linked to the Selected Product.",
                        icon: "warning",
                        buttons: {
                            cancel: "Cancel",
                            confirm: "Delete",
                        },
                        content: {
                            element: "input",
                            attributes: {
                                type: "checkbox",
                                id: "agreeCheckbox"
                            },
                        },
                        dangerMode: true,
                    })
                    .then((willDelete) => {
                        if (willDelete && $('#agreeCheckbox').prop('checked')) {
                            $(this).attr('disabled', true);
                            $("#loader_box").show();
                            var product_id = jQuery(this).data('product_id');
                            jQuery.ajax({
                                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                                method: 'post',
                                data: { action: 'delete_product', product_id: product_id },
                                success(response) {
                                    location.reload(true);
                                }
                            }); // ajax
                        }
                        $('.swal-modal').removeClass('crm-erp-products-delete-swal');
                    });
                    $('.swal-modal').addClass('crm-erp-products-delete-swal');
                });
  
                jQuery(document).on("click", ".export_products",function(){
                    jQuery(this).text('Please wait..');
                    jQuery(this).css('pointer-events','none');
                    var product_title = jQuery("#product_title").val();
                    var product_sku = jQuery("#product_sku").val();
                    var unit_price = jQuery("#unit_price").val();
                    var hourly_price = jQuery("#hourly_price").val();
                    var owner_id = jQuery("#owner_id").val();
                    var unit_type = jQuery("#unit_type").val();
                    var product_type = jQuery("#product_type").val();
                    var product_category = jQuery("#product_category").val();
                    var product_status = jQuery("#product_status").val();
                    var launch_date = jQuery("#launch_date").val();
                    var expiry_date = jQuery("#expiry_date").val();
                    jQuery.ajax({
                        url:'<?php echo admin_url('admin-ajax.php'); ?>',
                        method:'post',
                        data:{action: 'export_products',product_title: product_title,product_sku: product_sku,unit_price:unit_price,hourly_price:hourly_price,owner_id:owner_id,unit_type:unit_type,product_type:product_type,product_category:product_category,product_status:product_status,launch_date:launch_date,expiry_date:expiry_date},
                        success(response){
                            jQuery(".export_products").text('Export');
                            jQuery(".export_products").css('pointer-events','');
                            var downloadLink = document.createElement("a");
                            var responseData = jQuery.trim(response);
                            var fileData = ['\ufeff'+responseData];
                            var blobObject = new Blob(fileData,{
                            type: "text/csv;charset=utf-8;"
                            });
                            var url = URL.createObjectURL(blobObject);
                            downloadLink.href = url;
                            var currentDate = new Date();
                            //var dateString = currentDate.toISOString().slice(0, 10); // YYYY-MM-DD

                            // Extract month, day, and year
                            var month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Month is zero-based
                                var day = String(currentDate.getDate()).padStart(2, '0');
                                var year = currentDate.getFullYear();

                                // Combine into MM/DD/YYYY format
                                var dateString = month + '/' + day + '/' + year;
                        

                            var timeString = currentDate
                                .toLocaleTimeString("en-US", {
                                    hour12: false,
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    second: "2-digit",
                                })
                                .replace(/:/g, "_");
                            var filename = "Products_" + dateString + "_" + timeString + ".csv";
                            downloadLink.download = filename;
                            document.body.appendChild(downloadLink);
                            downloadLink.click();
                            document.body.removeChild(downloadLink);
                        }
                    })
                }) 
            </script>
    <?php
    }

    public function get_columns() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        $columns = array(
            'cb' => '<input type="checkbox" />',
            'ProductID' => 'Product ID',
            'Title' => 'Title',
            'SKU' => 'SKU',
            /*'UnitPrice' => 'Unit Price',
            'HourlyPrice' => 'Hourly Price',*/
            'UnitName' => 'Unit Type',
            'OwnerName' => 'Owner Name',
            'ProductType' => 'Product Type',
            'ProductCategory' => 'Product Category',
            'LaunchDate' => 'Launch Date',
            'ExpiryDate' => 'Expiry Date',
            'Status' => 'Status',
            'Action' => 'Action'
        );
        //$columns['action'] = 'Action';
        return $columns;
    }
    public function get_sortable_columns() {
        $sortable = array(
            'ProductID'     => array('ProductID',true),
            'Title'     => array('Title',true),
            'SKU'     => array('SKU',true),
            /*'UnitPrice'     => array('UnitPrice',true),
            'HourlyPrice'     => array('HourlyPrice',true),*/
            'UnitName'     => array('UnitName',true),
            'OwnerName'     => array('OwnerName',true),
            'ProductType'     => array('ProductType',true),
            'ProductCategory'     => array('ProductCategory',true),
            'LaunchDate'     => array('LaunchDate',true),
            'ExpiryDate'     => array('ExpiryDate',true),
            'Status'     => array('Status',true),

        );
        return $sortable;
    }
    function get_bulk_actions() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
          //if(in_array('administrator', $user_roles) || in_array('echeck_staff', $user_roles) || in_array('echeck_client', $user_roles) || in_array('master_sales', $user_roles)){
          if(in_array('administrator', $user_roles)){  
            return array(
                'delete' => __( 'Delete', ''),
            );
          }else{
            return array();
          }  
          //}    
    }
    function process_bulk_action() {
        if ('delete' === $this->current_action()) {
            $nonce = esc_attr($_REQUEST['_wpnonce']);
            if (isset($_REQUEST['action2']) && $_REQUEST['action2']=='delete'){
                
                if(!empty($_REQUEST['deleteItem'])){
                    $product_manager = new CRM_ERP_Product_Manager();
                    foreach($_REQUEST['deleteItem'] as $singleProduct){

                        $product_manager->delete_product($singleProduct);
                    }
                    wp_redirect('?page=crm_products');
                    exit;
                }
                
            }
        } 
    }

    /**
     * Define which columns are hidden
     *
     * @return Array
     */

    public function get_hidden_columns() {
        return array();
    }
    /**

     * edit a customer record.

     *

     * @param int $id customer ID

     */



    /**

     * Define the sortable columns

     *

     * @return Array

     */
    public function get_display_name($user_id) {
        if (!$user = get_userdata($user_id))
            return false;
        return $user->data->display_name;
    }

    /**

     * Get the table data

     *

     * @return Array

     */

    private function table_data($search) {
        global $wpdb;
        if( (isset($search['product_title']) && $search['product_title'] != '') OR (isset($search['product_sku']) && $search['product_sku'] != '') OR (isset($search['unit_price']) && $search['unit_price'] != '') OR (isset($search['hourly_price']) && $search['hourly_price'] != '') OR (isset($search['owner_id']) && $search['owner_id'] != '') OR (isset($search['start_date']) && $search['start_date'] != '') OR (isset($search['end_date']) && $search['end_date'] != '') OR (isset($search['status']) && $search['status'] != '') OR (isset($search['unit_type']) && $search['unit_type'] != '') OR (isset($search['product_type']) && $search['product_type'] != '') OR (isset($search['product_category']) && $search['product_category'] != '')){
            $product_manager = new CRM_ERP_Product_Manager();
            $products_data = $product_manager->search_products($search);
        }else{
            $product_manager = new CRM_ERP_Product_Manager();
            $products_data = $product_manager->get_products();
        }
        if(!empty($products_data)){
            $current_user_id = get_current_user_id();
            $user_data = get_user_by('id',$current_user_id);
            $user_roles = $user_data->roles;
              if(in_array('administrator', $user_roles)){ 
                foreach($products_data as $key => $productdata){
                    $productdata->Action = '<button data-product_id ="'.$productdata->ProductID.'"  class="delete_product" type="button" title="Remove"> <i class="fa-solid fa-trash"></i></button>';
                }
              }
            
        }
        return $products_data;
    }
    /**
     * Allows you to sort the data by the variables set in the $_GET
     *
     * @return Mixed
     */
    private function sort_data($a, $b) {
        // Set defaults
        $orderby = isset($_GET['orderby']) ? sanitize_text_field($_GET['orderby']) : 'ProductID';
        $order = isset($_GET['order']) ? sanitize_text_field($_GET['order']) : 'desc';
        
        $result = strnatcmp($a->$orderby, $b->$orderby);
    
        return ($order === 'asc') ? $result : -$result;
    }

    protected function pagination( $which ) {
        if ( empty( $this->_pagination_args ) ) {
            return;
        }
        $total_items     = $this->_pagination_args['total_items'];
        $total_pages     = $this->_pagination_args['total_pages'];
        $infinite_scroll = false;
        if ( isset( $this->_pagination_args['infinite_scroll'] ) ) {
            $infinite_scroll = $this->_pagination_args['infinite_scroll'];
        }
        if ( 'top' === $which && $total_pages > 1 ) {
            $this->screen->render_screen_reader_content( 'heading_pagination' );
        }
        $output = '<span class="displaying-num">' . sprintf(
            /* translators: %s: Number of items. */
            _n( '%s item', '%s items', $total_items ),
            number_format_i18n( $total_items )
        ) . '</span>';
        $current              = $this->get_pagenum();
        $removable_query_args = wp_removable_query_args();
        $current_url = set_url_scheme( 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] );
        $current_url = remove_query_arg( $removable_query_args, $current_url );
        $page_links = array();
        $total_pages_before = '<span class="paging-input">';
        $total_pages_after  = '</span></span>';
        $disable_first = false;
        $disable_last  = false;
        $disable_prev  = false;
        $disable_next  = false;
        if ( 1 == $current ) {
            $disable_first = true;
            $disable_prev  = true;
        }
        if ( 2 == $current ) {
            $disable_first = true;
        }
        if ( $total_pages == $current ) {
            $disable_last = true;
            $disable_next = true;
        }
        if ( $total_pages - 1 == $current ) {
            $disable_last = true;
        }
        if ( $disable_first ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&laquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='first-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( remove_query_arg( 'paged', $current_url ) ),
                __( 'First page' ),
                '&laquo;'
            );
        }
        if ( $disable_prev ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&lsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='prev-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( 'paged', max( 1, $current - 1 ), $current_url ) ),
                __( 'Previous page' ),
                '&lsaquo;'
            );
        }
        if ( 'bottom' === $which ) {
            $html_current_page  = $current;
            $total_pages_before = '<span class="screen-reader-text">' . __( 'Current Page' ) . '</span><span id="table-paging" class="paging-input"><span class="tablenav-paging-text">';
        } else {
            $html_current_page = sprintf(
                "%s<input class='current-page' id='current-page-selector' type='text' name='paged' value='%s' size='%d' aria-describedby='table-paging' /><span class='tablenav-paging-text'>",
                '<label for="current-page-selector" class="screen-reader-text">' . __( 'Current Page' ) . '</label>',
                $current,
                strlen( $total_pages )
            );
        }
        $html_total_pages = sprintf( "<span class='total-pages'>%s</span>", number_format_i18n( $total_pages ) );
        $page_links[]     = $total_pages_before . sprintf(
            _x( '%1$s of %2$s', 'paging' ),
            $html_current_page,
            $html_total_pages
        ) . $total_pages_after;
        if ( $disable_next ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&rsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='next-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=> min( $total_pages, $current + 1 )), $current_url ) ),
                __( 'Next page' ),
                '&rsaquo;'
            );
        }
        if ( $disable_last ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&raquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='last-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=>$total_pages), $current_url ) ),
                __( 'Last page' ),
                '&raquo;'
            );
        }
        $pagination_links_class = 'pagination-links';
        if ( ! empty( $infinite_scroll ) ) {
            $pagination_links_class .= ' hide-if-js';
        }
        $output .= "\n<span class='$pagination_links_class'>" . implode( "\n", $page_links ) . '</span>';
        if ( $total_pages ) {
            $page_class = $total_pages < 2 ? ' one-page' : '';
        } else {
            $page_class = ' no-pages';
        }
        $this->_pagination = "<div class='tablenav-pages{$page_class}'>$output</div>";
        echo $this->_pagination;
    }
    public function search_box_new($search_data)
    {
    ?>
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
        <?php
        global $wpdb;
        $args = array(
          'orderby' => 'display_name',
          'order'   => 'ASC'
        );
        $users = get_users( $args );
        $unit_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}unit_types WHERE {$wpdb->prefix}unit_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}unit_types.UnitTypeID DESC");
        $product_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_types WHERE {$wpdb->prefix}product_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_types.ProductTypeID DESC");
        $product_categories = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_categories WHERE {$wpdb->prefix}product_categories.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_categories.CategoryID DESC");
        ?>
        
            <input type="hidden" name="page" value="crm_products">
            <div id="overlay" onclick="overlay_off()"></div>
            <div class="row align-items-center mb-3">
                <div class="col-md-3 search_field">
                    <img src="https://design.echeck.money/portal/wp-content/plugins/echeck-theme111/assets/img/icon/Search.svg" alt="" class="icon">
                    <input type="text" placeholder="Search.." id="show_lead_search" autocomplete="off" class="open">
                    <input type="submit" style="visibility: hidden;position:absolute;">
                </div>

                <div class="col-md-9 invoice_date_range" style="margin-top: 10px;">
                    <p class="invoice_date_filter">
                        <span>Date From : <input type="text" id="date_timepicker_start" class="date_from" value="<?php if(isset($_GET['start_date'])){ echo $_GET['start_date']; }  ?>" name="start_date" pattern="\d{2}/\d{2}/\d{4}" placeholder="MM/DD/YYYY" autocomplete="off" onkeydown="return false"></span>

                        <span>Date To : <input type="text" id="date_timepicker_end" value="<?php if(isset($_GET['end_date'])){ echo $_GET['end_date']; }  ?>" name="end_date" class="date_to" pattern="\d{2}/\d{2}/\d{4}" placeholder="MM/DD/YYYY" autocomplete="off" onkeydown="return false"></span>

            
                        <input type="submit" class="button product_date_submit" value="Submit">
                        <input type="button" class="button product_date_submit" id="date_timepicker_reset" value="Reset">

                    </p>
                </div>
            </div>
            <div class="popup-overlay search-popup">
                <div class="popup-content">
                    <div class="search_header">
                        <h4>Search</h4>
                        <span class="close">
                            <i class="fa-solid fa-xmark"></i>
                        </span>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <input type="text" id="product_title" name="product_title" placeholder="Product Title" value="<?php if (isset($search_data['product_title'])) {echo $search_data['product_title'];} ?>" class="search-popup-input-select" >
                        </div>
                        <div class="col-md-6">
                            <input type="text" id="product_sku" name="product_sku" placeholder="Product SKU" value="<?php if (isset($search_data['product_sku'])) {echo $search_data['product_sku'];} ?>" class="search-popup-input-select" >
                        </div>
                    </div>
                    <!-- <div class="row mt-2">
                        <div class="col-md-6">
                            <input type="text" id="unit_price" name="unit_price" placeholder="Unit Price" value="<?php if (isset($search_data['unit_price'])) {echo $search_data['unit_price'];} ?>" class="search-popup-input-select" >
                        </div>
                        <div class="col-md-6">
                            <input type="text" id="hourly_price" name="hourly_price" placeholder="Hourly Price" value="<?php if (isset($search_data['hourly_price'])) {echo $search_data['hourly_price'];} ?>" class="search-popup-input-select" >
                        </div>
                    </div> -->
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <select name="owner_id" id="owner_id" class="search-popup-input-select">
                                <option value="">Select Owner</option>
                                <?php foreach($users as $user): 
                                        if($user->ID == $search_data['owner_id']){
                                            $sel = "selected";
                                        }else{
                                            $sel = '';
                                        }
                                ?>
                                    <option value="<?php echo $user->ID; ?>" <?php echo $sel;?>><?php echo $user->display_name; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">     
                            <select name="unit_type" id="unit_type" class="search-popup-input-select">
                                <option value="">Select Unit Type</option>
                                <?php foreach($unit_types as $unit): 
                                        if($unit->UnitTypeID == $search_data['unit_type']){
                                            $sel = "selected";
                                        }else{
                                            $sel = '';
                                        }
                                ?>
                                    <option value="<?php echo $unit->UnitTypeID; ?>" <?php echo $sel;?>><?php echo $unit->UnitName; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">     
                            <select name="product_type" id="product_type" class="search-popup-input-select">
                                <option value="">Select Product Type</option>
                                <?php foreach($product_types as $product_type): 
                                        if($product_type->ProductTypeID == $search_data['product_type']){
                                            $sel = "selected";
                                        }else{
                                            $sel = '';
                                        }
                                ?>
                                    <option value="<?php echo $product_type->ProductTypeID; ?>" <?php echo $sel;?>><?php echo $product_type->TypeName; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">     
                            <select name="product_category" id="product_category" class="search-popup-input-select">
                                <option value="">Select Product Category</option>
                                <?php foreach($product_categories as $product_category): 
                                        if($product_category->CategoryID == $search_data['product_category']){
                                            $sel = "selected";
                                        }else{
                                            $sel = '';
                                        }
                                ?>
                                    <option value="<?php echo $product_category->CategoryID; ?>" <?php echo $sel;?>><?php echo $product_category->Name; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <select id="product_status" name="status" class="search-popup-input-select">
                                <option value="">Select Status</option>
                                <option value="active" <?php if($search_data['status'] == 'active'){echo "selected";}?>>Active</option>
                                <option value="inactive" <?php if($search_data['status'] == 'inactive'){echo "selected";}?>>Inactive</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-12 text-center">
                        <input type="submit" id="btn_search_submit" class="search-popup-submit-btn" value="Search" />
                        <input type="button" class="search-popup-reset-btn" value="Clear" id="reset_Form_button" onclick="resetAffForm()"/>
                        </div>
                    </div>  
                </div>
            </div>

            <script>
                 jQuery(document).ready(function () {
                    jQuery("#owner_id").select2();
                    jQuery("#unit_type").select2();
                    jQuery("#product_type").select2();
                    jQuery("#product_category").select2();

                    jQuery('.date_from').datetimepicker({
                        format: 'm/d/Y',
                        autoclose: true,
                        orientation: 'bottom',
                        timepicker: false,
                        autocomplete: 'off',
                        maxDate : 'now',
                        onChangeDateTime:function(dp,$input){
                            console.log(new Date($('.date_to').val()));
                            if (new Date($('.date_to').val()) < new Date($input.val()) ) {
                                alert("Date To can not be less than Date from");
                                $('.date_to').val('');
                                $('.date_from').val('');
                            }
                        }
                    });


                    jQuery('.date_to').datetimepicker({
                        format: 'm/d/Y',
                        autoclose: true,
                        orientation: 'bottom',
                        timepicker: false,
                        autocomplete: 'off',
                        maxDate : 'now',
                        onChangeDateTime:function(dp,$input){
                            console.log(new Date($('.date_from').val()));
                            if (new Date($input.val() ) < new Date($('.date_from').val()) ) {
                                alert("Date To can not be less than Date from");
                                $('.date_to').val('');
                                $('.date_from').val('');
                            }
                        }
                    });


                 });
            </script>

        <script type="text/javascript">

            jQuery(".open").on("click", function() {
              jQuery('.status_box').hide(); 
              jQuery(".popup-overlay, .popup-content").addClass("active");
              jQuery(".search_lead_id").focus();
              jQuery('#overlay').show();
            });

            jQuery(".close").on("click", function() {
              jQuery(".popup-overlay, .popup-content").removeClass("active");
              jQuery('#overlay').hide();
            });

            function overlay_off(){
              jQuery(".close").trigger("click");
              jQuery('#overlay').hide();
            }
            function resetAffForm(){
                jQuery("#reset_Form_button").val('Clearing...');
                var site_url = '<?php  echo get_site_url() ?>';
                window.location.href = site_url+'/wp-admin/admin.php?page=crm_products';
            }
            jQuery(document).on("click",'#doaction2', function(e) {
                e.preventDefault();
                var is_check = 0;
                $(".chkbox").each(function(){
                    if($(this).is(":checked")){
                        is_check = 1;
                    }
                })
                if($('#bulk-action-selector-bottom').val()=='delete'){
                    if(is_check == 1){
                        swal({
                            title: "Are you sure?",
                            text: "Yes, I want to delete Selected Products. Related Milestones and Stages will delete and Opportunities will no longer be linked to the Products.",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                                confirm: "Delete",
                            },
                            content: {
                                element: "input",
                                attributes: {
                                    type: "checkbox",
                                    id: "agreeCheckbox"
                                },
                            },
                            dangerMode: true,
                        })
                        .then((willDelete) => {
                            if (willDelete && $('#agreeCheckbox').prop('checked')) {
                                $(this).attr('disabled',true);
                                $("#loader_box").show();
                                $('#search_all_form').submit();
                            }
                            $('.swal-modal').removeClass('crm-erp-products-delete-swal');
                        });
                        $('.swal-modal').addClass('crm-erp-products-delete-swal');
                    }else{
                        swal({
                            title: "Please select atleast one checkbox",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                        //alert('Please select some option.');
                    }
                }else{ 
                    swal({
                            title: "Please select some action.",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                }
            });
        </script>
    <?php
    }
}
?>