<?php
/**
 * Handles REST API endpoints for the  CRM ERP plugin.
 */

// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);

class CRM_ERP_REST_API {

    public function __construct() {

        add_action('rest_api_init', array($this, 'register_api_routes'));
    }

    public function register_api_routes() {
        // Products Endpoints
        register_rest_route('productsplugin/v1', '/products', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'get_products'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Products Endpoints
        register_rest_route('productsplugin/v1', '/all-products', array(
            'methods' => 'POST',
            'callback' => array($this, 'all_products'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product Info Endpoints
        register_rest_route('productsplugin/v1', '/product', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_product'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Endpoints
        register_rest_route('productsplugin/v1', '/create-product', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_product'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Product Endpoints
        register_rest_route('productsplugin/v1', '/edit-product', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_product'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Unit Types Endpoints
        register_rest_route('productsplugin/v1', '/unit-types', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'unit_types'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

         // Unit Type Info Endpoints
        register_rest_route('productsplugin/v1', '/unit-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'unit_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));
        // Create Unit Type Endpoints
        register_rest_route('productsplugin/v1', '/create-unit-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_unit_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Unit Type
        register_rest_route('productsplugin/v1', '/edit-unit-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_unit_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Type Endpoints
        register_rest_route('productsplugin/v1', '/create-product-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_product_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product Types Endpoints
        register_rest_route('productsplugin/v1', '/product-types', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'product_types'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Type Endpoints
        register_rest_route('productsplugin/v1', '/product-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'product_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Product Type
        register_rest_route('productsplugin/v1', '/edit-product-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_product_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product Categories Endpoints
        register_rest_route('productsplugin/v1', '/product-categories', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'product_categories'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Category Endpoints
        register_rest_route('productsplugin/v1', '/product-category', array(
            'methods' => 'POST',
            'callback' => array($this, 'product_category'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Category Endpoints
        register_rest_route('productsplugin/v1', '/create-product-category', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_product_category'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Product Category
        register_rest_route('productsplugin/v1', '/edit-product-category', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_product_category'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Currency List Endpoints
        register_rest_route('productsplugin/v1', '/currency-list', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'currency_list'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Currency Endpoints
        register_rest_route('productsplugin/v1', '/create-currency', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_currency'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Currency Endpoints
        register_rest_route('productsplugin/v1', '/edit-currency', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_currency'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Currency Info Endpoints
        register_rest_route('productsplugin/v1', '/currency', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_currency'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product Status List Endpoints
        register_rest_route('productsplugin/v1', '/product-status-list', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'product_status_list'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone Status List Endpoints
        register_rest_route('productsplugin/v1', '/milestone-stage-list', array(
            'methods' => 'POST',
            'callback' => array($this, 'milestone_stage_list')
        ));
        
        // Milestone list List Endpoints
        register_rest_route('productsplugin/v1', '/milestone-list', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'milestone_list'),
            //'permission_callback' => array($this, 'check_permissions')
        ));
        
        // Milestone all List Endpoints
        register_rest_route('productsplugin/v1', '/milestone-all-list', array(
            'methods' => 'POST',
            'callback' => array($this, 'milestone_all_list'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone search List Endpoints
        register_rest_route('productsplugin/v1', '/milestone-search', array(
            'methods' => 'POST',
            'callback' => array($this, 'milestone_search'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone detail Info Endpoints
        register_rest_route('productsplugin/v1', '/milestone-detail', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_milestone_detail'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone search List Endpoints
        register_rest_route('productsplugin/v1', '/check-milestone-unique', array(
            'methods' => 'POST',
            'callback' => array($this, 'check_milestone_unique'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone Stages Uniqueness Check Endpoints
        register_rest_route('productsplugin/v1', '/check-milestage-unique', array(
            'methods' => 'POST',
            'callback' => array($this, 'check_milestage_unique')
        ));

        // Update milestone opportunity mapping based on product Endpoints
        register_rest_route('productsplugin/v1', '/update-opportunity-mapping', array(
            'methods' => 'POST',
            'callback' => array($this, 'update_opportunity_mapping')
        ));

        // Opportunity search List Endpoints
        register_rest_route('productsplugin/v1', '/check-opportunity-unique', array(
            'methods' => 'POST',
            'callback' => array($this, 'check_opportunity_unique'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Status Endpoints
        register_rest_route('productsplugin/v1', '/create-product-status', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_product_status'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Product Status Endpoints
        register_rest_route('productsplugin/v1', '/edit-product-status', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_product_status'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product Status Info Endpoints
        register_rest_route('productsplugin/v1', '/product-status', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_product_status'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product All Milestones Info Endpoints
        register_rest_route('productsplugin/v1', '/product-milestones', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_product_all_milestones'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product All Milestones for opportunity Info Endpoints
        register_rest_route('productsplugin/v1', '/product-milestones-for-add-opportunity', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_milestones_for_add_opportunity'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

         // Product All Milestones Info Endpoints
        register_rest_route('productsplugin/v1', '/get-milestone-stage-for-product', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_milestone_stage_for_product'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone All Status Info Endpoints
        register_rest_route('productsplugin/v1', '/milestone-status', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_product_all_milestone_status'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Get Probability from Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/get-probability', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_probability')
        ));

        // Create Milestone Endpoints
        register_rest_route('productsplugin/v1', '/create-milestone', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_milestone'),
        ));

        // edit Milestone Endpoints
        register_rest_route('productsplugin/v1', '/edit-milestone', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_milestone'),
        ));

        // Create Milestone Status Endpoints
        register_rest_route('productsplugin/v1', '/create-milestone-stage', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_milestone_stage'),
        ));

        // Edit Milestone Status Endpoints
        register_rest_route('productsplugin/v1', '/edit-milestone-stage', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_milestone_stage'),
        ));

        // Opportunity All api listing 
        // Create Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/create-opportunity', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_opportunity'),
        ));

        // Edit Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/edit-opportunity', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_opportunity'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/edit-opportunity-optional-field', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_opportunity_optional_field'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Update Oppotunity And Stage
        register_rest_route('productsplugin/v1', '/update-milestone-stage', array(
            'methods' => 'POST',
            'callback' => array($this, 'update_milestone_stage'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Project Endpoints
        register_rest_route('productsplugin/v1', '/edit-project-optional-field', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_project_optional_field'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Project Endpoints
        register_rest_route('productsplugin/v1', '/edit-opportunity-project-owner', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_opportunity_project_optional_field'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        register_rest_route('productsplugin/v1', '/opportunities', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_opportunities'),
        ));

        // Opportunity Info Endpoints
        register_rest_route('productsplugin/v1', '/opportunity', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_opportunity'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Delere Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/delete_opportunity', array(
            'methods' => 'POST',
            'callback' => array($this, 'delete_opportunity'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Next Step Endpoints
        register_rest_route('productsplugin/v1', '/next-step-list', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'next_step_list'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Next Step Endpoints
        register_rest_route('productsplugin/v1', '/create-next-step', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_next_step'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Currency Endpoints
        register_rest_route('productsplugin/v1', '/edit-next-step', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_next_step'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Currency Info Endpoints
        register_rest_route('productsplugin/v1', '/next-step', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_next_step'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Get product detail Info Endpoints
        register_rest_route('productsplugin/v1', '/get-product-name', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_product_name'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Get lead contacts Endpoints
        register_rest_route('productsplugin/v1', '/lead-contacts', array(
            'methods' => 'POST',
            'callback' => array($this, 'lead_contacts'),
            //'permission_callback' => array($this, 'check_permissions')
        ));
        // Get opportunity notes Endpoints
        register_rest_route('productsplugin/v1', '/fetch_opportunity_notes', array(
            'methods' => 'POST',
            'callback' => array($this, 'fetch_opportunity_notes'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create opportunity notes Endpoints
        register_rest_route('productsplugin/v1', '/create_opportunity_notes', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_opportunity_notes'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        
        // Create opportunity/project notes Endpoints
        register_rest_route('productsplugin/v1', '/add_edit_notes', array(
            'methods' => 'POST',
            'callback' => array($this, 'add_edit_notes'),
            //'permission_callback' => array($this, 'check_permissions')
        ));    

        // delete opportunity/project notes Endpoints
        register_rest_route('productsplugin/v1', '/delete_notes', array(
            'methods' => 'POST',
            'callback' => array($this, 'delete_notes'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Fee Type Endpoints
        register_rest_route('productsplugin/v1', '/create-fee-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_fee_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Fee Type Info Endpoints
        register_rest_route('productsplugin/v1', '/fee-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_fee_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Fee Types Endpoints
        register_rest_route('productsplugin/v1', '/fee-types', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'fee_types'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Fee Type Endpoints
        register_rest_route('productsplugin/v1', '/edit-fee-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_fee_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Get Project Spaces Endpoints
        register_rest_route('productsplugin/v1', '/project-spaces', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'project_spaces'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product search List Endpoints
        register_rest_route('productsplugin/v1', '/check-product-unique', array(
            'methods' => 'POST',
            'callback' => array($this, 'check_product_unique'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Project Space Endpoints
        register_rest_route('productsplugin/v1', '/create-project-space', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_project_space'),
        ));

        // Create Project
        register_rest_route('productsplugin/v1', '/create-project', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_project'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create project notes Endpoints
        register_rest_route('productsplugin/v1', '/create_project_notes', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_project_notes'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Reoprn Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/reopen-opportunity', array(
            'methods' => 'POST',
            'callback' => array($this, 'reopen_opportunity'),
        ));

        // Reoprn STC Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/reopen-stc-opportunity', array(
            'methods' => 'POST',
            'callback' => array($this, 'reopen_stc_opportunity'),
        ));


        // Repayment liny by lead_id and  Opportunity IDEndpoints
        register_rest_route('productsplugin/v1', '/send-stc-payment-link', array(
            'methods' => 'POST',
            'callback' => array($this, 'send_stc_payment_link'),
        ));

        // submit-opportunity-stages api
        register_rest_route('productsplugin/v1', '/submit-opportunity-project-stages', array(
            'methods' => 'POST',
            'callback' => array($this, 'submit_opportunity_project_stages'),
        ));

        // submit-product-service-head-api
        register_rest_route('productsplugin/v1', '/create-product-service-head', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_product_service_head'),
        ));

        // list-product-service-head-api
        register_rest_route('productsplugin/v1', '/product-service-head-list', array(
            'methods' => 'POST',
            'callback' => array($this, 'product_service_head_list'),
        ));

        // update-product-service-head-api
        register_rest_route('productsplugin/v1', '/edit-product-service-head', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_product_service_head'),
        ));

        // Role Products Endpoints
        register_rest_route('productsplugin/v1', '/role-products', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'role_products'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Save Product Roles Endpoints
        register_rest_route('productsplugin/v1', '/save-product-roles', array(
            'methods' => 'POST',
            'callback' => array($this, 'save_product_roles'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Save Product Roles Endpoints
        register_rest_route('productsplugin/v1', '/get-product-settings', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_product_settings'),
            //'permission_callback' => array($this, 'check_permissions')
        ));
    }

    // Permissions check callback
    public function check_permissions($request) {
        // Implement permission checks
        //return current_user_can('manage_options');
    }

    /**
     * Create a new product.
     *
     * @param array $data Product data.
     * @return int|WP_Error The ID of the newly created product, or WP_Error on failure.
     */
    public function create_product(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'crm_products';
        /*check product sku*/
        $product_data = $wpdb->get_results("SELECT SKU,DeletedAt FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.SKU = '".$data['SKU']."'");
        if(empty($product_data)){
            
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to create product into database', $wpdb->last_error);
            }else{
                $product_id = $wpdb->insert_id;
                $fields[0]['Title'] = $data['Title'];
                $fields[1]['SKU'] = $data['SKU'];
                $fields[2]['currency_id'] = $data['currency_id'];
                $fields[3]['UnitPrice'] = $data['UnitPrice'];
                $fields[4]['HourlyPrice'] = $data['HourlyPrice'];
                $fields[5]['UnitTypeID'] = $data['UnitTypeID'];
                $fields[6]['OwnerID'] = $data['OwnerID'];
                $fields[7]['ProductTypeID'] = $data['ProductTypeID'];
                $fields[8]['CategoryID'] = $data['CategoryID'];
                $fields[9]['LaunchDate'] = $data['LaunchDate'];
                $fields[10]['ExpiryDate'] = $data['ExpiryDate'];
                $fields[11]['Description'] = $data['Description'];
                $fields[12]['Status'] = $data['Status'];
                $fields[13]['CustomFieldLabel1'] = $data['CustomFieldLabel1'];
                $fields[14]['CustomFieldValue1'] = $data['CustomFieldValue1'];
                $fields[15]['CustomFieldLabel2'] = $data['CustomFieldLabel2'];
                $fields[16]['CustomFieldValue2'] = $data['CustomFieldValue2'];
                $fields[17]['CustomFieldLabel3'] = $data['CustomFieldLabel3'];
                $fields[18]['CustomFieldValue3'] = $data['CustomFieldValue3'];
                $fields[19]['CustomFieldLabel4'] = $data['CustomFieldLabel4'];
                $fields[20]['CustomFieldValue4'] = $data['CustomFieldValue4'];
                $fields[21]['CustomFieldLabel5'] = $data['CustomFieldLabel5'];
                $fields[22]['CustomFieldValue5'] = $data['CustomFieldValue5'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['CreatedAt'];
                        $logs_data['CreatedBy'] = $data['CreatedBy'];
                        $logs_data['TableName'] = $wpdb->prefix.'crm_products';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'SKU Already Exist';
            echo json_encode($results);die;
        }
    }

    /**
     * Edit a new product.
     *
     * @param array $data Product data.
     * @return int|WP_Error The ID of the previously created product, or WP_Error on failure.
     */
    public function edit_product(WP_REST_Request $request) {
        global $wpdb;
        $product_id = $request->get_param('product_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'crm_products';
        unset($data['product_id']);
        /*check product sku*/
        $product_data = $wpdb->get_results("SELECT SKU FROM $table_name WHERE DeletedAt IS NULL AND  SKU = '".$data['SKU']."' AND ProductID != ".$product_id."");
        if(empty($product_data)){
            //Audit Logs Start
            $fields[0]['Title'] = $data['Title'];
            $fields[1]['SKU'] = $data['SKU'];
            $fields[2]['currency_id'] = $data['currency_id'];
            $fields[3]['UnitPrice'] = $data['UnitPrice'];
            $fields[4]['HourlyPrice'] = $data['HourlyPrice'];
            $fields[5]['UnitTypeID'] = isset($data['UnitTypeID']) ? $data['UnitTypeID'] : 0;
            $fields[6]['OwnerID'] = $data['OwnerID'];
            $fields[7]['ProductTypeID'] = $data['ProductTypeID'];
            $fields[8]['CategoryID'] = isset($data['CategoryID']) ? $data['CategoryID'] : 0;
            $fields[9]['LaunchDate'] = $data['LaunchDate'];
            $fields[10]['ExpiryDate'] = !empty($data['ExpiryDate']) ? $data['ExpiryDate'] : '0000-00-00';
            $fields[11]['Description'] = $data['Description'];
            $fields[12]['Status'] = $data['Status'];
            $fields[13]['CustomFieldLabel1'] = isset($data['CustomFieldLabel1']) ? $data['CustomFieldLabel1'] : '';
            $fields[14]['CustomFieldValue1'] = isset($data['CustomFieldValue1']) ? $data['CustomFieldValue1'] : '';
            $fields[15]['CustomFieldLabel2'] = isset($data['CustomFieldLabel2']) ? $data['CustomFieldLabel2'] : '';
            $fields[16]['CustomFieldValue2'] = isset($data['CustomFieldValue2']) ? $data['CustomFieldValue2'] : '';
            $fields[17]['CustomFieldLabel3'] = isset($data['CustomFieldLabel3']) ? $data['CustomFieldLabel3'] : '';
            $fields[18]['CustomFieldValue3'] = isset($data['CustomFieldValue3']) ? $data['CustomFieldValue3'] : '';
            $fields[19]['CustomFieldLabel4'] = isset($data['CustomFieldLabel4']) ? $data['CustomFieldLabel4'] : '';
            $fields[20]['CustomFieldValue4'] = isset($data['CustomFieldValue4']) ? $data['CustomFieldValue4'] : '';
            $fields[21]['CustomFieldLabel5'] = isset($data['CustomFieldLabel5']) ? $data['CustomFieldLabel5'] : '';
            $fields[22]['CustomFieldValue5'] = isset($data['CustomFieldValue5']) ? $data['CustomFieldValue5'] : '';
            //$fields[13]['ProductLogo'] = isset($data['ProductLogo']) ? $data['ProductLogo'] : (isset($data['exist_logo']) ? $data['exist_logo'] : '';
            //$fields[14]['ProductImages'] = isset($data['ProductImages']) ? $data['ProductImages'] : isset($data['exist_image']) ? $data['exist_image'] : '';
            $i = 0;
            $audit_logs = [];
            if(isset($data['exist_logo'])){
                unset($data['exist_logo']);
            }
            if(isset($data['exist_image'])){
                unset($data['exist_image']);
            }
            
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."'  AND ProductID = ".$product_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE ProductID = ".$product_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['ModifiedAt'];
                        $logs_data['CreatedBy'] = $data['ModifiedBy'];
                        $logs_data['TableName'] = $wpdb->prefix.'crm_products';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            

            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('ProductID' => $product_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update product into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Updated';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'SKU Already Exist';
            echo json_encode($results);die;
        }
    }

    // Callbacks for Products Endpoints
    public function get_products($request) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'crm_products';
        $unit_type_table = $wpdb->prefix . 'unit_types';
        $product_type_table = $wpdb->prefix . 'product_types';
        $product_category_table = $wpdb->prefix . 'product_categories';
        $user_table = $wpdb->prefix . 'users';
        $query = $wpdb->prepare("SELECT 
                $table_name.ProductID,$table_name.Title,$table_name.SKU,$table_name.OwnerID,$table_name.Status,$table_name.LaunchDate,$table_name.ExpiryDate,$unit_type_table.UnitName,$product_type_table.TypeName as ProductType,$product_category_table.Name as ProductCategory,$user_table.display_name as OwnerName
                FROM $table_name 
                LEFT JOIN $unit_type_table ON $table_name.UnitTypeID = $unit_type_table.UnitTypeID
                LEFT JOIN $product_type_table ON $table_name.ProductTypeID = $product_type_table.ProductTypeID
                LEFT JOIN $product_category_table ON $table_name.CategoryID = $product_category_table.CategoryID
                JOIN $user_table ON $table_name.OwnerID = $user_table.ID
                WHERE $table_name.DeletedAt IS NULL
                ORDER BY $table_name.ProductID DESC
                ");
        return $wpdb->get_results($query);
    }

    // Callbacks for All Products Endpoints
    public function all_products($request) {
        global $wpdb;
        $data = $request->get_json_params();
        $user_roles = implode(",", $data['user_roles']);
        $table_name = $wpdb->prefix . 'crm_products';
        $unit_type_table = $wpdb->prefix . 'unit_types';
        $product_type_table = $wpdb->prefix . 'product_types';
        $product_category_table = $wpdb->prefix . 'product_categories';
        $user_table = $wpdb->prefix . 'users';
        $query = $wpdb->prepare("SELECT 
                $table_name.ProductID,$table_name.Title
                FROM $table_name 
                WHERE $table_name.DeletedAt IS NULL
                AND $table_name.status = 'active'
                AND FIND_IN_SET('".$user_roles."',$table_name.product_roles)
                ORDER BY $table_name.ProductID DESC
                ");
        return $wpdb->get_results($query);
    }

    // Callbacks for Products Endpoints
    public function get_product(WP_REST_Request $request) {
        global $wpdb;
        $product_id = $request->get_param('id');
        $table_name = $wpdb->prefix . 'crm_products';
        $unit_type_table = $wpdb->prefix . 'unit_types';
        $product_type_table = $wpdb->prefix . 'product_types';
        $product_category_table = $wpdb->prefix . 'product_categories';
        $user_table = $wpdb->prefix . 'users';
        $query = $wpdb->prepare("SELECT 
                                $table_name.*,$unit_type_table.UnitName,$product_type_table.TypeName as ProductType,$product_category_table.Name as ProductCategory,$user_table.display_name as OwnerName
                                FROM $table_name 
                                LEFT JOIN $unit_type_table ON $table_name.UnitTypeID = $unit_type_table.UnitTypeID
                                LEFT JOIN $product_type_table ON $table_name.ProductTypeID = $product_type_table.ProductTypeID
                                LEFT JOIN $product_category_table ON $table_name.CategoryID = $product_category_table.CategoryID
                                JOIN $user_table ON $table_name.OwnerID = $user_table.ID
                                WHERE $table_name.ProductID = '".$product_id."'
                                ");
        return $wpdb->get_row($query);
    }

    /**
     * Create a new unit type.
     *
     * @param array $data unit type data.
     * @return int|WP_Error The ID of the newly created unit type, or WP_Error on failure.
     */
    public function create_unit_type(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'unit_types';
        $unit_data = $wpdb->get_results("SELECT UnitName FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.UnitName = '".$data['UnitName']."'");
        if(empty($unit_data)){
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to insert unit type into database', $wpdb->last_error);
            }else{
                $unit_type_id = $wpdb->insert_id;
                $fields[0]['UnitName'] = $data['UnitName'];
                $fields[1]['Description'] = $data['Description'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['CreatedAt'];
                        $logs_data['CreatedBy'] = $data['CreatedBy'];
                        $logs_data['TableName'] = $wpdb->prefix.'unit_types';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $unit_type_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Unit Type Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Unit Name Already Exist';
            echo json_encode($results);die;
        }
        //return $wpdb->insert_id;
    }

    /**
     * Edit unit type.
     *
     * @param array $data unit type data.
     * @return int|WP_Error The ID of the previously created product, or WP_Error on failure.
     */
    public function edit_unit_type(WP_REST_Request $request) {
        global $wpdb;
        $unit_type_id = $request->get_param('unit_type_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'unit_types';
        $unit_data = $wpdb->get_results("SELECT UnitName FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.UnitName = '".$data['UnitName']."' AND UnitTypeID != ".$unit_type_id."");
        if(empty($unit_data)){
            unset($data['unit_type_id']);
            //Audit Logs Start
            $fields[0]['UnitName'] = $data['UnitName'];
            $fields[1]['Description'] = $data['Description'];
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."'  AND UnitTypeID = ".$unit_type_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE UnitTypeID = ".$unit_type_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['ModifiedAt'];
                        $logs_data['CreatedBy'] = $data['ModifiedBy'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $unit_type_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('UnitTypeID' => $unit_type_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update unit type into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Unit Type Update Successfully';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Unit Name Already Exist';
            echo json_encode($results);die;
        }
        //return $unit_type_id;
    }

    // Callbacks for Unit Types Endpoints
    public function unit_types($request) {
        global $wpdb;
        $unit_type_table = $wpdb->prefix . 'unit_types';
        $query = $wpdb->prepare("SELECT 
                                $unit_type_table.UnitTypeID,$unit_type_table.UnitName,$unit_type_table.Description
                                FROM $unit_type_table 
                                WHERE $unit_type_table.DeletedAt IS NULL
                                ORDER BY $unit_type_table.UnitTypeID DESC
                                ");
        return $wpdb->get_results($query);
    }

    // Callbacks for Unit Type Endpoints
    public function unit_type($request) {
        global $wpdb;
        $UnitTypeID = $request->get_param('id');
        $unit_type_table = $wpdb->prefix . 'unit_types';
        $query = $wpdb->prepare("SELECT 
                                $unit_type_table.UnitTypeID,$unit_type_table.UnitName,$unit_type_table.Description
                                FROM $unit_type_table 
                                WHERE $unit_type_table.UnitTypeID = '".$UnitTypeID."'
                                ");
        return $wpdb->get_row($query);
    }


    /**
     * Create a new product type.
     *
     * @param array $data product type data.
     * @return int|WP_Error The ID of the newly created product type, or WP_Error on failure.
     */
    public function create_product_type(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_types';
        $product_type_data = $wpdb->get_results("SELECT TypeName FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.TypeName = '".$data['TypeName']."'");
        if(empty($product_type_data)){
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to insert product type into database', $wpdb->last_error);
            }else{
                $product_type_id = $wpdb->insert_id;
                $fields[0]['TypeName'] = $data['TypeName'];
                $fields[1]['Description'] = $data['Description'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['CreatedAt'];
                        $logs_data['CreatedBy'] = $data['CreatedBy'];
                        $logs_data['TableName'] = $wpdb->prefix.'product_types';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_type_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Type Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Product Type Already Exist';
            echo json_encode($results);die;
        }
        //return $wpdb->insert_id;
    }

    // Callbacks for Product Types Endpoints
    public function product_types($request) {
        global $wpdb;
        $product_type_table = $wpdb->prefix . 'product_types';
        $query = $wpdb->prepare("SELECT 
                                $product_type_table.ProductTypeID,$product_type_table.TypeName,$product_type_table.Description
                                FROM $product_type_table 
                                WHERE $product_type_table.DeletedAt IS NULL
                                ORDER BY $product_type_table.ProductTypeID DESC
                                ");
        return $wpdb->get_results($query);
    }

    // Callbacks for Product Type Info Endpoints
    public function product_type($request) {
        global $wpdb;
        $product_type_id = $request->get_param('id');
        $product_type_table = $wpdb->prefix . 'product_types';
        $query = $wpdb->prepare("SELECT 
                                $product_type_table.ProductTypeID,$product_type_table.TypeName,$product_type_table.Description
                                FROM $product_type_table 
                                WHERE $product_type_table.ProductTypeID = '".$product_type_id."'
                                ");
        return $wpdb->get_row($query);
    }

    /**
     * Edit product type.
     *
     * @param array $data product type data.
     * @return int|WP_Error The ID of the previously created product, or WP_Error on failure.
     */
    public function edit_product_type(WP_REST_Request $request) {
        global $wpdb;
        $product_type_id = $request->get_param('product_type_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_types';
        $product_type_data = $wpdb->get_results("SELECT TypeName FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.TypeName = '".$data['TypeName']."' AND ProductTypeID != ".$product_type_id."");
        if(empty($product_type_data)){
            unset($data['product_type_id']);
            //Audit Logs Start
            $fields[0]['TypeName'] = $data['TypeName'];
            $fields[1]['Description'] = $data['Description'];
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."' AND ProductTypeID = ".$product_type_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE ProductTypeID = ".$product_type_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['ModifiedAt'];
                        $logs_data['CreatedBy'] = $data['ModifiedBy'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_type_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
        
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('ProductTypeID' => $product_type_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update product type into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Type Update Successfully';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Product Type Already Exist';
            echo json_encode($results);die;
        }
        //return $product_type_id;
    }

    // Callbacks for Product Categories Endpoints
    public function product_categories($request) {
        global $wpdb;
        $product_categories_table = $wpdb->prefix . 'product_categories';
        $query = $wpdb->prepare("SELECT 
                                c1.CategoryID,c1.Name,c1.Description,c2.Name as ParentCategory
                                FROM $product_categories_table c1
                                LEFT JOIN $product_categories_table c2 on c2.CategoryID = c1.ParentCategoryID
                                WHERE c1.DeletedAt IS NULL
                                ORDER BY c1.CategoryID DESC
                                ");
        return $wpdb->get_results($query);
    }

    // Callbacks for Product Category Info Endpoints
    public function product_category($request) {
        global $wpdb;
        $CategoryID = $request->get_param('id');
        $product_categories_table = $wpdb->prefix . 'product_categories';
        $query = $wpdb->prepare("SELECT 
                                $product_categories_table.CategoryID,$product_categories_table.Name,$product_categories_table.Description,$product_categories_table.ParentCategoryID,$product_categories_table.ProductId
                                FROM $product_categories_table 
                                WHERE $product_categories_table.CategoryID = '".$CategoryID."'
                                ");
        return $wpdb->get_row($query);
    }

     /**
     * Create a new product category.
     *
     * @param array $data product category data.
     * @return int|WP_Error The ID of the newly created product category, or WP_Error on failure.
     */
    public function create_product_category(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_categories';
        $product_category_data = $wpdb->get_results("SELECT Name FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.Name = '".$data['Name']."'");
        if(empty($product_category_data)){
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to insert product category into database', $wpdb->last_error);
            }else{
                $product_category_id = $wpdb->insert_id;
                $fields[0]['Name'] = $data['Name'];
                $fields[1]['Description'] = $data['Description'];
                //$fields[2]['ProductId'] = $data['ProductId'];
                if(isset($data['ParentCategoryID'])){
                    $fields[3]['ParentCategoryID'] = $data['ParentCategoryID'];
                }
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['CreatedAt'];
                        $logs_data['CreatedBy'] = $data['CreatedBy'];
                        $logs_data['TableName'] = $wpdb->prefix.'product_categories';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_category_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Category Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Product Category Already Exist';
            echo json_encode($results);die;
        }
        //return $wpdb->insert_id;
    }

    /**
     * Edit product category.
     *
     * @param array $data product category data.
     * @return int|WP_Error The ID of the previously created product category, or WP_Error on failure.
     */
    public function edit_product_category(WP_REST_Request $request) {
        global $wpdb;
        $product_category_id = $request->get_param('product_category_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_categories';
        $product_category_data = $wpdb->get_results("SELECT Name FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.Name = '".$data['Name']."' AND CategoryID != ".$product_category_id."");
        if(empty($product_category_data)){
            unset($data['product_category_id']);
            //Audit Logs Start
            $fields[0]['Name'] = $data['Name'];
            $fields[1]['Description'] = $data['Description'];
            //$fields[2]['ProductId'] = $data['ProductId'];
            if(isset($data['ParentCategoryID'])){
                $fields[3]['ParentCategoryID'] = $data['ParentCategoryID'];
            }
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."' AND CategoryID = ".$product_category_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE CategoryID = ".$product_category_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['ModifiedAt'];
                        $logs_data['CreatedBy'] = $data['ModifiedBy'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_category_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('CategoryID' => $product_category_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update product category into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Category Updated';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Product Category Already Exist';
            echo json_encode($results);die;
        }
        //return $product_category_id;
    }

    // Callbacks for Currency List Endpoints
    public function currency_list($request) {
        global $wpdb;
        $currency_table = $wpdb->prefix . 'currencies';
        $query = $wpdb->prepare("SELECT 
                                $currency_table.currency_id,$currency_table.currency_code,$currency_table.currency_name
                                FROM $currency_table 
                                WHERE $currency_table.deleted_at IS NULL
                                ORDER BY $currency_table.currency_id DESC
                                ");
        return $wpdb->get_results($query);
    }

    /**
     * Create a new currency.
     *
     * @param array $data currency data.
     * @return int|WP_Error The ID of the newly created currency, or WP_Error on failure.
     */
    public function create_currency(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'currencies';
        $currency_data = $wpdb->get_results("SELECT currency_code FROM $table_name WHERE $table_name.deleted_at IS NULL AND $table_name.currency_code = '".$data['currency_code']."'");
        if(empty($currency_data)){
            $result = $wpdb->insert($table_name, $data);

            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to insert currency into database', $wpdb->last_error);
            }else{
                $currency_id = $wpdb->insert_id;
                $fields[0]['currency_code'] = $data['currency_code'];
                $fields[1]['currency_name'] = $data['currency_name'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['created_at'];
                        $logs_data['CreatedBy'] = $data['created_by'];
                        $logs_data['TableName'] = $wpdb->prefix.'currencies';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $currency_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Currency Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Currency Code Already Exist';
            echo json_encode($results);die;
        }
        //return $wpdb->insert_id;
    }

    /**
     * Edit currency.
     *
     * @param array $data currency data.
     * @return int|WP_Error The ID of the previously created currency, or WP_Error on failure.
     */
    public function edit_currency(WP_REST_Request $request) {
        global $wpdb;
        $currency_id = $request->get_param('currency_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'currencies';
        $currency_data = $wpdb->get_results("SELECT currency_code FROM $table_name WHERE $table_name.deleted_at IS NULL AND $table_name.currency_code = '".$data['currency_code']."' AND currency_id != ".$currency_id."");
        if(empty($currency_data)){
            unset($data['currency_id']);
            //Audit Logs Start
            $fields[0]['currency_code'] = $data['currency_code'];
            $fields[1]['currency_name'] = $data['currency_name'];
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."' AND currency_id = ".$currency_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE currency_id = ".$currency_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['modified_at'];
                        $logs_data['CreatedBy'] = $data['modified_by'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $currency_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('currency_id' => $currency_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update currency into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Currency Updated';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Currency Code Already Exist';
            echo json_encode($results);die;
        }
        //return $product_type_id;
    }

    // Callbacks for currency Endpoints
    public function get_currency($request) {
        global $wpdb;
        $currency_id = $request->get_param('id');
        $currency_table = $wpdb->prefix . 'currencies';
        $query = $wpdb->prepare("SELECT 
                                $currency_table.currency_id,$currency_table.currency_code,$currency_table.currency_name
                                FROM $currency_table 
                                WHERE $currency_table.currency_id = '".$currency_id."'
                                ");
        return $wpdb->get_row($query);
    }

    // Callbacks for Product Status List Endpoints
    public function product_status_list($request) {
        global $wpdb;
        $product_status_table = $wpdb->prefix . 'product_status';
        $product_table = $wpdb->prefix . 'crm_products';
        $query = $wpdb->prepare("SELECT 
                                $product_status_table.product_status_id,$product_status_table.status,$product_table.Title as product_name
                                FROM $product_status_table 
                                JOIN $product_table ON $product_status_table.product_id = $product_table.ProductID
                                WHERE $product_status_table.deleted_at IS NULL
                                ORDER BY $product_status_table.product_status_id DESC
                                ");
        return $wpdb->get_results($query);
    }
    
    // Callbacks for Milestone Status List Endpoints
    public function milestone_stage_list($request) {
        global $wpdb;
        $milestone_stages_table = $wpdb->prefix . 'milestone_stages';
        $milestone_id = $request->get_param('mid');

        $where = '';
        if(isset($milestone_id) && !empty($milestone_id)){
            $where .= " AND $milestone_stages_table.milestone_id='".$milestone_id."'";
        }
        $query = $wpdb->prepare("SELECT $milestone_stages_table.*
                                FROM $milestone_stages_table
                                WHERE $milestone_stages_table.deleted_at IS NULL $where
                                ORDER BY $milestone_stages_table.milestone_stage_id DESC
                                ");
        return $wpdb->get_results($query);
    }

    // Callbacks for Milestone List Endpoints
    public function milestone_list($request) {
        global $wpdb;
        $milestones_table = $wpdb->prefix . 'milestones';
        $milestone_status_table = $wpdb->prefix . 'milestone_status';
        $query = $wpdb->prepare("SELECT $milestones_table.*
                                FROM $milestones_table
                                WHERE $milestones_table.deleted_at IS NULL
                                ORDER BY $milestones_table.milestone_id DESC
                                ");
        return $wpdb->get_results($query);
    }

    
    // Callbacks for Milestone Search List Endpoints
    public function milestone_all_list($request) {
        global $wpdb;
        $milestones_table = $wpdb->prefix . 'milestones';
        //         ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);

        $milestone_id = $request->get_param('search_milestone_id');
        $milestone_name = $request->get_param('search_milestone_name');
        
        $where = '';  
        $order_by_query="ORDER BY $milestones_table.milestone_id DESC";

        // if( isset($request->get_param('orderby')) && isset($request->get_param('order')) ){
            $orderby = $request->get_param('orderby');
            if(!empty($orderby)){
                $order = $request->get_param('order');
                $order_by_query = " ORDER BY $milestones_table.".$orderby." ".$order;
            }
        // }

        $query = "SELECT $milestones_table.* FROM $milestones_table WHERE $milestones_table.deleted_at IS NULL ".$order_by_query;

        // $query = "SELECT $milestones_table.*
        //                         FROM $milestones_table
        //                         WHERE $milestones_table.deleted_at IS NULL $where
        //                         ORDER BY $milestones_table.milestone_id DESC
        //                         ";
        // // echo 'query=';
        // echo $query;
        return $wpdb->get_results($query);
        // return true;
    }

    // Callbacks for Milestone Search List Endpoints
    public function milestone_search($request) {
        global $wpdb;
        $milestones_table = $wpdb->prefix . 'milestones';
        /*
         $data = $request->get_json_params();
        $milestone_id = $data['search_milestone_id'];
        $milestone_name = $data['search_milestone_name'];
        $map_lead = isset($data['search_map_lead']) ? $data['search_map_lead'] : '';
        $map_opportunity = isset($data['search_map_opportunity']) ? $data['search_map_opportunity'] : '';
        $map_account = isset($data['search_map_account']) ? $data['search_map_account'] : '';
        $status = $data['search_status'];
        $search_date = $data['search_created_at'];
        */
        $milestone_id = $request->get_param('search_milestone_id');
        $milestone_name = $request->get_param('search_milestone_name');
        $search_map_lead = $request->get_param('search_map_lead');
        $map_lead = isset($search_map_lead) ? $search_map_lead : '';
        $search_map_opportunity = $request->get_param('search_map_opportunity');
        $map_opportunity = isset($search_map_opportunity) ? $search_map_opportunity : '';
        $search_map_project = $request->get_param('search_map_project');
        $map_project = isset($search_map_project) ? $search_map_project : '';
        $search_map_account = $request->get_param('search_map_account');
        $map_account = isset($search_map_account) ? $search_map_account : '';
        $status = $request->get_param('search_status');
        $search_date = $request->get_param('search_created_at');
        $search_product_id = $request->get_param('search_product_id');
        
        // $k=1;
        $where = '';  
        foreach ($search_product_id as $pkey => $pvalue) {
            $where .= "  AND FIND_IN_SET($pvalue,$milestones_table.product_id)";
        }
        // echo "==1where1==";
        //     echo $where;

        if(isset($milestone_id) && $milestone_id != ''){
            $where .= " AND $milestones_table.milestone_id = '".$milestone_id."'";
        }

        if(isset($milestone_name) && !empty($milestone_name)){
            $where .= " AND $milestones_table.milestone_name like '%".$milestone_name."%'";
        }

        if ($map_lead == 'on') {
            $where .= " AND $milestones_table.map LIKE '%\"lead\"%'";
        }
        
        if ($map_opportunity == 'on') {
            $where .= " AND $milestones_table.map LIKE '%\"opportunity\"%'";
        }
        
        if ($map_project == 'on') {
            $where .= " AND $milestones_table.map LIKE '%\"project\"%'";
        }

        if ($map_account == 'on') {
            $where .= " AND $milestones_table.map LIKE '%\"account\"%'";
        }

        if(isset($status) && !empty($status)){
            $where .= " AND $milestones_table.status='".$status."'";
        }

        if(isset($search_date) && $search_date != ''){
            // Calculate the start and end of the day
            $start_of_day = date('Y-m-d', strtotime($search_date)) . ' 00:00:00';
            $end_of_day = date('Y-m-d H:i:s', strtotime($search_date . ' +1 day'));
            
            $where .= " AND $milestones_table.created_at >= '$start_of_day' AND $milestones_table.created_at < '$end_of_day'";
        }

            $order_by_query=" ORDER BY $milestones_table.milestone_id DESC";
            $orderby = $request->get_param('orderby');
            if(!empty($orderby)){
                $order = $request->get_param('order');
                $order_by_query = " ORDER BY $milestones_table.".$orderby." ".$order;
            }

        $query = "SELECT $milestones_table.* FROM $milestones_table WHERE $milestones_table.deleted_at IS NULL $where $order_by_query";

        // echo 'query=';
        // echo $query;
        return $wpdb->get_results($query);
    }

    // Callbacks for product status Endpoints
    public function get_milestone_detail($request) {
        global $wpdb;
        $mile_id = $request->get_param('id');
        $milestones_table = $wpdb->prefix . 'milestones';
        $query = $wpdb->prepare("SELECT 
                                $milestones_table.*
                                FROM $milestones_table 
                                WHERE $milestones_table.milestone_id = ".$mile_id."
                                ");
        return $wpdb->get_row($query);
    }
    
    // check milestone unique or not
    public function check_milestone_unique($request){
        global $wpdb;
        
        $milestone_name = $request->get_param('milestone_name');
        $product_id = $request->get_param('product_id');
        $milestone_id = $request->get_param('milestone_id');
        
        $milestones_table = $wpdb->prefix . 'milestones';
        if(isset($milestone_id) && !empty($milestone_id)){
            $miles_data = $wpdb->get_results("SELECT $milestones_table.milestone_id,$milestones_table.milestone_name,$milestones_table.product_id FROM $milestones_table WHERE $milestones_table.milestone_name = '".$milestone_name."' AND $milestones_table.deleted_at IS NULL AND milestone_id!=".$milestone_id."");
        }else{
            $miles_data = $wpdb->get_results("SELECT $milestones_table.milestone_id,$milestones_table.milestone_name,$milestones_table.product_id FROM $milestones_table WHERE $milestones_table.milestone_name = '".$milestone_name."' AND $milestones_table.deleted_at IS NULL");
        }
        
        // print_r($miles_data);
        $match=0;
        if(count($miles_data)!=0){
            $match=1;
        }//miles data
        
        if($match==1){
            $reuslts['status'] = 400;
            $reuslts['message'] = 'Milestone Already Created.';
            echo json_encode($reuslts);die;
        }else{
            $reuslts['status'] = 200;
            $reuslts['message'] = 'Milestone Not Created.';
            echo json_encode($reuslts);die;
        }
    }

    // check Milestone Stage is unique or not
    public function check_milestage_unique($request){
        global $wpdb;
        
        $milestone_id = $request->get_param('milestone_id');
        $stage_name = $request->get_param('stage_name');
        $milestone_stage_id = $request->get_param('milestone_stage_id');
        
        $table = $wpdb->prefix . 'milestone_stages';
        if(isset($milestone_stage_id) && !empty($milestone_stage_id)){
            $miles_data = $wpdb->get_results("SELECT milestone_stage_id FROM $table WHERE stage_name = '".$stage_name."' AND deleted_at IS NULL AND milestone_id = $milestone_id AND milestone_stage_id != $milestone_stage_id");
        }else{
            $miles_data = $wpdb->get_results("SELECT milestone_stage_id FROM $table WHERE stage_name = '".$stage_name."' AND deleted_at IS NULL AND milestone_id = $milestone_id");
        }
        
        $match=0;
        if(count($miles_data)!=0){
            $match=1;
        }//miles data
        
        if($match==1){
            $reuslts['status'] = 400;
            $reuslts['message'] = 'Stage Name Already Exists.';
            echo json_encode($reuslts);
        }else{
            $reuslts['status'] = 200;
            $reuslts['message'] = 'Valid Stage Name.';
            echo json_encode($reuslts);
        }
        die;
    }

    

    public function check_opportunity_unique($request){
            global $wpdb;

        $OpportunityName = $request->get_param('OpportunityName');
        $Opportunity_id = $request->get_param('Opportunity_id');
        $LeadID = $request->get_param('lead_id');
        $product_id = $request->get_param('product_id');

        $table_name = $wpdb->prefix . 'opportunities';
        $oppo_product = $wpdb->prefix . 'opportunity_products';

        if(isset($Opportunity_id) && $Opportunity_id > 0){
            $opps_data = $wpdb->get_results("SELECT $table_name.OpportunityID FROM $table_name WHERE $table_name.OpportunityName = '".$OpportunityName."' AND $table_name.DeletedAt IS NULL AND OpportunityID !=".$Opportunity_id."");
            $Opportunity_where = " AND $table_name.OpportunityID !=".$Opportunity_id;
        }else{
            
            $Opportunity_where = "";
            $opps_data = $wpdb->get_results("SELECT $table_name.OpportunityID FROM $table_name WHERE $table_name.OpportunityName = '".$OpportunityName."' AND $table_name.DeletedAt IS NULL ");
        }
        
        try{
            $lead_oppo_data = $wpdb->get_results("SELECT $table_name.OpportunityID FROM $table_name JOIN $oppo_product ON $table_name.OpportunityID=$oppo_product.Opportunity_id WHERE $table_name.LeadID = '".$LeadID."' AND $table_name.DeletedAt IS NULL AND $oppo_product.product_id='".$product_id."'" .$Opportunity_where );
        }CATCH(EXCEPTION $e){
            echo $e->getMessage();
        }
        
        // echo "SELECT $table_name.OpportunityID FROM $table_name JOIN $oppo_product ON $table_name.OpportunityID=$oppo_product.Opportunity_id WHERE $table_name.LeadID = '".$LeadID."' AND $table_name.DeletedAt IS NULL AND $oppo_product.product_id='".$product_id."'" .$Opportunity_where ;
        // print_r($miles_data);

        $match=0;
        if(count($opps_data)!=0){
            $match=1;
        }else if(count($lead_oppo_data)!=0){
            $match=2;
        }
        if($match==1){
            $reuslts['status'] = 400;
            $reuslts['message'] = "This Opportunity already created.";
            echo json_encode($reuslts);die;
        }else if($match == 2){
            $reuslts['status'] = 400;
            $reuslts['message'] = "Can't create same product & same lead opportunity.";
            echo json_encode($reuslts);die;
        }else{
            $reuslts['status'] = 200;
            $reuslts['message'] = 'Opportunity Not Created.';
            echo json_encode($reuslts);die;
        }
    }

    /**
     * Create a new product status.
     *
     * @param array $data product status data.
     * @return int|WP_Error The ID of the newly created product status, or WP_Error on failure.
     */
    public function create_product_status(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_status';
        $result = $wpdb->insert($table_name, $data);

        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to insert product status into database', $wpdb->last_error);
        }else{
            $product_status_id = $wpdb->insert_id;
            $fields[0]['product_id'] = $data['product_id'];
            $fields[1]['status'] = $data['status'];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['created_at'];
                    $logs_data['CreatedBy'] = $data['created_by'];
                    $logs_data['TableName'] = $wpdb->prefix.'product_status';
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = 'N/A';
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $product_status_id;
                    $logs_data['Action'] = 'Create';
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                }
            }
        }

        return $wpdb->insert_id;
    }
    /**
     * Edit Product Status.
     *
     * @param array $data Product Status data.
     * @return int|WP_Error The ID of the previously created Product Status, or WP_Error on failure.
     */
    public function edit_product_status(WP_REST_Request $request) {
        global $wpdb;
        $product_status_id = $request->get_param('product_status_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_status';
        unset($data['currency_id']);
        //Audit Logs Start
        $fields[0]['product_id'] = $data['product_id'];
        $fields[1]['status'] = $data['status'];
        $i = 0;
        $audit_logs = [];
        foreach($fields as $field){
            foreach($field as $FieldName => $fieldValue){
                $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."' AND product_status_id = ".$product_status_id."");
                if(empty($checkPreviousData)){
                    $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE product_status_id = ".$product_status_id."");
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['modified_at'];
                    $logs_data['CreatedBy'] = $data['modified_by'];
                    $logs_data['TableName'] = $table_name;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $product_status_id;
                    $logs_data['Action'] = 'Update';
                    $audit_logs[$i]['logs_data'] = $logs_data;
                    $i++;
                }
            }
        }
        //Audit Logs End
        $result = $wpdb->update($table_name, $data, array('product_status_id' => $product_status_id));
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to update product status into database', $wpdb->last_error);
        }else{
            if(!empty($audit_logs)){
                foreach($audit_logs as $audit_log){
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                }
            }
        }

        return $product_status_id;
    }
    // Callbacks for product status Endpoints
    public function get_product_status($request) {
        global $wpdb;
        $product_status_id = $request->get_param('id');
        $product_status_table = $wpdb->prefix . 'product_status';
        $query = $wpdb->prepare("SELECT 
                                $product_status_table.product_status_id,$product_status_table.product_id,$product_status_table.status
                                FROM $product_status_table 
                                WHERE $product_status_table.product_status_id = '".$product_status_id."'
                                ");
        return $wpdb->get_row($query);
    }

    // Callbacks for milestone Endpoints
    public function get_product_all_milestones($request) {
        
        global $wpdb;
        $product_id = $request->get_param('id');
        $milestones_table = $wpdb->prefix . 'milestones';
        $milestone_stages_table = $wpdb->prefix . 'milestone_stages';
        if($request->get_param('type')){
            $type = $request->get_param('type');
             $query = "SELECT $milestones_table.milestone_id,$milestones_table.milestone_name, $milestones_table.deleted_at , $milestones_table.status, $milestones_table.map FROM $milestones_table WHERE FIND_IN_SET($product_id,$milestones_table.product_id) AND $milestones_table.map LIKE '%\"".$type."\"%'";
         }else{
            $query = "SELECT $milestones_table.milestone_id,$milestones_table.milestone_name, $milestones_table.deleted_at , $milestones_table.status, $milestones_table.map FROM $milestones_table WHERE FIND_IN_SET($product_id,$milestones_table.product_id) ";
        }
        $all_milestones = $wpdb->get_results($query);
        $count=0;
        foreach($all_milestones as $singleMilestone){
            $all_milestones[$count]->map = json_encode(unserialize($singleMilestone->map));
            $count++; 
        }

        
        if(!empty($all_milestones)){
            $data['milestone_results'] = $all_milestones;
            if($request->get_param('milestone_id')){
                $milestone_id = $request->get_param('milestone_id');
            }else{
                $milestone_id = $all_milestones[0]->milestone_id;
            }
            $stage_results = $wpdb->get_results("SELECT $milestone_stages_table.milestone_stage_id,$milestone_stages_table.stage_name, $milestone_stages_table.deleted_at , $milestone_stages_table.status FROM $milestone_stages_table WHERE milestone_id = ".$milestone_id."");
            if(!empty($stage_results)){
                $data['stage_results'] = $stage_results;
            }else{
                $data['stage_results'] = array();
            }
        }else{
            $data['milestone_results'] = array();
            $data['stage_results'] = array();
        }
        //return $wpdb->get_results($query);
        echo json_encode($data);die;
    }

    // Callbacks for milestone stage for product
    public function get_milestone_stage_for_product($request) {
        
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);
        global $wpdb;
        $data = array();
        $removedProductList = $request->get_param('removedProductList');
        $new_products = $request->get_param('new_products');
        $old_products = $request->get_param('old_products');
        $milestone_id = $request->get_param('milestone_id');

        $milestones_table = $wpdb->prefix . 'milestones';
        $milestone_stages_table = $wpdb->prefix . 'milestone_stages';
        $product_table = $wpdb->prefix . 'crm_products';
        $opportinty_product_table = $wpdb->prefix . 'opportunity_products';


        $deleted_product_array = explode(",", $removedProductList);
        $html = '';
        $html .= '<div class="productContainerMain">';
        $html .= '<div class="productContainer productContainerHeader"><div class="productDetail">Product Name</div><div class="milestoneStageContainer"><div class="milestoneContainer">Select Milestone</div><div class="stageContainer">Select Stage</div><div class="opportuintyContainer">Total Opportunity</div></div></div>';

        foreach($deleted_product_array as $deleted_product){
            $query = "SELECT * FROM $milestones_table WHERE FIND_IN_SET($deleted_product,$milestones_table.product_id) and $milestones_table.milestone_id != $milestone_id and $milestones_table.status = 'active' and $milestones_table.deleted_at IS NULL and $milestones_table.map like '%opportunity%'";
            $all_milestones = $wpdb->get_results($query);

            $product_query = "SELECT * FROM $product_table WHERE $product_table.ProductID = $deleted_product";
            $single_product = $wpdb->get_results($product_query)[0];

            $html .= '<div class="productContainer">';
            $html .= '<div class="productDetail">'.$single_product->Title.'</div>';
    
            if( count($all_milestones)>0){
                $html .= '<div class="milestoneStageContainer">';

                    $html .= '<div class="milestoneContainer">';
                    $count=0;        
                                       
                    $html .= '<select class="form-control custom-mile-status select-milestone milestone'.$deleted_product.'" id="Milestone" product_id="'.$deleted_product.'" style="">';
                    if( count($all_milestones)>0){
                        foreach($all_milestones as $singleMilestone){
                           $html .= '<option value="'.$singleMilestone->milestone_id.'" ="">'.$singleMilestone->milestone_name.'</option>';
                            $count++; 
                        }
                    }
                    $html .= '</select>';
                    $html .= '</div>';

                    $html .= '<div class="stageContainer">';
                    
                        $first_milestones = $all_milestones[0];
                        $stage_query = "SELECT * FROM $milestone_stages_table WHERE $milestone_stages_table.milestone_id = $first_milestones->milestone_id && $milestone_stages_table.deleted_at IS NULL && $milestone_stages_table.status = 'active'";
                        $all_stage = $wpdb->get_results($stage_query);


                    
                        $html .= '<select class="form-control custom-mile-status stage'.$deleted_product.'" id="MilestoneStage" name="milestone_status-23" style="" >';
                        if( count($all_stage)>0){
                            foreach ($all_stage as $single_stage) {
                                $html .= '<option value="'.$single_stage->milestone_stage_id.'">'.$single_stage->stage_name.'</option>';
                            }
                        }else{
                            $html .= '<option value="">Select Stage</option>';
                        }
                        $html .= '</select>';
                    

                    
                    $html .= '</div>';

                

                $opportunity_query = "SELECT count(*) as opportunity_count FROM $opportinty_product_table WHERE $opportinty_product_table.product_id = $deleted_product and $opportinty_product_table.milestone_id = $milestone_id and $opportinty_product_table.DeletedAt IS NULL ";
                $opportunity_count = $wpdb->get_results($opportunity_query)[0]->opportunity_count;
                $html .= '<div class="opportuintyContainer" >'.$opportunity_count.'</div>';
                $html .= '</div>';
            }else{
                $html .= '<div class="milestoneStageContainer"><p>No Other Milestone Found for this product. So You can not remove/unmap this product from opportunity.</p></div>';
            }
            
            $html .= '</div>';
        }
        
        $html .= '</div>';



        //return $wpdb->get_results($query);
        $data['html'] = $html;
        $data['status'] = true;
        $data['message'] = 'Milestone Successfully fetched';
        echo json_encode($data);die;
    }


    // Callbacks for milestone stage for product
    public function get_milestones_for_add_opportunity($request) {
                global $wpdb;
        $product_id = $request->get_param('id');
        $milestones_table = $wpdb->prefix . 'milestones';
        $milestone_stages_table = $wpdb->prefix . 'milestone_stages';
        
        $query = "SELECT $milestones_table.milestone_id,$milestones_table.milestone_name, $milestones_table.deleted_at , $milestones_table.status, $milestones_table.map FROM $milestones_table WHERE FIND_IN_SET($product_id,$milestones_table.product_id) and $milestones_table.status = 'active' and $milestones_table.map like '%opportunity%' and $milestones_table.deleted_at is null";
        
        $all_milestones = $wpdb->get_results($query);
        $count=0;
        foreach($all_milestones as $singleMilestone){
            $all_milestones[$count]->map = json_encode(unserialize($singleMilestone->map));
            $count++; 
        }

        
        if(!empty($all_milestones)){
            $data['milestone_results'] = $all_milestones;
            if($request->get_param('milestone_id')){
                $milestone_id = $request->get_param('milestone_id');
            }else{
                $milestone_id = $all_milestones[0]->milestone_id;
            }
            $stage_results = $wpdb->get_results("SELECT $milestone_stages_table.milestone_stage_id,$milestone_stages_table.stage_name, $milestone_stages_table.deleted_at , $milestone_stages_table.status FROM $milestone_stages_table WHERE milestone_id = ".$milestone_id."");
            if(!empty($stage_results)){
                $data['stage_results'] = $stage_results;
            }else{
                $data['stage_results'] = array();
            }
        }else{
            $data['milestone_results'] = array();
            $data['stage_results'] = array();
        }
        //return $wpdb->get_results($query);
        echo json_encode($data);die;
        
                        
    }

    // Updating milestoneopportunity mapping based on milestone
    public function update_opportunity_mapping($request){
        ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);
        global $wpdb;

        $milestones_table = $wpdb->prefix . 'milestones';
        $milestone_stages_table = $wpdb->prefix . 'milestone_stages';
        $product_table = $wpdb->prefix . 'crm_products';
        $opportinty_product_table = $wpdb->prefix . 'opportunity_products';
        $opportunities_table = $wpdb->prefix . 'opportunities';

        

        $data = json_decode($request->get_param('myArray'));
        //print_r($data);
        //echo '<br>';
        $all_product_data = $data->data;
        $removedProductArray = $data->removedProductArray;
        $new_products = $data->new_products;
        $old_products = $data->old_products;
        $milestone_id = $data->milestone_id;
        $mile_status = $data->mile_status;
        $mile_name = $data->mile_name;
        $mile_map = $data->mile_map;
        $user_name = $data->user_name;

        $old_removedProductArray = explode(",", $removedProductArray);
        $new_products_array = explode(",", $new_products);
        $new_removed_product = array();

        foreach($all_product_data as $key => $value){

            //creating activity log
            $opp_query = "select $opportinty_product_table.opportunity_id , $milestones_table.milestone_name, $opportunities_table.LeadID  from $opportinty_product_table left join $milestones_table on $opportinty_product_table.milestone_id = $milestones_table.milestone_id left join $opportunities_table on $opportinty_product_table.opportunity_id = $opportunities_table.OpportunityID WHERE $opportinty_product_table.milestone_id = $milestone_id and $opportinty_product_table.product_id = $value->product_id";
            $opp_result = $wpdb->get_results($opp_query);
            //print_r($opp_result);
            array_push( $new_removed_product, $value->product_id);
            $query = "UPDATE $opportinty_product_table SET milestone_id = '$value->new_milestone', milestone_stage_id = '$value->stage' WHERE milestone_id = $milestone_id and product_id = $value->product_id";
            $result = $wpdb->get_results($query);
            //echo '<br>';



            $milestone_query = "select $milestones_table.milestone_name  from  $milestones_table WHERE $milestones_table.milestone_id = $value->new_milestone ";
            $milestone_result = $wpdb->get_results($milestone_query)[0];


            foreach($opp_result as $single_result){
                $note = 'Occams Sales made an update to the opportunity';
                update_activity_log($single_result->LeadID, $single_result->opportunity_id, $single_result->milestone_name, $milestone_result->milestone_name, 'milestone', $user_name, $note); 
            }
        }
        $unique_values = array_diff($old_removedProductArray, $new_removed_product);

        $combinedNewProduct = implode(',',array_merge($new_products_array, $unique_values));
        $mapping_data = serialize(explode(",", $mile_map));

        $query = "UPDATE $milestones_table SET milestone_name = '$mile_name', product_id = '$combinedNewProduct' , status = '$mile_status' , map = '$mapping_data' WHERE milestone_id = $milestone_id;";
        $result = $wpdb->get_results($query);
        
        $reuslts['status'] = 1;
        $reuslts['message'] = 'Milestone succesfully updated';
        echo json_encode($reuslts);die;
        
        die;
    }

    // Callbacks for milestone status Endpoints
    public function get_product_all_milestone_status($request) {
        
        global $wpdb;
        $milestone_id = $request->get_param('id');
        if(empty($milestone_id)){
            return '';
        }
        $milestone_status_table = $wpdb->prefix . 'milestone_stages';
        $query = $wpdb->prepare("SELECT $milestone_status_table.milestone_stage_id,$milestone_status_table.stage_name, $milestone_status_table.status, $milestone_status_table.deleted_at FROM $milestone_status_table WHERE $milestone_status_table.milestone_id = ".$milestone_id." ");
        return $wpdb->get_results($query);

    }

    public function get_probability($request) {
        global $wpdb;
        $product = $request->get_param('product');
        $milestone = $request->get_param('milestone');
        $stage = $request->get_param('stage');
        
        if (empty($product) || empty($milestone) || empty($stage)) {
            return '';
        }
    
        $lead_stage_probability_table = $wpdb->prefix . 'lead_stage_probability';
        $query = $wpdb->prepare(
            "SELECT probability FROM $lead_stage_probability_table WHERE product = %s AND milestone = %s AND stage = %s",
            $product, $milestone, $stage
        );
    
        $probability = $wpdb->get_var($query);
        return $probability ? $probability : '';
    }     

    public function create_milestone(WP_REST_Request $request){
            global $wpdb;
            $data = $request->get_json_params();
            $milestones_table = $wpdb->prefix . 'milestones';
            
            $fields[0]['milestone_name'] = $data['milestone_name'];
            $fields[1]['product_id'] = $data['product_id'];
            $fields[2]['map'] = $data['map'];
            $fields[3]['status'] = $data['status'];
            $fields[4]['created_at'] = $data['created_at'];
            $fields[5]['created_by'] = $data['created_by'];

            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$milestones_table."' AND COLUMN_NAME = '".$FieldName."'");
                        
                        $logs_data['DateCreated'] = $data['created_at'];
                        $logs_data['CreatedBy'] = $data['created_by'];
                        $logs_data['TableName'] = $milestones_table;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = '';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = '';
                        $logs_data['Action'] = 'Create';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                }
            }

            $result = $wpdb->insert($milestones_table, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to create product into database', $wpdb->last_error);
            }else{
            
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }

                $reuslts['status'] = 1;
                $reuslts['message'] = 'Milestone Created.';
                echo json_encode($reuslts);die;
            }
    }

    public function edit_milestone(WP_REST_Request $request){
            global $wpdb;
            $data = $request->get_json_params();
            $milestones_table = $wpdb->prefix . 'milestones';
            $milestone_id = $data['milestone_id'];
            
            $fields[0]['milestone_name'] = $data['milestone_name'];
            $fields[1]['product_id'] = $data['product_id'];
            $fields[2]['status'] = $data['status'];
            $fields[3]['modified_at'] = $data['modified_at'];
            $fields[4]['modified_by'] = $data['modified_by'];

            $audit_logs = [];

            foreach($fields as $field){
            foreach($field as $FieldName => $fieldValue){
                $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $milestones_table WHERE $FieldName = '".$fieldValue."'  AND milestone_id = ".$milestone_id."");
                if(empty($checkPreviousData)){
                    $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $milestones_table WHERE milestone_id = ".$milestone_id."");
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$milestones_table."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['modified_at'];
                    $logs_data['CreatedBy'] = $data['modified_by'];
                    $logs_data['TableName'] = $milestones_table;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $milestone_id;
                    $logs_data['Action'] = 'Update';
                    $audit_logs[$i]['logs_data'] = $logs_data;
                    $i++;
                }
            }
        }


            $result = $wpdb->update($milestones_table, $data, array('milestone_id' => $milestone_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to create product into database', $wpdb->last_error);
            }else{
                
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }

                $reuslts['status'] = 1;
                $reuslts['message'] = 'Milestone Updated.';
                echo json_encode($reuslts);die;
            }

    }

    public function create_milestone_stage(WP_REST_Request $request){
        global $wpdb;
        $data = $request->get_json_params();
        $milestatus_table = $wpdb->prefix . 'milestone_stages';
        // Execute the query and get a single value
        $fields[0]['milestone_id'] = $data['milestone_id'];
        $fields[1]['stage_name'] = $data['stage_name'];
        $fields[2]['status'] = $data['status'];
        $fields[3]['probability'] = $data['probability'];
        $fields[4]['definition'] = $data['definition'];
        $fields[5]['created_at'] = $data['created_at'];
        $fields[6]['created_by'] = $data['created_by'];

        $i = 0;
        $audit_logs = [];
        foreach($fields as $field){
            foreach($field as $FieldName => $fieldValue){
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$milestatus_table."' AND COLUMN_NAME = '".$FieldName."'");
                    
                    $logs_data['DateCreated'] = $data['created_at'];
                    $logs_data['CreatedBy'] = $data['created_by'];
                    $logs_data['TableName'] = $milestatus_table;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = '';
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = '';
                    $logs_data['Action'] = 'Create';
                    $audit_logs[$i]['logs_data'] = $logs_data;
                    $i++;
            }
        }
        
        $result = $wpdb->insert($milestatus_table, $data);
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to create Milestone Stage into database', $wpdb->last_error);
        }else {

            $milestone_manager = new CRM_ERP_Milestone_Manager();
            $fetched_milestone = $milestone_manager->get_milestone($data['milestone_id']);
            if ($fetched_milestone) {
                $mapArray = unserialize($fetched_milestone->map);

                if (is_array($mapArray) && in_array('opportunity', $mapArray)) {
                    $prob_data['milestone'] = $fetched_milestone->milestone_name;
                    $prob_data['stage'] = $data['stage_name'];

                    $product_ids = $fetched_milestone->product_id;
                    $all_products = explode(",",$product_ids);
                    $product_names='';
                    foreach ($all_products as $all_key => $all_value) {
                        $milestone_manager = new CRM_ERP_Milestone_Manager();
                        $product_name = $milestone_manager->get_product_name($all_value);
                        if(empty($product_names)){
                            $product_names .= $product_name->Title;
                        }else{
                            $product_names .= ','.$product_name->Title;
                        }
                    }
                    $prob_data['product'] = ucwords($product_names);

                    $prob_data['probability'] = (!empty($data['probability']) && $data['probability'] != 0) ? $data['probability'] : 'n/a';

                    $result = $wpdb->insert($wpdb->prefix . 'lead_stage_probability', $prob_data);
                    if ($result === false) {
                        return new WP_Error('db_insert_error', 'Failed to insert probability into database', $wpdb->last_error);
                    }
                }

            }
        
            if(!empty($audit_logs)){
                foreach($audit_logs as $audit_log){
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                }
            }

            $reuslts['status'] = 1;
            $reuslts['message'] = 'Milestone Stage Created.';
            echo json_encode($reuslts);die;
        }
    }

    public function edit_milestone_stage(WP_REST_Request $request){
        global $wpdb;
        $data = $request->get_json_params();
        $milestatus_table = $wpdb->prefix . 'milestone_stages';
        $milestone_stage_id = $data['milestone_stage_id'];
        
        $fields[0]['milestone_id'] = $data['milestone_id'];
        $fields[1]['stage_name'] = $data['stage_name'];
        $fields[2]['status'] = $data['status'];
        $fields[3]['probability'] = $data['probability'];
        $fields[4]['definition'] = $data['definition'];
        $fields[5]['modified_at'] = $data['modified_at'];
        $fields[6]['modified_by'] = $data['modified_by'];

        $audit_logs = [];

        foreach($fields as $field){
            foreach($field as $FieldName => $fieldValue){
                $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $milestatus_table WHERE $FieldName = '".$fieldValue."'  AND milestone_stage_id = ".$milestone_stage_id."");
                if(empty($checkPreviousData)){
                    $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $milestatus_table WHERE milestone_stage_id = ".$milestone_stage_id."");
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$milestatus_table."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['modified_at'];
                    $logs_data['CreatedBy'] = $data['modified_by'];
                    $logs_data['TableName'] = $milestatus_table;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $milestone_stage_id;
                    $logs_data['Action'] = 'Update';
                    $audit_logs[$i]['logs_data'] = $logs_data;
                    $i++;
                }
            }
        }
        
        $result = $wpdb->update($milestatus_table, $data, array('milestone_stage_id' => $milestone_stage_id));
        
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to create Milestone Stage into database', $wpdb->last_error);
        }else{

            $milestone_manager = new CRM_ERP_Milestone_Manager();
            $fetched_milestone = $milestone_manager->get_milestone($data['milestone_id']);
            if ($fetched_milestone) {
                $mapArray = unserialize($fetched_milestone->map);

                if (is_array($mapArray) && in_array('opportunity', $mapArray)) {
                    $milestone = $fetched_milestone->milestone_name;
                    $stage = $data['stage_name'];

                    $product_ids = $fetched_milestone->product_id;
                    $all_products = explode(",",$product_ids);
                    $product_names='';
                    foreach ($all_products as $all_key => $all_value) {
                        $milestone_manager = new CRM_ERP_Milestone_Manager();
                        $product_name = $milestone_manager->get_product_name($all_value);
                        if(empty($product_names)){
                            $product_names .= $product_name->Title;
                        }else{
                            $product_names .= ','.$product_name->Title;
                        }
                    }
                    $product = ucwords($product_names);
                    $probability = (!empty($data['probability']) && $data['probability'] != 0) ? $data['probability'] : 'n/a';

                    $result = $wpdb->update(
                        $wpdb->prefix . 'lead_stage_probability',
                        array(
                            'probability' => $probability,
                        ),
                        array(
                            'product' => $product,
                            'milestone' => $milestone,
                            'stage' => $stage,
                        ),
                        array('%s'),
                        array('%s', '%s', '%s')
                    );
            
                    if ($result === false) {
                        return new WP_Error('db_insert_error', 'Failed to update probability in database', $wpdb->last_error);
                    }

                    $opportunity_table = $wpdb->prefix.'opportunities';
                    $opportunity_product_table = $wpdb->prefix.'opportunity_products';
                    $product_table = $wpdb->prefix.'crm_products';
                    $milestone_table = $wpdb->prefix.'milestones';
                    $milestone_status_table = $wpdb->prefix.'milestone_stages';

                    // Prepare the SQL query to get opportunities
                    $sql = $wpdb->prepare(
                        "SELECT opp.OpportunityID, opp.Probability 
                        FROM $opportunity_table opp 
                        LEFT JOIN $opportunity_product_table oppPro ON opp.OpportunityID = oppPro.opportunity_id 
                        LEFT JOIN $product_table prod ON oppPro.product_id = prod.ProductID 
                        LEFT JOIN $milestone_table mile ON oppPro.milestone_id = mile.milestone_id 
                        LEFT JOIN $milestone_status_table mile_status ON oppPro.milestone_stage_id = mile_status.milestone_stage_id 
                        WHERE mile.milestone_name = %s AND mile_status.stage_name = %s AND prod.Title = %s",
                        $milestone,
                        $stage,
                        $product
                    );

                    // Get the results
                    $opportunities = $wpdb->get_results($sql);

                    if (!empty($opportunities)) {
                        foreach ($opportunities as $opportunity) {
                            $opportunity_id = $opportunity->OpportunityID;
                            
                            // Update the Probability for each matching OpportunityID
                            $update_result = $wpdb->update(
                                $opportunity_table,
                                array('Probability' => $probability),
                                array('OpportunityID' => $opportunity_id),
                                array('%d'),
                                array('%d')
                            );
                            
                            if ($update_result === false) {
                                return new WP_Error('db_insert_error', "Failed to update probability for OpportunityID: $opportunity_id", $wpdb->last_error);
                            }
                        }
                    }
                }

            }
            
            if(!empty($audit_logs)){
                foreach($audit_logs as $audit_log){
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                }
            }

            $reuslts['status'] = 1;
            $reuslts['message'] = 'Milestone Stage Updated.';
            echo json_encode($reuslts);die;
        }

    }


    public function create_opportunity(WP_REST_Request $request) {
        global $wpdb;
        
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'opportunities';
        $opportunity_product_table = $wpdb->prefix . 'opportunity_products';
            
            // wp_mail("<EMAIL>","test",json_encode($data));

            $post_data['OpportunityName'] = $data['OpportunityName'];
            $post_data['LeadID'] = $data['LeadID'];
            $post_data['ContactID'] = $data['ContactID'];
            $post_data['ExpectedCloseDate'] = date("Y-m-d", strtotime($data['ExpectedCloseDate']));
            $post_data['OpportunityCurrency'] = $data['OpportunityCurrency'];
            $post_data['OpportunityAmount'] = $data['OpportunityAmount'];
            $post_data['Probability'] = $data['Probability'];
            // $post_data['OpportunityCategory'] = $data['OpportunityCategory'];
            $post_data['NextStep'] = $data['NextStep'];
            $post_data['Description'] = $data['Description'];
            $post_data['CreatedBy'] = $data['CreatedBy'];
            $post_data['CreatedAt'] = $data['CreatedAt'];

            // $results = $wpdb->get_results('DESCRIBE '.$table_name);

            // echo 'i am here4';print_r($results);exit;
            $result = $wpdb->insert($table_name, $post_data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to create product into database', $wpdb->last_error);
            }else{
                $opportunity_id = $wpdb->insert_id;
                $user_details = get_user_by( 'id', $data['CreatedBy']);
                
                if(isset($data['Notes']) && !empty($data['Notes'])){
                    // $note = $user_details->display_name.' created an Opportunity "'.$data['OpportunityName'].'" with comment - '.$data['Notes'];
                    $note = ' Opportunity Created By "'.$user_details->display_name .' - '.$data['OpportunityName'].'" with comment - '.$data['Notes'];
                }else{
                    // $note = $user_details->display_name.' created an Opportunity "'.$data['OpportunityName'].'"';
                    $note = ' Opportunity created by "'.$user_details->display_name.' - '.$data['OpportunityName'].'"';
                }
                
                $user_id = $data['CreatedBy'];
                $time = date("Y-m-d h:i:sa");                   
                $notes_table = $wpdb->prefix.'erc_opportunity_notes';
                $result = $wpdb->query("INSERT INTO `eccom_erc_opportunity_notes` (`opportunity_id`, `created_by`, `note`, `created`) VALUES ('".$opportunity_id."', '".$user_id."', '".$note."', '".$time."');");
                
                foreach($post_data as $FieldName => $fieldValue){
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                    

                    $logs_data['DateCreated'] = $data['CreatedAt'];
                    $logs_data['CreatedBy'] = $data['CreatedBy'];
                    $logs_data['TableName'] = $table_name;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = '';
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $opportunity_id;
                    $logs_data['Action'] = 'Create';
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                }

                $total_product = $data['all_product-listing'];
                $product_da['opportunity_id'] = $opportunity_id;
                $product_da['CreatedAt'] = $data['CreatedAt'];
                $product_da['CreatedBy'] = $data['CreatedBy'];
                
                $total_products = 1;
                $set_product_no = 0;
                $i = 1;
                for($i=1;$i<=$total_products;$i++){
                    if(isset($data['product_id-'.$i])){
                        $product_da['product_id'] = $data['product_id-'.$i];
                        $product_da['milestone_id'] = $data['milestone-'.$i];
                        $product_da['milestone_stage_id'] = $data['milestone_status-'.$i];
                        //$product_da['product_amount'] = $data['product_amount-'.$i];
                        
                        $result = $wpdb->insert($opportunity_product_table, $product_da);
                        $product_insert_id = $wpdb->insert_id;
                        $set_product_no +=1;
                        
                         foreach($product_da as $pFieldName => $pfieldValue){
                            $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$opportunity_product_table."' AND COLUMN_NAME = '".$pFieldName."'");
                            $pro_logs_data['DateCreated'] = $data['CreatedAt'];
                            $pro_logs_data['CreatedBy'] = $data['CreatedBy'];
                            $pro_logs_data['TableName'] = $opportunity_product_table;
                            $pro_logs_data['FieldName'] = $pFieldName;
                            $pro_logs_data['DataType'] = $DATA_TYPE;
                            $pro_logs_data['BeforeValueString'] = 'N/A';
                            $pro_logs_data['AfterValueString'] = $pfieldValue;
                            $pro_logs_data['FieldID'] = $product_insert_id;
                            $pro_logs_data['Action'] = 'Create';
                            $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                            $audit_log_data = $audit_log_manager->record_audit_log($pro_logs_data);
                        }
					
                            // ------ create milestone stage log ----
                            $milestone_stage_log_table = $wpdb->prefix.'milestone_stage_log';
                            $mile_data = array();
                            $mile_data['lead_id']=$data['LeadID'];
                            $mile_data['project_id']=0;
                            $mile_data['opportunity_id']=$opportunity_id;
                            $mile_data['product_id']=$data['product_id-'.$i];
                            $mile_data['updated_milestone_id']=$data['milestone-'.$i];
                            $mile_data['previous_milestone_id']=0;
                            $mile_data['updated_stage_id']=$data['milestone_status-'.$i];
                            $mile_data['previous_stage_id']=0;
                            $mile_data['changed_date']=date("Y-m-d h:i:sa");
                            $mile_data['changed_by']=$data['CreatedBy'];

                            $insert_mile_log = $wpdb->insert($milestone_stage_log_table ,$mile_data);
                            // ------ create milestone stage log ----
                   }
                     if($total_product==$set_product_no){
                         break;
                     }
                }//loop    
                
                $i = 1;
                if($data['product_id-'.$i] == 936 || $data['product_id-'.$i] == '936' || $data['product_id-1'] == '936' || $data['product_id-1'] == 936){
                        
                        $lead_id = $data['LeadID'];
                        $additional_info = $wpdb->prefix.'erc_iris_leads_additional_info';
                        $sql =  "SELECT lead_id,created FROM $additional_info WHERE lead_id=".$lead_id." AND created<='2024-05-01 23:59:59'" ;
                        $data = $wpdb->get_results($sql);
                        if($data){
                            // mail sent
                        }
                    /*
                    $lead_id = $data['LeadID'];
                    $lead_ids = base64_encode($lead_id);
                    
                    $bus_table = $wpdb->prefix . 'erc_business_info';
                    $bus_data = $wpdb->get_results("SELECT business_legal_name,authorized_signatory_name,business_email FROM $bus_table WHERE lead_id='".$lead_id."'");
                    if(count($bus_data)!=0){
                        $business_legal_name = $bus_data[0]->business_legal_name;
                        $authorized_signatory_name = $bus_data[0]->authorized_signatory_name;
                        $business_email = $bus_data[0]->business_email;
                    }
                
                    $link = "https://portal.occamsadvisory.com/tax-amendment-agreement/?data=".$lead_ids;
                    
                    $ir_dynamic_var = array(
                        'id'=>98,
                        'to'=>$business_email,
                        'customer_name'=>$authorized_signatory_name,
                        'click_here_link'=>$link,
                        'dynamic'=>true,
                         'from'=> '<EMAIL>',
                         'password'=> 'Jah09143'
                      );
                    $email_sent_response = send_email_template($ir_dynamic_var);

                    $log_user_table = $wpdb->prefix.'log_user_data';
                    $created = date("Y-m-d H:i:s");
                    $wpdb->query("INSERT INTO $log_user_table (lead_id,agreement_type,created_at) VALUES (".$lead_id.",'tax','".$created."')");

                    $opport_data['last_agreement_sent']= date('Y-m-d H:i:s');
                    $opport_data['opportunityClosure'] = 'send_agreement';
                    $result = $wpdb->update($table_name, $opport_data, array('OpportunityID' => $opportunity_id));
                    */
                }    
				//Error in this code please correct it.
               /* if(isset($data['product_id-1'] == 936)){ // if product_id = Tax Amendment

                    // send Agreement

                }  */ 
                 
                $reuslts['status'] = 1;
                $reuslts['message'] = 'Opportunity Created';
                echo json_encode($reuslts);die;
            }    
    }// create opportunity function end

    //Edit Opportunity
    public function edit_opportunity(WP_REST_Request $request) {
        global $wpdb;
        $i = 0;
        $data = $request->get_json_params();
        $opportunity_id = $request->get_param('opportunity_id');
        $table_name = $wpdb->prefix . 'opportunities';
        $opportunity_product_table = $wpdb->prefix . 'opportunity_products';
        unset($data['OpportunityID']);
        $fields[0]['OpportunityName'] = $data['OpportunityName'];
        $fields[0]['LeadID'] = $data['LeadID'];
        $fields[0]['ExpectedCloseDate'] = $data['ExpectedCloseDate'];
        $fields[0]['OpportunityCurrency'] = $data['OpportunityCurrency'];
        $fields[0]['OpportunityAmount'] = $data['OpportunityAmount'];
        $fields[0]['Probability'] = $data['Probability'];
        //$fields[0]['OpportunityCategory'] = $data['OpportunityCategory'];
        $fields[0]['NextStep'] = $data['NextStep'];
        $fields[0]['Description'] = $data['Description'];
        $audit_logs = [];
        $post_data['OpportunityName'] = $data['OpportunityName'];
        $post_data['LeadID'] = $data['LeadID'];
        $post_data['ExpectedCloseDate'] = $data['ExpectedCloseDate'];
        $post_data['OpportunityCurrency'] = $data['OpportunityCurrency'];
        $post_data['OpportunityAmount'] = $data['OpportunityAmount'];
        $post_data['Probability'] = $data['Probability'];
        $post_data['OpportunityCategory'] = $data['OpportunityCategory'];
        $post_data['NextStep'] = $data['NextStep'];
        $post_data['Description'] = $data['Description'];
        $post_data['ModifiedBy'] = $data['ModifiedBy'];
        $post_data['ModifiedAt'] = $data['ModifiedAt'];
        foreach($fields as $field){
            foreach($field as $FieldName => $fieldValue){
                $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."'  AND OpportunityID = ".$opportunity_id."");
                if(empty($checkPreviousData)){
                    $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE OpportunityID = ".$opportunity_id."");
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['ModifiedAt'];
                    $logs_data['CreatedBy'] = $data['ModifiedBy'];
                    $logs_data['TableName'] = $wpdb->prefix.'opportunities';
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $opportunity_id;
                    $logs_data['Action'] = 'Update';
                    $audit_logs[$i]['logs_data'] = $logs_data;
                    $i++;
                }
            }
        }   
        //Audit Logs End
        $result = $wpdb->update($table_name, $post_data, array('OpportunityID' => $opportunity_id));
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to update opportunity into database', $wpdb->last_error);
        }else{
            $total_product = $data['all_product-listing'];
            $product_da['opportunity_id'] = $opportunity_id;
            $product_ids = '';
            for($i=1;$i<=$total_product;$i++){
                if(isset($data['product_id-'.$i])){
                    $product_ids .= $data['product_id-'.$i].',';
                    $opportunity_product_id = $data['opportunity_product_id-'.$i];
                    $product_da['product_id'] = $data['product_id-'.$i];
                    $product_da['milestone_id'] = $data['milestone-'.$i];
                    $product_da['milestone_status_id'] = $data['milestone_status-'.$i];
                    //$product_da['product_amount'] = $data['product_amount-'.$i];
                    $pro_fields[0]['product_id'] = $data['product_id-'.$i];
                    $pro_fields[0]['milestone_id'] = $data['milestone-'.$i];
                    $pro_fields[0]['milestone_status_id'] = $data['milestone_status-'.$i];
                    $pro_fields[0]['product_amount'] = $data['product_amount-'.$i];
                    $j = 0;
                    foreach($pro_fields as $pro_field){
                        foreach($pro_field as $ProFieldName => $profieldValue){
                            $checkProPreviousData = $wpdb->get_row("SELECT $ProFieldName FROM $opportunity_product_table WHERE $ProFieldName = '".$profieldValue."'  AND opportunity_product_id = ".$opportunity_product_id."");
                            if(empty($checkProPreviousData)){
                                $getProPreviousVal = $wpdb->get_row("SELECT $ProFieldName FROM $opportunity_product_table WHERE opportunity_product_id = ".$opportunity_product_id."");
                                $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$opportunity_product_table."' AND COLUMN_NAME = '".$ProFieldName."'");
                                if($opportunity_product_id > 0 ){
                                    $pro_logs_data['DateCreated'] = $data['ModifiedAt'];
                                    $pro_logs_data['CreatedBy'] = $data['ModifiedBy'];
                                    $pro_logs_data['BeforeValueString'] = $getProPreviousVal->$ProFieldName;
                                }else{
                                    $pro_logs_data['DateCreated'] = $data['CreatedAt'];
                                    $pro_logs_data['CreatedBy'] = $data['CreatedBy'];
                                    $pro_logs_data['BeforeValueString'] = 'N/A';
                                }
                                $pro_logs_data['TableName'] = $opportunity_product_table;
                                $pro_logs_data['FieldName'] = $ProFieldName;
                                $pro_logs_data['DataType'] = $DATA_TYPE;
                                $pro_logs_data['AfterValueString'] = $profieldValue;
                                $pro_logs_data['FieldID'] = $opportunity_product_id;
                                $pro_logs_data['Action'] = 'Update';
                                $pro_audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                                $pro_audit_log_data = $pro_audit_log_manager->record_audit_log($pro_logs_data);
                            }
                        }
                    }
                    if($opportunity_product_id > 0 ){
                        $product_da['ModifiedAt'] = $data['ModifiedAt'];
                        $product_da['ModifiedBy'] = $data['ModifiedBy'];
                        $result = $wpdb->update($opportunity_product_table, $product_da, array('opportunity_product_id' => $opportunity_product_id));
                    }else{
                        /*$product_da['CreatedAt'] = $data['CreatedAt'];
                        $product_da['CreatedBy'] = $data['CreatedBy'];
                        $result = $wpdb->insert($opportunity_product_table, $product_da);*/
                    }
                }
            }
            $product_ids = rtrim($product_ids,',');
            if($product_ids != ''){
                //echo "UPDATE $opportunity_product_table SET DeletedAt = '".$data['ModifiedAt']."', DeletedBy = ".$data['ModifiedBy']." WHERE product_id NOT IN (".$product_ids.")";die;
                $wpdb->query("UPDATE ".$opportunity_product_table." SET DeletedAt = '".$data['ModifiedAt']."', DeletedBy = ".$data['ModifiedBy']." WHERE product_id NOT IN (".$product_ids.")");
            }
            if(!empty($audit_logs)){
                foreach($audit_logs as $audit_log){
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                }
            }
            $reuslts['status'] = 1;
            $reuslts['message'] = 'Opportunity Updated';
            echo json_encode($reuslts);die;
        }
    }






    //Edit Opportunity with optional field
    public function edit_opportunity_optional_field($request) {
        
        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);

        global $wpdb;
        $reuslts = array();
        $opportunity_table = $wpdb->prefix . 'opportunities';
        $lead_stage_probability_table = $wpdb->prefix . 'lead_stage_probability';

        $OpportunityID = $request->get_param('OpportunityID');
        $NextStep = $request->get_param('NextStep');
        $opportunity_description = $request->get_param('opportunity_description');
        $ExpectedCloseDate = $request->get_param('ExpectedCloseDate');
        $OpportunityAmount = $request->get_param('OpportunityAmount');
        $Probability = $request->get_param('Probability');
        $OwnerList = $request->get_param('OwnerList');
        $ContactList = $request->get_param('ContactList');
        $opportunityClosure = $request->get_param('opportunityClosure');
        $envelop_id = $request->get_param('envelop_id');
        $last_agreement_sent = $request->get_param('last_agreement_sent');
        $user_id = $request->get_param('user_id');
        
        $business_legal_name = $request->get_param('business_legal_name');
        $business_email = $request->get_param('business_email');
        $state_formation = $request->get_param('state_formation');
        $street_address = $request->get_param('street_address');
        $zip = $request->get_param('zip');
        $city = $request->get_param('city');
        $state = $request->get_param('state');
        $authorized_signatory_name = $request->get_param('authorized_signatory_name');
        $auths_mobile_number = $request->get_param('auths_mobile_number');
        $business_title = $request->get_param('business_title');
        $hrms_value = $request->get_param('hrms_value');
        $estimated_emp = $request->get_param('estimated_emp');
        $tax_years = $request->get_param('tax_years');
        
        // --- new fields
        $erc_retainer = $request->get_param('erc_retainer');
        $erc_success_fee = $request->get_param('erc_success_fee');
        $erc_completion_fee = $request->get_param('erc_completion_fee');
        $erc_first_installment = $request->get_param('erc_first_installment');
        $erc_second_installment = $request->get_param('erc_second_installment');

        //getting old data
        $additional_table = $wpdb->prefix.'erc_iris_leads_additional_info';
        $business_info_table = $wpdb->prefix.'erc_business_info';
        $opportunity_table = $wpdb->prefix.'opportunities';
        $opportunity_product_table = $wpdb->prefix.'opportunity_products';
        $product_table = $wpdb->prefix.'crm_products';
        $milestone_table = $wpdb->prefix.'milestones';
        $milestone_status_table = $wpdb->prefix.'milestone_stages';
        $next_step_table = $wpdb->prefix.'next_step';
        $currency_table = $wpdb->prefix.'currencies';
        $contacts_table = $wpdb->prefix.'op_contacts';
        $where = ' WHERE 1=1 ';

        if(!empty($OpportunityID)){
            $where .= " AND opp.OpportunityID ='".$OpportunityID."'";
        }

        $old_sql = "SELECT opp.*, prod.Title as productnName,prod.ProductID as product_ID, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus, ns.next_step  as NextStepName, ns.next_step_id, buss.business_legal_name as AccountName, mile.milestone_id, mile_status.milestone_stage_id, ns.next_step, curr.currency_code as currencyName FROM $opportunity_table opp left join $opportunity_product_table oppPro on opp.OpportunityID = oppPro.opportunity_id left join $product_table prod on oppPro.product_id = prod.ProductID left join $milestone_table mile on oppPro.milestone_id = mile.milestone_id left join $milestone_status_table mile_status on oppPro.milestone_stage_id = mile_status.milestone_stage_id left join $next_step_table ns on opp.NextStep = ns.next_step_id left join $business_info_table buss on opp.LeadID = buss.lead_id left join $currency_table curr on opp.OpportunityCurrency = curr.currency_id " . $where;
        $old_opportunity = $wpdb->get_results($old_sql);
        $old_single_opportunity = $old_opportunity[0];
         
        $old_milestone = $old_single_opportunity->milestone_id;
        $old_milestonestage = $old_single_opportunity->milestone_stage_id;

        //print_r($request->get_param('OpportunityID'));exit;
        if(!empty($OpportunityID)){
            $opportunity_id = $OpportunityID;
            $post_data = array();

            $edit = 0;
            if(!empty($NextStep) || $NextStep == 0){
                if($NextStep == 0){
                    $NextStep = '';
                }
                $post_data['NextStep'] = $NextStep;
                $post_data['nextStep_deletedat'] = NULL;
                $post_data['nextStep_deletedby'] = NULL;
                $edit = 1;
            }
            if(!empty($ExpectedCloseDate)){
                $post_data['ExpectedCloseDate'] = date("Y-m-d", strtotime($ExpectedCloseDate));
                $edit = 1;
            }else{
                $ExpectedCloseDate = '';
            }
            if(!empty($opportunity_description)){
                $post_data['Description'] = $opportunity_description;
                $edit = 1;
            }
            if(!empty($opportunity_description) || $opportunity_description == 0){
                if($opportunity_description == 0){
                    $opportunity_description = '';
                }
                $post_data['Description'] = $opportunity_description;
                $edit = 1;
            }
            if(!empty($OpportunityAmount)){
                $post_data['OpportunityAmount'] = str_replace( ',', '', $OpportunityAmount);
                $OpportunityAmount = $post_data['OpportunityAmount'];
                $edit = 1;
            }
            /*if(!empty($Probability) || $Probability == 0){
                if($Probability == 0){
                    $Probability = '';
                }
                $post_data['Probability'] = $Probability;
                $edit = 1;
            }*/
            if(!empty($OwnerList)){
                $post_data['CreatedBy'] = $OwnerList;
                $edit = 1;
            }

            if(!empty($ContactList)){
                $post_data['ContactID'] = $ContactList;
                $post_data['contact_deletedat'] = NULL;
                $post_data['contact_deletedby'] = NULL;
                $edit = 1;
            }

            if(!empty($opportunityClosure)){
                $post_data['opportunityClosure'] = $opportunityClosure;
                $edit = 1;
            }
            
            if(!empty($envelop_id)){
                $post_data['envelop_id'] = $envelop_id;
            }

            if(!empty($last_agreement_sent)){
                $post_data['last_agreement_sent'] = $last_agreement_sent;
                $edit = 1;
            }

            if(!empty($business_legal_name)){
                $post_data['business_name'] = $business_legal_name;
                $edit = 1;
            }
            
            if(!empty($business_email)){
                $post_data['business_email'] = $business_email;
                $edit = 1;
            }
            if(!empty($state_formation)){
                    $post_data['state_of_formation'] = $state_formation;
                    $edit = 1;
            }
            if(!empty($tax_years)){
                $post_data['tax_years'] = $tax_years;
                $edit = 1;
            }
            if(!empty($street_address)){
                    $post_data['street_address'] = $street_address;
                    $edit = 1;
            }
            if(!empty($zip)){
                $post_data['zip'] = $zip;
                $edit = 1;
            }
            if(!empty($city)){
                    $post_data['city'] = $city;
                    $edit = 1;
            }
            if(!empty($state)){
                $post_data['state'] = $state;
                $edit = 1;
            }
            if(!empty($estimated_emp)){
                    $post_data['eligible_employees'] = $estimated_emp;
                    $edit = 1;
            }
            if(!empty($authorized_signatory_name)){
                $post_data['authorized_signatory'] = $authorized_signatory_name;
                $edit = 1;
            }
            if(!empty($auths_mobile_number)){
                    $post_data['authorized_signatory_phone'] = $auths_mobile_number;
                    $edit = 1;
            }
            if(!empty($business_title)){
                $post_data['business_title'] = $business_title;
                $edit = 1;
            }
            if(!empty($hrms_value)){
                $post_data['hrms'] = $hrms_value;
                $edit = 1;
            }
            
            // --- new fields
            if(!empty($erc_retainer)){
                $post_data['erc_retainer'] = $erc_retainer;
                $edit = 1;
            }
            if(!empty($erc_success_fee)){
                $post_data['erc_success_fee'] = $erc_success_fee;
                $edit = 1;
            }
            if(!empty($erc_completion_fee)){
                $post_data['erc_completion_fee'] = $erc_completion_fee;
                $edit = 1;
            }
            if(!empty($erc_first_installment)){
                $post_data['erc_first_installment'] = $erc_first_installment;
                $edit = 1;
            }
            if(!empty($erc_second_installment)){
                $post_data['erc_second_installment'] = $erc_second_installment;
                $edit = 1;
            }
            // --- new fields

            //update milestone
            $milestone_data = array();
            $opportunity_product_table = $wpdb->prefix.'opportunity_products';
            $Milestone = $request->get_param('Milestone');
            $MilestoneStage = $request->get_param('MilestoneStage');

            if($opportunityClosure=='void_agreement'){
                $post_data['envelop_id'] = '';
                if($old_single_opportunity->product_ID == 935){ // erc
                    $Milestone = 112;
                    $MilestoneStage = 188;        
                }else if($old_single_opportunity->product_ID == 932){ // RDC
                    $Milestone = 101;
                    $MilestoneStage = 314;        
                }
            }
                
            if($edit){
                $post_data['ModifiedAt'] = date('Y-m-d H:i:s',time());
                $post_data['ModifiedBy'] = get_current_user_id();
                $result = $wpdb->update($opportunity_table, $post_data, array('OpportunityID' => $opportunity_id));
            }
            

            $edit = 0;
            $add_milestone_log = 0;
            if(!empty($Milestone)){
                $milestone_data['milestone_id'] = $Milestone;
                $milestone_data['milestone_deletedat'] = NULL;
                $milestone_data['milestone_deletedby'] = NULL;
                $edit = 1;
                if($Milestone != $old_milestone){
                    $add_milestone_log = 1;
                }
                if($opportunityClosure == ''){
                    if($Milestone == 113){
                        $closure_data['opportunityClosure'] = 'opportunity_won';
                    }
                    if($Milestone == 114){
                        $closure_data['opportunityClosure'] = 'opportunity_lose';
                    }
                    $result = $wpdb->update($opportunity_table, $closure_data, array('OpportunityID' => $opportunity_id));
                }
            }
            if(!empty($MilestoneStage)){
                $milestone_data['milestone_stage_id'] = $MilestoneStage;
                $milestone_data['stage_deletedat'] = NULL;
                $milestone_data['stage_deletedby'] = NULL;
                $edit = 1;
                if($MilestoneStage != $old_milestonestage){
                    $add_milestone_log = 1;
                }
            }

            if($edit){
                $post_data['ModifiedAt'] = date('Y-m-d H:i:s',time());
                $post_data['ModifiedBy'] = get_current_user_id();
                $result = $wpdb->update($opportunity_product_table, $milestone_data, array('opportunity_id' => $opportunity_id));

                        // ----- milestone log maintain -----
                        if($add_milestone_log==1){
                            $milestone_stage_log_table = $wpdb->prefix.'milestone_stage_log';
                            $mile_data = array();
                            $mile_data['lead_id']=$old_single_opportunity->LeadID;
                            $mile_data['project_id']=0;
                            $mile_data['opportunity_id']=$opportunity_id;
                            $mile_data['product_id']=$old_single_opportunity->product_ID;
                            $mile_data['updated_milestone_id']=$Milestone;
                            $mile_data['previous_milestone_id']=$old_milestone;
                            $mile_data['updated_stage_id']=$MilestoneStage;
                            $mile_data['previous_stage_id']=$old_milestonestage;
                            $mile_data['changed_date']=date("Y-m-d h:i:sa");
                            
                            if(!empty($request->get_param('user_id'))){
                                $user_id = $request->get_param('user_id');
                            }else{
                                $user_id = 0;
                            }
                            $mile_data['changed_by']=$user_id;

                            $insert_mile_log = $wpdb->insert($milestone_stage_log_table ,$mile_data);
                        }
            }            

            //get opportunity data
            $where = ' WHERE 1=1 ';

            if(!empty($OpportunityID)){
                $where .= " AND opp.OpportunityID ='".$OpportunityID."'";
            }

            $sql = "SELECT opp.*, prod.Title as productnName,prod.ProductID as product_ID, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus, ns.next_step  as NextStepName, ns.next_step_id, buss.business_legal_name as AccountName, mile.milestone_id, mile_status.milestone_stage_id, ns.next_step, curr.currency_code as currencyName FROM $opportunity_table opp left join $opportunity_product_table oppPro on opp.OpportunityID = oppPro.opportunity_id left join $product_table prod on oppPro.product_id = prod.ProductID left join $milestone_table mile on oppPro.milestone_id = mile.milestone_id left join $milestone_status_table mile_status on oppPro.milestone_stage_id = mile_status.milestone_stage_id left join $next_step_table ns on opp.NextStep = ns.next_step_id left join $business_info_table buss on opp.LeadID = buss.lead_id left join $currency_table curr on opp.OpportunityCurrency = curr.currency_id " . $where;
            $opportunity = $wpdb->get_results($sql);
            $single_opportunity = $opportunity[0];

            // Query to get the probability
            $query = $wpdb->prepare(
                "SELECT probability FROM $lead_stage_probability_table WHERE milestone = %s AND stage = %s AND product = %s",
                $single_opportunity->milestoneName, $single_opportunity->milestoneStatus, $single_opportunity->productnName
            );

            // Execute the query
            $opp_probability = $wpdb->get_var($query);

            // Check if probability is found
            if ($opp_probability !== null) {
                $Probability = $opp_probability;
            } else {
                $Probability = $request->get_param('Probability');
            }

            if(!empty($Probability) || $Probability == 0){
                if($Probability == 0){
                    $Probability = '';
                }
                $prob_data['Probability'] = $Probability;
                $result = $wpdb->update($opportunity_table, $prob_data, array('OpportunityID' => $opportunity_id));
            }

            $user_details = get_user_by( 'id', $single_opportunity->CreatedBy );
            $single_opportunity->ownerName = $user_details->display_name;
            $contact_data = $wpdb->get_row("SELECT first_name,last_name FROM $contacts_table WHERE id = ".$single_opportunity->ContactID."");
            if(!empty($contact_data)){
                $primary_contact = $contact_data->first_name.' '.$contact_data->last_name;
            }else{
                $primary_contact = '';
            }
            $single_opportunity->ContactName = $primary_contact;

            if($opportunity_description!=$old_single_opportunity->Description){
                $note = $single_opportunity->ownerName." made an update to the opportunity";
                update_activity_log($old_single_opportunity->LeadID, $old_single_opportunity->OpportunityID, $old_single_opportunity->Description, $opportunity_description, 'description', $single_opportunity->ownerName, $note);
                    
            }
            if($NextStep!=$old_single_opportunity->NextStep){
                $note = $single_opportunity->ownerName." made an update to the opportunity";
                update_activity_log($old_single_opportunity->LeadID, $old_single_opportunity->OpportunityID, $old_single_opportunity->NextStep, $NextStep, 'next step', $single_opportunity->ownerName, $note);     
            }

            if(!empty($ExpectedCloseDate)){
            if(date("Y-m-d", strtotime($ExpectedCloseDate))!=$old_single_opportunity->ExpectedCloseDate){
                $note = $single_opportunity->ownerName." made an update to the opportunity";
                update_activity_log($old_single_opportunity->LeadID, $old_single_opportunity->OpportunityID, $old_single_opportunity->ExpectedCloseDate, date("Y-m-d", strtotime($ExpectedCloseDate)), 'expected close date', $single_opportunity->ownerName, $note);     
            }
            }

            if($OpportunityAmount!=$old_single_opportunity->OpportunityAmount){
                $note = $single_opportunity->ownerName." made an update to the opportunity";
                update_activity_log($old_single_opportunity->LeadID, $old_single_opportunity->OpportunityID, $old_single_opportunity->OpportunityAmount, $OpportunityAmount, 'amount', $single_opportunity->ownerName, $note);     
            }

            if($Probability!=$old_single_opportunity->Probability){
                $note = $single_opportunity->ownerName." made an update to the opportunity";
                update_activity_log($old_single_opportunity->LeadID, $old_single_opportunity->OpportunityID, $old_single_opportunity->Probability, $Probability, 'probability', $single_opportunity->ownerName, $note);     
            }

            if($single_opportunity->milestoneName!=$old_single_opportunity->milestoneName){
                $note = $single_opportunity->ownerName." made an update to the opportunity";
                update_activity_log($old_single_opportunity->LeadID, $old_single_opportunity->OpportunityID, $old_single_opportunity->milestoneName, $single_opportunity->milestoneName, 'milestone', $single_opportunity->ownerName, $note);     
            }

            if($single_opportunity->milestoneStatus!=$old_single_opportunity->milestoneStatus){
                $note = $single_opportunity->ownerName." made an update to the opportunity";
                update_activity_log($old_single_opportunity->LeadID, $old_single_opportunity->OpportunityID, $old_single_opportunity->milestoneStatus, $single_opportunity->milestoneStatus, 'stage', $single_opportunity->ownerName, $note,$user_id);     
            }

            if($opportunityClosure!=$old_single_opportunity->opportunityClosure && $opportunityClosure!=''){ // audit log changes
                $note = $single_opportunity->ownerName." made an update to the opportunity";
                update_activity_log($old_single_opportunity->LeadID, $old_single_opportunity->OpportunityID, $old_single_opportunity->opportunityClosure, $opportunityClosure, 'action', $single_opportunity->ownerName, $note);     
            }elseif($opportunityClosure=='resend_payment_link'){
                $note = $single_opportunity->ownerName." made an update to the opportunity";
                update_activity_log($old_single_opportunity->LeadID, $old_single_opportunity->OpportunityID, $old_single_opportunity->opportunityClosure, $opportunityClosure, 'action', $single_opportunity->ownerName, $note);
            }
             
            // audit log changes
            if($ContactList!=$old_single_opportunity->ContactID && $request->get_param('ContactList')!= NULL){
                $note = $single_opportunity->ownerName." made an update to the opportunity";
                update_activity_log($old_single_opportunity->LeadID, $old_single_opportunity->OpportunityID, $old_single_opportunity->ContactID, $ContactList, 'ContactID', $single_opportunity->ownerName, $note,$user_id);     
            }
               
            if($opportunityClosure == 'opportunity_won' || $Milestone == 116 ){
               
                $opportunity_data = $wpdb->get_row("SELECT {$wpdb->prefix}opportunities.OpportunityName,{$wpdb->prefix}opportunities.LeadID,{$wpdb->prefix}opportunities.ContactID,{$wpdb->prefix}opportunities.CreatedBy,{$wpdb->prefix}opportunity_products.product_id,{$wpdb->prefix}erc_iris_leads_additional_info.sales_user_id FROM {$wpdb->prefix}opportunities
                                                    JOIN {$wpdb->prefix}erc_iris_leads_additional_info ON {$wpdb->prefix}opportunities.LeadID = {$wpdb->prefix}erc_iris_leads_additional_info.lead_id
                                                    JOIN {$wpdb->prefix}opportunity_products ON {$wpdb->prefix}opportunities.OpportunityID = {$wpdb->prefix}opportunity_products.opportunity_id
                                                    WHERE {$wpdb->prefix}opportunities.DeletedAt IS NULL AND {$wpdb->prefix}opportunities.OpportunityID = ".$opportunity_id."");
                $project_name = '';
                $lead_id = 0;
                $product_id = 0;
                $contact_id = 0;
                $sales_user_id = 0;
                $created_at = date('Y-m-d H:i:s');
                $created_by = 0;
                if(!empty($opportunity_data)){
                    $lead_id = $opportunity_data->LeadID;
                    $product_id = $opportunity_data->product_id;
                    $contact_id = $opportunity_data->ContactID;
                    $project_name = $opportunity_data->OpportunityName;
                    $sales_user_id = $opportunity_data->sales_user_id;
                    $created_by = $opportunity_data->CreatedBy;
                }
                
                $invoice_table = $wpdb->prefix.'invoices'; 
                //Get Project Milestone & Stage
                if($product_id == 935){
                    // $df .= 0;
                    $invoice_data = $wpdb->get_row("SELECT id,retainer_type,total_amount FROM $invoice_table WHERE lead_id=$lead_id ORDER BY id desc LIMIT 1");
                    
                    $milestone_id = 111;
                    $milestone_stage_id = 182; //Payment Processing Client Initiate (Retainer Invoice Sent 
                      
                      $retainer_type ='';
                      $total_amount ='';

                    if($invoice_data){
                      $retainer_type = $invoice_data->retainer_type;
                      $total_amount  = $invoice_data->total_amount;
                    }

                    if($retainer_type == 'IR' && ($total_amount == '0.00' || empty($total_amount))){
                      $milestone_stage_id = 179; // Documents Pending if Retainer invoice == $0   
                    }
                }elseif($product_id == 936){
                    $milestone_id = 123;
                    $milestone_stage_id = 259;
                }else{  

                    $milestone_data = $wpdb->get_row("SELECT milestone_id FROM {$wpdb->prefix}milestones WHERE map LIKE '%\"project\"%' AND product_id = ".$product_id." AND deleted_at IS NULL ORDER BY milestone_id ASC LIMIT 1");
                    $milestone_id = 0;
                    $milestone_stage_id = 0;
                    if(!empty($milestone_data)){
                        $milestone_id = $milestone_data->milestone_id;
                        $milestone_stage_data = $wpdb->get_row("SELECT milestone_stage_id FROM {$wpdb->prefix}milestone_stages WHERE milestone_id = $milestone_id AND deleted_at IS NULL ORDER BY milestone_stage_id ASC LIMIT 1");
                        if(!empty($milestone_stage_data)){
                            $milestone_stage_id = $milestone_stage_data->milestone_stage_id;
                        }
                    }
                }
                
                $project_data['lead_id']=$lead_id;
                $project_data['product_id']=$product_id;
                $project_data['contact_id']=$contact_id;
                $project_data['project_name']=$project_name;
                $project_data['milestone_id']=$milestone_id;
                $project_data['milestone_stage_id']=$milestone_stage_id;
                $project_data['sales_user_id']=$sales_user_id;
                $project_data['sales_support_id']= 0;
                $project_data['created_at']=$created_at;
                $project_data['created_by']=$created_by;
                $projects_data = json_encode($project_data);

                $url = get_site_url()."/wp-json/productsplugin/v1/create-project";
                $args = array('body' => $projects_data,'headers' => array('Content-Type' => 'application/json'));
                $response = wp_remote_post($url,$args);
                
                if (is_wp_error($response)) {
                  return $response->get_error_message();
                }
                $project_res = json_decode(wp_remote_retrieve_body($response));
                if($project_res->status == 1){
                    $status = true;
                    $message = 'Project Added Against Opportunity';
                }else{
                    $status = false;
                    $message = $project_res->message; 
                }
                $reuslts['status'] = $status;
                $reuslts['message'] = $message;  
            }else{
                $reuslts['status'] = true;
                $reuslts['message'] = 'Opportunity Updated';
                $reuslts['data'] = $single_opportunity;
            }
        }else{
            $reuslts['status'] = false;
            $reuslts['message'] = 'Something Went Worng';
        }

        echo json_encode($reuslts);die;
        
    }

    //Edit Opportunity with optional field
    public function update_milestone_stage($request) {
        global $wpdb;
        $reuslts = array();

        $OpportunityID = $request->get_param('OpportunityID');
        $status = $request->get_param('status');
        $note = $request->get_param('note');
        $Milestone = $request->get_param('milestone');
        $MilestoneStage = $request->get_param('milestoneStage');

        
        if(empty($OpportunityID)){
            $reuslts['status'] = false;
            $reuslts['message'] = 'OpportunityID filed is required.';
            echo json_encode($reuslts);die;
        }

        if(empty($Milestone)){
            $reuslts['status'] = false;
            $reuslts['message'] = 'Milestone filed is required.';
            echo json_encode($reuslts);die;
        }

        if(empty($MilestoneStage)){
            $reuslts['status'] = false;
            $reuslts['message'] = 'Stage filed is required.';
            echo json_encode($reuslts);die;
        }

        if(empty($status)){
            $reuslts['status'] = false;
            $reuslts['message'] = 'Status filed is required.';
            echo json_encode($reuslts);die;
        }elseif($status != 'paid'){
            $reuslts['status'] = false;
            $reuslts['message'] = 'Status should be paid.';
            echo json_encode($reuslts);die;
        }
        if(empty($note)){
            $reuslts['status'] = false;
            $reuslts['message'] = 'Note filed is required.';
            echo json_encode($reuslts);die;
        }

        //getting old data
        $additional_table = $wpdb->prefix.'erc_iris_leads_additional_info';
        $business_info_table = $wpdb->prefix.'erc_business_info';
        $opportunity_table = $wpdb->prefix.'opportunities';
        $opportunity_product_table = $wpdb->prefix.'opportunity_products';
        $product_table = $wpdb->prefix.'crm_products';
        $milestone_table = $wpdb->prefix.'milestones';
        $milestone_status_table = $wpdb->prefix.'milestone_stages';
        $next_step_table = $wpdb->prefix.'next_step';
        $currency_table = $wpdb->prefix.'currencies';
        $contacts_table = $wpdb->prefix.'op_contacts';
        $where = ' WHERE 1=1 ';

        
        $where .= " AND opp.OpportunityID ='".$OpportunityID."'";
        

        $old_sql = "SELECT opp.*, prod.Title as productnName,prod.ProductID as product_ID, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus, ns.next_step  as NextStepName, ns.next_step_id, buss.business_legal_name as AccountName, mile.milestone_id, mile_status.milestone_stage_id, ns.next_step, curr.currency_code as currencyName FROM $opportunity_table opp left join $opportunity_product_table oppPro on opp.OpportunityID = oppPro.opportunity_id left join $product_table prod on oppPro.product_id = prod.ProductID left join $milestone_table mile on oppPro.milestone_id = mile.milestone_id left join $milestone_status_table mile_status on oppPro.milestone_stage_id = mile_status.milestone_stage_id left join $next_step_table ns on opp.NextStep = ns.next_step_id left join $business_info_table buss on opp.LeadID = buss.lead_id left join $currency_table curr on opp.OpportunityCurrency = curr.currency_id " . $where;

        
        $old_opportunity = $wpdb->get_results($old_sql);
        $old_single_opportunity = $old_opportunity[0];
        
        $old_milestone = $old_single_opportunity->milestone_id;
        $old_milestonestage = $old_single_opportunity->milestone_stage_id;

        //print_r($request->get_param('OpportunityID'));exit;
        //if(!empty($OpportunityID) && $status == 'paid'){
            
            //update milestone
            $milestone_data = array();
            $opportunity_product_table = $wpdb->prefix.'opportunity_products';
            

            $edit = 0;
            $add_milestone_log = 0;
            if(!empty($Milestone)){
                $milestone_data['milestone_id'] = $Milestone;
                $milestone_data['milestone_deletedat'] = NULL;
                $milestone_data['milestone_deletedby'] = NULL;
                $edit = 1;
                if($Milestone != $old_milestone){
                    $add_milestone_log = 1;
                }
            }

            if(!empty($MilestoneStage)){
                $milestone_data['milestone_stage_id'] = $MilestoneStage;
                $milestone_data['stage_deletedat'] = NULL;
                $milestone_data['stage_deletedby'] = NULL;
                $edit = 1;
                if($MilestoneStage != $old_milestonestage){
                    $add_milestone_log = 1;
                }
            }

            if($edit){
                $post_data['ModifiedAt'] = date('Y-m-d H:i:s',time());
                $post_data['ModifiedBy'] = get_current_user_id();
                $result = $wpdb->update($opportunity_product_table, $milestone_data, array('opportunity_id' => $OpportunityID));

                // ----- milestone log maintain -----
                if($add_milestone_log==1){
                        $milestone_stage_log_table = $wpdb->prefix.'milestone_stage_log';
                        $mile_data = array();
                        $mile_data['lead_id']=$old_single_opportunity->LeadID;
                        $mile_data['project_id']=0;
                        $mile_data['opportunity_id']=$opportunity_id;
                        $mile_data['product_id']=$old_single_opportunity->product_ID;
                        $mile_data['updated_milestone_id']=$Milestone;
                        $mile_data['previous_milestone_id']=$old_milestone;
                        $mile_data['updated_stage_id']=$MilestoneStage;
                        $mile_data['previous_stage_id']=$old_milestonestage;
                        $mile_data['changed_date']=date("Y-m-d h:i:sa");
                        
                        if(!empty($request->get_param('user_id'))){
                            $user_id = $request->get_param('user_id');
                        }else{
                            $user_id = 0;
                        }
                        $mile_data['changed_by']=$user_id;

                        $insert_mile_log = $wpdb->insert($milestone_stage_log_table ,$mile_data);
                }
            }

            //get opportunity data
            $where = ' WHERE 1=1 ';

            if(!empty($OpportunityID)){
                $where .= " AND opp.OpportunityID ='".$OpportunityID."'";
            }

            $sql = "SELECT opp.*, prod.Title as productnName,prod.ProductID as product_ID, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus, ns.next_step  as NextStepName, ns.next_step_id, buss.business_legal_name as AccountName, mile.milestone_id, mile_status.milestone_stage_id, ns.next_step, curr.currency_code as currencyName FROM $opportunity_table opp left join $opportunity_product_table oppPro on opp.OpportunityID = oppPro.opportunity_id left join $product_table prod on oppPro.product_id = prod.ProductID left join $milestone_table mile on oppPro.milestone_id = mile.milestone_id left join $milestone_status_table mile_status on oppPro.milestone_stage_id = mile_status.milestone_stage_id left join $next_step_table ns on opp.NextStep = ns.next_step_id left join $business_info_table buss on opp.LeadID = buss.lead_id left join $currency_table curr on opp.OpportunityCurrency = curr.currency_id " . $where;
            $opportunity = $wpdb->get_results($sql);
            $single_opportunity = $opportunity[0];

            $user_details = get_user_by( 'id', $single_opportunity->CreatedBy );
            $single_opportunity->ownerName = $user_details->display_name;

            create_opportunity_notes_finction($OpportunityID, $note, 1);
            if($old_single_opportunity->milestoneName != $single_opportunity->milestoneName){
                update_activity_log($single_opportunity->LeadID, $OpportunityID, $old_single_opportunity->milestoneName, $single_opportunity->milestoneName, 'Milestone', 'Admin', 'Admin made an update to the opportunity');
            }

            if($old_single_opportunity->milestoneName != $single_opportunity->milestoneName){
                update_activity_log($single_opportunity->LeadID, $OpportunityID, $old_single_opportunity->milestoneStatus, $single_opportunity->milestoneStatus, 'Stage', 'Admin', 'Admin made an update to the opportunity');
            }
            
            $reuslts['status'] = true;
            $reuslts['message'] = 'Opportunity Updated';
            $reuslts['data'] = $single_opportunity;
            
        echo json_encode($reuslts);die;
        
    }

    //Edit Project and opportunity owner
    public function edit_opportunity_project_optional_field($request){
            global $wpdb;
        $reuslts = array();
        $project_table = $wpdb->prefix . 'projects';
        $opportunity_table = $wpdb->prefix . 'opportunities';
        $opportunity_pro_table = $wpdb->prefix . 'opportunity_products';
        
        $edit = 0;
        $lead_id = $request->get_param('lead_id');
        $OwnerList = $request->get_param('OwnerList');
        $product_id =  $request->get_param('product_id');
        $user_id =  $request->get_param('user_id');

        if(!empty($OwnerList)){
            $post_data['created_by'] = $OwnerList;
            $opp_data['CreatedBy'] = $OwnerList;
            $edit = 1;
        }

        $projectID = $request->get_param('projectID');
        $OpportunityID = $request->get_param('OpportunityID');    

            if($edit){
              if(isset($projectID) && !empty($projectID)){  
                $id = $projectID;
                $post_data['modified_at'] = date('Y-m-d H:i:s',time());
                $post_data['modified_by'] = $user_id;
                
                $this->log_audit_entry($projectID,'project_id', array('created_by'=>$OwnerList) , $project_table);
                $result = $wpdb->update($project_table, $post_data, array('project_id' => $projectID));
                

                // $product_id = $wpdb->get_var("SELECT product_id FROM $project_table WHERE project_id=$projectID");

                $opportunity_id = $wpdb->get_var("SELECT $opportunity_pro_table.opportunity_id FROM $opportunity_pro_table LEFT JOIN $opportunity_table ON $opportunity_pro_table.opportunity_id=$opportunity_table.OpportunityID WHERE $opportunity_pro_table.product_id=$product_id AND $opportunity_table.LeadID=$lead_id");
                   
                $opp_data['ModifiedAt'] = date('Y-m-d H:i:s',time());
                $opp_data['ModifiedBy'] = $user_id;
                if($opportunity_id){
                
                    $this->log_audit_entry($opportunity_id,'OpportunityID', array('CreatedBy'=>$OwnerList) , $opportunity_table);
                    $result = $wpdb->update($opportunity_table, $opp_data, array('OpportunityID' => $opportunity_id));
                    
                }
                $reuslts['message'] = 'Project Updated';
                
             }else if(isset($OpportunityID) && !empty($OpportunityID)){
                $id = $OpportunityID;
                
                $opp_data['ModifiedAt'] = date('Y-m-d H:i:s',time());
                $opp_data['ModifiedBy'] = $user_id;

                $this->log_audit_entry($OpportunityID,'OpportunityID', array('CreatedBy'=>$OwnerList) , $opportunity_table);
                $result = $wpdb->update($opportunity_table, $opp_data, array('OpportunityID' => $OpportunityID));
                
                // $product_id = $wpdb->get_var("SELECT product_id FROM $opportunity_pro_table WHERE opportunity_id=$OpportunityID");
                   
                $post_data['modified_at'] = date('Y-m-d H:i:s',time());
                $post_data['modified_by'] = $user_id;
                $this->log_audit_entry($projectID,'project_id', array('created_by'=>$OwnerList) , $project_table);
                $result = $wpdb->update($project_table, $post_data, array('lead_id' => $lead_id, 'product_id'=>$product_id));
                
                $reuslts['message'] = 'Opportunity Updated';
             }   
            }// edit check 

            $reuslts['status'] = true;
            
            $reuslts['data'] = $id;
            echo json_encode($reuslts);die;
    }


    //Edit Project with optional field
    public function edit_project_optional_field($request) {
       /*  ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL); */
		
        global $wpdb;
        $reuslts = array();
        $project_table = $wpdb->prefix . 'projects';
        $product_table = $wpdb->prefix . 'crm_products';
        $contacts_table = $wpdb->prefix.'op_contacts';
        $milestone_table = $wpdb->prefix.'milestones';
        $milestone_status_table = $wpdb->prefix.'milestone_stages';
        $leads_impacted_days_table = $wpdb->prefix . 'leads_impacted_days';
        $project_assign_users = $wpdb->prefix.'collaborators';
        $lead_id = $request->get_param('lead_id');
        $projectID = $request->get_param('projectID');
        $project_name = $request->get_param('project_name');
        $project_fee = $request->get_param('project_fee');
        $self_time_off_days_april_2020 = $request->get_param('self_time_off_days_april_2020');
        $self_time_off_days_january_2021 = $request->get_param('self_time_off_days_january_2021');
        $self_time_off_days_april_2021 = $request->get_param('self_time_off_days_april_2021');
        $other_time_off_days_april_2020 = $request->get_param('other_time_off_days_april_2020');
        $other_time_off_days_january_2021 = $request->get_param('other_time_off_days_january_2021');
        $other_time_off_days_april_2021 = $request->get_param('other_time_off_days_april_2021');
        $where = ' WHERE 1=1 ';
        if($request->get_param('full_name')){
                $full_name = $request->get_param('full_name');
                $contact_no = $request->get_param('contact_no');
                $email = $request->get_param('email');
                $title = $request->get_param('title');
                $zip = $request->get_param('zip');
                $street_address = $request->get_param('street_address');
                $city = $request->get_param('city');
                $state = $request->get_param('state');
                $identity_document_type = $request->get_param('identity_document_type');
                $identity_document_number = $request->get_param('identity_document_number');
                $business_legal_name = $request->get_param('business_legal_name');
                $doing_business_as = $request->get_param('doing_business_as');
                $business_category = $request->get_param('business_category');
                $website_url = $request->get_param('website_url');
                $business_entity_type = $request->get_param('business_entity_type');
                $registration_number = $request->get_param('registration_number');
                $registration_date = $request->get_param('registration_date');
				$state_of_registration = $request->get_param('state_of_registration');
				
				$business_zip = $request->get_param('business_zip');
				$business_address = $request->get_param('business_address');
				$business_city = $request->get_param('business_city');
				$business_state = $request->get_param('business_state');
				$ein = $request->get_param('ein');

                $new_busi_data['authorized_signatory_name'] = $full_name;
                $new_busi_data['business_phone'] = $contact_no;
                $new_busi_data['business_email'] = $email;
                $new_busi_data['business_title'] = $title;
                $new_busi_data['zip'] = $zip;
                $new_busi_data['street_address'] = $street_address;
                $new_busi_data['city'] = $city;
                $new_busi_data['state'] = $state;
                $new_busi_data['identity_document_type'] = esc_attr($identity_document_type);
                $new_busi_data['identity_document_number'] = $identity_document_number;
                $new_busi_data['business_legal_name'] = $business_legal_name;
                $new_busi_data['doing_business_as'] = $doing_business_as;
                $new_busi_data['business_category'] = $business_category;
                $new_busi_data['website_url'] = $website_url;
                $new_busi_data['business_entity_type'] = $business_entity_type;
                $new_busi_data['registration_number'] = $registration_number;
                $new_busi_data['registration_date'] = $registration_date;
                $new_busi_data['state_of_registration'] = $state_of_registration;
				
                $new_busi_data['business_zip'] = $business_zip;
                $new_busi_data['business_address'] = $business_address;
                $new_busi_data['business_city'] = $business_city;
                $new_busi_data['business_state'] = $business_state;
            
                $bus_table = $wpdb->prefix.'erc_business_info';
                $this->log_audit_entry($lead_id,'lead_id', $new_busi_data , $bus_table);


                $wpdb->query("UPDATE {$wpdb->prefix}erc_business_info SET 
                                                                        authorized_signatory_name = '".$full_name."',
                                                                        business_phone = '".$contact_no."',
                                                                        business_email = '".$email."',
                                                                        business_title = '".$title."',
																		
																	business_zip = '".$business_zip."',
																	business_address = '".$business_address."',
																	business_city = '".$business_city."',
																	business_state = '".$business_state."',
																	ein = '".$ein."',
																		
																		
                                                                        zip = '".$zip."',
                                                                        street_address = '".$street_address."',
                                                                        city = '".$city."',
                                                                        state = '".$state."',
                                                                        identity_document_type = '".esc_attr($identity_document_type)."',
                                                                        identity_document_number = '".$identity_document_number."',
                                                                        business_legal_name = '".$business_legal_name."',
                                                                        doing_business_as = '".$doing_business_as."',
                                                                        business_category = '".$business_category."',
                                                                        website_url = '".$website_url."',
                                                                        business_entity_type = '".$business_entity_type."',
                                                                        registration_number = '".$registration_number."',
                                                                        registration_date = '".$registration_date."',
                                                                        state_of_registration = '".$state_of_registration."'
                                                                        WHERE lead_id = ".$lead_id."");

        }

        // if($request->has_param('income_2019')){ //|| $request->has_param('maximum_credit') || $request->has_param('estimated_fee')
            $income_2019 = $request->get_param('income_2019');
            $income_2020 = $request->get_param('income_2020');
            $income_2021 = $request->get_param('income_2021');
            
            $bank_name = $request->get_param('bank_name');
            $account_holder_name = $request->get_param('account_holder_name');
            $account_number = $request->get_param('account_number');
            $aba_routing_no = $request->get_param('aba_routing_no');
            
            $stc_amount_2020 = $request->get_param('stc_amount_2020');
            $stc_amount_2021 = $request->get_param('stc_amount_2021');
            $maximum_credit = $request->get_param('maximum_credit');
            $actual_credit = $request->get_param('actual_credit');
            $estimated_fee = $request->get_param('estimated_fee');
            $actual_fee = $request->get_param('actual_fee');
            $years = $request->get_param('years');
            //check bank info
            if($lead_id){
            $bank_info = $wpdb->get_results("SELECT lead_id FROM {$wpdb->prefix}erc_bank_info WHERE lead_id = ".$lead_id."");
           
            if(empty($bank_info)){
                $wpdb->query("INSERT INTO {$wpdb->prefix}erc_bank_info (lead_id,bank_name,account_holder_name,account_number,aba_routing_no) VALUES (".$lead_id.",'".$bank_name."','".$account_holder_name."','".$account_number."','".$aba_routing_no."')");
                
            }else{
                
                /*
                    $wpdb->query("UPDATE {$wpdb->prefix}erc_bank_info SET bank_name = '".$bank_name."', account_holder_name = '".$account_holder_name."', account_number = '".$account_number."', aba_routing_no = '".$aba_routing_no."' WHERE lead_id = ".$lead_id."");
                */

                $bank_update_data = array();
                $edit_bank_data = 0;

                if($bank_name != null ){
                    $bank_update_data['bank_name'] = $bank_name; 
                    $edit_bank_data = 1;
                }

                if($account_holder_name != null ){
                    $bank_update_data['account_holder_name'] = $account_holder_name; 
                    $edit_bank_data = 1;
                }

                if($account_number!= null){
                    $bank_update_data['account_number'] = $account_number; 
                    $edit_bank_data = 1;
                }

                if($aba_routing_no!= null){
                    $bank_update_data['aba_routing_no'] = $aba_routing_no; 
                    $edit_bank_data = 1;
                }                  
                
                if($edit_bank_data){                                  
                    $bank_info_table = $wpdb->prefix.'erc_bank_info';
                    $this->log_audit_entry($lead_id,'lead_id', $bank_update_data , $bank_info_table);
                    $bank_update = $wpdb->update($bank_info_table, $bank_update_data, array('lead_id' => $lead_id));
                    
                }    
            }
         }// lead id check

            
            /*
                $wpdb->query("UPDATE {$wpdb->prefix}projects SET income_2019 = '".$income_2019."', income_2020 = '".$income_2020."', income_2021 = '".$income_2021."', stc_amount_2020 = '".$stc_amount_2020."', stc_amount_2021 = '".$stc_amount_2021."', maximum_credit = '".$maximum_credit."', actual_credit = '".$actual_credit."', estimated_fee = '".$estimated_fee."', actual_fee = '".$actual_fee."', years = '".$years."' WHERE project_id = ".$projectID.""); 
            */

                $stc_update_data = array();
                $edit_stc_data = 0;

                if( $income_2019 != null ){
                    $stc_update_data['income_2019'] = $income_2019; 
                    $edit_stc_data = 1;
                }
                if($income_2020 != null ){
                    $stc_update_data['income_2020'] = $income_2020; 
                    $edit_stc_data = 1;
                }
                if($income_2021 != null ){
                    $stc_update_data['income_2021'] = $income_2021; 
                    $edit_stc_data = 1;
                }
                if($stc_amount_2020 != null ){
                    $stc_update_data['stc_amount_2020'] = $stc_amount_2020; 
                    $edit_stc_data = 1;
                }
                if($stc_amount_2021 != null ){
                    $stc_update_data['stc_amount_2021'] = $stc_amount_2021; 
                    $edit_stc_data = 1;
                }   
                if($maximum_credit != null ){
                    $stc_update_data['maximum_credit'] = $maximum_credit; 
                    $edit_stc_data = 1;
                }
                if($actual_credit != null ){
                    $stc_update_data['actual_credit'] = $actual_credit; 
                    $edit_stc_data = 1;
                }
                if($estimated_fee != null ){
                    $stc_update_data['estimated_fee'] = $estimated_fee; 
                    $edit_stc_data = 1;
                }
                if($actual_fee != null ){
                    $stc_update_data['actual_fee'] = $actual_fee; 
                    $edit_stc_data = 1;
                }
                if($years != null ){
                    $stc_update_data['years'] = $years; 
                    $edit_stc_data = 1;
                }

                // wp_mail("<EMAIL>","play - test1",json_encode($stc_update_data));
                
                if($edit_stc_data){
                    
                    $this->log_audit_entry($projectID,'project_id', $stc_update_data , $project_table);

                    $result = $wpdb->update($project_table, $stc_update_data, array('project_id' => $projectID));
                }
        
            // }

        if(!empty($projectID)){
            $where .= " AND projects.project_id ='".$projectID."'";
        }
 
        if(!empty($projectID)){
            $project_id = $projectID;
            $old_project_info = $wpdb->get_row("SELECT projects.*, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus, products.Title ProductName 
                                                    FROM $project_table projects 
                                                    JOIN $product_table products ON projects.product_id = products.ProductID
                                                    left join $milestone_table mile on projects.milestone_id = mile.milestone_id 
                                                    left join $milestone_status_table mile_status on projects.milestone_stage_id = mile_status.milestone_stage_id
                                                    WHERE project_id = ".$project_id."");
            
            $old_milestone = $old_project_info->milestone_id;
            $old_milestonestage = $old_project_info->milestone_stage_id;

            $post_data = array();
            $post_data['project_name'] = $project_name;
            $post_data['project_fee'] = $project_fee;
            $post_data['maximum_credit'] = $maximum_credit;
            $post_data['estimated_fee'] = $estimated_fee;
            $post_data['modified_at'] = date('Y-m-d H:i:s',time());
            $post_data['modified_by'] = get_current_user_id();
            if($request->get_param('business_legal_name')){
                $post_data['project_name'] = $request->get_param('business_legal_name').' - '.$old_project_info->ProductName;
            }
            $result = $wpdb->update($project_table, $post_data, array('project_id' => $project_id));
            if ($result !== false) {
                $modified_by = get_current_user_id();
                // Iterate over the fields to log changes
                foreach ($post_data as $field => $newValue) {
                    if (isset($old_project_info->$field) && $old_project_info->$field != $newValue) {
                        //$DATA_TYPE = '';
                        // Prepare log data
                        $logData = array(
                            'DateCreated' => current_time('mysql'),
                            'CreatedBy' => $modified_by,
                            'TableName' => $project_table,
                            'FieldName' => $field,
                            'DataType' => gettype($newValue),
                            'BeforeValueString' => $old_project_info->$field,
                            'AfterValueString' => $newValue,
                            'FieldID' => $project_id,
                            'Action' => 'Update',
                        );

                        // Log the change
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logData);
                    }
                }
            }           
            //update impacted days
            if($request->get_param('lead_id')){
                $check_self_time_off_days_april_2020 = $wpdb->get_row("SELECT id FROM {$wpdb->prefix}project_impacted_days WHERE lead_id = ".$lead_id." AND type = 'self' AND time_of_days = 'self_time_off_days_april_2020'");
                if(!empty($check_self_time_off_days_april_2020)){
                    $wpdb->query("UPDATE {$wpdb->prefix}project_impacted_days SET no_of_days = '".$self_time_off_days_april_2020."' WHERE id = ".$check_self_time_off_days_april_2020->id."");
                }else{
                    $wpdb->query("INSERT INTO {$wpdb->prefix}project_impacted_days (lead_id,type,time_of_days,title,no_of_days) VALUES (".$lead_id.",'self','self_time_off_days_april_2020','Time off Days between April 1, 2020, and December 31, 2020','".$self_time_off_days_april_2020."')");
                }
           
                $check_self_time_off_days_january_2021 = $wpdb->get_row("SELECT id FROM {$wpdb->prefix}project_impacted_days WHERE lead_id = ".$lead_id." AND type = 'self' AND time_of_days = 'self_time_off_days_january_2021'");
                if(!empty($check_self_time_off_days_january_2021)){
                    $wpdb->query("UPDATE {$wpdb->prefix}project_impacted_days SET no_of_days = '".$self_time_off_days_january_2021."' WHERE id = ".$check_self_time_off_days_january_2021->id."");
                }else{
                    $wpdb->query("INSERT INTO {$wpdb->prefix}project_impacted_days (lead_id,type,time_of_days,title,no_of_days) VALUES (".$lead_id.",'self','self_time_off_days_january_2021','Time off Days between April 1, 2021, and September 30, 2021','".$self_time_off_days_january_2021."')");
                }
            
                $check_self_time_off_days_april_2021 = $wpdb->get_row("SELECT id FROM {$wpdb->prefix}project_impacted_days WHERE lead_id = ".$lead_id." AND type = 'self' AND time_of_days = 'self_time_off_days_april_2021'");
                if(!empty($check_self_time_off_days_april_2021)){
                    $wpdb->query("UPDATE {$wpdb->prefix}project_impacted_days SET no_of_days = '".$self_time_off_days_april_2021."' WHERE id = ".$check_self_time_off_days_april_2021->id."");
                }else{
                    $wpdb->query("INSERT INTO {$wpdb->prefix}project_impacted_days (lead_id,type,time_of_days,title,no_of_days) VALUES (".$lead_id.",'self','self_time_off_days_april_2021','Time off Days between April 1, 2021, and September 30, 2021','".$self_time_off_days_april_2021."')");
                }
           
                $check_other_time_off_days_april_2020 = $wpdb->get_row("SELECT id FROM {$wpdb->prefix}project_impacted_days WHERE lead_id = ".$lead_id." AND type = 'others' AND time_of_days = 'other_time_off_days_april_2020'");
                if(!empty($check_other_time_off_days_april_2020)){
                    $wpdb->query("UPDATE {$wpdb->prefix}project_impacted_days SET no_of_days = '".$other_time_off_days_april_2020."' WHERE id = ".$check_other_time_off_days_april_2020->id."");
                }else{
                    $wpdb->query("INSERT INTO {$wpdb->prefix}project_impacted_days (lead_id,type,time_of_days,title,no_of_days) VALUES (".$lead_id.",'others','other_time_off_days_april_2020','Time off Days between April 1, 2020, and December 31, 2020','".$other_time_off_days_april_2020."')");
                }
            
                $check_other_time_off_days_january_2021 = $wpdb->get_row("SELECT id FROM {$wpdb->prefix}project_impacted_days WHERE lead_id = ".$lead_id." AND type = 'others' AND time_of_days = 'other_time_off_days_january_2021'");
                if(!empty($check_other_time_off_days_january_2021)){
                    $wpdb->query("UPDATE {$wpdb->prefix}project_impacted_days SET no_of_days = '".$other_time_off_days_january_2021."' WHERE id = ".$check_other_time_off_days_january_2021->id."");
                }else{
                    $wpdb->query("INSERT INTO {$wpdb->prefix}project_impacted_days (lead_id,type,time_of_days,title,no_of_days) VALUES (".$lead_id.",'others','other_time_off_days_january_2021','Time off Days between January 1, 2021, and March 31, 2021','".$other_time_off_days_january_2021."')");
                }
       
                $check_other_time_off_days_april_2021 = $wpdb->get_row("SELECT id FROM {$wpdb->prefix}project_impacted_days WHERE lead_id = ".$lead_id." AND type = 'others' AND time_of_days = 'other_time_off_days_april_2021'");
                if(!empty($check_other_time_off_days_april_2021)){
                    $wpdb->query("UPDATE {$wpdb->prefix}project_impacted_days SET no_of_days = '".$other_time_off_days_april_2021."' WHERE id = ".$check_other_time_off_days_april_2021->id."");
                }else{
                    $wpdb->query("INSERT INTO {$wpdb->prefix}project_impacted_days (lead_id,type,time_of_days,title,no_of_days) VALUES (".$lead_id.",'others','other_time_off_days_april_2021','Time off Days between January 1, 2021, and March 31, 2021','".$other_time_off_days_april_2021."')");
                }
            }
            $edit = 0;
            $OwnerList = $request->get_param('OwnerList');
            $ContactList = $request->get_param('ContactList');
            if(!empty($OwnerList)){
                $post_data['CreatedBy'] = $OwnerList;
                $edit = 1;
            }
            if(!empty($ContactList)){
                $post_data['contact_id'] = $ContactList;
                //$post_data['contact_deletedat'] = NULL;
                //$post_data['contact_deletedby'] = NULL;
                $edit = 1;
                $this->log_audit_entry($project_id,'project_id', array('contact_id'=>$ContactList) , $project_table);
            }
            if($edit){
                $post_data['modified_at'] = date('Y-m-d H:i:s',time());
                $post_data['modified_by'] = get_current_user_id();
                $result = $wpdb->update($project_table, $post_data, array('project_id' => $project_id));
            }
            
            //update milestone
            $milestone_data = array();
            $opportunity_product_table = $wpdb->prefix.'opportunity_products';
            $Milestone = $request->get_param('Milestone');
            $MilestoneStage = $request->get_param('MilestoneStage');

            $edit = 0;
            $add_milestone_log = 0;
            if(!empty($Milestone)){
                $milestone_data['milestone_id'] = $Milestone;
                $milestone_data['milestone_deletedat'] = NULL;
                $milestone_data['milestone_deletedby'] = NULL;
                $edit = 1;

                if($Milestone != $old_milestone){
                    $add_milestone_log = 1;
                }

            }
            if(!empty($MilestoneStage)){
                $milestone_data['milestone_stage_id'] = $MilestoneStage;
                $milestone_data['stage_deletedat'] = NULL;
                $milestone_data['stage_deletedby'] = NULL;
                $edit = 1;
                if($MilestoneStage != $old_milestonestage){
                    $add_milestone_log = 1;
                }
            }

            if($edit){
                $milestone_data['modified_at'] = date('Y-m-d H:i:s',time());
                $milestone_data['modified_by'] = get_current_user_id();
                $result = $wpdb->update($project_table, $milestone_data, array('project_id' => $project_id));

                // ----- milestone log maintain -----
                if($add_milestone_log==1){
                        $milestone_stage_log_table = $wpdb->prefix.'milestone_stage_log';
                        $mile_data = array();
                        $mile_data['lead_id']=$old_project_info->lead_id;
                        $mile_data['project_id']=$project_id;
                        $mile_data['opportunity_id']=0;
                        $mile_data['product_id']=$old_project_info->product_id;
                        $mile_data['updated_milestone_id']=$Milestone;
                        $mile_data['previous_milestone_id']=$old_milestone;
                        $mile_data['updated_stage_id']=$MilestoneStage;
                        $mile_data['previous_stage_id']=$old_milestonestage;
                        $mile_data['changed_date']=date("Y-m-d h:i:sa");

                        if(!empty($request->get_param('user_id'))) {
                                $user_id = $request->get_param('user_id');
                            }else{
                                $user_id = 0;
                            }
                            $mile_data['changed_by']=$user_id;


                        $insert_mile_log = $wpdb->insert($milestone_stage_log_table ,$mile_data);
                }
            }

            $project_info = $wpdb->get_row("SELECT projects.*, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus 
                                                    FROM $project_table projects 
                                                    left join $milestone_table mile on projects.milestone_id = mile.milestone_id 
                                                    left join $milestone_status_table mile_status on projects.milestone_stage_id = mile_status.milestone_stage_id
                                                    WHERE project_id = ".$project_id."");
            if(!empty($project_info)){
                $user_details = get_user_by( 'id', $project_info->created_by );
                $project_info->ownerName = $user_details->display_name;
                $contact_data = $wpdb->get_row("SELECT first_name,last_name FROM $contacts_table WHERE id = ".$project_info->contact_id."");
                if(!empty($contact_data)){
                    $primary_contact = $contact_data->first_name.' '.$contact_data->last_name;
                }else{
                    $primary_contact = '';
                }
                $project_info->ContactName = $primary_contact;
                //Activity Log
                /*if($project_fee != $old_project_info->project_fee){
                    $note = $project_info->ownerName." made an update to the project";
                    update_activity_log($old_single_opportunity->LeadID, $old_single_opportunity->OpportunityID, $old_single_opportunity->Description, $opportunity_description, 'description', $single_opportunity->ownerName, $note); 
                }*/
            }
            if($request->get_param('collaborators')){
                $wpdb->query("DELETE FROM $project_assign_users WHERE project_id = ".$project_id."");
                $collaborators = $request->get_param('collaborators');
                $this->log_audit_entry($projectID, 'project_id',$collaborators , $project_assign_users);
                foreach($collaborators as $collaborator => $val){
                    $wpdb->query("INSERT INTO $project_assign_users (project_id,user_id) VALUES ($project_id,$val)");
                }
                    
            }
            $reuslts['status'] = true;
            $reuslts['message'] = 'Opportunity Updated';
            $reuslts['data'] = $project_info;
        }else{
            $reuslts['status'] = false;
            $reuslts['message'] = 'Something Went Worng';
        }
        echo json_encode($reuslts);die;
    }// update function close 
    
    // Opportunity listing
    public function get_opportunities($request) {
        global $wpdb;
        
        $additional_table = $wpdb->prefix.'erc_iris_leads_additional_info';
        $business_info_table = $wpdb->prefix.'erc_business_info';
        $opportunity_table = $wpdb->prefix.'opportunities';
        $opportunity_product_table = $wpdb->prefix.'opportunity_products';
        $product_table = $wpdb->prefix.'crm_products';
        $milestone_table = $wpdb->prefix.'milestones';
        $milestone_status_table = $wpdb->prefix.'milestone_stages';
        $next_step_table = $wpdb->prefix.'next_step';
        $currency_table = $wpdb->prefix.'currencies';

        $where = " WHERE opp.DeletedAt IS NULL ";

        $sql = "SELECT opp.*, prod.Title as productName,prod.ProductID as product_ID, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus, ns.next_step  as NextStepName, buss.business_legal_name as AccountName, mile.milestone_id, mile_status.milestone_stage_id, ns.next_step, curr.currency_code as currencyName FROM $opportunity_table opp left join $opportunity_product_table oppPro on opp.OpportunityID = oppPro.opportunity_id left join $product_table prod on oppPro.product_id = prod.ProductID left join $milestone_table mile on oppPro.milestone_id = mile.milestone_id left join $milestone_status_table mile_status on oppPro.milestone_stage_id = mile_status.milestone_stage_id left join $next_step_table ns on opp.NextStep = ns.next_step_id left join $business_info_table buss on opp.LeadID = buss.lead_id   left join $currency_table curr on opp.OpportunityCurrency = curr.currency_id " . $where;
        
        $order_by = ' ORDER BY OpportunityID DESC ';
        $sql .= $order_by;
        // echo $sql;
        $opportunity = $wpdb->get_results($sql);
        return $opportunity;
    }

     // Opportunity get
    public function get_opportunity($request) {
        global $wpdb;
        $opportunity_id = $request->get_param('id');
        $opportunity_table = $wpdb->prefix . 'opportunities';
        $opportunity_products_table = $wpdb->prefix . 'opportunity_products';
        $audit_logs = $wpdb->prefix . 'audit_logs';
        $user_table = $wpdb->prefix . 'users';
        $query = $wpdb->prepare("SELECT 
                                $opportunity_table.*
                                FROM $opportunity_table 
                                WHERE $opportunity_table.DeletedAt IS NULL AND $opportunity_table.OpportunityID = ".$opportunity_id."
                                ORDER BY $opportunity_table.OpportunityID DESC
                                ");
        $data = $wpdb->get_row($query);

        $pro_query = $wpdb->prepare("SELECT 
                                $opportunity_products_table.*
                                FROM $opportunity_products_table 
                                WHERE $opportunity_products_table.DeletedAt IS NULL AND $opportunity_products_table.opportunity_id = ".$opportunity_id);
        // print_r($data);
        $products = $wpdb->get_results($pro_query);
        // print_r($products);die();
        $data->products = $products;
        return $data;
    }

    // Delete Opportunity get
    public function delete_opportunity($request) {
        global $wpdb;
        $response = array();
        $opportunity_id = $request->get_param('id');
        
        if(!empty($opportunity_id)){
            $opportunity_table = $wpdb->prefix.'opportunities';
            $opportunity_product_table = $wpdb->prefix.'opportunity_products';

            $sql = "Delete FROM $opportunity_product_table where opportunity_id  = ".$opportunity_id;
            $result = $wpdb->get_results($sql);
            $sql = "Delete FROM $opportunity_table where OpportunityID  = ".$opportunity_id;
            $result = $wpdb->get_results($sql);
            $response['status'] = true;
            $response['message'] = "Opportunity Successfully Deleted";
        }else{
            $response['status'] = false;
            $response['message'] = "Please provide the opportunity ID.";
        }
        
        

        return json_encode($response);
    }

    // Callbacks for Next Step Endpoints
    public function next_step_list($request) {
        global $wpdb;
        $next_step_table = $wpdb->prefix . 'next_step';
        $query = $wpdb->prepare("SELECT 
                                $next_step_table.next_step_id,$next_step_table.next_step,$next_step_table.created_at
                                FROM $next_step_table 
                                WHERE $next_step_table.deleted_at IS NULL
                                ORDER BY $next_step_table.next_step_id DESC
                                ");
        return $wpdb->get_results($query);
    }

    /**
     * Create a new next step.
     *
     * @param array $data next step data.
     * @return int|WP_Error The ID of the newly created next step, or WP_Error on failure.
     */
    public function create_next_step(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'next_step';
        $next_step_data = $wpdb->get_results("SELECT next_step FROM $table_name WHERE $table_name.deleted_at IS NULL AND $table_name.next_step = '".$data['next_step']."'");
        if(empty($next_step_data)){
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to insert next step into database', $wpdb->last_error);
            }else{
                $next_step_id = $wpdb->insert_id;
                $fields[0]['next_step'] = $data['next_step'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['created_at'];
                        $logs_data['CreatedBy'] = $data['created_by'];
                        $logs_data['TableName'] = $wpdb->prefix.'next_step';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $next_step_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Next Step Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Next Step Already Exist';
            echo json_encode($results);die;
        }
        //return $wpdb->insert_id;
    }

    /**
     * Edit next step.
     *
     * @param array $data next step data.
     * @return int|WP_Error The ID of the previously created next step, or WP_Error on failure.
     */
    public function edit_next_step(WP_REST_Request $request) {
        global $wpdb;
        $next_step_id = $request->get_param('next_step_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'next_step';
        $next_step_data = $wpdb->get_results("SELECT next_step FROM $table_name WHERE $table_name.deleted_at IS NULL AND $table_name.next_step = '".$data['next_step']."' AND next_step_id != ".$next_step_id."");
        if(empty($next_step_data)){
            unset($data['next_step_id']);
            //Audit Logs Start
            $fields[0]['next_step'] = $data['next_step'];
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."' AND next_step_id = ".$next_step_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE next_step_id = ".$next_step_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['modified_at'];
                        $logs_data['CreatedBy'] = $data['modified_by'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $next_step_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('next_step_id' => $next_step_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update next step into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Next Step Updated';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Next Step Already Exist';
            echo json_encode($results);die;
        }
        //return $next_step_id;
    }

    // Callbacks for next step Endpoints
    public function get_next_step($request) {
        global $wpdb;
        $next_step_id = $request->get_param('id');
        $next_step_table = $wpdb->prefix . 'next_step';
        $query = $wpdb->prepare("SELECT 
                                $next_step_table.next_step_id,$next_step_table.next_step,$next_step_table.created_at
                                FROM $next_step_table 
                                WHERE $next_step_table.next_step_id = '".$next_step_id."'
                                ");
        return $wpdb->get_row($query);
    }

    public function get_product_name($request) {
        global $wpdb;
        $product_id = $request->get_param('id');
        $table_name = $wpdb->prefix . 'crm_products';

        $query = $wpdb->prepare("SELECT $table_name.Title
                                FROM $table_name 
                                WHERE $table_name.ProductID = $product_id AND $table_name.DeletedAt IS NULL
                                ");
        return $wpdb->get_row($query);
    }

    public function lead_contacts($request) {
        global $wpdb;
        $lead_id = $request->get_param('id');
        $table_name = $wpdb->prefix . 'op_contacts';

        /* $query = $wpdb->prepare("SELECT $table_name.id,$table_name.first_name,$table_name.last_name FROM $table_name WHERE $table_name.report_to_id = $lead_id AND $table_name.active = 1 AND $table_name.trash = 0"); */
		
		$query = $wpdb->prepare("SELECT c.id, c.first_name, c.last_name FROM {$wpdb->prefix}op_contact_lead cl INNER JOIN {$wpdb->prefix}op_contacts c ON cl.contact_id = c.id WHERE cl.lead_id = %d AND c.trash=0", $lead_id);
		
        return $wpdb->get_results($query);
    }

    public function fetch_opportunity_notes($request) {
        global $wpdb;
        //print_r($request);
        $opportunity_id = $request->get_param('opportunity_id');
        $offset = $request->get_param('offset');

       

		if(!empty($opportunity_id)){
		   
		    global $wpdb;
		    $notes_table = $wpdb->prefix.'erc_opportunity_notes';

		    $all_notes = $wpdb->get_results("SELECT * FROM $notes_table WHERE opportunity_id='".$opportunity_id."' ORDER BY id DESC ");

		        $html = ''; 
		        $i = $offset+1;
		                $from='UTC';
		                $to='America/New_York';
		                $format='Y-m-d h:i:s A';
		        foreach ($all_notes as $n_key => $n_value) { 
		            $date = $n_value->created;//UTC time
		            date_default_timezone_set($from);
		            $newDatetime = strtotime($date);
		            date_default_timezone_set($to);
		            $newDatetime = date($format, $newDatetime);
		            date_default_timezone_set('UTC');
		            $datetime = date_create($newDatetime);
		            // $datetime = date_create($n_value->created);
		            $time = date_format($datetime,"h:ia"); 
		            $day = date_format($datetime," D ");
		            $month = date_format($datetime," M ");
		            $date = date_format($datetime,"dS,");
		            $year = date_format($datetime," Y ");
		            $actual_date = $time." on ".$day.$month.$date.$year;
		            
		            $notes = $n_value->note;

		            $html .='<div class="note-listing-div shadow">';   
		            $html .='<p id="'.$n_value->id.'" class="notes">'.$notes.'</p>';
                    $html .='<p class="date-time">('.$actual_date.')</p>';
		            $html .='</div>';
		                $i++; 
			        } 

			 return $html;
		}
    }

       public function add_edit_notes($request) {
        global $wpdb;
        //print_r($request);
        
        $note_type = $request->get_param('note_type');
        $opp_product_id = $request->get_param('opp_product_id');
        $note = $request->get_param('note');
        $user_id = $request->get_param('user_id');
        $confidential_notes = $request->get_param('confidence_notes_access');
        
        $note_id = 0;
        if( !empty( $request->get_param('note_id') )){
            $note_id = $request->get_param('note_id');
        }

        $time = date("Y-m-d h:i:sa");

        if(!empty($opp_product_id)){
           
            global $wpdb;
            if($note_type == 'opportunity'){
                $notes_table = $wpdb->prefix.'erc_opportunity_notes';
                $column = 'opportunity_id';
            }else if($note_type == 'project'){
                $notes_table = $wpdb->prefix.'erc_project_notes';                    
                $column = 'project_id';
            }
            
                
                if($confidential_notes==1){
                    $conf_note = 'CONFIDENTIAL note';    
                }else{
                    $conf_note = 'comment';    
                }
                
                $comment_pre_tag = strpos($note,"added a");
                if(empty($comment_pre_tag)){
                    $login_user_details = get_user_by( 'id', $user_id );
                    $user_name = $login_user_details->display_name;
                    $note = $user_name .' added a '.$conf_note.' : '.$note; 
                }

            $response = array();
            if($note_id==0){
                $insert = $wpdb->query("INSERT INTO $notes_table (`".$column."`, `created_by`, `note`, `created`,`confidential_notes`) VALUES ('".$opp_product_id."', '".$user_id."', '".$note."', '".$time."',".$confidential_notes.");");
                $response['add'] = $insert;
                $response['note_id'] = $wpdb->insert_id;
                $response['message'] = 'Notes Insert Successfully.';
            }else{
                $update = $wpdb->query("UPDATE $notes_table SET note='".$note."',modified='".$time."',confidential_notes=".$confidential_notes." WHERE id=$note_id ");                
                $response['update'] = $update;
                $response['note_id'] = $note_id;
                $response['message'] = 'Notes Update Successfully.';
            }

            return json_encode($response);

        }
       }


       public function delete_notes($request) {
        global $wpdb;
        //print_r($request);
        
        $note_type = $request->get_param('note_type');
        
        $note_id = 0;
        if( !empty( $request->get_param('note_id') )){
            $note_id = $request->get_param('note_id');
        }

        // $time = date("Y-m-d h:i:sa");

        if(!empty($note_id)){
           
            global $wpdb;
            if($note_type == 'opportunity'){
                $notes_table = $wpdb->prefix.'erc_opportunity_notes';
            }else if($note_type == 'project'){
                $notes_table = $wpdb->prefix.'erc_project_notes';                    
            }
            
            $response = array();
            if($note_id!=0){
                $delete = $wpdb->query("DELETE FROM $notes_table WHERE id=$note_id ");
                $response['delete'] = $delete;
                $response['note_id'] = $note_id;
                $response['message'] = 'Notes Deleted Successfully.';
            }

            return json_encode($response);
        }

       }

    public function create_opportunity_notes($request) {
        global $wpdb;
        //print_r($request);
        $opportunity_id = $request->get_param('opportunity_id');
        $note = $request->get_param('note');
        $user_id = $request->get_param('user_id');
        $time = date("Y-m-d h:i:sa");

		if(!empty($opportunity_id)){
		   
		    global $wpdb;
		    $notes_table = $wpdb->prefix.'erc_opportunity_notes';

		    $result = $wpdb->query("INSERT INTO `eccom_erc_opportunity_notes` (`opportunity_id`, `created_by`, `note`, `created`) VALUES ('".$opportunity_id."', '".$user_id."', '".$note."', '".$time."');");

		    $all_notes = $wpdb->get_results("SELECT * FROM $notes_table WHERE opportunity_id='".$opportunity_id."' ORDER BY id DESC LIMIT 1");

		        $html = ''; 
		        $i = $offset+1;
		                $from='UTC';
		                $to='America/New_York';
		                $format='Y-m-d h:i:s A';
		        foreach ($all_notes as $n_key => $n_value) { 
		            $date = $n_value->created;//UTC time
		            date_default_timezone_set($from);
		            $newDatetime = strtotime($date);
		            date_default_timezone_set($to);
		            $newDatetime = date($format, $newDatetime);
		            date_default_timezone_set('UTC');
		            $datetime = date_create($newDatetime);
		            // $datetime = date_create($n_value->created);
		            $time = date_format($datetime,"h:ia"); 
		            $day = date_format($datetime," D ");
		            $month = date_format($datetime," M ");
		            $date = date_format($datetime,"dS,");
		            $year = date_format($datetime," Y");
		            $actual_date = $time." on ".$day.$month.$date.$year;
		            
		            $notes = $n_value->note;

		            $html .='<div class="note-listing-div shadow">';   
		            $html .='<p id="'.$n_value->id.'" class="notes">'.$notes.'</p>';
                    $html .='<p class="date-time">('.$actual_date.')</p>';
		            $html .='</div>';
		                $i++; 
			        } 

			 return $html;
		}
    }

    // Callbacks for fee type info Endpoints
    public function get_fee_type($request) {
        global $wpdb;
        $fee_type_id = $request->get_param('id');
        $fee_type_table = $wpdb->prefix . 'fee_structure';
        $query = $wpdb->prepare("SELECT 
                                $fee_type_table.*
                                FROM $fee_type_table 
                                WHERE $fee_type_table.fee_type_id = '".$fee_type_id."'
                                ");
        return $wpdb->get_row($query);
    }

    // Callbacks for Fee Types Endpoints
    public function fee_types($request) {
        global $wpdb;
        $fee_type_table = $wpdb->prefix . 'fee_structure';
        $query = $wpdb->prepare("SELECT 
                                $fee_type_table.*
                                FROM $fee_type_table 
                                WHERE $fee_type_table.deleted_at IS NULL
                                ORDER BY $fee_type_table.fee_type_id DESC
                                ");
        return $wpdb->get_results($query);
    }

    /**
     * Create a new fee type.
     *
     * @param array $data fee type data.
     * @return int|WP_Error The ID of the newly created fee type, or WP_Error on failure.
     */
    public function create_fee_type(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'fee_structure';
        $data['fee_structure'] = str_replace("Plus", "+", $data['fee_structure']);
        $result = $wpdb->insert($table_name, $data);
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to insert fee type into database', $wpdb->last_error);
        }else{
            $fee_type_id = $wpdb->insert_id;
            $fields[0]['fee_structure'] = $data['fee_structure'];
            if($data['retainer_fee_name'] != ''){
                $fields[1]['retainer_fee_options'] = $data['retainer_fee_options'];
                $fields[2]['retainer_fee_name'] = $data['retainer_fee_name'];
                if($data['retainer_fee_options'] != 'Basis of'){
                    $fields[3]['retainer_amount_per'] = $data['retainer_amount_per'];
                }else{
                    $fields[3]['retainer_identifier'] = $data['retainer_identifier'];
                }
                $fields[4]['selected_fee_options'] = $data['selected_fee_options'];
                $fields[5]['selected_fee_name'] = $data['selected_fee_name'];
                if($data['selected_fee_options'] != 'Basis of'){
                    $fields[6]['selected_amount_per'] = $data['selected_amount_per'];
                }else{
                    $fields[6]['selected_identifier'] = $data['selected_identifier'];
                }
            }else{
                $fields[1]['selected_fee_options'] = $data['selected_fee_options'];
                $fields[2]['selected_fee_name'] = $data['selected_fee_name'];
                if($data['selected_fee_options'] != 'Basis of'){
                    $fields[3]['selected_amount_per'] = $data['selected_amount_per'];
                }else{
                    $fields[3]['selected_identifier'] = $data['selected_identifier'];
                }
            }
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['created_at'];
                    $logs_data['CreatedBy'] = $data['created_by'];
                    $logs_data['TableName'] = $table_name;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = 'N/A';
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $product_type_id;
                    $logs_data['Action'] = 'Create';
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                }
            }
            $results['status'] = 1;
            $results['message'] = 'Fee Type Created';
            echo json_encode($results);
        }
    }

    /**
     * Edit fee type.
     *
     * @param array $data fee type data.
     * @return int|WP_Error The ID of the previously edit fee type, or WP_Error on failure.
     */
    public function edit_fee_type(WP_REST_Request $request) {
        global $wpdb;
        $fee_type_id = $request->get_param('fee_type_id');
        $data = $request->get_json_params();
        $data['fee_structure'] = str_replace("Plus", "+", $data['fee_structure']);
        $table_name = $wpdb->prefix . 'fee_structure';
        /*$unit_data = $wpdb->get_results("SELECT UnitName FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.UnitName = '".$data['UnitName']."' AND UnitTypeID != ".$unit_type_id."");
        if(empty($unit_data)){*/
            unset($data['fee_type_id']);
            //Audit Logs Start
            $fields[0]['fee_structure'] = $data['fee_structure'];
            if($data['retainer_fee_name'] != ''){
                $fields[1]['retainer_fee_options'] = $data['retainer_fee_options'];
                $fields[2]['retainer_fee_name'] = $data['retainer_fee_name'];
                if($data['retainer_fee_options'] != 'Basis of'){
                    $fields[3]['retainer_amount_per'] = $data['retainer_amount_per'];
                }else{
                    $fields[3]['retainer_identifier'] = $data['retainer_identifier'];
                }
                $fields[4]['selected_fee_options'] = $data['selected_fee_options'];
                $fields[5]['selected_fee_name'] = $data['selected_fee_name'];
                if($data['selected_fee_options'] != 'Basis of'){
                    $fields[6]['selected_amount_per'] = $data['selected_amount_per'];
                }else{
                    $fields[6]['selected_identifier'] = $data['selected_identifier'];
                }
            }else{
                $fields[1]['selected_fee_options'] = $data['selected_fee_options'];
                $fields[2]['selected_fee_name'] = $data['selected_fee_name'];
                if($data['selected_fee_options'] != 'Basis of'){
                    $fields[3]['selected_amount_per'] = $data['selected_amount_per'];
                }else{
                    $fields[3]['selected_identifier'] = $data['selected_identifier'];
                }
            }
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."'  AND fee_type_id = ".$fee_type_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE fee_type_id = ".$fee_type_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['modified_at'];
                        $logs_data['CreatedBy'] = $data['modified_by'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $fee_type_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('fee_type_id' => $fee_type_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update fee type into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Fee Type Update Successfully';
                echo json_encode($results);die;
            }
        /*}else{
            $results['status'] = 0;
            $results['message'] = 'Unit Name Already Exist';
            echo json_encode($results);die;
        }*/
        //return $unit_type_id;
    }

    // Callbacks for Project Spaces Endpoints
    public function project_spaces($request) {
        global $wpdb;
        $project_spaces_table = $wpdb->prefix . 'project_spaces';
        $products_table = $wpdb->prefix . 'crm_products';
        $query = $wpdb->prepare("SELECT 
                                $project_spaces_table.project_space_id,$project_spaces_table.subsections_count,$project_spaces_table.subsections_name,$products_table.Title as product_name
                                FROM $project_spaces_table 
                                JOIN $products_table on $project_spaces_table.product_id = $products_table.ProductID
                                WHERE $project_spaces_table.deleted_at IS NULL
                                ORDER BY $project_spaces_table.project_space_id DESC
                                ");
        return $wpdb->get_results($query);
    }

    // check product unique or not
    public function check_product_unique($request){
        global $wpdb;
        $product_id = $request->get_param('product_id');
        $project_space_id = $request->get_param('project_space_id');
        
        $project_spaces_table = $wpdb->prefix . 'project_spaces';
        if(isset($project_space_id) && !empty($project_space_id)){
            $project_space_data = $wpdb->get_results("SELECT $project_spaces_table.project_space_id,$project_spaces_table.product_id FROM $project_spaces_table WHERE $project_spaces_table.product_id = '".$product_id."' AND $project_spaces_table.deleted_at IS NULL AND project_space_id!=".$project_space_id."");
        }else{
            $project_space_data = $wpdb->get_results("SELECT $project_spaces_table.project_space_id,$project_spaces_table.product_id FROM $project_spaces_table WHERE $project_spaces_table.product_id = '".$product_id."' AND $project_spaces_table.deleted_at IS NULL");
        }
        
        // print_r($miles_data);
        $match=0;
        if(count($project_space_data)!=0){
            $match=1;
        }//miles data
        
        if($match==1){
            $reuslts['status'] = 400;
            $reuslts['message'] = 'Project Space Already Created For This Product.';
            echo json_encode($reuslts);die;
        }else{
            $reuslts['status'] = 200;
            $reuslts['message'] = 'Project Space Not Created.';
            echo json_encode($reuslts);die;
        }
    }
    /**
     * Create a new project space.
     *
     * @param array $data project space data.
     * @return int|WP_Error The ID of the newly created project space, or WP_Error on failure.
     */
    public function create_project_space(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'project_spaces';
        $result = $wpdb->insert($table_name, $data);
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to insert project space into database', $wpdb->last_error);
        }else{
            $project_space_id = $wpdb->insert_id;
            $fields[0]['product_id'] = $data['product_id'];
            $fields[1]['subsections_count'] = $data['subsections_count'];
            $fields[2]['subsections_name'] = $data['subsections_name'];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['CreatedAt'];
                    $logs_data['CreatedBy'] = $data['CreatedBy'];
                    $logs_data['TableName'] = $table_name;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = 'N/A';
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $unit_type_id;
                    $logs_data['Action'] = 'Create';
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                }
            }
            $results['status'] = 1;
            $results['message'] = 'Product Space Created';
            echo json_encode($results);die;
        }
        //return $wpdb->insert_id;
    }

    /**
     * Create a new project.
     *
     * @param array $data Project data.
     * @return int|WP_Error The ID of the newly created project, or WP_Error on failure.
     */
    public function create_project(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'projects';
        
        $project_data = $wpdb->get_results("SELECT project_name FROM $table_name WHERE $table_name.deleted_at IS NULL AND $table_name.project_name = '".$data['project_name']."'");
        if(empty($project_data)){
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to create project into database', $wpdb->last_error);
            }else{
                $project_id = $wpdb->insert_id;
                //check affiliate user on lead
                $leadData = $wpdb->get_row("SELECT {$wpdb->prefix}erc_iris_leads_additional_info.affiliate_user_id,{$wpdb->prefix}erc_business_info.business_legal_name FROM {$wpdb->prefix}erc_iris_leads_additional_info 
                                                                      JOIN {$wpdb->prefix}erc_business_info ON {$wpdb->prefix}erc_iris_leads_additional_info.lead_id = {$wpdb->prefix}erc_business_info.lead_id
                                                                      WHERE {$wpdb->prefix}erc_iris_leads_additional_info.lead_id = ".$data['lead_id']."");
                if(!empty($leadData)){
                    $affiliate_user_id = $leadData->affiliate_user_id;
                    $business_legal_name = $leadData->business_legal_name;
                }else{
                    $affiliate_user_id = 0;
                    $business_legal_name = '';
                }
                if($affiliate_user_id > 0){
                    $wpdb->query("INSERT INTO {$wpdb->prefix}collaborators (project_id,user_id) VALUES (".$project_id.",".$affiliate_user_id.")");
                }
                //Product Data
                $product_data = $wpdb->get_row("SELECT Title as product_name FROM {$wpdb->prefix}crm_products WHERE ProductID = ".$data['product_id']."");
                if(!empty($product_data)){
                    $product_name = $product_data->product_name;
                }else{
                    $product_name = '';
                }
                //create note for project start
                $Created_User_Data = get_user_by('id',$data['created_by']);
                if(!empty($Created_User_Data)){
                    $username = $Created_User_Data->data->display_name;
                }else{
                    $username = '';
                }
                $project_note_data['project_id']=$project_id;
                $project_note_data['user_id']=$data['created_by'];
                $project_note_data['note'] = $username.' added a comment: Project Created For '.$business_legal_name.' - '.$product_name;
                $project_note_data = json_encode($project_note_data);
                $url = get_site_url()."/wp-json/productsplugin/v1/create_project_notes";
                $args = array('body' => $project_note_data,'headers' => array('Content-Type' => 'application/json'));
                $response = wp_remote_post($url,$args);
                //create note for project end
                $fields[0]['lead_id'] = $data['lead_id'];
                $fields[0]['product_id'] = $data['product_id'];
                $fields[0]['contact_id'] = $data['contact_id'];
                $fields[0]['project_name'] = $data['project_name'];
                $fields[1]['milestone_id'] = $data['milestone_id'];
                $fields[2]['milestone_stage_id'] = $data['milestone_stage_id'];
                $fields[3]['sales_user_id'] = $data['sales_user_id'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['created_at'];
                        $logs_data['CreatedBy'] = $data['created_by'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $project_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
            

                            // ------ create milestone stage log ----
                            $milestone_stage_log_table = $wpdb->prefix.'milestone_stage_log';
                            $mile_data = array();
                            $mile_data['lead_id']=$data['lead_id'];
                            $mile_data['project_id']=$project_id;
                            $mile_data['opportunity_id']=0;
                            $mile_data['product_id']=$data['product_id'];
                            $mile_data['updated_milestone_id']=$data['milestone_id'];
                            $mile_data['previous_milestone_id']=0;
                            $mile_data['updated_stage_id']=$data['milestone_stage_id'];
                            $mile_data['previous_stage_id']=0;
                            $mile_data['changed_date']=date("Y-m-d h:i:sa");
                            $mile_data['changed_by']=$data['created_by'];

                            $insert_mile_log = $wpdb->insert($milestone_stage_log_table ,$mile_data);
                            // ------ create milestone stage log ----
                            

                $results['status'] = 1;
                $results['message'] = 'Project Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Project Name Already Exist';
            echo json_encode($results);die;
        }
    }
    public function create_project_notes($request) {
        global $wpdb;
        $project_id = $request->get_param('project_id');
        $note = $request->get_param('note');
        $user_id = $request->get_param('user_id');
        $time = date("Y-m-d h:i:sa");
        if(!empty($project_id)){
            global $wpdb;
            $notes_table = $wpdb->prefix.'erc_project_notes';
            $result = $wpdb->query("INSERT INTO $notes_table (`project_id`, `created_by`, `note`, `created`) VALUES ('".$project_id."', '".$user_id."', '".$note."', '".$time."');");
            $all_notes = $wpdb->get_results("SELECT * FROM $notes_table WHERE project_id='".$project_id."' ORDER BY id DESC LIMIT 1");
                $html = ''; 
                $i = $offset+1;
                $from='UTC';
                $to='America/New_York';
                $format='Y-m-d h:i:s A';
                foreach ($all_notes as $n_key => $n_value) { 
                    $date = $n_value->created;//UTC time
                    date_default_timezone_set($from);
                    $newDatetime = strtotime($date);
                    date_default_timezone_set($to);
                    $newDatetime = date($format, $newDatetime);
                    date_default_timezone_set('UTC');
                    $datetime = date_create($newDatetime);
                    // $datetime = date_create($n_value->created);
                    $time = date_format($datetime,"h:ia"); 
                    $day = date_format($datetime," D ");
                    $month = date_format($datetime," M ");
                    $date = date_format($datetime,"dS,");
                    $year = date_format($datetime," Y");
                    $actual_date = $time." on ".$day.$month.$date.$year;
                    
                    $notes = $n_value->note;

                    $html .='<div class="note-listing-div shadow">';   
                    $html .='<p id="'.$n_value->id.'" class="notes">'.$notes.'</p>';
                    $html .='<p class="date-time">('.$actual_date.')</p>';
                    $html .='</div>';
                        $i++; 
                    } 

             return $html;
        }
    }

    //reopen opportunity
    public function reopen_opportunity($request) {
        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);
        global $wpdb;
        $response = array();
        $OpportunityID = $request->get_param('OpportunityID');
        $product_id = $request->get_param('product_id');
        $opportunity_table = $wpdb->prefix.'opportunities';
        $opportunity_product_table = $wpdb->prefix.'opportunity_products';
        $user_id = $request->get_param('user_id');
        $time = date("Y-m-d h:i:sa");
        if(!empty($OpportunityID)){
            $result = $wpdb->get_results("UPDATE $opportunity_table SET opportunityClosure = '', envelop_id = '', last_agreement_sent = '', last_payment_sent = ''  WHERE OpportunityID='".$OpportunityID."'");

            $old_data = $wpdb->get_row("SELECT milestone_id,milestone_stage_id FROM $opportunity_product_table WHERE opportunity_id='".$OpportunityID."'");

            $old_milestone_id = $old_data->milestone_id;
            $old_milestone_stage_id = $old_data->milestone_stage_id;
            $add_milestone_log = 0;

            if(isset($product_id) && $product_id == 936){  
          
            $result = $wpdb->get_results("UPDATE $opportunity_product_table SET milestone_id = '126', milestone_stage_id = '327'  WHERE opportunity_id='".$OpportunityID."'");
            
            if($old_milestone_stage_id != 327){
                $Milestone = 126;
                $MilestoneStage = 327;
                $add_milestone_log=1;
            }

          }else if(isset($product_id) && $product_id == 932){  

            $result = $wpdb->get_results("UPDATE $opportunity_product_table SET milestone_id = '100', milestone_stage_id = '142'  WHERE opportunity_id='".$OpportunityID."'");
            
            if($old_milestone_stage_id != 142){
                $Milestone = 100;
                $MilestoneStage = 142;
                $add_milestone_log=1;
            }

          }else if(isset($product_id) && $product_id == 935){  

            $result = $wpdb->get_results("UPDATE $opportunity_product_table SET milestone_id = '112', milestone_stage_id = '290'  WHERE opportunity_id='".$OpportunityID."'");
                
                if($old_milestone_stage_id != 290){
                    $Milestone = 112;
                    $MilestoneStage = 290;
                    $add_milestone_log=1;
                }

          }else{

            // $result = $wpdb->get_results("UPDATE $opportunity_product_table SET milestone_id = '126', milestone_stage_id = '327'  WHERE opportunity_id='".$OpportunityID."'");
            
          }  

          // ----- milestone log maintain -----
                        if($add_milestone_log==1){

                            $lead_id = $wpdb->get_var("SELECT LeadID FROM $opportunity_table WHERE OpportunityID='".$OpportunityID."'");

                            $milestone_stage_log_table = $wpdb->prefix.'milestone_stage_log';
                            $mile_data = array();
                            $mile_data['lead_id']=$lead_id;
                            $mile_data['project_id']=0;
                            $mile_data['opportunity_id']=$OpportunityID;
                            $mile_data['product_id']=$product_id;
                            $mile_data['updated_milestone_id']=$Milestone;
                            $mile_data['previous_milestone_id']=$old_milestone_id;
                            $mile_data['updated_stage_id']=$MilestoneStage;
                            $mile_data['previous_stage_id']=$old_milestone_stage_id;
                            $mile_data['changed_date']=date("Y-m-d h:i:sa");
                            $mile_data['changed_by']=0;

                            $insert_mile_log = $wpdb->insert($milestone_stage_log_table ,$mile_data);
                        }// milestone log maintain end


            $response['status'] = true;
            $response['code'] = 201;
            $response['message'] = 'Opportunity Successfully Reopened.';
        }else{
            $response['status'] = false;
            $response['code'] = 302;
            $response['message'] = 'Something went wrong';
        }
        echo json_encode($response);exit;
    }

    //reopen stc opportunity
    public function reopen_stc_opportunity($request) {
        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);
        global $wpdb;
        $response = array();
        $OpportunityID = $request->get_param('OpportunityID');
        $opportunity_table = $wpdb->prefix.'opportunities';
        $opportunity_product_table = $wpdb->prefix.'opportunity_products';
        $user_id = $request->get_param('user_id');
        $time = date("Y-m-d h:i:sa");
        if(!empty($OpportunityID)){
            $result = $wpdb->get_results("UPDATE $opportunity_table SET opportunityClosure = '', envelop_id = '', last_agreement_sent = '', last_payment_sent = ''  WHERE OpportunityID='".$OpportunityID."'");
            $result = $wpdb->get_results("UPDATE $opportunity_product_table SET milestone_id = '122', milestone_stage_id = '323'  WHERE opportunity_id='".$OpportunityID."'");

            $old_data = $wpdb->get_row("SELECT milestone_id,milestone_stage_id FROM $opportunity_product_table WHERE opportunity_id='".$OpportunityID."'");

            $old_milestone_id = $old_data->milestone_id;
            $old_milestone_stage_id = $old_data->milestone_stage_id;
            $add_milestone_log = 0;

            if($old_milestone_stage_id != 323){
                $Milestone = 122;
                $MilestoneStage = 323;
                $add_milestone_log=1;
            }

            if($add_milestone_log==1){

                $lead_id = $wpdb->get_var("SELECT LeadID FROM $opportunity_table WHERE OpportunityID='".$OpportunityID."'");

                $milestone_stage_log_table = $wpdb->prefix.'milestone_stage_log';
                $mile_data = array();
                $mile_data['lead_id']=$lead_id;
                $mile_data['project_id']=0;
                $mile_data['opportunity_id']=$OpportunityID;
                $mile_data['product_id']=937;
                $mile_data['updated_milestone_id']=$Milestone;
                $mile_data['previous_milestone_id']=$old_milestone_id;
                $mile_data['updated_stage_id']=$MilestoneStage;
                $mile_data['previous_stage_id']=$old_milestone_stage_id;
                $mile_data['changed_date']=date("Y-m-d h:i:sa");
                $mile_data['changed_by']=0;

                $insert_mile_log = $wpdb->insert($milestone_stage_log_table ,$mile_data);
            }// milestone log maintain end


            $response['status'] = true;
            $response['code'] = 201;
            $response['message'] = 'Opportunity Successfully Reopened.';
        }else{
            $response['status'] = false;
            $response['code'] = 302;
            $response['message'] = 'Something went wrong';
        }
        echo json_encode($response);exit;
    }
    
    public function fetch_project_notes($request) {
        global $wpdb;
        $project_id = $request->get_param('project_id');
        $offset = $request->get_param('offset');
        if(!empty($project_id)){
            global $wpdb;
            $notes_table = $wpdb->prefix.'erc_project_notes';
            $all_notes = $wpdb->get_results("SELECT * FROM $notes_table WHERE project_id='".$project_id."' ORDER BY id DESC ");
                $html = ''; 
                $i = $offset+1;
                $from='UTC';
                $to='America/New_York';
                $format='Y-m-d h:i:s A';
                foreach ($all_notes as $n_key => $n_value) { 
                    $date = $n_value->created;//UTC time
                    date_default_timezone_set($from);
                    $newDatetime = strtotime($date);
                    date_default_timezone_set($to);
                    $newDatetime = date($format, $newDatetime);
                    date_default_timezone_set('UTC');
                    $datetime = date_create($newDatetime);
                    // $datetime = date_create($n_value->created);
                    $time = date_format($datetime,"h:ia"); 
                    $day = date_format($datetime," D ");
                    $month = date_format($datetime," M ");
                    $date = date_format($datetime,"dS,");
                    $year = date_format($datetime," Y ");
                    $actual_date = $time." on ".$day.$month.$date.$year;
                    $notes = $n_value->note;
                    $html .='<div class="note-listing-div shadow">';   
                    $html .='<p id="'.$n_value->id.'" class="notes">'.$notes.'</p>';
                    $html .='<p class="date-time">('.$actual_date.')</p>';
                    $html .='</div>';
                    $i++; 
                } 
             return $html;
        }
    }

    /**
     * Resending Payment Link 
     * Applicable only for Product ID 937
     * Getting email from ooprtunity_id
     * Using lead_id to pull email.
    */
     function send_stc_payment_link($request){
        global $wpdb;
        $opportunity_id = $request->get_param('opportunity_id');
        $lead_id = $request->get_param('lead_id');

        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => get_site_url().'/wp-json/v1/generate-auth-key-by-lead-id',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS => array('lead_id' => $lead_id),
          CURLOPT_HTTPHEADER => array(
            'Cookie: PHPSESSID=taamorg590sbpoh6l7833p34ql'
          ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $response = json_decode($response);

        $auth_id = $response->data->payment_auth;
        
        
        $table_contacts = $wpdb->prefix . 'op_contacts';
        $table_email_contact = $wpdb->prefix . 'op_email_contact';
        $table_emails = $wpdb->prefix . 'op_emails';
        $table_opportunities = $wpdb->prefix . 'opportunities';

        // Query to retrieve the contact details
        $query = $wpdb->prepare("
            SELECT 
                c.first_name, 
                c.last_name, 
                c.trash, 
                e.email 
            FROM 
                $table_contacts AS c 
            INNER JOIN 
                $table_email_contact AS ec 
            ON 
                c.id = ec.contact_id 
            INNER JOIN 
                $table_emails AS e 
            ON 
                ec.email_id = e.id 
            WHERE 
                c.id = (
                    SELECT ContactID 
                    FROM $table_opportunities 
                    WHERE OpportunityID = %d
                )
        ", $opportunity_id);

        
        $contact_details = $wpdb->get_row($query);

        
        if ($contact_details) {
            $first_name= $contact_details->first_name;
            $last_name= $contact_details->last_name;
            $email= $contact_details->email;
            $email= '<EMAIL>';
            //$email= '<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>';
            $trash= $contact_details->trash;
            if($trash !=1)
            {
                $dataArray1 = array(
                    'id'  => 96,
                    'dynamic' => true,
                    'to' => $email,
                    'from'=>'<EMAIL>',
                    'password'=>'Tal40369',
                    'client_name'=> $first_name." ".$last_name,
                    'payment_link'=>'https://portal.occamsadvisory.com/payment/?data='.$auth_id
                                     
                );
            
                send_email_template($dataArray1);
                $return_data= array('response'=>'success','lead_id'=>$lead_id,'message'=>'Payment Link Sent');
                echo json_encode($return_data);
                die();

                
            }
            else
            {
                $return_data= array('response'=>'failed','lead_id'=>$lead_id,'message'=>'Contact marked as Deleted');
                echo json_encode($return_data);
                die();
            }
        } else {
                $return_data= array('response'=>'failed','lead_id'=>$lead_id,'message'=>'Contact not found.');
                echo json_encode($return_data);
                die();
        }
         
    }

    
    // call this api on change project stages
    function submit_opportunity_project_stages($request){
        global $wpdb;
        $table_name = $wpdb->prefix.'opportunity_project_stage_log';
        $opportunity_project_id = $request->get_param('opportunity_project_id');
        $stage = $request->get_param('stage');
        $user_id = $request->get_param('user_id');
        $type = $request->get_param('type');

        $date = date('Y-m-d H:i:s');

        $data['opportunity_project_id'] =  $opportunity_project_id;
        $data['stage'] =  $stage;
        $data['user_id'] =  $user_id;
        $data['modification_date'] =  $date;
        $data['type'] =  $type;
        
        $result = $wpdb->insert($table_name, $data);

        if($result){
            $return_data= array('response'=>'success','opportunity_project_id'=>$opportunity_project_id,'message'=>$type.' stage store successfully.','status'=>200);
        }else{
               $return_data= array('response'=>'failed','opportunity_project_id'=>$opportunity_project_id,'message'=>$type.' stage store failed.','status'=>400);
        }

        echo json_encode($return_data);
        die();
    }
    /**
     * Create a new product service head.
     *
     * @param array $data Product service head data.
     * @return int|WP_Error The ID of the newly created product service head, or WP_Error on failure.
     */
    public function create_product_service_head(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_service_head';
        /*check product sku*/
        $product_data = $wpdb->get_results("SELECT DeletedAt FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.qb_product_id = ".$data['qb_product_id']."");
        /* if(empty($product_data)){ */
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to create product service head into database', $wpdb->last_error);
            }else{
                $product_service_id = $wpdb->insert_id;
                $fields[0]['product_id'] = $data['product_id'];
                $fields[1]['qb_product_id'] = $data['qb_product_id'];
                $fields[2]['product_head'] = $data['product_head'];
                $fields[3]['amount_type'] = $data['amount_type'];
                $fields[4]['product_rate'] = $data['product_rate'];
                $fields[5]['product_unit'] = $data['product_unit'];
                $fields[6]['invoice_type'] = $data['invoice_type'];
                $fields[7]['alias'] = $data['alias'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['CreatedAt'];
                        $logs_data['CreatedBy'] = $data['CreatedBy'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_service_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Service Head Created Successfully.';
                echo json_encode($results);die;
            }
        /* }else{
            $results['status'] = 0;
            $results['message'] = 'Product Head Already Exists For This Service.';
            echo json_encode($results);die;
        } */
    }
    // Callbacks for Products Service Head Endpoints
    public function product_service_head_list(WP_REST_Request $request) {
        
        global $wpdb;
        $product_id = $request->get_param('product_id');
        $billing_profile = $request->get_param('billing_profile');
        $table_name = $wpdb->prefix . 'product_service_head';
        $pro_table_name = $wpdb->prefix . 'crm_products';

        // check billing profile is present or not if present then add to a when variable and use it in query
        $where = '';
        if($billing_profile){
            $where .= " AND $table_name.billing_profile = ".$billing_profile." ";
        }

        $query = $wpdb->prepare("SELECT 
            $table_name.id,$table_name.qb_product_id,$table_name.product_head,$table_name.amount_type,$table_name.product_rate,$table_name.product_unit,$table_name.invoice_type,$table_name.alias,$table_name.billing_profile,$pro_table_name.Title as product_name
            FROM $table_name 
            JOIN $pro_table_name ON $table_name.product_id = $pro_table_name.ProductID
            WHERE $table_name.product_id = %d AND $table_name.DeletedAt IS NULL $where
            ORDER BY $table_name.id DESC
            ", $product_id);
        return $wpdb->get_results($query);
    }
    /**
     * Edit a product service head.
     *
     * @param array $data Product service head data.
     * @return int|WP_Error The ID of the previously edit product service head, or WP_Error on failure.
     */
    public function edit_product_service_head(WP_REST_Request $request) {
        global $wpdb;
        $product_service_head_id = $request->get_param('product_service_head_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_service_head';
        unset($data['product_service_head_id']);
        /*check product service head*/
        $pro_service_head_data = $wpdb->get_results("SELECT DeletedAt FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.qb_product_id = ".$data['qb_product_id']." AND $table_name.id != ".$product_service_head_id."");
        if(empty($pro_service_head_data)){
            //Audit Logs Start
            $fields[0]['qb_product_id'] = $data['qb_product_id'];
            $fields[1]['product_head'] = $data['product_head'];
            $fields[2]['amount_type'] = $data['amount_type'];
            $fields[3]['product_rate'] = $data['product_rate'];
            $fields[4]['product_unit'] = $data['product_unit'];
            $fields[5]['invoice_type'] = $data['invoice_type'];
            $fields[6]['alias'] = $data['alias'];
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."'  AND id = ".$product_service_head_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE id = ".$product_service_head_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['ModifiedAt'];
                        $logs_data['CreatedBy'] = $data['ModifiedBy'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_service_head_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }         
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('id' => $product_service_head_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update product service head into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Service Head Updated';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Product Head Already Exist For This Service';
            echo json_encode($results);die;
        }
    }
    // Callbacks for Role Products Endpoints
    public function role_products($request) {
        global $wpdb;
        $role_products_table = $wpdb->prefix . 'role_products';
        $products_table = $wpdb->prefix . 'crm_products';
        $query = $wpdb->prepare("SELECT 
                                $role_products_table.id,$role_products_table.role,$role_products_table.product_ids
                                FROM $role_products_table 
                                ORDER BY $role_products_table.id DESC
                                ");
        $role_products = $wpdb->get_results($query);
        if(!empty($role_products)){
                $i=0;
                foreach($role_products as $role_product){
                    $data[$i]['id'] = $role_product->id;
                    $data[$i]['role'] = $role_product->role;
                    //get products name
                    $product_ids = explode(",",$role_product->product_ids);
                    $productNames = '';
                    foreach($product_ids as $key => $value){
                        $products = $wpdb->get_row("SELECT Title FROM $products_table WHERE ProductID = ".$value." AND Status = 'active' AND DeletedAt IS NULL");
                        if(!empty($products)){
                            $productNames.= $products->Title.', ';
                        }
                    }
                    $productNames = rtrim($productNames,', ');
                    $data[$i]['products'] = $productNames;
                }
                $results['status'] = 1;
                $results['message'] = 'List of all role products';
                $results['products'] = $data;
        }else{
            $results['status'] = 0;
            $results['message'] = 'No role product found';
        }
        echo json_encode($results);die;
    }

    /**
     * Edit a product service head.
     *
     * @param array $data Product service head data.
     * @return int|WP_Error The ID of the previously edit product service head, or WP_Error on failure.
     */
    public function save_product_roles(WP_REST_Request $request) {
        global $wpdb;
        $table_name = $wpdb->prefix.'crm_products';
        $product_id = $request->get_param('product_id');
        $product_roles = implode(',',$request->get_param('product_roles'));
        $data = $request->get_json_params();
        $datas['product_roles'] = $product_roles;
        $result = $wpdb->update($table_name, $datas, array('ProductID' => $product_id));
        $results['status'] = 'success';
        $results['message'] = 'Product Roles Updated';
        echo json_encode($results);die;
    }

    public function get_product_settings(WP_REST_Request $request){
            global $wpdb;
            $product_setting_table = $wpdb->prefix.'crm_product_setting';
            $product_id = $request->get_param('product_id');
            $product_setting =  $wpdb->get_results("SELECT * FROM $product_setting_table WHERE product_id=$product_id");
            return $product_setting;
    }

    public function log_audit_entry($id,$column_name, $new_data , $table_name){
        global $wpdb;
        $collaborator_table = $wpdb->prefix.'collaborators';
        $user_id = get_current_user_id();
        if($table_name != $collaborator_table){
            $old_data = $wpdb->get_row("SELECT * FROM $table_name WHERE $column_name = ".$id."");

            if ($old_data) {
                $changes = array();
                foreach ($new_data as $field_name => $new_value) {
                    $old_value = isset($old_data->$field_name) ? $old_data->$field_name : null;
                    if (trim($new_value) !== trim($old_value) ) {
                        // echo $old_value;
                         if($new_value =='N/A' && $old_value==''){ 
                         }else{
                                // echo $old_value;
                                $changes[$field_name] = array('old' => $old_value, 'new' => $new_value);
                        }
                   }
                }
        // print_r($changes);
                // Insert each change into the audit log
                foreach ($changes as $field_name => $change) {

                    $audit_data = array(
                                    'DateCreated' => current_time('mysql'),
                                    'CreatedBy' => $user_id,
                                    'TableName' => $table_name,
                                    'FieldName' => $field_name,
                                    'DataType' => gettype($change['new']),
                                    'BeforeValueString' => $change['old'],
                                    'AfterValueString' => $change['new'],
                                    'FieldID' => $id,
                                    'Action' => 'Update',
                                );
                            // print_r($audit_data);
                    // Insert the audit log entry
                    $result = $wpdb->insert($wpdb->prefix . 'audit_logs', $audit_data);
                    
                    if ($result === false) {
                        // error_log("Error inserting audit log: " . $wpdb->last_error);
                    }
                }// changes loop
            }// old data check
        }else if($table_name == $collaborator_table){ // table name check
            // $wpdb->query("SELECT user_id FROM $collaborator_table WHERE $column_name=$id)");

        }
        return true;
    }// log function close

}

$crm_erp_rest_api = new CRM_ERP_REST_API();