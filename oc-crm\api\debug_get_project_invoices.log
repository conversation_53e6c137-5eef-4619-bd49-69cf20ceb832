=== New Request ===
Request Received
Params: Array
(
    [project_id] => 1700
)
Parsed project_id: 1700
Resolved lead_id: 9301, product_id: 935
get_invoices_by_lead_and_product called
Params: lead_id = 9301, product_id = 935
Executed Query: 
       SELECT 
           inv.*, 
           
           CASE 
               WHEN prod.product_name <> '' THEN 
                   GROUP_CONCAT(DISTINCT prod.product_name ORDER BY prod.product_name SEPARATOR ', ') 
               ELSE ''
           END as product_names,
           
           COUNT(latest_payment.payment_id) as payment_count,
           latest_payment.payment_mode as payments_mode,
           latest_payment.payment_cleared_date as payments_cleared_date,
           latest_payment.payment_date as payments_date,
           latest_payment.received_amt as received_amts,
           inv_token.token
       FROM eccom_invoices inv
       LEFT JOIN eccom_invoices_products prod
           ON inv.customer_invoice_no = prod.invoice_number
       LEFT JOIN 
           (
               SELECT 
                   p1.*
               FROM 
                   eccom_invoice_payments p1
               INNER JOIN 
                   (
                       SELECT 
                           invoice_id, 
                           MAX(payment_date) as latest_payment_date
                       FROM 
                           eccom_invoice_payments
                       GROUP BY 
                           invoice_id
                   ) p2 
                   ON p1.invoice_id = p2.invoice_id 
                   AND p1.payment_date = p2.latest_payment_date
           ) latest_payment
       ON inv.id = latest_payment.invoice_id
       LEFT JOIN eccom_invoice_token inv_token 
       ON inv.customer_invoice_no = inv_token.invoice_id
       WHERE inv.lead_id = '9301' 
       AND inv.parent_product = '935' 
       AND inv.status != 13
       GROUP BY inv.id 
       ORDER BY inv.id DESC
   
Query Error: 
Invoice count: 7
Invoices found: 7
