<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
<script src="https://cdn.rawgit.com/harvesthq/chosen/gh-pages/chosen.jquery.min.js"></script>
<link href="https://cdn.rawgit.com/harvesthq/chosen/gh-pages/chosen.min.css" rel="stylesheet"/>

<style type="text/css">
    select.floating__input{max-width: 100%;}
    .crm_erp_btn {
      padding: 11px 50px;
      color: #FF5F00;
      font-size: 16px;
      font-weight: 600;
      background: #FFFFFF 0% 0% no-repeat padding-box;
      box-shadow: 0px 3px 6px #00000029;
      border: 2px solid #FF5F00;
      border-radius: 11px;
    }
    #form-response {
	  color: green;
	  margin-top: 10px;
	  font-size: 15px;
	}
    .fade:not(.show) {
      opacity: 1;
    }
</style>
<?php
if($_REQUEST['action'] == 'edit'){
    $mile_id = $_REQUEST['id'];
    $milestone_manager = new CRM_ERP_Milestone_Manager();
    $milestone_data = $milestone_manager->get_milestone($mile_id);
    $product_id = $milestone_data->product_id;
    $status = $milestone_data->status;
    $heading_text = 'Edit';
    $btn_text = 'Update';
    $heading_img = 'edit-crm-icon.png';
}else{
	$product_status_id = '';
	$product_id = '';
	$status = '';
	$heading_text = 'New';
	$btn_text = 'Submit';
    $heading_img = 'add-crm-icon.png';
}
?>
<div class="main_content_iner ">
    <div class="container-fluid p-0">
         <div class="row justify-content-center">
            <div class="col-lg-12">
                <div id="form_2" class="white_card card_height_100 mb_30" style="display: block;">
                     <div class="white_card_header">
                        <div class="box_header m-0">
                            <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/<?php echo $heading_img; ?>" class="page-title-img" alt="">
                            <h4><?php echo $heading_text;?> Milestone</h4>
                        </div>
                        <?php if($product_status_id != ''){?>
                        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                            <li class="nav-item active">
                                <a class="nav-link" id="pills-erc-edit-info" data-toggle="pill" href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true">Edit</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="pills-erc-audit-log-info" data-toggle="pill" href="#pills-audit-log" role="tab" aria-controls="pills-audit-log" aria-selected="true">Audit Log</a>
                            </li>
                        </ul>
                        <?php } ?>
                    </div>
                    <div class="white_card_body">
                        <div class="container-fluid">
                            <div class="tab-content" id="pills-tabContent">
                                <div class="tab-pane scroll_height-business scroll-common fade shows active" id="pills-home" role="tabpanel" aria-labelledby="pills-erc-edit-info">
                                    <form id="product-status-form" method="post">
                                        <div class="row">
                                            <div class="floating col-md-6">     
                                                <label for="Name">Milestone Name:</label>
                                                <input type="text" name="milestone_name" id="milestone_name" class="floating__input form-control" value="<?php echo $milestone_name;?>">
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="title">Select Product:*</label>
                                                <select id="product_id" name="product_id" class="floating__input form-control product_id" multiple >
                                                    <option value="">Select Product</option>
                                                    <?php foreach($products as $product): 
                                                            if($product->ProductID == $product_id){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = '';
                                                            }
                                                    ?>
                                                        <option value="<?php echo $product->ProductID; ?>" <?php echo $sel;?>><?php echo $product->Title; ?></option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="description">Status:</label>
                                               <label class="switch">
                                                <input type="checkbox" checked="">
                                                <span class="slider round"></span>
                                                </label>
                                            </div>
                                        </div>
                                        <input type="hidden" name="product_status_id" id="product_status_id" value="<?php echo $product_status_id;?>">
                                        <p><button type="submit" id="crm_erp_btn" class="crm_erp_btn"><?php echo $btn_text;?></button></p>
                                    </form>
                                    <div id="form-response"></div>
                                </div>
                                <?php if($product_status_id != ''){?>
                                <div class="tab-pane scroll_height-business scroll-common fade" id="pills-audit-log" role="tabpanel" aria-labelledby="pills-erc-audit-log-info">
                                    <div class="row">
                                        <div class="col-sm-12">
                                            <h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;font-weight: bold;">Audit Logs</h5>
                                        </div>
                                        <table id="audit_log_table">
                                            <thead>
                                                <tr>
                                                    <th>Field Name</th>
                                                    <th>Changed From</th>
                                                    <th>Changed To</th>
                                                    <th>Data Type</th>
                                                    <th>Action</th>
                                                    <th>Changed Time</th>
                                                    <th>Changed By</th>
                                                </tr>
                                            </thead>
                                            <tbody class="audit_data1">
                                                <?php
                                                global $wpdb;
                                                $audit_log_table = $wpdb->prefix . 'audit_logs';
                                                $audit_logs = $wpdb->get_results("SELECT $audit_log_table.* FROM $audit_log_table WHERE $audit_log_table.TableName = '" . $wpdb->prefix . "product_status'  AND $audit_log_table.FieldID = " . $product_status_id . " ORDER BY $audit_log_table.LogID DESC");
                                                $c = 0;
                                                $audit_log_history = '';
                                                if (!empty($audit_logs)) {
                                                    $count = 0;
                                                    foreach ($audit_logs as $audit_log) {
                                                        $CreatedBy = get_userdata($audit_log->CreatedBy);
                                                        if(!empty($CreatedBy)){
                                                            $changed_by = $CreatedBy->data->display_name;
                                                        }else{
                                                            $changed_by = '';
                                                        }
                                                        $DateCreated = date('Y-m-d H:i a',strtotime($audit_log->DateCreated));
                                                        $audit_log_history .= "<tr>
                                                              <td>" . $audit_log->FieldName . "</td>
                                                              <td>" . $audit_log->BeforeValueString . "</td>
                                                              <td>" . $audit_log->AfterValueString . "</td>
                                                              <td>" . $audit_log->DataType . "</td>
                                                              <td>" . $audit_log->Action . "</td>
                                                              <td>" . $DateCreated . "</td>
                                                              <td>" . $changed_by . "</td>
                                                            </tr>";
                                                    }
                                                } else {
                                                    $audit_log_history .= "<tr>
                                                      <td></td>
                                                      <td></td>
                                                      <td></td>
                                                      <td>No Data Found</td>
                                                      <td></td>
                                                      <td></td>
                                                      <td></td>
                                                    </tr>";
                                                }
                                                echo $audit_log_history;?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- .site-main -->
</div><!-- .content-area -->

<script type="text/javascript">
    jQuery(document).ready(function($) {
        jQuery("#audit_log_table").DataTable({"ordering": false});
        jQuery(document).on('click','#crm_erp_btn',function(e) {
            //e.preventDefault();
            jQuery('#product-status-form').customValidation({
            rules: {
                "product_id": ["required"],
                "status": ["required"],
            },
            messages: {
                "product_id": {
                    "required": "Product Type is required.",
                },
                "status": {
                    "required": "Status is required.",
                },
            },
            success: function() {
              jQuery("#crm_erp_btn").html('Please wait..');
              jQuery("#crm_erp_btn").attr('disabled',true);
                var $form = jQuery("#product-status-form");
                var $responseDiv = jQuery('#form-response');

                jQuery.ajax({
                    type: 'POST',
                    url: 'admin-ajax.php',
                    data: $form.serialize() + '&action=product_status_submit',
                    success: function(response) {
                        jQuery("#crm_erp_btn").html('Submit');
                        jQuery("#crm_erp_btn").attr('disabled',false);
                        //jQuery("#product-form").trigger('reset');
                        window.location.href = '?page=crm_erp_product_status';
                        //$responseDiv.html(response.data);
                    },
                    error: function() {
                        //$responseDiv.html('An error occurred');
                    }
                });
            }
        });
    });
});
    $(".product_id").chosen({
        no_results_text: "Oops, nothing found!"
        })
</script>