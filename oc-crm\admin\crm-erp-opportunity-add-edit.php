<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<?php
$userdata = get_user_by( 'id', get_current_user_id() );
$user_roles=$userdata->roles;
$milestones_table = $wpdb->prefix.'milestones';    
$milestone_status_table = $wpdb->prefix.'milestone_stages';    
$product_status_table = $wpdb->prefix.'product_status';    
$products_table = $wpdb->prefix.'crm_products';    
$business_table = $wpdb->prefix.'erc_business_info';    
$additional_table = $wpdb->prefix.'erc_iris_leads_additional_info';
$contacts_table = $wpdb->prefix.'op_contacts'; 
$contact_lead_table = $wpdb->prefix.'op_contact_lead';    
$opportunity_table = $wpdb->prefix.'opportunities';    

$all_products =  $wpdb->get_results("SELECT $products_table.productID,$products_table.title,$products_table.unitPrice,$products_table.deletedBy FROM $products_table WHERE $products_table.DeletedAt IS NULL AND $products_table.Status = 'active' GROUP BY $products_table.productID");
$where = " WHERE ( (lower($business_table.business_legal_name) NOT LIKE 'test%') AND $business_table.business_legal_name!='')";
if(in_array("iris_affiliate_users", $user_roles)){
    $all_leads = $wpdb->get_results("SELECT bi.lead_id, bi.business_legal_name FROM $business_table bi JOIN $additional_table ai ON bi.lead_id = ai.lead_id WHERE bi.business_legal_name IS NOT NULL AND bi.business_legal_name != '' AND (ai.erc_reffrer_id = ".get_current_user_id()." OR ai.affiliate_user_id = ".get_current_user_id().")");
}else{
    $all_leads = $wpdb->get_results("SELECT lead_id, business_legal_name FROM $business_table WHERE business_legal_name IS NOT NULL AND business_legal_name != ''");
}
$all_nextStep = $wpdb->get_results("SELECT NextStep FROM $opportunity_table WHERE $opportunity_table.DeletedAt IS NULL");
$myarray = array();
foreach($all_nextStep as $da){
    if(!empty($da->NextStep)){
        array_push($myarray,$da->NextStep);
    }
}
$allNextStep = json_encode($myarray);

$searchable_class = "show_search";
if($_REQUEST['action'] == 'edit'){
    $searchable_class = "";
    $opportunity_id = $_REQUEST['id'];
    $product_manager = new CRM_ERP_Opportunity_Manager();
    $opportunity_data = $product_manager->get_opportunity($opportunity_id);
  
    $OpportunityName = $opportunity_data->OpportunityName;
    $LeadID = $opportunity_data->LeadID;
    $ContactID = $opportunity_data->ContactID;
    $ExpectedCloseDate = $opportunity_data->ExpectedCloseDate;
    $OpportunityCurrency = $opportunity_data->OpportunityCurrency;
    $OpportunityAmount = $opportunity_data->OpportunityAmount;
    $Probability = $opportunity_data->Probability;
    $Description = $opportunity_data->Description;
    $Notes = $opportunity_data->Notes;
    $next_step = $opportunity_data->NextStep;
    $OpportunityCategory = $opportunity_data->OpportunityCategory;
    $heading_text = 'Edit';
    $btn_text = 'Update';
    $opportunity_product = $opportunity_data->products;
    $total_opportunity_products = 1;
    $heading_img = 'edit-crm-icon.png';

}else{
	$opportunity_id = '';
	$OpportunityName = '';
	if($_GET['lead_id']){
        $LeadID = $_GET['lead_id'];
        $OpportunityName = $wpdb->get_var("select business_legal_name FROM $business_table WHERE lead_id=$LeadID");
    }else{
        $LeadID ='';
    }
    $ContactID = '';
    $ExpectedCloseDate = '';
	$OpportunityCurrency = '';
	$OpportunityAmount = '';
    $OpportunityCategory = '';    
	$Probability = '';
	$Description = '';
    $Notes = '';
    $next_step = '';
	$heading_text = 'New';
	$btn_text = 'Submit';
    $opportunity_product = array();
    $total_opportunity_products = 1;
    $heading_img = 'add-crm-icon.png';
}

$heading_img = 'opportunity-icon.png';

if($LeadID != ''){
    /* $OpportunityContacts = $wpdb->get_results("select id,first_name,last_name FROM $contacts_table WHERE report_to_id=$LeadID"); */
	
	$query = $wpdb->prepare("SELECT c.id, c.first_name, c.last_name FROM {$wpdb->prefix}op_contact_lead cl INNER JOIN {$wpdb->prefix}op_contacts c ON cl.contact_id = c.id WHERE cl.lead_id = %d", $LeadID);

	$OpportunityContacts = $wpdb->get_results($query);
	
	
}
else{
    $OpportunityContacts = array();
}

//this function return last day of current quarter
function getLastDateOfCurrentQuarter() {
    $currentMonth = date('n');
    $currentYear = date('Y');
    
    // Determine the quarter
    if ($currentMonth <= 3) {
        $quarter = 1;
    } elseif ($currentMonth <= 6) {
        $quarter = 2;
    } elseif ($currentMonth <= 9) {
        $quarter = 3;
    } else {
        $quarter = 4;
    }
    
    // Calculate the last date of the quarter
    switch ($quarter) {
        case 1:
            $lastDate = date('m/d/Y', strtotime('March 31, '.$currentYear));
            break;
        case 2:
            $lastDate = date('m/d/Y', strtotime('June 30, '.$currentYear));
            break;
        case 3:
            $lastDate = date('m/d/Y', strtotime('September 30, '.$currentYear));
            break;
        case 4:
            $lastDate = date('m/d/Y', strtotime('December 31, '.$currentYear));
            break;
        default:
            $lastDate = "";
            break;
    }
    
    return $lastDate;
}
?>
<div class="loader_box" id="loader_box" style="display: none;">
    <div class="loading">
        <p class="loading__text">Please Wait. Deleting Opportunity.</p>
        <div class="loading__bar"></div>
    </div>
</div>
<div class="main_content_iner ">
    <div class="container-fluid p-0">
         <div class="row justify-content-center">
            <div class="col-lg-12">
                <div id="form_2" class="white_card card_height_100 mb_30" style="display: block;">
                     <div class="white_card_header">
                        <div class="box_header m-0 new_report_header">
                            <div class="title_img">
                                <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/<?php echo $heading_img; ?>" class="page-title-img" alt="">
                            <h4><?php echo $heading_text;?> Opportunity</h4>
                            </div>
                        </div>
                        <?php if($opportunity_id != ''){?>
                        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                            <li class="nav-item active">
                                <a class="nav-link" id="pills-erc-edit-info" data-toggle="pill" href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true">Edit</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="pills-erc-audit-log-info" data-toggle="pill" href="#pills-audit-log" role="tab" aria-controls="pills-audit-log" aria-selected="true">Audit Log</a>
                            </li>
                        </ul>
                        <?php } ?>
                    </div>
                    <div class="white_card_body p-15">
                        <div class="container-fluid">
                            <div class="tab-content" id="pills-tabContent">
                                <div class="tab-pane scroll_height-business scroll-common active" id="pills-home" role="tabpanel" aria-labelledby="pills-erc-edit-info">
                                    <form id="product-form" method="post" enctype="multipart/form-data">
                                        <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="title">Business:*</label>
                                                <!-- <input type="hidden" name="LeadID" id="lead_id" value="" > -->
                                                <select name="LeadID" id="LeadID" class="crm-erp-field form-control <?php echo $searchable_class;  ?>">
                                                    <option value="">Select Business</option>
                                                    <?php foreach ($all_leads as $akey => $avalue) { 
                                                        $sel=''; if($avalue->lead_id==$LeadID){ $sel='selected';}
                                                        ?>
                                                      <option value="<?php echo $avalue->lead_id; ?>" <?php echo $sel; ?>><?php echo $avalue->business_legal_name; ?></option>  
                                                    <?php
                                                    }
                                                    ?>
                                                </select>
                                            </div>

                                            <div class="floating col-md-6">  
                                                <label for="title">Product:*</label>   
                                                <select name="product_id-1" id="product_id" class="crm-erp-field form-control select-product <?php echo $searchable_class;  ?>" data-no="1">
                                                    <option value="">Select Product</option>
                                                    <?php 
                                                    foreach ($all_products as $pro_key => $pro_value) { 
                                                        $productid = $pro_value->productID;     
                                                        $sel='';
                                                        if($o_product_id == $productid){
                                                            $sel='selected';
                                                        }else{
                                                            $sel = '';
                                                        }
                                                       $product_title = $pro_value->title;     
                                                       $product_amount = $pro_value->unitPrice;     
                                                       $product_status = $pro_value->status;     
                                                        echo '<option id="product-id-'.$productid.'" value="'.$productid.'" '.$sel.'>'.$product_title.'</option>';
                                                             }
                                                         ?>         
                                                </select>
                                            </div>

                                        </div>
                                        <!-- Product listing start here -->
                                        <input type="hidden" id="all_product-listing" value="<?php echo $total_opportunity_products;?>" name="all_product-listing">
                                        <input name="opportunity_id" id="opportunity_id" type="hidden"  value="<?php echo $opportunity_id; ?>">
                                        <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="title">Name:*</label>
                                                <input type="text" name="OpportunityName" id="OpportunityName" class="crm-erp-field form-control" value="<?php echo $OpportunityName;?>" maxlength="50">
                                                <input type="hidden" id="hiddenOpportunityName" class="crm-erp-field form-control" value="<?php echo $OpportunityName;?>">
                                                <span id="TitleErr" class="error"></span>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="title">Contact:*</label>
                                                <select name="ContactID" id="ContactID" class="crm-erp-field form-control <?php echo $searchable_class;  ?>">
                                                    <option value="">Select Contact</option>
                                                    <?php
                                                    if(!empty($OpportunityContacts)){
                                                        foreach($OpportunityContacts as $OpportunityContact){
                                                            if($OpportunityContact->id == $ContactID){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = '';
                                                            }
                                                            echo '<option value="'.$OpportunityContact->id.'" '.$sel.'>'.$OpportunityContact->first_name.' '.$OpportunityContact->last_name.'</option>';
                                                        }
                                                    }
                                                    ?>
                                                </select>
                                            </div>
                                            <?php 
                                            if(!empty($opportunity_product)){
                                                $i=1;
                                                foreach ($opportunity_product as $o_key => $o_value) { 
                                                    $opportunity_product_id = $o_value->opportunity_product_id;     
                                                    $o_product_id = $o_value->product_id;     
                                                    $o_milestone_id = $o_value->milestone_id;        
                                                    $o_milestone_status_id = $o_value->milestone_status_id;        
                                                    $o_product_amount = $o_value->product_amount;    
                                                    $all_milestones =  $wpdb->get_results("SELECT $milestones_table.milestone_id,$milestones_table.milestone_name FROM $milestones_table WHERE FIND_IN_SET($o_product_id,$milestones_table.product_id) AND $milestones_table.status = 'active' AND $milestones_table.deleted_at IS NULL");   

                                                    $all_milestone_status =  $wpdb->get_results("SELECT $milestone_status_table.milestone_stage_id,$milestone_status_table.stage_name FROM $milestone_status_table WHERE $milestone_status_table.milestone_id = ".$o_milestone_id." AND $milestone_status_table.status = 'active' AND $milestone_status_table.deleted_at IS NULL");     
                                                ?>                                         
                                            
                                        </div>
                                        <div class="row mb-2">
                                            <div class="floating col-md-6">    
                                                <label for="title">Milestone:*</label>
                                                <select name="milestone-<?php echo $i; ?>" id="milestone" class="crm-erp-field form-control select-milestone <?php echo $searchable_class;  ?>" data-no="<?php echo $i; ?>">
                                                    <option value="0">Select Milestone</option>
                                                <?php 
                                                foreach ($all_milestones as $all_milestone) { 
                                                    $milestone_id = $all_milestone->milestone_id;     
                                                    $sel='';
                                                    if($o_milestone_id == $milestone_id){
                                                        $sel='selected';
                                                    }  else{
                                                        $sel = '';
                                                    }
                                                   $milestone_name = $all_milestone->milestone_name;     
                                                    echo '<option value="'.$milestone_id.'" '.$sel.'>'.$milestone_name.'</option>';
                                                         }
                                                     ?>         
                                                </select>                                    
                                            </div>
                                            <div class="floating col-md-6">  
                                                <label for="title">Stage:*</label>  
                                                <select name="milestone_status-<?php echo $i; ?>" id="milestone_status" class="crm-erp-field form-control select-milestone-status <?php echo $searchable_class;  ?>" data-no="<?php echo $i; ?>">
                                                    <option value="0">Select Stage</option>
                                                <?php 
                                                foreach ($all_milestone_status as $allmilestonestatus) { 
                                                    $milestone_status_id = $allmilestonestatus->milestone_status_id;     
                                                    $sel='';
                                                    if($o_milestone_status_id == $milestone_status_id){
                                                        $sel='selected';
                                                    }  else{
                                                        $sel = '';
                                                    }
                                                   $promilestonestatus = $allmilestonestatus->milestone_status;     
                                                    echo '<option value="'.$milestone_status_id.'" '.$sel.'>'.$promilestonestatus.'</option>';
                                                         }
                                                     ?>         
                                                </select>                                    
                                            </div>
                                            <input type="hidden" id="opportunity_product_id" name="opportunity_product_id-<?php echo $i;?>" value = "<?php echo $opportunity_product_id;?>">
                                        </div>
                                            <?php
                                                $i++;
                                                }
                                            }else{    
                                            ?>    
                                        <!-- <div class="floating col-md-6" style="display:none;">   
                                            <label for="title">Product Name*:</label>  
                                            <select name="product_id-1" id="product_id" class="crm-erp-field form-control select-product <?php echo $searchable_class;  ?>" data-no="1">
                                                <option value="">Select Product</option>
                                                <?php 
                                                $sel = '';
                                                foreach ($all_products as $pro_key => $pro_value) { 
                                                    $productid = $pro_value->productID;     
                                                    $product_title = $pro_value->title;     
                                                    $product_amount = $pro_value->unitPrice;     
                                                    $product_status = $pro_value->status;     
                                                    echo '<option id="product-id-'.$productid.'" value="'.$productid.'" data-amount="'.$product_amount.'" data-status="'.$product_status.'" '.$sel.'>'.$product_title.'</option>';

                                                }
                                                ?>         
                                            </select>
                                        </div> -->
                                            </div>
                                            <div class="row mb-2">
                                                <div class="floating col-md-6">        
                                                    <label for="title">Milestone:*</label>                                        
                                                    <select name="milestone-1" id="milestone" class="crm-erp-field form-control select-milestone <?php echo $searchable_class;  ?>" data-no="1">
                                                        <option value="">Select Milestone</option>      
                                                    </select>   
                                                </div>
                                                <div class="floating col-md-6">  
                                                    <label for="title">Stage:*</label>                                              
                                                    <select name="milestone_status-1" id="milestone_status" class="crm-erp-field form-control select-milestone-status <?php echo $searchable_class;  ?>" data-no="1">
                                                        <option value="">Select Stage</option>      
                                                    </select>   
                                                </div>
                                                <input type="hidden" id="opportunity_product_id" name="opportunity_product_id-1" value = "0">
                                            </div>
                                            <?php } ?>
                                            <div class="row mb-2">
                                                
                                            
                                            <div class="floating col-md-6">     
                                                <label for="title">Currency:*</label>
                                                <select id="OpportunityCurrency" class="crm-erp-field form-control <?php echo $searchable_class;  ?>" disabled>
                                                    <option value="">Select Currency</option>
                                                    <?php foreach($currencies as $currency): 
                                                            if($currency->currency_id == 4){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = "";
                                                            }
                                                    ?>
                                                    <option value="<?php echo $currency->currency_id; ?>" <?php echo $sel;?>><?php echo $currency->currency_code.' ('.$currency->currency_name.')'; ?></option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <input type="hidden" name="OpportunityCurrency" value="4">
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="title">Opportunity Amount:*</label>
                                                <input type="text" step="0.01" name="OpportunityAmount" id="opportunity_amount" class="crm-erp-field form-control" value="<?php echo $OpportunityAmount;?>" min="0" maxlength="15">
                                            </div>

                                            <input style="display:none" id="Probability" name="Probability" type="number" min="0" max="100" oninput="if(this.value > 100) this.value = 100;">
                                        
                                            <!--<div class="floating col-md-6">     
                                                <label for="title">Probability (%):</label>
                                                <select name="Probability" id="Probability" class="crm-erp-field form-control <?php echo $searchable_class;  ?>">
                                                       <option value="">Select Probability</option> 
                                                       <option value="25" selected>25%</option>
                                                       <option value="50" >50%</option>
                                                       <option value="75" >75%</option>
                                                       <option value="99" >99%</option>
                                                </select>
                                            </div>-->
                                        </div>

                                        <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="title">Expected Close Date:*</label>
                                                <input type="text" name="ExpectedCloseDate" id="ExpectedCloseDate" class="crm-erp-field form-control ExpectedCloseDate" value="<?php echo getLastDateOfCurrentQuarter();?>" pattern="\d{2}/\d{2}/\d{4}" placeholder="MM-DD-YYYY" autocomplete="off" onkeydown="return false">
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="title">Next Step:</label>
                                                <input type="text" id="NextStep" name="NextStep" class="crm-erp-field form-control " value=""  autocomplete="off" maxlength="75">
                                                <div id="nextStepDropdown" class="next-step-dropdown"></div>
                                            </div>     
                                        </div>
                                        
                                        <div class="row mb-2">
                                        	<div class="floating col-md-6">     
                                                <label for="description">Description:</label>
                                                <textarea name="Description" id="Description" rows="5" class="form-control" maxlength="2000"><?php echo $Description;?></textarea>
                                            </div>
                                            <?php if($_REQUEST['action'] != 'edit'){ ?>
                                        	<div class="floating col-md-6">     
                                                <label for="notes">Notes:</label>
                                                <textarea name="Notes" id="Notes" rows="5" class="form-control" maxlength="300"><?php echo $Notes;?></textarea>
                                            </div>
                                            <?php } ?>
                                        </div>
                                        <div class="button-container">
                                            <button type="submit" id="product_btn" class="crm_erp_btn"><?php echo $btn_text;?></button>
                                            <?php if($_REQUEST['action'] == 'edit'){ ?>
                                                <button type="button" id="delete_opportunity_btn" class="crm_erp_btn">Delete</button>
                                            <?php } ?>
                                        </div>
                                    </form>
                                    <div id="form-response"></div>
                                </div>
                                <?php if($opportunity_id != ''){?>
                                <div class="tab-pane scroll_height-business scroll-common" id="pills-audit-log" role="tabpanel" aria-labelledby="pills-erc-audit-log-info">
                                    <div class="row">
                                        <table id="audit_log_table">
                                            <thead>
                                                <tr>
                                                    <th>Field Name</th>
                                                    <th>Changed From</th>
                                                    <th>Changed To</th>
                                                    <th>Data Type</th>
                                                    <th>Action</th>
                                                    <th>Changed Time</th>
                                                    <th>Changed By</th>
                                                </tr>
                                            </thead>
                                            <tbody class="audit_data1">
                                                <?php
                                                global $wpdb;
                                                $audit_log_table = $wpdb->prefix . 'audit_logs';
                                                $audit_logs = $wpdb->get_results("SELECT $audit_log_table.* FROM $audit_log_table WHERE $audit_log_table.TableName = '" . $wpdb->prefix . "opportunities'  AND $audit_log_table.FieldID = " . $opportunity_id . " ORDER BY $audit_log_table.LogID DESC");
                                                $c = 0;
                                                $audit_log_history = '';
                                                if (!empty($audit_logs)) {
                                                    $count = 0;
                                                    foreach ($audit_logs as $audit_log) {
                                                        $CreatedBy = get_userdata($audit_log->CreatedBy);
                                                        if(!empty($CreatedBy)){
                                                            $changed_by = $CreatedBy->data->display_name;
                                                        }else{
                                                            $changed_by = '';
                                                        }
                                                        $DateCreated = date('Y-m-d H:i a',strtotime($audit_log->DateCreated));
                                                        $audit_log_history .= "<tr>
                                                              <td>" . $audit_log->FieldName . "</td>
                                                              <td>" . $audit_log->BeforeValueString . "</td>
                                                              <td>" . $audit_log->AfterValueString . "</td>
                                                              <td>" . $audit_log->DataType . "</td>
                                                              <td>" . $audit_log->Action . "</td>
                                                              <td>" . $DateCreated . "</td>
                                                              <td>" . $changed_by . "</td>
                                                            </tr>";
                                                    }
                                                } else {
                                                    $audit_log_history .= "<tr>
                                                      <td></td>
                                                      <td></td>
                                                      <td></td>
                                                      <td>No Data Found</td>
                                                      <td></td>
                                                      <td></td>
                                                      <td></td>
                                                    </tr>";
                                                }
                                                echo $audit_log_history;?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- .site-main -->
</div><!-- .content-area -->

<script type="text/javascript">
    jQuery(document).ready(function($) {
        $('.show_search').select2();
        let opportunitynamemanuallychange = 0;
        jQuery("#audit_log_table").DataTable({"ordering": false});
            jQuery('#product-form').customValidation({
                // , "validName"
            rules: {
                "OpportunityName": ["required"],
                "LeadID": ["required"],
                "product_id-1": ["required"],
                "ContactID": ["required"],
                "milestone-1": ["required"],
                "milestone_status-1": ["required"],
                "ExpectedCloseDate": ["required"],
                "OpportunityCurrency": ["required"],
                "OpportunityAmount": ["required"],
            },
            messages: {
                "OpportunityName": {
                    "required": "Name is required.",
                    "validName": "Please enter a valid Name."
                },
                "LeadID": {
                    "required": "Business is required."
                },
                "product_id-1": {
                    "required": "Product is required."
                },
                "ContactID": {
                    "required": "Contact is required."
                },
                "milestone-1": {
                    "required": "Milestone is required."
                },
                "milestone_status-1": {
                    "required": "Stage is required."
                },
                "ExpectedCloseDate": {
                    "required": "Expected Close Date is required.",
                },
                "OpportunityCurrency": {
                    "required": "Currency is required.",
                },
                "OpportunityAmount": {
                    "required": "Opportunity Amount is required.",
                },
            },
            success: function() {
              jQuery("#product_btn").html('Please wait..');
              jQuery("#product_btn").attr('disabled',true);
              jQuery('#form-response').html("").hide();
              var OpportunityName = jQuery("#OpportunityName").val();
              var opportunity_id = jQuery("#opportunity_id").val();

              var lead_id = jQuery("#LeadID").val();
              var product_id = jQuery("#product_id").val();

              // console.log(OpportunityName);
              jQuery.ajax({
                    url:"<?php echo site_url().'/wp-json/productsplugin/v1/check-opportunity-unique'; ?>",
                    method:'post',
                    data:{
                        OpportunityName:OpportunityName,
                        Opportunity_id:opportunity_id,
                        lead_id:lead_id,
                        product_id:product_id},
                    success(response){
                    if(response.status==400){
                        jQuery('#form-response').html(response.message).css('text-align', 'center').show();
                        jQuery("#product_btn").html('Submit');
                        jQuery("#product_btn").attr('disabled',false);
                        return false;
                    }else{
                
                        jQuery('#form-response').html("").hide();
                        var $responseDiv = jQuery('#form-response');
                        var $form = jQuery("#product-form");
                        var formDataObject = {};
                        var alldata = new URLSearchParams($form.serialize());
                        alldata.forEach(function (value, key) {
                            formDataObject[key] = value;
                        });
                        formDataObject['OpportunityAmount'] = formDataObject['OpportunityAmount'].replaceAll(',', '');
                        var updatedSerializedData = $.param(formDataObject);
                
                        jQuery.ajax({
                            type: 'POST',
                            url: 'admin-ajax.php',
                            data: updatedSerializedData + '&action=opportunity_submit',
                            success: function(response) {
                                jQuery("#product_btn").html('Submit');
                                jQuery("#product_btn").attr('disabled',false);
                                if(response.data.status == 1){
                                    swal({
                                         title: 'Success',
                                         icon: 'success',
                                         type: "success"
                                    });
                                    window.location.href = '?page=opportunities';
                                }else{
                                    $responseDiv.html(response.data.message);
                                }
                            },
                            error: function() {
                                $responseDiv.html('An error occurred');
                            }
                        });
                }
               }
              });
            }
        });

        
          jQuery('.ExpectedCloseDate').datetimepicker({
            format: 'm/d/Y',
            autoclose: true,
            orientation: 'bottom',
            timepicker: false,
            autocomplete: 'off'
        });


        /**
         * Delete Opportunity functionality Start Here
         */ 
        $(document).on('change', '#agreeCheckbox', function() { // Handler for the change event of #agreeCheckbox
            // Check if #agreeCheckbox is checked
            if ($(this).prop('checked')) {
                $(".swal-button--confirm").css("pointer-events", "auto");
            } else {
                $(".swal-button--confirm").css("pointer-events", "none");
            }
        });

        $(document).on('click', '.swal-button-container:has(.swal-button--confirm)', function() {
            if ($('#agreeCheckbox').prop('checked')) {
                return true;
            } else {
                // Check if error message already exists
                if (!$('.swal-content + p.error-message').length) {
                    // Append the error message if it doesn't exist
                    $('.swal-content').after('<p class="error-message" style="color: red; margin: 20px;">Please agree to delete this Opportunity!</p>');
                }
            }
        });

        jQuery(document).on('click','#delete_opportunity_btn',function(){
            swal({
                title: "Are you sure?",
                text: "Yes, I want to delete the Opportunity.",
                icon: "warning",
                buttons: {
                    cancel: "Cancel",
                    confirm: "Delete",
                },
                content: {
                    element: "input",
                    attributes: {
                        type: "checkbox",
                        id: "agreeCheckbox"
                    },
                },
                dangerMode: true,
            })
            .then((willDelete) => {
                if (willDelete && $('#agreeCheckbox').prop('checked')) {
                    $(this).html('wait..');
                    $(this).attr('disabled',true);
                    $("#loader_box").show();
                    var opportunityIdValue = $("#opportunity_id").val();
                    jQuery.ajax({
                        url:'<?php echo admin_url('admin-ajax.php'); ?>',
                        method:'post',
                        data:{action: 'delete_opportunity', opportunity_id:opportunityIdValue},
                        success(response){
                            window.location.href = '?page=opportunities';
                        }
                    });
                }
            }); 
        }); 


    jQuery(document).on('change','.select-product',function(){
        
        var no = jQuery(this).data('no');
        jQuery('select[name=milestone-'+no+']').prop('disabled',false);       
        jQuery('select[name=milestone_status-'+no+']').prop('disabled',false);       

        var val = jQuery(this).val();
        var product_name = $(this).find(":selected").html();
        if(val==0 || val.length==0){
            product_name='';    
        }
        // console.log(product_name);
        
        if(opportunitynamemanuallychange <= 1){
            var opp_name = jQuery('#hiddenOpportunityName').val();
            var new_opp_names = product_name;
            // if(opp_name.length!=0){
            //     var new_opp_names = opp_name +' - '+product_name;
            // }
                var lead_id_val = jQuery("#LeadID").find(":selected").html();    
                var lead_id = jQuery("#LeadID").val();    
                if(lead_id_val.length!=0 && lead_id.length!=0){
                    if(new_opp_names.length!=0){
                        var new_opp_names = lead_id_val + ' - '+new_opp_names;
                    }else{
                        var new_opp_names = lead_id_val;
                    }
                }
                jQuery('#OpportunityName').val(new_opp_names);
        }
        
        var url = "<?php echo site_url().'/wp-json/productsplugin/v1/product-milestones-for-add-opportunity';?>";
        jQuery.ajax({
            type: "POST",
            url: url,
            data: {id:val},
            success: function(response) {
                // console.log(response);
                var milestone_results = response.milestone_results;
                var stage_results = response.stage_results;
                
                jQuery('select[name=milestone-'+no+']').html('');       
                jQuery('select[name=milestone_status-'+no+']').html('');       
                if(val!=934 && val!=935){
                    /*jQuery('select[name=milestone-'+no+']').prop('disabled',true);       
                    jQuery('select[name=milestone_status-'+no+']').prop('disabled',true);       */
                }    
               if(milestone_results.length!=0){ 
                jQuery.each( milestone_results, function (indexes, values){

                    var milestoneIssue = '';
                    if(values.deleted_at!=null){
                        milestoneIssue = 'Deleted';
                    }
                    if(values.status=='inactive'){
                        milestoneIssue = 'Inactive';
                    }
                    opportunityMap = JSON.parse(values.map);
                    console.log(opportunityMap);
                    if(opportunityMap==null){
                        milestoneIssue = 'Unassigned';
                    }else if(!opportunityMap.includes('opportunity')){
                        milestoneIssue = 'Unassigned';
                    }
                    console.log(opportunityMap);
                    milestoneName = '';
                    milestoneID = '';
                    if(milestoneIssue){
                    }else{
                        
                        var selected = '';
                        if((values.milestone_name=='TA Prospecting' || values.milestone_name=='STC Prospecting' || values.milestone_name=='Prospecting') && val!=934 && val!=935){
                            var selected = 'selected';
                        }    

                        var optionHTML = `<option value="${values.milestone_id}" ${selected}> ${values.milestone_name} </option>`;
                        jQuery('select[name=milestone-'+no+']').append(optionHTML);   
                    }

                });
               }else{
                    var optionHTML = `<option value="0">Select Milestone</option>`;
                    jQuery('select[name=milestone-'+no+']').append(optionHTML);       
               } 
               if(stage_results.length!=0){ 
                jQuery.each( stage_results, function (indexes, values){
                    
                    var stageIssue = '';
                    if(values.deleted_at!=null){
                        stageIssue = 'Deleted';
                    }
                    if(values.status=='inactive'){
                        stageIssue = 'Inactive';
                    }
                    stageName = '';
                    stageID = '';
                    if(stageIssue){
                    }else{
                        var selected = '';
                        if(values.stage_name=='Opportunity Identified' && val!=934 && val!=935){
                            var selected = 'selected';
                        }
                        var optionStageHTML = `<option value="${values.milestone_stage_id}" ${selected}> ${values.stage_name} </option>`;
                        jQuery('select[name=milestone_status-'+no+']').append(optionStageHTML);  
                    }
                });
               }else{
                    var optionStageHTML = `<option value="0">Select Milestone Stage</option>`;
                    jQuery('select[name=milestone_status-'+no+']').append(optionStageHTML);       
               }    
            }
        });            
    });      


    jQuery(document).on('change','.select-milestone',function(){
        var no = jQuery(this).data('no');
        var val = jQuery(this).val();
        var product_name = $(this).find(":selected").html();
        console.log(product_name);
        var url = "<?php echo site_url().'/wp-json/productsplugin/v1/milestone-status';?>";
        jQuery.ajax({
            type: "POST",
            url: url,
            data: {id:val},
            success: function(response) {
                jQuery('select[name=milestone_status-'+no+']').html('');       
               if(response.length!=0){ 
                jQuery.each( response, function (indexes, values){
                    var optionHTML = `<option value="${values.milestone_stage_id}"> ${values.stage_name} </option>`;
                    jQuery('select[name=milestone_status-'+no+']').append(optionHTML);       
                });
               }else{
                    var optionStageHTML = `<option value="0">Select Milestone Stage</option>`;
                    jQuery('select[name=milestone_status-'+no+']').append(optionStageHTML);       
               }      
            }
        });            
    });

    jQuery(document).on('change','.select-product, .select-milestone, .select-milestone-status',function(){
        var prod_name = jQuery('select[name=product_id-1] option:selected').text().trim();
        var mile_name = jQuery('select[name=milestone-1] option:selected').text().trim();
        var stage_name = jQuery('select[name=milestone_status-1] option:selected').text().trim();

        // Fetch the probability and select appropriate probability from dropdown
        getProbability(prod_name, mile_name, stage_name);    
    });

    // Function to get probability
    function getProbability(prod_name, mile_name, stage_name) {
        var probability_url = "<?php echo site_url().'/wp-json/productsplugin/v1/get-probability'; ?>";

        jQuery.ajax({
            type: "POST",
            url: probability_url,
            data: {
                product: prod_name,
                milestone: mile_name,
                stage: stage_name
            },
            success: function(response) {
                if (response) {
                    // Set the probability in the probability field
                    jQuery('#Probability').val(response);
                } else {
                    jQuery('#Probability').val('');
                }
            }
        });
    }

    jQuery(document).on('change','#LeadID',function(){
        var product_id = $('.select-product').val();
        var product_name = $('.select-product').find(":selected").html();
        if(product_id==0 || product_id.length==0){
            product_name='';    
        }
        var lead_id = jQuery(this).val();
        if(opportunitynamemanuallychange <= 1){
            var opp_name = jQuery('#hiddenOpportunityName').val();
            var new_opp_names = product_name;
            // if(opp_name.length!=0){
            //     var new_opp_names = opp_name +' - '+product_name;
            // }
                var lead_id_val = jQuery("#LeadID").find(":selected").html();    
                if(lead_id_val.length!=0 && lead_id.length!=0){
                    if(new_opp_names.length!=0){
                        var new_opp_names = lead_id_val + ' - '+new_opp_names;
                    }else{
                        var new_opp_names = lead_id_val;
                    }
                }
                jQuery('#OpportunityName').val(new_opp_names);
        }

        
        var url = "<?php echo site_url().'/wp-json/productsplugin/v1/lead-contacts';?>";
        jQuery.ajax({
            type: "POST",
            url: url,
            data: {id:lead_id},
            success: function(response) {
                jQuery('#ContactID').html('');       
                if(response.length!=0){ 
                    jQuery.each( response, function (indexes, values){
                        var optionHTML = `<option value="${values.id}"> ${values.first_name} ${values.last_name} </option>`;
                        jQuery('#ContactID').append(optionHTML);       
                    });
                }else{
                    var optionStageHTML = `<option value="">No Contacts Found</option>`;
                    jQuery('#ContactID').append(optionStageHTML);       
                }      
            }
        });            
    });        

    jQuery(document).on('blur','#OpportunityName',function(){
        var vals = jQuery(this).val(); 
        jQuery('#hiddenOpportunityName').val(vals);
        opportunitynamemanuallychange = opportunitynamemanuallychange + 1;
    });   

    //convert input international comma saparated
    $('#opportunity_amount').on('input', function() {
        let inputValue = $(this).val();
        inputValue = inputValue.replace(/[^0-9.]/g, '');
        let [integerPart, decimalPart] = inputValue.split('.');
        integerPart = Number(integerPart).toLocaleString('en-US');
        let result = `${integerPart}`;
        if (decimalPart !== undefined) {
            result += `.${decimalPart}`;
        }
        $(this).val(result);
    }); 
});
</script>

<script type="text/javascript">

    $(document).ready(function(){
    var AllNextSteps = <?php echo $allNextStep ?>;

  $("#NextStep").on("keyup", function() {
    var inputVal = $(this).val();
    var dropdown = $("#nextStepDropdown");
    dropdown.empty();
    if (inputVal.length) {
      var matches = AllNextSteps.filter(function(singleNextSteps) {
        return singleNextSteps.toLowerCase().startsWith(inputVal.toLowerCase());
      });
      let counter = 1;
      matches.forEach(function(match) {
        if(counter<6){
            dropdown.append("<a href='javascript:void(0)'>" + match + "</a>");
        }
        counter = counter + 1;
      });
      if(counter>1){
        dropdown.addClass("showNextStep");
      }
    } else {
      dropdown.removeClass("showNextStep");
    }
  });

  $(document).on("click", ".next-step-dropdown a", function() {
    $("#NextStep").val($(this).text());
    $("#nextStepDropdown").removeClass("showNextStep");
  });

  $(document).click(function(e) {
    if (!$(e.target).closest(".autocompleteNextStep").length) {
      $(".next-step-dropdown").removeClass("showNextStep");
    }
  });
});

</script>