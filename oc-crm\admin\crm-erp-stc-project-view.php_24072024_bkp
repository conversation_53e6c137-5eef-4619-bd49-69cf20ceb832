<?php
$current_user_id = get_current_user_id();
$user_data = get_user_by('id', $current_user_id);
$user_roles = $user_data->roles;
if(in_array('iris_affiliate_users', $user_roles) || in_array("iris_employee", $user_roles) || in_array("account_manager", $user_roles)){
    $is_view = 0;
}else{
    $is_view = 1;
}
$additional_table = $wpdb->prefix . 'erc_iris_leads_additional_info';
$business_info_table = $wpdb->prefix . 'erc_business_info';
$product_table = $wpdb->prefix . 'crm_products';
$milestone_table = $wpdb->prefix . 'milestones';
$milestone_status_table = $wpdb->prefix . 'milestone_stages';
$project_id = $_REQUEST['id'];
$project = $wpdb->get_row("SELECT {$wpdb->prefix}projects.*,{$wpdb->prefix}erc_business_info.business_legal_name,{$wpdb->prefix}erc_business_info.authorized_signatory_name,{$wpdb->prefix}erc_business_info.business_phone,{$wpdb->prefix}erc_business_info.business_email,{$wpdb->prefix}erc_business_info.business_title,{$wpdb->prefix}erc_business_info.zip,{$wpdb->prefix}erc_business_info.street_address,{$wpdb->prefix}erc_business_info.state,{$wpdb->prefix}erc_business_info.city,{$wpdb->prefix}crm_products.Title as product_name,{$wpdb->prefix}milestones.milestone_name, {$wpdb->prefix}milestones.status as milestoneActiveStatus, {$wpdb->prefix}milestones.map as milestoneMap,{$wpdb->prefix}milestone_stages.stage_name as milestoneStatus,{$wpdb->prefix}milestone_stages.status as StageActiveStatus,{$wpdb->prefix}milestone_stages.deleted_at as StageDeleteStatus
                                                                    FROM {$wpdb->prefix}projects 
                                                                    JOIN {$wpdb->prefix}erc_business_info 
                                                                    ON {$wpdb->prefix}projects.lead_id = {$wpdb->prefix}erc_business_info.lead_id
                                                                    JOIN {$wpdb->prefix}crm_products 
                                                                    ON {$wpdb->prefix}projects.product_id = {$wpdb->prefix}crm_products.ProductID 
                                                                    LEFT JOIN {$wpdb->prefix}milestones 
                                                                    ON {$wpdb->prefix}projects.milestone_id = {$wpdb->prefix}milestones.milestone_id 
                                                                    LEFT JOIN {$wpdb->prefix}milestone_stages
                                                                    ON {$wpdb->prefix}projects.milestone_stage_id = {$wpdb->prefix}milestone_stages.milestone_stage_id 
                                                                    WHERE {$wpdb->prefix}projects.deleted_at IS NULL 
                                                                    AND {$wpdb->prefix}projects.project_id = ".$project_id." ");
$contact_id = $project->contact_id;
$lead_id = $project->lead_id;
if($contact_id > 0){
    $contactdata = $wpdb->get_row("SELECT first_name,last_name,title FROM {$wpdb->prefix}op_contacts WHERE id = ".$contact_id."");
    $full_name = $contactdata->first_name.' '.$contactdata->last_name;
    $contact_title = $contactdata->title;
}else{
    $full_name = '';
    $contact_title = '';
}
$phonedata = $wpdb->get_row("SELECT {$wpdb->prefix}op_phone.phone 
                                    FROM {$wpdb->prefix}op_phone_contact 
                                    JOIN {$wpdb->prefix}op_phone ON {$wpdb->prefix}op_phone_contact.phone_id = {$wpdb->prefix}op_phone.id
                                    WHERE {$wpdb->prefix}op_phone_contact.contact_id = ".$contact_id."");
if(!empty($phonedata)){
    $phone = $phonedata->phone;
}else{
    $phone = '';
}

$emaildata = $wpdb->get_row("SELECT {$wpdb->prefix}op_emails.email 
                                    FROM {$wpdb->prefix}op_email_contact 
                                    JOIN {$wpdb->prefix}op_emails ON {$wpdb->prefix}op_email_contact.email_id = {$wpdb->prefix}op_emails.id
                                    WHERE {$wpdb->prefix}op_email_contact.contact_id = ".$contact_id."");
if(!empty($emaildata)){
    $email = $emaildata->email;
}else{
    $email = '';
}

/*$addressdata = $wpdb->get_row("SELECT {$wpdb->prefix}op_address.primary_address_postalcode,{$wpdb->prefix}op_address.primary_address_street,{$wpdb->prefix}op_address.primary_address_city,{$wpdb->prefix}op_address.primary_address_state 
                                    FROM {$wpdb->prefix}op_address_contact 
                                    JOIN {$wpdb->prefix}op_address ON {$wpdb->prefix}op_address_contact.address_id = {$wpdb->prefix}op_address.id
                                    WHERE {$wpdb->prefix}op_address_contact.contact_id = ".$contact_id."");
if(!empty($addressdata)){
    $zip = $addressdata->primary_address_postalcode;
    $street_address = $addressdata->primary_address_street;
    $city = $addressdata->primary_address_city;
    $state = $addressdata->primary_address_state;
}else{
    $zip = '';
    $street_address = '';
    $city = '';
    $state = '';
}*/

//Self impacted days
$self_april_2020_days = $wpdb->get_row("SELECT no_of_days FROM {$wpdb->prefix}project_impacted_days WHERE time_of_days = 'self_time_off_days_april_2020' AND lead_id = ".$lead_id."");
if(!empty($self_april_2020_days)){
    $self_april_2020_no_days = $self_april_2020_days->no_of_days;
}else{
    $self_april_2020_no_days = '';
}
$self_jan_2021_days = $wpdb->get_row("SELECT no_of_days FROM {$wpdb->prefix}project_impacted_days WHERE time_of_days = 'self_time_off_days_january_2021' AND lead_id = ".$lead_id."");
if(!empty($self_jan_2021_days)){
    $self_jan_2021_no_days = $self_jan_2021_days->no_of_days;
}else{
    $self_jan_2021_no_days = '';
}
$self_april_2021_days = $wpdb->get_row("SELECT no_of_days FROM {$wpdb->prefix}project_impacted_days WHERE time_of_days = 'self_time_off_days_april_2021' AND lead_id = ".$lead_id."");
if(!empty($self_april_2021_days)){
    $self_april_2021_no_days = $self_april_2021_days->no_of_days;
}else{
    $self_april_2021_no_days = '';
}
$other_april_2020_days = $wpdb->get_row("SELECT no_of_days FROM {$wpdb->prefix}project_impacted_days WHERE time_of_days = 'other_time_off_days_april_2020' AND lead_id = ".$lead_id."");
if(!empty($other_april_2020_days)){
    $other_april_2020_no_days = $other_april_2020_days->no_of_days;
}else{
    $other_april_2020_no_days = '';
}
$other_jan_2021_days = $wpdb->get_row("SELECT no_of_days FROM {$wpdb->prefix}project_impacted_days WHERE time_of_days = 'other_time_off_days_january_2021' AND lead_id = ".$lead_id."");
if(!empty($other_jan_2021_days)){
    $other_jan_2021_no_days = $other_jan_2021_days->no_of_days;
}else{
    $other_jan_2021_no_days = '';
}
$other_april_2021_days = $wpdb->get_row("SELECT no_of_days FROM {$wpdb->prefix}project_impacted_days WHERE time_of_days = 'other_time_off_days_april_2021' AND lead_id = ".$lead_id."");
if(!empty($other_april_2021_days)){
    $other_april_2021_no_days = $other_april_2021_days->no_of_days;
}else{
    $other_april_2021_no_days = '';
}
$where_milestone = ' 1=1 ';
if (isset($project->product_id) && !empty($project->product_id)) {
    $where_milestone .= ' AND FIND_IN_SET(' . $project->product_id . ',' . $milestone_table . '.product_id) ';
}
$all_milestones =  $wpdb->get_results("SELECT $milestone_table.milestone_id,$milestone_table.milestone_name, $milestone_table.map,$milestone_table.status,$milestone_table.deleted_at FROM $milestone_table WHERE $where_milestone  AND $milestone_table.status = 'active' AND $milestone_table.deleted_at IS NULL AND $milestone_table.map LIKE '%\"project\"%'");
$where_stage = ' 1=1 ';
if (isset($project->milestone_id) && !empty($project->milestone_id)) {
    $where_stage .= ' AND ' . $milestone_status_table . '.milestone_id = ' . $project->milestone_id . ' ';
}
$all_milestone_status =  $wpdb->get_results("SELECT $milestone_status_table.milestone_stage_id,$milestone_status_table.stage_name,$milestone_status_table.status,$milestone_status_table.deleted_at FROM $milestone_status_table WHERE $where_stage AND $milestone_status_table.status = 'active' AND $milestone_status_table.deleted_at IS NULL");
$user_details = get_user_by('id', $project->created_by);
$owner_name = $user_details->display_name;

$login_user_details = get_user_by('id', get_current_user_id());
$login_user_name = $login_user_details->display_name;

$all_users = get_users(array('role__in' => array('Master_Sales', 'Iris_Sales_Agent')));
$active_sales_agents = $wpdb->get_results("SELECT userid,full_name FROM {$wpdb->prefix}erc_sales_team WHERE active_sales_agent = 1");
$contacts_table = $wpdb->prefix . 'op_contacts';
if ($project->contact_id > 0) {
    $contact_data = $wpdb->get_row("SELECT first_name,last_name,trash FROM $contacts_table WHERE id = " . $project->contact_id . "");
    if (!empty($contact_data)) {

        if ($contact_data->trash == 1) {
            $primary_contact = $contact_data->first_name . ' ' . $contact_data->last_name . ' (disabled)';
        } else {
            $primary_contact = $contact_data->first_name . ' ' . $contact_data->last_name;
        }
    } else {
        $primary_contact = '';
    }
} else {
    $primary_contact = '';
}

$lead_id = $project->lead_id;
$table_name = $wpdb->prefix . 'op_contacts';
$query = $wpdb->prepare("SELECT $table_name.id,$table_name.first_name,$table_name.last_name, $table_name.trash FROM $table_name WHERE $table_name.report_to_id = $lead_id AND $table_name.active = 1 AND $table_name.trash = 0");
$all_contacts = $wpdb->get_results($query);

$project_id = $project->project_id;
$authorized_signatory_name = $project->authorized_signatory_name;
$business_phone = $project->business_phone;
$business_email = $project->business_email;
$business_title = $project->business_title;
$street_address = $project->street_address;
$city = $project->city;
$state = $project->state;
$zip = $project->zip;
$notes_table = $wpdb->prefix . 'erc_project_notes';
$total_notes = $wpdb->get_results("SELECT id FROM $notes_table WHERE project_id='" . $project_id . "' ORDER BY id DESC ");
$total_notes_count = count($total_notes);
$all_notes = $wpdb->get_results("SELECT * FROM $notes_table WHERE project_id='" . $project_id . "' ORDER BY id DESC LIMIT 10 OFFSET 0");
//collaborators
$collaborators = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}collaborators WHERE project_id = ".$project_id."");
$collaborator_users = array( 
                        'role__in' => array( 'iris_sales_agent', 'iris_sales_agent_rep', 'master_sales', 'echeck_staff', 'echeck_admin', 'master_ops', 'iris_affiliate_users' ),
                        'orderby' => 'display_name',
                        'order' => 'ASC' 
                    );
$listed_users = get_users( $collaborator_users );
$impacted_table_name = $wpdb->prefix . 'leads_impacted_dates';
// Check if the form is submitted
if (isset($_POST['impacted_dates_submit'])) {
    if ($lead_id > 0) {
        $posted_data = $_POST;
        foreach ($posted_data as $key => $value) {
            if (strpos($key, '2020_Q2_Q3_Q4') !== false) {
                $date_field = $key;
                //$table_name = '2020_Q2_Q3_Q4_table_name';
            } elseif (strpos($key, '2021_Q1') !== false) {
                $date_field = $key;
                //$table_name = '2021_Q1_table_name';
            } elseif (strpos($key, '2021_Q2_Q3') !== false) {
                $date_field = $key;
                //$table_name = '2021_Q2_Q3_table_name';
            } else {
                continue;
            }
            $existing_row = $wpdb->get_row(
                $wpdb->prepare("SELECT * FROM $table_name WHERE lead_id = %s AND field_title = %s", $lead_id, $date_field)
            );
            $data = array(
                'type' => 'date_tab',
                'lead_id' => $lead_id,
                'field_title' => $date_field,
                'field_value' => $value
            );
            if ($existing_row) {
                // Update the existing record
                $wpdb->update($impacted_table_name, $data, array('id' => $existing_row->id));
            } else {
                // Insert a new record
                $wpdb->insert($impacted_table_name, $data);
            }
        }
    }
}


//process impacted tab data


$table_name = $wpdb->prefix . 'leads_impacted_days';
$table_name_dates = $wpdb->prefix . 'leads_impacted_dates';
$impacted_days_table = $wpdb->prefix . 'impacted_date_ranges';
function process_data($type, $title, $time_of_days_array, $no_of_days_array, $lead_id, $table_name, $wpdb, $data, $no_of_availed_days_array) {

//    echo "process_data";
//    
//    print_r($time_of_days_array);
    
    foreach ($time_of_days_array as $key => $time_of_days) {
        if (!empty($no_of_days_array[$key])) {
            $no_of_days = $no_of_days_array[$key];
        } else {
            $no_of_days = "";
        }

        if (!empty($no_of_availed_days_array[$key])) {
            $no_of_availed_days = $no_of_availed_days_array[$key];
        } else {
            $no_of_availed_days = "";
        }
        //$no_of_availed_days = $no_of_availed_days_array[$key];
        // $unemp_benefit_claimed='';
        if (isset($_POST[$time_of_days . '_unemployment_claimed'])) {
            $unemp_benefit_claimed = $_POST[$time_of_days . '_unemployment_claimed'];
        }

        // Check and set undertaking
        $undertaking_key = 'undertaking_' . strtolower($time_of_days);
        $undertaking = isset($_POST[$undertaking_key]) ? 1 : 0;


        // Check if the record already exists
        $existing_row = $wpdb->get_row(
                $wpdb->prepare("SELECT * FROM $table_name WHERE type = %s AND time_of_days = %s AND lead_id = %s", $type, $time_of_days, $lead_id)
        );

        // Prepare data for insertion or update
        $data = array(
            'type' => $type,
            'lead_id' => $lead_id,
            'title' => $title[$key],
            'time_of_days' => $time_of_days,
            'no_of_days' => $no_of_days,
            'no_of_availed_days' => $no_of_availed_days,
            'unemp_benefit_claimed' => $unemp_benefit_claimed,
            'updated_datetime' => date('Y-m-d H:i:s'), // Add current timestamp
            'undertaking' => $undertaking
        );
        
//                echo "process_data <pre>";
//                print_r($data);
//                echo '</pre>';
        // Update or insert the record
        if ($existing_row) {
            $wpdb->update($table_name, array('no_of_days' => $no_of_days, 'unemp_benefit_claimed' => $unemp_benefit_claimed, 'no_of_availed_days' => $no_of_availed_days, 'undertaking' => $undertaking), array('id' => $existing_row->id));
        } else {
            $wpdb->insert($table_name, $data);
        }
    }
}


function getDatesInRange($start_date, $end_date) {
    $start = new DateTime($start_date);
    $end = new DateTime($end_date);
    $interval = new DateInterval('P1D');
    $dateRange = new DatePeriod($start, $interval, $end->modify('+1 day'));

    $dates = [];
    foreach ($dateRange as $date) {
        $dates[] = $date->format('m/d/Y');
    }
    return $dates;
}


function get_dates_from_range($start, $end) {
    $start_date = DateTime::createFromFormat('m/d/Y', $start);
    $end_date = DateTime::createFromFormat('m/d/Y', $end);
    
    // Check if the date conversion was successful
    if (!$start_date || !$end_date) {
        return [];
    }

    $dates = [];

    while ($start_date <= $end_date) {
        $dates[] = $start_date->format('m/d/Y');
        $start_date->modify('+1 day');
    }

    return $dates;
}


function process_date_ranges($lead_id, $table_name_dates, $posted_data, $wpdb) {
    $mappings = [
        'selectedDateRangeInput_0_' => '2020_Q2_Q3_Q4_',
        'selectedDateRangeInput_1_' => '2021_Q1_',
        'selectedDateRangeInput_2_' => '2021_Q2_Q3_',
    ];

    foreach ($mappings as $input_prefix => $field_prefix) {
        $date_index = 1;

        // Loop to handle up to 10 date range inputs
        for ($i = 0; $i < 10; $i++) {
            $input_key = $input_prefix . $i;

            // Check if the input key exists, if not, set value to empty
            $value = isset($posted_data[$input_key]) ? $posted_data[$input_key] : '';

            // Split the value into start and end dates if not empty
            if (!empty($value)) {
                list($start_date, $end_date) = explode(' - ', $value);
                $dates = get_dates_from_range($start_date, $end_date);
            } else {
                $dates = []; // Handle empty input
            }

            // If dates are empty, still need to handle updating blank values
            if (empty($dates)) {
                $field_key = $field_prefix . $date_index;
                $un_benefit = isset($posted_data[$input_key . '_un_benefit']) ? $posted_data[$input_key . '_un_benefit'] : '';

                $data = array(
                    'type' => 'date_tab',
                    'lead_id' => $lead_id,
                    'field_title' => $field_key,
                    'field_value' => '', // Insert/Update with blank value
                    'unemp_benefit_claimed' => $un_benefit,
                    'updated_datetime' => date('Y-m-d H:i:s') // Add current timestamp
                );
                
//                echo 'process date range <pre>';
//                print_r($data);
//                echo '</pre>';
                // Check if an existing record exists
                $existing_row = $wpdb->get_row(
                    $wpdb->prepare("SELECT * FROM $table_name_dates WHERE lead_id = %s AND field_title = %s", $lead_id, $field_key)
                );

                if ($existing_row) {
                    // Update the existing record
                    $wpdb->update($table_name_dates, $data, array('id' => $existing_row->id));
                } else {
                    // Insert a new record
                    $wpdb->insert($table_name_dates, $data);
                }

                $date_index++;
            } else {
                // Process non-empty dates
                foreach ($dates as $date) {
                    $field_key = $field_prefix . $date_index;
                    $un_benefit = isset($posted_data[$input_key . '_un_benefit']) ? $posted_data[$input_key . '_un_benefit'] : '';

                    $data = array(
                        'type' => 'date_tab',
                        'lead_id' => $lead_id,
                        'field_title' => $field_key,
                        'field_value' => $date,
                        'unemp_benefit_claimed' => $un_benefit,
                        'updated_datetime' => date('Y-m-d H:i:s') // Add current timestamp
                    );

                    // Check if an existing record exists
                    $existing_row = $wpdb->get_row(
                        $wpdb->prepare("SELECT * FROM $table_name_dates WHERE lead_id = %s AND field_title = %s", $lead_id, $field_key)
                    );

                    if ($existing_row) {
                        // Update the existing record
                        $wpdb->update($table_name_dates, $data, array('id' => $existing_row->id));
                    } else {
                        // Insert a new record
                        $wpdb->insert($table_name_dates, $data);
                    }

                    $date_index++;
                }
            }
        }
    }
}


if (isset($_POST['selfdocumentsubmit_impacted'])) {
    if ($lead_id > 0) {

// Process 'self' data
//        echo "process for self";
//        die();
        process_data('self', $_POST['self_title'], $_POST['self_time_of_days'], $_POST['self_no_of_days'], $lead_id, $table_name, $wpdb, $_POST, $_POST['self_no_of_availed_days']);

// Process 'others' data
        //echo "process for others";
        process_data('others', $_POST['other_title'], $_POST['other_time_of_days'], $_POST['other_no_of_days'], $lead_id, $table_name, $wpdb, $_POST, $_POST['other_no_of_availed_days']);

//save dates data

        $posted_data = $_POST;

//        echo '<pre>';
//        print_r($posted_data);
//        echo '</pre>';
//        die();
        $un_benefit = '';

// Loop through keys from '2020_Q2_Q3_Q4_2' to '2020_Q2_Q3_Q4_10'
        for ($i = 1; $i <= 10; $i++) {
            $key = "2020_Q2_Q3_Q4_$i";
            if (!isset($posted_data[$key])) {
                $posted_data[$key] = '';
            }
        }


        for ($i = 1; $i <= 10; $i++) {
            $key = "2021_Q1_$i";
            if (!isset($posted_data[$key])) {
                $posted_data[$key] = '';
            }
        }



        for ($i = 1; $i <= 10; $i++) {
            $key = "2021_Q2_Q3_$i";
            if (!isset($posted_data[$key])) {
                $posted_data[$key] = '';
            }
        }

        // Save date ranges data
        process_date_ranges($lead_id, $table_name_dates, $posted_data, $wpdb);

        
    
        // Delete existing records for the specific lead_id
        $wpdb->delete("{$wpdb->prefix}impacted_date_ranges", array('lead_id' => $lead_id));

        // Loop through the POST data
        foreach ($posted_data as $key => $value) {
            if (strpos($key, 'selectedDateRangeInput') === 0 && !empty($value)) {
                // Extract doc_key and date_range_index from the key
                preg_match('/selectedDateRangeInput_(\d+)_(\d+)/', $key, $matches);
                $doc_key = $matches[1];
                $date_range_index = $matches[2];

                // Split the start and end dates
                list($start_date, $end_date) = explode(' - ', $value);

                // Check if a record already exists
                $existing_entry = $wpdb->get_row(
                    $wpdb->prepare(
                        "SELECT * FROM eccom_impacted_date_ranges WHERE lead_id = %d AND doc_key = %s AND date_range_index = %d",
                        $lead_id, $doc_key, $date_range_index
                    )
                );

                // Insert or update based on the existence of the entry
                if ($existing_entry) {
                    // Update the existing record
                    //echo "Update a new record";
                    $wpdb->update(
                        'eccom_impacted_date_ranges',
                        array(
                            'start_date' => $start_date,
                            'end_date' => $end_date,
                            'updated_date' => current_time('mysql')
                        ),
                        array(
                            'lead_id' => $lead_id,
                            'doc_key' => $doc_key,
                            'date_range_index' => $date_range_index
                        )
                    );
                    
                    
                   $updatearraydate = array(
                           'lead_id' => $lead_id,
                           'doc_key' => $doc_key,
                           'date_range_index' => $date_range_index,
                           'start_date' => $start_date,
                           'end_date' => $end_date,
                           'created_date' => current_time('mysql'),
                           'updated_date' => current_time('mysql')
                       );
//                   echo "updated <pre>";
//                       print_r($updatearraydate);
//                       echo "</pre>";
                        
                        
                } else {
                    // Insert a new record
                    //echo "Insert a new record";
                    $wpdb->insert(
                        'eccom_impacted_date_ranges',
                        array(
                            'lead_id' => $lead_id,
                            'doc_key' => $doc_key,
                            'date_range_index' => $date_range_index,
                            'start_date' => $start_date,
                            'end_date' => $end_date,
                            'created_date' => current_time('mysql'),
                            'updated_date' => current_time('mysql')
                        )
                    );
                    
                    
                   $insertarraydate = array(
                           'lead_id' => $lead_id,
                           'doc_key' => $doc_key,
                           'date_range_index' => $date_range_index,
                           'start_date' => $start_date,
                           'end_date' => $end_date,
                           'created_date' => current_time('mysql'),
                           'updated_date' => current_time('mysql')
                       );
//                       echo "inserted <pre>";
//                       print_r($insertarraydate);
//                       echo "</pre>";
                }
            }
        }
        //end loop for daterange
        
        echo "<script>
         jQuery(document).ready(function($) {
            // Remove 'active' class from the 'Project' tab
            $('#eleve-documents-tab').click();
//            $('#eleve-project-tab').removeClass('active');
//            $('#eleve-project-tab').parent().removeClass('active');
            $('#pills-tab a[href=\"#eleve-documents\"]').tab('show');
        });
        </script>";

    }
}

// Fetch the data from the database
$date_results = $wpdb->get_results("SELECT * FROM $impacted_table_name WHERE lead_id = $lead_id");

// Store the date values in an array for easier access
$date_values = array();
foreach ($date_results as $row) {
    $date_values[$row->field_title] = $row->field_value;

    if (strpos($row->field_title, '_un_benefit') == false) {
        $date_values[$row->field_title . '_checkbox'] = $row->unemp_benefit_claimed;
    }
}


$_2020_Q2_Q3_Q4_count = $wpdb->get_var($wpdb->prepare(
                                                    "SELECT COUNT(*) FROM $impacted_table_name 
WHERE lead_id = %s 
AND field_title REGEXP '^[0-9]{4}_Q2_Q3_Q4_[0-9]{1,2}$'
AND field_value <> ''",
                                                    $lead_id
                                    ));

                                    $_2021_Q1_count = $wpdb->get_var($wpdb->prepare(
                                                    "SELECT COUNT(*) FROM $impacted_table_name 
WHERE lead_id = %s 
AND field_title REGEXP '^2021_Q1_(10|[1-9])$'
AND field_value <> ''",
                                                    $lead_id
                                    ));

                                    $_2021_Q2_Q3_count = $wpdb->get_var($wpdb->prepare(
                                                    "SELECT COUNT(*) FROM $impacted_table_name 
WHERE lead_id = %s 
AND field_title REGEXP '^2021_Q2_Q3_(10|[1-9])$'
AND field_value <> ''",
                                                    $lead_id
                                    ));
                                    
                                    
                                    
                                    
$parent_folder = 'Impacted Days';
$form_id = 5;
$doc_mapping_table = $wpdb->prefix . 'leads_document_mapping';
$doc_uplod_table = $wpdb->prefix . 'leads_document_upload';
$doc_uplod_status = $wpdb->prefix . 'leads_document_status';
$doc_uplod_notes = $wpdb->prefix . 'leads_document_notes';
$leads_impacted_days = $wpdb->prefix . 'leads_impacted_days';
$company_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `form_id` = %d", $form_id));
//sales, ops, 
//lead mapping data
$document_folder = '';
$agreement_folder = '';
$lead_mapping_data = $wpdb->get_row("SELECT document_folder,agreement_folder FROM {$wpdb->prefix}lead_folder_mapping WHERE lead_id = ".$lead_id."");
if(!empty($lead_mapping_data)){
    $document_folder = $lead_mapping_data->document_folder;
    $agreement_folder = $lead_mapping_data->agreement_folder;
}
$current_user_id = get_current_user_id();
$user_data = get_user_by('id', $current_user_id);
$user_roles = $user_data->roles;
if(in_array("iris_affiliate_users", $user_roles) || in_array("iris_employee", $user_roles) || in_array("account_manager", $user_roles)){
    $readonly = "readonly";
    $disabled = "disabled";
    $is_affiliate = 1;
}else{
    $readonly = "";
    $disabled = "";
    $is_affiliate = 0;
}



function convertDatesToRangesprojects($date_values) {
    // Split the string into an array of dates
    $dates_array = explode(',', $date_values);

    // Convert date strings to DateTime objects and ensure valid conversion
    $date_objects = array_map(function($date) {
        return DateTime::createFromFormat('m/d/Y', trim($date));
    }, $dates_array);

    // Filter out any invalid DateTime objects
    $date_objects = array_filter($date_objects, function($date) {
        return $date !== false;
    });

    // Sort the dates
    usort($date_objects, function($a, $b) {
        return $a <=> $b;
    });

    $ranges = [];
    if (count($date_objects) > 0) {
        $range_start = $date_objects[0];
        $range_end = $date_objects[0];

        for ($i = 1; $i < count($date_objects); $i++) {
            $diff = $date_objects[$i]->diff($range_end)->days;

            if ($diff > 1) {
                // Add the current range to the ranges array
                $ranges[] = $range_start->format('m/d/Y') . ' - ' . $range_end->format('m/d/Y');
                // Start a new range
                $range_start = $date_objects[$i];
            }

            $range_end = $date_objects[$i];
        }

        // Add the last range
        $ranges[] = $range_start->format('m/d/Y') . ' - ' . $range_end->format('m/d/Y');
    }

    return $ranges;
}


if (!empty($lead_id) && $lead_id != 'na' ) {


    $impacted_days_results = $wpdb->get_results("SELECT * FROM $impacted_days_table WHERE lead_id = $lead_id");

    // echo "hello";
// die();
// Group data by doc_key
    $impacted_days_grouped_data = [];
    foreach ($impacted_days_results as $item) {
        $impacted_days_grouped_data[$item->doc_key][] = $item;
    }

    // Check if grouped_data is empty and provide default values if necessary
    if (empty($impacted_days_grouped_data)) {
        $impacted_days_grouped_data = [
            0 => [
                (object) [
                    'doc_key' => 0,
                    'date_range_index' => 0,
                    'start_date' => '',
                    'end_date' => '',
                ]
            ],
            1 => [
                (object) [
                    'doc_key' => 1,
                    'date_range_index' => 0,
                    'start_date' => '',
                    'end_date' => '',
                ]
            ],
            2 => [
                (object) [
                    'doc_key' => 2,
                    'date_range_index' => 0,
                    'start_date' => '',
                    'end_date' => '',
                ]
            ]
        ];
    }
}


$table_name = $wpdb->prefix . 'leads_stc_documents_additional_info';

// Check if the form is submitted
if (isset($_POST['selfdocumentsubmit'])) {
    if ($lead_id > 0) {
        $requestArr = $_POST;
        unset($requestArr['selfdocumentsubmit']);
        unset($requestArr['taxnow_signup_stc_section_required']);

        foreach ($requestArr as $key => $isapplicable) {
            if (!empty($isapplicable)) {
                // Check if the record already exists
                $existing_row = $wpdb->get_row(
                    $wpdb->prepare("SELECT * FROM $table_name WHERE  doc_key = %s AND lead_id = %s", $key, $lead_id)
                );

                // Update or insert the record
                if ($existing_row) {
                    $wpdb->update($table_name, array('is_applicable' => $isapplicable), array('id' => $existing_row->id));
                } else {
                    // Prepare data for insertion or update
                    $data = array(
                        'type' => 'is_appicable',
                        'lead_id' => $lead_id,
                        'doc_key' => $key,
                        'is_applicable' => $isapplicable,
                        'created_at' => time(),
                    );
                    $wpdb->insert($table_name, $data);
                }
            }


        }
    }
}
?>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-daterangepicker/3.0.5/daterangepicker.css">

<style>

    .input-group-append-second, .input-group-append-third{
            display: flex;
    }
    
    #addMoreDateBtn{
        display: inline-flex;
        height: 44px;
        padding: 14px 15px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        flex-shrink: 0;
        border-radius: 5px;
        background: var(--ff-6-a-00, #FF6A00);
        color: var(--ffffff, #FFF);
        text-align: center;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        border: 0;
    }
    .addmore-checkbox {
        align-items: center;
        justify-content: space-between;
        margin-top: 40px;
    }
    .checkbox-type input {
        width: 25px;
        height: 25px;
        margin-right: 0px;
        border-radius: 5px !important;
        border: 1px solid var(--afafaf, #AFAFAF);
    }
    .checkbox-type label {
        color: var(--1-a-1-a-1-a, #1A1A1A);
        font-size: 14px !important;
        font-style: normal;
        font-weight: 400 !important;
        line-height: normal;
        margin-top: 5px;
        margin-left: 10px;
    }
    .checkbox-type {
        cursor: pointer;
    }
    .checkbox-type input[type=checkbox]:checked::before {
        content: none;
    }
    /* .date-range-container{
        margin: 30px 0px 30px 0px;
        padding: 0px;
    } */
    .date-range-container {
        position: relative;
        margin-bottom: 15px;
    }
    .calenday-icon {
        position: absolute;
        right: 30px;
        top: 13px;
        color: #333;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }
    .date-title {
        margin-bottom: 15px;
        color: #333;
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        margin-top: 19px;
    }
    .date-range-input{
        height: 44px;
        padding: 12px 20px !important;
        color: #1A1A1A !important;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        width: 100%;
    }
    .remove-date {
        display: flex;
        justify-content: end;
        align-items: center;
        color: var(--777777, #777);
        font-family: Arial;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        margin-top: 10px;
        margin-bottom: 0;
        margin-right: 15px;
    }
    .remove-date i {
        padding-left: 10px;
        color: var(--777777, #777);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        cursor: pointer;
    }
    .date-range-container {
        position: relative;
        margin-bottom: 15px;
    }
    .calenday-icon {
        position: absolute;
        right: 30px;
        top: 13px;
        color: #AFAFAF;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }
    .date-title {
        margin-bottom: 15px;
        color: #828383;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        margin-top: 19px;
    }
    .date-range-input{
        height: 44px;
        padding: 12px 20px !important;
        color: #1A1A1A !important;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        width: 100%;
    }
    .remove-date {
        display: flex;
        justify-content: end;
        align-items: center;
        color: var(--777777, #777);
        font-family: Arial;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        margin-top: 10px;
        margin-bottom: 0;
        margin-right: 15px;
    }
    .remove-date i {
        padding-left: 10px;
        color: var(--777777, #777);
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        cursor: pointer;
    }

</style>
<div class="main_content_iner">
    <div class="container-fluid p-0">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <div class="white_card card_height_100 mb_30">
                    <div class="white_card_header">
                        <div class="box_header m-0 new_report_header">
                            <div class="title_img">
                                <img src="<?php echo get_site_url(); ?>/wp-content/plugins/oc-crm/assets/img/opportunity-icon.png" class="page-title-img" alt="">
                                <h4>Manage STC Project</h4>
                            </div>
                        </div>
                        <div class="d-flex">
                            <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                <li class="nav-item active">
                                    <a class="nav-link" id="eleve-project-tab" data-toggle="tab" href="#eleve-project" role="tab" aria-controls="first" aria-selected="true">Project</a>
                                </li>
                                <!-- <li class="nav-item">
                                    <a class="nav-link" id="eleve-impacted-days-tab" data-toggle="tab" href="#eleve-impacted-days" role="tab" aria-controls="second" aria-selected="false">STC Intake</a>
                                </li> -->
                                <?php //if(!in_array('iris_affiliate_users', $user_roles) && !in_array('iris_employee', $user_roles)){?>
                                <li class="nav-item">
                                    <a class="nav-link" id="eleve-documents-tab" data-toggle="tab" href="#eleve-documents" role="tab" aria-controls="second" aria-selected="true">Documents</a>
                                </li>
                                <?php //} ?>
                            </ul>
                        </div>
                    </div>
                    <div class="white_card_body p-15">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="show_message_popup"></div>
                                <div class="tab-content" id="pills-tabContent">
                                    <div class="tab-pane active erc-project-scroll" id="eleve-project" role="tabpanel" aria-labelledby="eleve-project-tab">
                                        <div class="erc-project-view">
                                            <fieldset>
                                                <legend>Project Details</legend>
                                                <div class="row mb-3 align-items-center">
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Name</label>
                                                        <input type="text" id="project_name" name="project_name" class="crm-erp-field form-control" value="<?php echo $project->project_name;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Business</label>
                                                        <a href="<?php echo get_site_url();  ?>/wp-admin/admin.php?page=iris-fields-v1.php&lead_id=<?php echo $lead_id;  ?>" target="_blank" class="btn btn-primary view_business">View</a>
                                                        <input type="text" class="crm-erp-field form-control" value="<?php echo $project->business_legal_name;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Products</label>
                                                        <input type="text" class="crm-erp-field form-control" value="<?php echo $project->product_name;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Fee</label>
                                                        <input type="text" id="project_fee" class="crm-erp-field form-control" value="<?php echo $project->project_fee;?>" <?php echo $readonly;?>>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Maximum Credit</label>
                                                        <input type="text" id="maximum_credit" class="crm-erp-field form-control" value="<?php echo $project->maximum_credit;?>" <?php echo $readonly;?>>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Estimated Fee</label>
                                                        <input type="text" id="estimated_fee" class="crm-erp-field form-control" value="<?php echo $project->estimated_fee;?>" <?php echo $readonly;?>>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>

                                        <div class="erc-project-view">
                                            <fieldset>
                                                <legend>Account Info</legend>
                                                <div class="row mb-3">
                                                    <div class="col-sm-12 erc-project-view-title">
                                                        <h2>Contact Info Section</h2>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Full Name</label>
                                                        <input type="text" class="crm-erp-field form-control" name="full_name" value="<?php echo $authorized_signatory_name;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Contact No.</label>
                                                        <input type="text" class="crm-erp-field form-control" name="contact_no" value="<?php echo $business_phone;?>" readonly>
                                                    </div>
                                                     <div class="floating col-sm-4 mb-3">
                                                        <label>Email</label>
                                                        <input type="text" class="crm-erp-field form-control" name="email"  value="<?php echo $business_email;?>" readonly>
                                                    </div>
                                                     <div class="floating col-sm-4 mb-3">
                                                        <label>Title</label>
                                                        <input type="text" class="crm-erp-field form-control" name="title" value="<?php echo $business_title;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Zip</label>
                                                        <input type="text" class="crm-erp-field form-control" name="zip" value="<?php echo $zip;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Street Address</label>
                                                        <input type="text" class="crm-erp-field form-control" name="street_address" value="<?php echo $street_address;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>City</label>
                                                        <input type="text" class="crm-erp-field form-control" name="city" value="<?php echo $city;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>State</label>
                                                        <input type="text" class="crm-erp-field form-control" name="state" value="<?php echo $state;?>" readonly>
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                        <?php if($is_affiliate == 0){?>
                                        <div class="erc-project-view">
                                                <fieldset>
                                                    <legend>Folder Info</legend>
                                                    <div class="row mb-3">
                                                        <div class="col-sm-12 erc-project-view-title">
                                                            <h2>Folder Info Section</h2>
                                                        </div>
                                                        <?php if($agreement_folder != ''){?>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Agreement & invoice folder</label><br/>
                                                            <a href="https://occams-my.sharepoint.com/personal/admin_occamsparadigm_com/_layouts/15/onedrive.aspx?id=%2Fpersonal%2Fadmin%5Foccamsparadigm%5Fcom%2FDocuments%2FOccams%2FUSA%2FBusiness%20Segments%2FFinancial%20%26%20Tax%20Advisory%20%28FTA%29%2FTax%20Credits%2FERC%2FERC%20Client%27s%2FAutoX/<?php echo $agreement_folder;?>">
                                                                Agreement & invoice folder
                                                            </a>
                                                        </div>
                                                        <?php } ?>
                                                        <?php if($document_folder != ''){?>
                                                            <div class="floating col-sm-4 mb-3">
                                                                <label>Documents folder</label><br/>
                                                                <a href="https://occams-my.sharepoint.com/personal/admin_occamsparadigm_com/_layouts/15/onedrive.aspx?id=%2Fpersonal%2Fadmin%5Foccamsparadigm%5Fcom%2FDocuments%2FOccams%2FUSA%2FBusiness%20Segments%2FFinancial%20%26%20Tax%20Advisory%20%28FTA%29%2FTax%20Credits%2FERC%2FERC%20Client%27s%2FAutoX/<?php echo $document_folder;?>">
                                                                    Documents folder
                                                                </a>
                                                            </div>
                                                        <?php } ?>
                                                    </div>
                                                </fieldset>
                                            </div>
                                        
                                        <div class="">
                                            <div class="button-container button_container" style="">
                                                <button type="submit" class="create_project_btn update_project" data-projectid="<?php echo $project_id;?>">Update</button>
                                            </div>
                                        </div>
                                        <?php } ?>
                                    </div>

                                    <div class="tab-pane erc-project-scroll" id="eleve-impacted-days" role="tabpanel" aria-labelledby="eleve-impacted-days-tab">
                                        <div class="erc-project-view">
                                            <fieldset>
                                                <legend>Self</legend>
                                                <div class="row">
                                                    <div class="floating col-sm-6 mb-3">
                                                        <label>Time Off Days (04/01/2020 - 12/31/2020)</label>
                                                        <input type="text" class="crm-erp-field form-control" id="self_time_off_days_april_2020" name="self_time_off_days_april_2020" value="<?php echo $self_april_2020_no_days;?>" name="self_time_off_days_april_2020">
                                                    </div>
                                                    <div class="floating col-sm-6 mb-3">
                                                        <label>Time Off Days (01/01/2021 - 03/31/2021)</label>
                                                        <input type="text" class="crm-erp-field form-control" id="self_time_off_days_january_2021" name="self_time_off_days_january_2021" value="<?php echo $self_jan_2021_no_days;?>" name="self_time_off_days_january_2021">
                                                    </div>
                                                    <div class="floating col-sm-6 mb-3">
                                                        <label>Time Off Days (04/01/2021 - 09/31/2021)</label>
                                                        <input type="text" class="crm-erp-field form-control" id="self_time_off_days_april_2021" name="self_time_off_days_april_2021" value="<?php echo $self_april_2021_no_days;?>">
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>

                                        <div class="erc-project-view">
                                            <fieldset>
                                                <legend>Others</legend>
                                                <div class="row">
                                                    <div class="floating col-sm-6 mb-3">
                                                        <label>Time Off Days (04/01/2020 - 12/31/2020)</label>
                                                        <input type="text" class="crm-erp-field form-control" id="other_time_off_days_april_2020" name="other_time_off_days_april_2020" value="<?php echo $other_april_2020_no_days;?>">
                                                    </div>
                                                    <div class="floating col-sm-6 mb-3">
                                                        <label>Time Off Days (01/01/2021 - 03/31/2021)</label>
                                                        <input type="text" class="crm-erp-field form-control" id="other_time_off_days_january_2021" name="other_time_off_days_january_2021" value="<?php echo $other_jan_2021_no_days;?>">
                                                    </div>
                                                    <div class="floating col-sm-6 mb-3">
                                                        <label>Time Off Days (04/01/2021 - 09/31/2021)</label>
                                                        <input type="text" class="crm-erp-field form-control" id="other_time_off_days_april_2021" name="other_time_off_days_april_2021" value="<?php echo $other_april_2021_no_days;?>">
                                                    </div>
                                                </div>
                                            </fieldset>
                                        </div>
                                        <div class="">
                                            <div class="button-container button_container" style="">
                                                <button type="submit" class="create_project_btn update_project" data-projectid="<?php echo $project_id;?>">Update</button>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="tab-pane" id="eleve-documents" role="tabpanel" aria-labelledby="eleve-documents-tab">
                                        <ul class="nav nav-pills stc_document_tabs" id="pills-tab" role="tablist">
                                            <li class="nav-item active">
                                                <a class="nav-link" id="eleve-required-tab" data-toggle="tab" href="#eleve-required" role="tab" aria-controls="first" aria-selected="true">Required Documents</a>
                                            </li>
                                            <li class="nav-item">
                                                <a class="nav-link" id="eleve-doc-impacted-days-tab" data-toggle="tab" href="#eleve-doc-impacted-days" role="tab" aria-controls="second" aria-selected="false">Impacted Days</a>
                                            </li>
                                            <!-- <li class="nav-item">
                                                <a class="nav-link" id="eleve-doc-impacted-dates-tab" data-toggle="tab" href="#eleve-doc-impacted-dates" role="tab" aria-controls="second" aria-selected="false">Impacted Dates</a>
                                            </li> -->
                                        </ul>
                                        <div class="tab-content">
                                            <div class="tab-pane active erc-project-scroll" id="eleve-required" role="tabpanel" aria-labelledby="eleve-required-tab">
                                                <div class="white_card_body document-impact">
                                                    <?php
                                                    $parent_folder = 'Required Documents';
                                                    $form_id = 6;
                                                    $doc_mapping_table = $wpdb->prefix . 'leads_document_mapping';
                                                    $doc_uplod_table = $wpdb->prefix . 'leads_document_upload';
                                                    $doc_uplod_status = $wpdb->prefix . 'leads_document_status';
                                                    $doc_uplod_notes = $wpdb->prefix . 'leads_document_notes';
                                                    $company_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `form_id` = %d", $form_id));
                                                    ?>
                                                    <!-- For OPS user table Start -->
                                                    <div class="mt-3 mb-3">
                                                        <?php
                                                        if (isset($_POST['selfdocumentsubmit'])) {
                                                            $result = $wpdb->get_row($wpdb->prepare("SELECT count(*) as mandatory_docs FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` IN (39,40,41) ORDER BY `id` DESC limit 1;"));
                                                            if ($lead_id > 0) {
                                                            if($result->mandatory_docs == 3){
                                                                ?>
                                                                <script>
                                                                    swal("Success", "Files Uploaded Successfully.", "success").then(function () {
                                                                        //window.location.href = window.location.origin + "/portal/wp-admin/admin.php?page=impacted_doc";
                                                                    });
                                                                </script>
                                                            <?php
                                                            }else{
                                                            ?>
                                                                <script>
                                                                    swal("Success", "Files Uploaded Successfully.", "success").then(function () {
                                                                    });
                                                                </script>
                                                            <?php
                                                            }
                                                            ?>
                                                            <?php } else { ?>
                                                                <script>
                                                                    swal("Error", "Data not stored. Please select lead first.", "error").then(function () {
                                                                    });
                                                                </script>
                                                                <?php
                                                            }
                                                        }
                                                        ?>
                                                    </div>
                                                    <div class="alert alert-warning" role="alert">
                                                        <b>Note:</b> Please finalize your uploads by clicking on the "Submit" option.
                                                    </div>

                                                    <form action="" method="post">
                                                    <h5 class="mt-3 mb-3 title_text"><b>Tax Returns - 1040</b></h5>
                                                    <div class="row">
                                                        <div class="col-md-12 table-responsive">
                                                            <table class="table com_doc_display payroll_sec">
                                                                <thead>
                                                                <tr>
                                                                    <th style="text-align: left;">Years</th>
                                                                    <th>Mandatory</th>
                                                                    <th>Tax Returns - 1040(Mandatory)</th>
                                                                    <th>Status</th>
                                                                    <th>Comments</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <?php
                                                                $m = 1;
                                                                $n=1;
        foreach ($company_docs as $key => $value) {
            $doc_type_id = $value->doc_type_id;
            $doc_key = $value->doc_key;
            $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
            if ($m >= 1 && $m <= 3) {
                ?>
                <tr>
            <?php
            $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
            $file_url = $result[0]->uploded_documents;
            $file_name = $result[0]->local_path_filename;
            $doc_id = $result[0]->id;
            $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
            ?>
            <td class="first_column">
                <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
            </td>
            <td>Yes</td>
            <?php if ($file_url != '') {
                $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                $file_status = $status_data[0]->doc_status;
                ?>
                <td style="text-align: center;">
                    <label class="file_remove_label">
                    <span class="material-symbols-outlined">download_done</span>
                    <a class="text_overflow_ellipse" <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                class="custom_file"><?php echo $file_name; ?>
                    </a> &nbsp;<span
                            class="material-symbols-outlined cancel_icon"
                            data-doc_id="<?php echo $doc_id; ?>"
                            data-folder_name="<?php echo $parent_folder; ?>"
                            data-lead_id="<?php echo $lead_id; ?>"
                            data-doc_key="<?php echo $doc_key; ?>"
                            data-file_name="<?php echo $file_url; ?>"
                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                    </label>
                </td>
                <td>
                    <select name="change_document_status"
                            id="change_stc_document_status" data-index="<?php echo $n;?>"
                            class="change_doc change_doc_<?php echo $n;?>" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                        <option value="">- Select Status -</option>
                        <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>">
                            In
                            review
                        </option>
                        <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                        <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                    </select>
                </td>
                <td style="text-align: center;">
                    <?php if (!empty($comment_data)) { ?>
                        <?php if($is_affiliate == 0){?>
                            <span class="add_stc_comment_btn"
                                  data-doc_id="<?php echo $doc_id; ?>"
                                  data-doc_type_id="<?php echo $doc_type_id; ?>"
                                  title="Add">
                            <i class="fa-solid fa-comment-medical"></i>
                        </span>
                        <?php } ?>
                        <span class="view_stc_comment"
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                            <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php } ?>
                </td>
            <?php } else { ?>
                <td>
                    <?php if($file_url == ''){
                        ?>
                        <label class="custom-file-upload" >
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                        </label>
                        <label class="file_remove_label" style="display:none;">
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                        </label>
                    <?php }else{
                        $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                        $file_status = $status_data[0]->doc_status;
                        ?>
                        <label class="custom-file-upload" style="display:none;">
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-product_id="<?php echo $project->product_id;?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>">Upload
                        </label>
                        <label class="file_remove_label" >
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                        </label>
                    <?php } ?>
                </td>
                <td style="text-align: center;">
                    <span class="not_uploaded">Yet to upload</span>
                    <select name="change_document_status" id="change_stc_document_status" data-index="<?php echo $n;?>" class="change_doc change_doc_<?php echo $n;?>" style="display: none;">
                        <option value="">- Select Status -</option>
                        <option value="In review" data-doc_id="" data-doc_type_id="">In review</option>
                        <option value="Approved" data-doc_id="" data-doc_type_id="">Approve</option>
                        <option value="Rejected" data-doc_id="" data-doc_type_id="">Reject</option>
                    </select>
                </td>
                <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                        <span class="view_stc_comment"
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                              <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php } ?>   
                </td>
            <?php } ?>
                        </tr>
                        <?php
                        $n++;
                    }
                    $m++;
                    
                } ?>
                </tbody>

            </table>
        </div>
    </div>

                                                    <h5 class="mt-3 mb-3 title_text"><b>W2 (If Applicable)</b></h5>
                                                    <div class="row">
                                                        <div class="col-md-12 table-responsive">
                                                            <table class="table com_doc_display payroll_sec">
                                                                <thead>
                                                                <tr>
                                                                    <th style="text-align: left;">Years</th>
                                                                    <th>Were you a W2 employee in 2020/2021?</th>
                                                                    <th>W2 (If Applicable)</th>
                                                                    <th>Status</th>
                                                                    <th>Comments</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <?php
                                                                $m = 1;
                                                                foreach ($company_docs as $key => $value) {
                                                                    if ($m >= 4 && $m <= 5) {
                                                                        $doc_type_id = $value->doc_type_id;
                                                                        $doc_key = $value->doc_key;
                                                                        $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                        $isapplicable = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$wpdb->prefix}leads_stc_documents_additional_info WHERE  doc_key = %s AND lead_id = %d", $doc_key.'_applicable', $lead_id)
                                                                        );
                                                                        ?>
                                                                        <tr>
            <?php
            $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
            $file_url = $result[0]->uploded_documents;
            $file_name = $result[0]->local_path_filename;
            $doc_id = $result[0]->id;
            $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));

            $fade_doc_upload = ($isapplicable->is_applicable == 'No') ? 1 : 0;

            if($fade_doc_upload){
                $fade_style = 'style="filter: grayscale(100%); pointer-events: none"';
            }else{
                $fade_style = '';
            }
            ?>
            <td class="first_column">
                <label for=""
                       class="questions_Q"><?php echo $doc_label; ?></label>
            </td>
            <!--<td><?php /*echo $isapplicable->is_applicable; */?></td>-->
            <td style="text-align: center;">
                <input type="radio"
                       name="<?php echo $doc_key . '_applicable'; ?>"
                       data-index_key="0yes"
                       data-doc_type_id="<?php echo $doc_type_id; ?>"
                       value="Yes" data-doc_id="" <?php if ($isapplicable->is_applicable == 'Yes' || $isapplicable->is_applicable == '') { echo 'checked';} ?> data-section="section2">

                &nbsp; <label for="html">Yes</label>
                &nbsp; <input type="radio"
                              name="<?php echo $doc_key . '_applicable'; ?>"
                              class="" data-index_key="0no"
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                              value="No" <?php if ($isapplicable->is_applicable == 'No') { echo 'checked';} ?> data-doc_id="" data-section="section2">
                &nbsp; <label for="css">No</label>

            </td>
            <?php if ($file_url != '') {
                $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                $file_status = $status_data[0]->doc_status;
                if($fade_doc_upload){ ?>
                    <td>
                        <label class="custom-file-upload" <?php echo $fade_style;?>>
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" data-type="ops" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                        </label>
                        <label class="file_remove_label" style="display:none;">
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                        </label>
                    </td>

                    <td style="text-align: center;"><span class="not_required" style="display:block; filter: grayscale(100%); pointer-events: none">Not Applicable</span>
                        <span class="not_uploaded"
                              style="display: none">Yet to upload</span>
                    </td>

                <?php }else{ ?>
                    ?>
                <td style="text-align: center;">
                    <label class="file_remove_label">
                    <span class="material-symbols-outlined">download_done</span>
                    <a class="text_overflow_ellipse"
                       <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                class="custom_file"><?php echo $file_name; ?>
                    </a> &nbsp;<span
                            class="material-symbols-outlined cancel_icon"
                            data-doc_id="<?php echo $doc_id; ?>"
                            data-folder_name="<?php echo $parent_folder; ?>"
                            data-lead_id="<?php echo $lead_id; ?>"
                            data-doc_key="<?php echo $doc_key; ?>"
                            data-file_name="<?php echo $file_url; ?>"
                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                    </label>
                </td>
                <td>
                    <select name="change_document_status"
                            id="change_stc_document_status" data-index="<?php echo $n;?>"
                            class="change_doc change_doc_<?php echo $n;?>" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                        <option value="">- Select Status -</option>
                        <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>">
                            In
                            review
                        </option>
                        <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                        <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                    </select>
                </td>
                    <?php } ?>
                <td style="text-align: center;">
                    <?php if (!empty($comment_data)) { ?>
                        <?php if($is_affiliate == 0){?>
                            <span class="add_stc_comment_btn"
                                  data-doc_id="<?php echo $doc_id; ?>"
                                  data-doc_type_id="<?php echo $doc_type_id; ?>"
                                  title="Add">
                            <i class="fa-solid fa-comment-medical"></i>
                        </span>
                        <?php } ?>
                        <span class="view_stc_comment" 
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                            <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php }?>

                </td>
            <?php } else { ?>
                <td>
                    <?php if($file_url == ''){
                        ?>
                        <label class="custom-file-upload" <?php echo $fade_style;?>>
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                        </label>
                        <label class="file_remove_label" style="display:none;">
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                        </label>
                    <?php }else{
                        $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                        $file_status = $status_data[0]->doc_status;
                        ?>
                        <label class="custom-file-upload" style="display:none;">
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>">Upload
                        </label>
                        <label class="file_remove_label" >
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                        </label>
                    <?php } ?>
                </td>
                <td style="text-align: center;">
                    <?php if($fade_doc_upload){ ?>
                    <span class="not_required"
                          style="display:block;filter: grayscale(100%); pointer-events: none">Not Applicable</span>
                        <span class="not_uploaded"
                              style="display: none">Yet to upload</span>
                        <?php }else{ ?>
                    <span class="not_uploaded" style="display: block">Yet to upload</span>
                        <span class="not_required"
                              style="display:none;filter: grayscale(100%); pointer-events: none">Not Applicable</span>
                        <?php } ?>
                    <select name="change_document_status" id="change_stc_document_status" data-index="<?php echo $n;?>" class="change_doc change_doc_<?php echo $n;?>" style="display: none;">
                        <option value="">- Select Status -</option>
                        <option value="In review" data-doc_id="" data-doc_type_id="">In review</option>
                        <option value="Approved" data-doc_id="" data-doc_type_id="">Approve</option>
                        <option value="Rejected" data-doc_id="" data-doc_type_id="">Reject</option>
                    </select>
                </td>
                <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                        <span class="view_stc_comment" 
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                            <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php } ?>   </td>
            <?php } ?>
                                                                        </tr>

                                                                    <?php $n++; }
                                                                    $m++;

                                                                } ?>
                                                                </tbody>

                                                            </table>
                                                        </div>
                                                    </div>

                                                    <h5 class="mt-3 mb-3 title_text" style="display:none;"><b>Documents supporting Covid -19 Illness - Self</b></h5>
                                                    <div class="row" style="display:none;">
                                                        <div class="col-md-12 table-responsive">
                                                            <table class="table com_doc_display payroll_sec">
                                                                <thead>
                                                                <tr>
                                                                    <th style="text-align: left;">Years</th>
                                                                    <th>Is Applicable?</th>
                                                                    <th>Supporting Documents</th>
                                                                    <th>Status</th>
                                                                    <th>Comments</th>
                                                                </tr>
                                                                </thead>

                                                                <tbody>
                                                                <?php
                                                                $m = 1;
                                                                foreach ($company_docs as $key => $value) {
                                                                    if ($m >= 6 && $m <= 7) {
                                                                        $doc_type_id = $value->doc_type_id;
                                                                        $doc_key = $value->doc_key;
                                                                        $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                        $isapplicable = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$wpdb->prefix}leads_stc_documents_additional_info WHERE  doc_key = %s AND lead_id = %d", $doc_key.'_applicable', $lead_id)
                                                                        );
                                                                        ?>
                                                                        <tr>

            <?php

            $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
            $file_url = $result[0]->uploded_documents;
            $file_name = $result[0]->local_path_filename;
            $doc_id = $result[0]->id;
            $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
            ?>
            <td class="first_column">
                <label for=""
                       class="questions_Q"><?php echo $doc_label; ?></label>
            </td>
            <td><?php echo $isapplicable->is_applicable; ?></td>
            <?php if ($file_url != '') {
                $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                $file_status = $status_data[0]->doc_status;
                ?>
                <td style="text-align: center;">
                    <label class="file_remove_label">
                    <span class="material-symbols-outlined">download_done</span>
                    <a class="text_overflow_ellipse"
                       <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                class="custom_file"><?php echo $file_name; ?>
                    </a> &nbsp;<span
                            class="material-symbols-outlined cancel_icon"
                            data-doc_id="<?php echo $doc_id; ?>"
                            data-folder_name="<?php echo $parent_folder; ?>"
                            data-lead_id="<?php echo $lead_id; ?>"
                            data-doc_key="<?php echo $doc_key; ?>"
                            data-file_name="<?php echo $file_url; ?>"
                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                    </label>
                </td>
                <td>
                    <select name="change_document_status"
                            id="change_stc_document_status" data-index="<?php echo $n;?>"
                            class="change_doc change_doc_<?php echo $n;?>" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1)? 'disabled' : ''; ?> >
                        <option value="">- Select Status -</option>
                        <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>">
                            In
                            review
                        </option>
                        <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                        <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                    </select>
                </td>
                <td style="text-align: center;">
                    <?php if (!empty($comment_data)) { ?>
                        <?php if($is_affiliate == 0){?>
                            <span class="add_stc_comment_btn"
                                  data-doc_id="<?php echo $doc_id; ?>"
                                  data-doc_type_id="<?php echo $doc_type_id; ?>"
                                  title="Add">
                            <i class="fa-solid fa-comment-medical"></i>
                        </span>
                        <?php } ?>
                        <span class="view_stc_comment" 
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                            <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php } ?>

                </td>
            <?php } else { ?>
                <td>
                    <?php if($file_url == ''){
                        ?>
                        <label class="custom-file-upload" >
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                        </label>
                        <label class="file_remove_label" style="display:none;">
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                        </label>
                    <?php }else{
                        $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                        $file_status = $status_data[0]->doc_status;
                        ?>
                        <label class="custom-file-upload" style="display:none;">
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>">Upload
                        </label>
                        <label class="file_remove_label" >
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                        </label>
                    <?php }?>
                </td>
                <td style="text-align: center;">
                    <span class="not_uploaded">Yet to upload</span>
                    <select name="change_document_status" id="change_stc_document_status" data-index="<?php echo $n;?>" class="change_doc change_doc_<?php echo $n;?>" style="display: none;">
                        <option value="">- Select Status -</option>
                        <option value="In review" data-doc_id="" data-doc_type_id="">In review</option>
                        <option value="Approved" data-doc_id="" data-doc_type_id="">Approve</option>
                        <option value="Rejected" data-doc_id="" data-doc_type_id="">Reject</option>
                    </select>
                </td>
                <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                        <span class="view_stc_comment" 
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                            <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php } ?>   </td>
            <?php } ?>
                                                                        </tr>

                                                                    <?php $n++; }
                                                                    $m++;
                                                                } ?>
                                                                </tbody>

                                                            </table>
                                                        </div>
                                                    </div>

                                                    <h5 class="mt-3 mb-3 title_text" style="display:none;"><b>Documents supporting Covid -19 Illness/document from school - Other</b></h5>
                                                    <div class="row" style="display:none;">
                                                        <div class="col-md-12 table-responsive">
                                                            <table class="table com_doc_display payroll_sec">
                                                                <thead>
                                                                <tr>
                                                                    <th style="text-align: left;">Years</th>
                                                                    <th>Is Applicable?</th>
                                                                    <th>Supporting Documents</th>
                                                                    <th>Status</th>
                                                                    <th>Comments</th>
                                                                </tr>
                                                                </thead>

                                                                <tbody>
                                                                <?php
                                                                $m = 1;
                                                                foreach ($company_docs as $key => $value) {
                                                                    if ($m >= 8 && $m <= 9) {
                                                                        $doc_type_id = $value->doc_type_id;
                                                                        $doc_key = $value->doc_key;
                                                                        $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                        $isapplicable = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$wpdb->prefix}leads_stc_documents_additional_info WHERE  doc_key = %s AND lead_id = %d", $doc_key.'_applicable', $lead_id)
                                                                        );
                                                                        ?>
                                                                        <tr>
            <?php
            $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
            $file_url = $result[0]->uploded_documents;
            $file_name = $result[0]->local_path_filename;
            $doc_id = $result[0]->id;
            $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
            ?>
            <td class="first_column">
                <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
            </td>
            <td><?php echo $isapplicable->is_applicable; ?></td>
            <?php if ($file_url != '') {
                $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                $file_status = $status_data[0]->doc_status;
                ?>
                <td style="text-align: center;">
                    <label class="file_remove_label">
                    <span class="material-symbols-outlined">download_done</span>
                    <a class="text_overflow_ellipse"
                       <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                class="custom_file"><?php echo $file_name; ?>
                    </a> &nbsp;<span
                            class="material-symbols-outlined cancel_icon"
                            data-doc_id="<?php echo $doc_id; ?>"
                            data-folder_name="<?php echo $parent_folder; ?>"
                            data-lead_id="<?php echo $lead_id; ?>"
                            data-doc_key="<?php echo $doc_key; ?>"
                            data-file_name="<?php echo $file_url; ?>"
                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                    </label>
                </td>
                <td>
                    <select name="change_document_status"
                            id="change_stc_document_status" data-index="<?php echo $n;?>"
                            class="change_doc change_doc_<?php echo $n;?>" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                        <option value="">- Select Status -</option>
                        <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>">
                            In
                            review
                        </option>
                        <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                        <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                    </select>
                </td>
                <td style="text-align: center;">
                    <?php if (!empty($comment_data)) { ?>
                        <?php if($is_affiliate == 0){?>
                            <span class="add_stc_comment_btn"
                                  data-doc_id="<?php echo $doc_id; ?>"
                                  data-doc_type_id="<?php echo $doc_type_id; ?>"
                                  title="Add">
                            <i class="fa-solid fa-comment-medical"></i>
                        </span>
                        <?php } ?>
                        <span class="view_stc_comment" 
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                            <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php } ?>

                </td>
            <?php } else { ?>
                <td>
                    <?php if($file_url == ''){
                        ?>
                        <label class="custom-file-upload" >
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                        </label>
                        <label class="file_remove_label" style="display:none;">
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                        </label>
                    <?php }else{
                        $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                        $file_status = $status_data[0]->doc_status;
                        ?>
                        <label class="custom-file-upload" style="display:none;">
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>">Upload
                        </label>
                        <label class="file_remove_label" >
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                        </label>
                    <?php } ?>
                </td>
                <td style="text-align: center;">
                    <span class="not_uploaded">Yet to upload</span>
                    <select name="change_document_status" id="change_stc_document_status" data-index="<?php echo $n;?>" class="change_doc change_doc_<?php echo $n;?>" style="display: none;">
                        <option value="">- Select Status -</option>
                        <option value="In review" data-doc_id="" data-doc_type_id="">In review</option>
                        <option value="Approved" data-doc_id="" data-doc_type_id="">Approve</option>
                        <option value="Rejected" data-doc_id="" data-doc_type_id="">Reject</option>
                    </select>
                </td>
                <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                        <span class="view_stc_comment"
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                            <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php } ?>   </td>
            <?php } ?>
                                                                        </tr>

                                                                    <?php $n++; }
                                                                    $m++;
                                                                } ?>
                                                                </tbody>

                                                            </table>
                                                        </div>
                                                    </div>

                                                    <h5 class="mt-3 mb-3 title_text"><b>Form - 1099 G (If Applicable)</b></h5>
                                                    <div class="row">
                                                        <div class="col-md-12 table-responsive">
                                                            <table class="table com_doc_display payroll_sec">
                                                                <thead>
                                                                <tr>
                                                                    <th style="text-align: left;">Years</th>
                                                                    <th>Do you have 1099 - G for 2020/2021?	</th>
                                                                    <th>Form - 1099 G (If Applicable)	</th>
                                                                    <th>Status</th>
                                                                    <th>Comments</th>
                                                                </tr>
                                                                </thead>

                                                                <tbody>
                                                                <?php
                                                                $m = 1;
                                                                foreach ($company_docs as $key => $value) {
                                                                    if ($m >= 10 && $m <= 11) {
                                                                        $doc_type_id = $value->doc_type_id;
                                                                        $doc_key = $value->doc_key;
                                                                        $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                        $isapplicable = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$wpdb->prefix}leads_stc_documents_additional_info WHERE  doc_key = %s AND lead_id = %d", $doc_key.'_applicable', $lead_id)
                                                                        );
                                                                        ?>
                                                                        <tr>

            <?php

            $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
            $file_url = $result[0]->uploded_documents;
            $file_name = $result[0]->local_path_filename;
            $doc_id = $result[0]->id;
            $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));

            $fade_doc_upload = ($isapplicable->is_applicable == 'No') ? 1 : 0;

            if($fade_doc_upload){
                $fade_style = 'style="filter: grayscale(100%); pointer-events: none"';
            }else{
                $fade_style = '';
            }
            ?>
            <td class="first_column">
                <label for=""
                       class="questions_Q"><?php echo $doc_label; ?></label>
            </td>
            <!--<td><?php /*echo $isapplicable->is_applicable; */?></td>-->
            <td style="text-align: center;">
                <input type="radio"
                       name="<?php echo $doc_key . '_applicable'; ?>"
                       data-index_key="0yes"
                       data-doc_type_id="<?php echo $doc_type_id; ?>"
                       value="Yes" data-doc_id="" <?php if ($isapplicable->is_applicable == 'Yes' || $isapplicable->is_applicable == '') { echo 'checked';} ?> data-section="section2">

                &nbsp; <label for="html">Yes</label>
                &nbsp; <input type="radio"
                              name="<?php echo $doc_key . '_applicable'; ?>"
                              class="" data-index_key="0no"
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                              value="No" <?php if ($isapplicable->is_applicable == 'No') { echo 'checked';} ?> data-doc_id="" data-section="section2">
                &nbsp; <label for="css">No</label>

            </td>
            <?php if ($file_url != '') {
                $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                $file_status = $status_data[0]->doc_status;
                if($fade_doc_upload){ ?>
                    <td>
                        <label class="custom-file-upload" <?php echo $fade_style;?>>
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" data-type="ops" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                        </label>
                        <label class="file_remove_label" style="display:none;">
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                        </label>
                    </td>

                    <td style="text-align: center;"><span class="not_required" style="display:block; filter: grayscale(100%); pointer-events: none">Not Applicable</span>
                        <span class="not_uploaded"
                              style="display: none">Yet to upload</span>
                    </td>

                <?php }else{ ?>
                ?>
                <td style="text-align: center;">
                    <label class="file_remove_label">
                    <span class="material-symbols-outlined">download_done</span>
                    <a class="text_overflow_ellipse"
                       <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                class="custom_file"><?php echo $file_name; ?>
                    </a> &nbsp;<span
                            class="material-symbols-outlined cancel_icon"
                            data-doc_id="<?php echo $doc_id; ?>"
                            data-folder_name="<?php echo $parent_folder; ?>"
                            data-lead_id="<?php echo $lead_id; ?>"
                            data-doc_key="<?php echo $doc_key; ?>"
                            data-file_name="<?php echo $file_url; ?>"
                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                    </label>
                </td>
                <td>
                    <select name="change_document_status"
                            id="change_stc_document_status" data-index="<?php echo $n;?>"
                            class="change_doc change_doc_<?php echo $n;?>" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                        <option value="">- Select Status -</option>
                        <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>">
                            In
                            review
                        </option>
                        <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                        <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                    </select>
                </td>
                                                                            <?php } ?>
                <td style="text-align: center;">
                    <?php if (!empty($comment_data)) { ?>
                        <?php if($is_affiliate == 0){?>
                            <span class="add_stc_comment_btn"
                                  data-doc_id="<?php echo $doc_id; ?>"
                                  data-doc_type_id="<?php echo $doc_type_id; ?>"
                                  title="Add">
                            <i class="fa-solid fa-comment-medical"></i>
                        </span>
                        <?php } ?>
                        <span class="view_stc_comment" 
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                            <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php } ?>

                </td>
            <?php } else { ?>
                <td>
                    <?php if($file_url == ''){
                        ?>
                        <label class="custom-file-upload" <?php echo $fade_style;?>>
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                        </label>
                        <label class="file_remove_label" style="display:none;">
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                        </label>
                    <?php }else{
                        $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                        $file_status = $status_data[0]->doc_status;
                        ?>
                        <label class="custom-file-upload" style="display:none;">
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>">Upload
                        </label>
                        <label class="file_remove_label" >
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                        </label>
                    <?php } ?>
                </td>
                <td style="text-align: center;">
                    <?php if($fade_doc_upload){ ?>
                        <span class="not_required"
                              style="display:block;filter: grayscale(100%); pointer-events: none">Not Applicable</span>
                        <span class="not_uploaded"
                              style="display: none">Yet to upload</span>
                    <?php }else{ ?>
                        <span class="not_uploaded" style="display: block">Yet to upload</span>
                        <span class="not_required"
                              style="display:none;filter: grayscale(100%); pointer-events: none">Not Applicable</span>
                    <?php } ?>
                    <select name="change_document_status" id="change_stc_document_status" data-index="<?php echo $n;?>" class="change_doc change_doc_<?php echo $n;?>" style="display: none;">
                        <option value="">- Select Status -</option>
                        <option value="In review" data-doc_id="" data-doc_type_id="">In review</option>
                        <option value="Approved" data-doc_id="" data-doc_type_id="">Approve</option>
                        <option value="Rejected" data-doc_id="" data-doc_type_id="">Reject</option>
                    </select>
                </td>
                <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                        <span class="view_stc_comment" 
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                            <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php } ?>   </td>
            <?php } ?>
                                                                        </tr>

                                                                    <?php $n++; }
                                                                    $m++;
                                                                } ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>

                                                    <h5 class="mt-3 mb-3 title_text" style="display:none;"><b>Any supporting documents to prove you were not able to work due to #3 and 4 above
                                                        </b></h5>
                                                    <div class="row" style="display:none;">
                                                        <div class="col-md-12 table-responsive">
                                                            <table class="table com_doc_display payroll_sec">
                                                                <thead>
                                                                <tr>
                                                                    <th style="text-align: left;">Years</th>
                                                                    <th>Is Applicable?</th>
                                                                    <th>Supporting Documents</th>
                                                                    <th>Status</th>
                                                                    <th>Comments</th>
                                                                </tr>
                                                                </thead>

                                                                <tbody>
                                                                <?php
                                                                $m = 1;
                                                                foreach ($company_docs as $key => $value) {
                                                                    if ($m >= 12 && $m <= 13) {
                                                                        $doc_type_id = $value->doc_type_id;
                                                                        $doc_key = $value->doc_key;
                                                                        $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                        $isapplicable = $wpdb->get_row(
            $wpdb->prepare("SELECT * FROM {$wpdb->prefix}leads_stc_documents_additional_info WHERE  doc_key = %s AND lead_id = %d", $doc_key.'_applicable', $lead_id)
                                                                        );
                                                                        ?>
                                                                        <tr>

            <?php

            $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
            $file_url = $result[0]->uploded_documents;
            $file_name = $result[0]->local_path_filename;
            $doc_id = $result[0]->id;
            $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
            ?>
            <td class="first_column">
                <label for=""
                       class="questions_Q"><?php echo $doc_label; ?></label>
            </td>
            <td><?php echo $isapplicable->is_applicable; ?></td>
            <?php if ($file_url != '') {
                $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                $file_status = $status_data[0]->doc_status;
                ?>
                <td style="text-align: center;">
                        <label class="file_remove_label">
                        <span class="material-symbols-outlined">download_done</span>
                        <a class="text_overflow_ellipse"
                           <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                    class="custom_file"><?php echo $file_name; ?>
                        </a> &nbsp;<span
                                class="material-symbols-outlined cancel_icon"
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-folder_name="<?php echo $parent_folder; ?>"
                                data-lead_id="<?php echo $lead_id; ?>"
                                data-doc_key="<?php echo $doc_key; ?>"
                                data-file_name="<?php echo $file_url; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"
                                style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                        </label>
                </td>
                <td>
                    <select name="change_document_status"
                            id="change_stc_document_status" data-index="<?php echo $n;?>"
                            class="change_doc change_doc_<?php echo $n;?>" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                        <option value="">- Select Status -</option>
                        <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>">
                            In
                            review
                        </option>
                        <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                        <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                    </select>
                </td>
                <td style="text-align: center;">
                    <?php if (!empty($comment_data)) { ?>
                        <?php if($is_affiliate == 0){?>
                            <span class="add_stc_comment_btn"
                                  data-doc_id="<?php echo $doc_id; ?>"
                                  data-doc_type_id="<?php echo $doc_type_id; ?>"
                                  title="Add">
                            <i class="fa-solid fa-comment-medical"></i>
                        </span>
                        <?php } ?>
                        <span class="view_stc_comment" 
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                                <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php }?>

                </td>
            <?php } else { ?>
                <td>
                    <?php if($file_url == ''){
                        ?>
                        <label class="custom-file-upload" >
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                        </label>
                        <label class="file_remove_label" style="display:none;">
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                        </label>
                    <?php }else{
                        $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                        $file_status = $status_data[0]->doc_status;
                        ?>
                        <label class="custom-file-upload" style="display:none;">
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>">Upload
                        </label>
                        <label class="file_remove_label" >
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                        </label>
                    <?php } ?>
                </td>
                <td style="text-align: center;">
                    <span class="not_uploaded">Yet to upload</span>
                    <select name="change_document_status" id="change_stc_document_status" data-index="<?php echo $n;?>" class="change_doc change_doc_<?php echo $n;?>" style="display: none;">
                        <option value="">- Select Status -</option>
                        <option value="In review" data-doc_id="" data-doc_type_id="">In review</option>
                        <option value="Approved" data-doc_id="" data-doc_type_id="">Approve</option>
                        <option value="Rejected" data-doc_id="" data-doc_type_id="">Reject</option>
                    </select>
                </td>
                <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                        <span class="view_stc_comment" 
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                               data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                            <i class="fa-regular fa-comment"></i>
                        </span>
                    <?php } ?>   </td>
            <?php } ?>
                                                                        </tr>

                                                                    <?php $n++; }
                                                                    $m++;
                                                                } ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>

                                                    <h5 class="mt-3 mb-3 title_text"><b>TaxNow Sign-up</b></h5>
                                                    <div class="row">
                                                        <div class="col-md-12 table-responsive">
                                                            <table class="table com_doc_display payroll_sec">
                                                                <thead>
                                                                <tr>
                                                                    <th style="text-align: left;">Document</th>
                                                                    <th>Have you signed up? </th>
                                                                    <th>Signed up as per TaxNow file?</th>
                                                                    <th class="hideshow_onupload">Status</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <tr>
                                                                    <?php
                                                                    $doc_type_id = '138';
                                                                    $doc_mapping_table = $wpdb->prefix . 'leads_document_mapping';
                                                                    $doc_uplod_table =  $wpdb->prefix . 'leads_document_upload';
                                                                    $doc_uplod_status = $wpdb->prefix . 'leads_document_status';
                                                                    $doc_required_mapping = $wpdb->prefix . 'leads_document_required_mapping';
                                                                    $document_taxnow_status = $wpdb->prefix . 'leads_document_taxnow_status';
                                                                    $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));

                                                                    $file_url = $result[0]->uploded_documents;
                                                                    $file_name = $result[0]->local_path_filename;
                                                                    $doc_id = $result[0]->id ;

                                                                    $doc_required_mapping_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_required_mapping WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                    $doc_required = empty($doc_required_mapping_data) ? '' : $doc_required_mapping_data[0]->required_flag;

                                                                    $document_taxnow_status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $document_taxnow_status WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                    $taxnow_required = empty($document_taxnow_status_data) ? '' : $document_taxnow_status_data[0]->taxnow_signup_status;
                                                                    ?>
                                                                    <td class="first_column">
                                                                        <label class="questions_Q initiate-signup">Initiate Sign-Up <a target="_blank" href="https://apps.taxnow.com/signup?utm_source=occams&utm_medium=email&utm_campaign=sign-up">(TaxNow)</a> <span class="add-taxnow-btn"><i class="fa fa-info-circle"></i></span></label>
                                                                    </td>
                                                                    <td>
                                                                        <?php if(isset($doc_required) && $doc_required == '1'){ ?>   <label for="html">Yes</label><?php } ?>
                                                                        <?php if(isset($doc_required) && $doc_required == '0'){ ?>   <label for="css">No</label><?php } ?>
                                                                    </td>
                                                                    <td style="text-align: center;">
                                                                        <input type="radio" id=""
                                                                               name="taxnow_signup_stc_section_required"
                                                                               class="taxnow_signup_masterops_doc_section_required"
                                                                               data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                               value="Yes" <?php echo (isset($taxnow_required) && $taxnow_required === '1') ? 'checked' : ''; ?>
                                                                               data-doc_id="<?php echo $doc_id ?>"
                                                                               data-section="section1">
                                                                          <label for="html">Yes</label>
                                                                          <input type="radio" id=""
                                                                                 name="taxnow_signup_stc_section_required"
                                                                                 class="taxnow_signup_masterops_doc_section_required"
                                                                                 data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                 value="No" <?php echo (isset($taxnow_required) && $taxnow_required === '0') ? 'checked' : ''; ?>
                                                                                 data-doc_id="<?php echo $doc_id ?>"
                                                                                 data-section="section1">
                                                                          <label for="css">No</label>
                                                                    </td>
                                                                    <td class="hideshow_onupload">
                                                                        <?php
                                                                        if($taxnow_required == '1'){ ?>
                                                                            <span class="approved_status" style="display:block; pointer-events: none;">Signed Up</span>
                                                                        <?php }else if (($taxnow_required ==  0 && $doc_required ==  0)) {  ?>
                                                                            <span class="not_required" style="display:block;filter: grayscale(100%); pointer-events: none">Sign Up Pending</span>
                                                                        <?php }else if (($taxnow_required ==  0 && $doc_required ==  1)) {  ?>
                                                                            <span class="not_required" style="display:block;filter: grayscale(100%); pointer-events: none">In Review</span>
                                                                        <?php }else  if(($taxnow_required == 0 && $doc_required == '1')){ ?>
                                                                            <span class="not_required" style="display:block; pointer-events: none;">In Review</span>
                                                                        <?php }else  if(($doc_required == '0')){ ?>
                                                                            <span class="not_required" style="display:block; pointer-events: none;">In Review</span>
                                                                        <?php }else  if(($doc_required == '1')){ ?>
                                                                            <span class="not_required" style="display:block; pointer-events: none;">In Review</span>
                                                                        <?php }else{ ?>
                                                                            <span class="approved_status" style="display:block; pointer-events: none;color: #2479c5 !important;
background-color: rgb(72 164 201 / 38%) !important;">Pending</span>
                                                                        <?php } ?>
                                                                    </td>
                                                                </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>

                                                        <?php if(isset($lead_id)) { ?>
                                                            <div class="button_next_prev">
                                                                <input type="submit" name="selfdocumentsubmit" id="selfdocumentsubmit" class="associatetsubmit nxt_btn" value="Submit">
                                                            </div>
                                                        <?php } ?>
                                                    </form>
                                                    <!-- For OPS user table END -->
                                                </div>
                                            </div>

                                            <?php 
                                            $form_id = 5;
                                            $company_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `form_id` = %d", $form_id));

                                            ?>
                                            
                                            
                                            <div class="tab-pane erc-project-scroll" id="eleve-doc-impacted-days" role="tabpanel" aria-labelledby="eleve-doc-impacted-days-tab">
                                                <form  action="" method="post">
                                                <div class="white_card_body">
                                                    <h5 class="mt-3 mb-3 title_text"><b>Impacted days for self (Note: Please upload any supporting documents for the period if available)</b></h5>
                                                    <div class="row">
                                                        <div class="col-md-12 table-responsive">
                                                            <table class="table com_doc_display payroll_sec">
                                                                <thead>
                                                                <tr>
                                                                    <th style="text-align: left;">Documents</th>
                                                                    <th>Period<br>(No. of Days)</th>
                                                                    <th class=""><!--Unemployment benefit availed?--></th>
                                                                    <th>Supporting Documents(Optional)	</th>
                                                                    <th>Status</th>
                                                                    <th>Comments</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <?php
                                                                $m = 1;
                                                                foreach ($company_docs as $key => $value) {
                                                                    if ($m >= 1 && $m <= 3) {
                                                                        $doc_type_id = $value->doc_type_id;
                                                                        $doc_key = $value->doc_key;
                                                                        $doc_label = $value->doc_label;
                                                                        ?>
                                                                        <tr>
            <?php
            $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
            $file_url = $result[0]->uploded_documents;
            $file_name = $result[0]->local_path_filename;
            $doc_id = $result[0]->id;
            $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
            $self_impacted_days1 = $wpdb->get_row($wpdb->prepare("SELECT * FROM $leads_impacted_days where `lead_id` = %d  AND type = %s AND time_of_days = %s", $lead_id, 'self', 'self_time_off_days_april_2020'));
            $self_impacted_days2 = $wpdb->get_row($wpdb->prepare("SELECT * FROM $leads_impacted_days where `lead_id` = %d  AND type = %s AND time_of_days = %s", $lead_id, 'self', 'self_time_off_days_january_2021'));
            $self_impacted_days3 = $wpdb->get_row($wpdb->prepare("SELECT * FROM $leads_impacted_days where `lead_id` = %d  AND type = %s AND time_of_days = %s", $lead_id, 'self', 'self_time_off_days_april_2021'));

            ?>
            <td class="first_column">
                <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
            </td>
            <td>
                <?php
//                    if ($m == 1) {
//                        echo $self_impacted_days1->no_of_days;
//                    }
//                    if ($m == 2) {
//                        echo $self_impacted_days2->no_of_days;
//                    }
//                    if ($m == 3) {
//                        echo $self_impacted_days3->no_of_days;
//                    }
                    
                    if ($m == 1) {
                                                                echo '<input type="hidden" name="self_time_of_days[]" value="self_time_off_days_april_2020"><input type="hidden" name="self_title[]" value="Time off Days between April 1, 2020, and December 31, 2020"><input type="number" name="self_no_of_days[]" value="' . $self_impacted_days1->no_of_days . '" class="self-no-days" id="self-no-days0">';
                                                            }
                                                            if ($m == 2) {
                                                                echo '<input type="hidden" name="self_time_of_days[]" value="self_time_off_days_january_2021"><input type="hidden" name="self_title[]" value="Time off Days between January 1, 2021, and March 31, 2021"><input type="number" name="self_no_of_days[]" value="' . $self_impacted_days2->no_of_days . '" class="self-no-days" id="self-no-days1">';
                                                            }
                                                            if ($m == 3) {
                                                                echo '<input type="hidden" name="self_time_of_days[]" value="self_time_off_days_april_2021"><input type="hidden" name="self_title[]" value="Time off Days between April 1, 2021, and September 30, 2021"><input type="number" name="self_no_of_days[]" value="' . $self_impacted_days3->no_of_days . '" class="self-no-days" id="self-no-days2">';
                                                            }
                ?>
            </td>
            <td class="unemp_benefit_availed_col"><?php
                    if ($m == 1) {
                        echo ucfirst($self_impacted_days1->unemp_benefit_claimed);
                    }
                    if ($m == 2) {
                        echo ucfirst($self_impacted_days2->unemp_benefit_claimed);
                    }
                    if ($m == 3) {
                        echo ucfirst($self_impacted_days3->unemp_benefit_claimed);
                    }
                ?>
            </td>
            <?php if ($file_url != '') {
                $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                $file_status = $status_data[0]->doc_status;
                ?>
                <td style="text-align: center;">
                        <label class="file_remove_label">
                        <span class="material-symbols-outlined">download_done</span>
                        <a class="text_overflow_ellipse"
                           <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                    class="custom_file"><?php echo $file_name; ?>
                        </a> &nbsp;<span
                                class="material-symbols-outlined cancel_icon"
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-folder_name="<?php echo $parent_folder; ?>"
                                data-lead_id="<?php echo $lead_id; ?>"
                                data-doc_key="<?php echo $doc_key; ?>"
                                data-file_name="<?php echo $file_url; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"
                                style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                        </label>
                </td>
                <td>
                    <select name="change_document_status"
                            id="change_stc_document_status" data-index="<?php echo $n;?>"
                            class="change_doc change_doc_<?php echo $n;?>" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                        <option value="">- Select Status -</option>
                        <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>">In
                            review
                        </option>
                        <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                        <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                data-doc_id="<?php echo $doc_id; ?>"
                                data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                    </select>
                </td>
                <td style="text-align: center;">
                    <?php if (!empty($comment_data)) { ?>
                        <?php if($is_affiliate == 0){?>
                            <span class="add_stc_comment_btn"
                                  data-doc_id="<?php echo $doc_id; ?>"
                                  data-doc_type_id="<?php echo $doc_type_id; ?>"
                                  title="Add">
                            <i class="fa-solid fa-comment-medical"></i>
                        </span>
                        <?php } ?>
                        <span class="view_stc_comment" data-doc_type_id="<?php echo $doc_type_id; ?>"  data-doc_id="<?php echo $doc_id; ?>"
                              title="View">
                              <i class="fa-regular fa-comment"></i>
                              </span>
                    <?php }?>
                </td>
                <?php } else { ?>
                <td>
                    <?php if($file_url == ''){
                        ?>
                        <label class="custom-file-upload" >
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                        </label>
                        <label class="file_remove_label" style="display:none;">
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                        </label>
                    <?php }else{
                        $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                        $file_status = $status_data[0]->doc_status;
                        ?>
                        <label class="custom-file-upload" style="display:none;">
                            <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                        </label>
                        <label class="file_remove_label" >
                            <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                        </label>
                    <?php } ?>
                </td>
                <td style="text-align: center;">
                    <span class="not_uploaded">Yet to upload</span>
                    <select name="change_document_status" id="change_stc_document_status" data-index="<?php echo $n;?>" class="change_doc change_doc_<?php echo $n;?>" style="display: none;">
                        <option value="">- Select Status -</option>
                        <option value="In review" data-doc_id="" data-doc_type_id="">In review</option>
                        <option value="Approved" data-doc_id="" data-doc_type_id="">Approve</option>
                        <option value="Rejected" data-doc_id="" data-doc_type_id="">Reject</option>
                    </select>
                </td>
                <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                    <span class="view_stc_comment" data-doc_type_id="<?php echo $doc_type_id; ?>"  data-doc_id="<?php echo $doc_id; ?>"
                          title="View">
                      <i class="fa-regular fa-comment"></i>
                      </span>
                    <?php } ?>   
                </td>
            <?php } ?>
                                                                        </tr>
                                                                        
                                                                        
                                                                   <tr <?php
                                                        if ($m == 1) {
                                                            $dateRangeContainerMain = 0;
                                                            if (empty($_2020_Q2_Q3_Q4_count) || $_2020_Q2_Q3_Q4_count == 0) {
                                                                //echo 'style="display:none"';
                                                                //echo 'style="display:inline-block"';
                                                            }
                                                        }
                                                        if ($m == 2) {
                                                            $dateRangeContainerMain = 1;
                                                            if (empty($_2021_Q1_count) || $_2021_Q1_count == 0) {
                                                                //echo 'style="display:none"';
                                                                //echo 'style="display:inline-block"';
                                                            }
                                                        }
                                                        if ($m == 3) {
                                                            $dateRangeContainerMain = 2;
                                                            if (empty($_2021_Q2_Q3_count) || $_2021_Q2_Q3_count == 0) {
                                                                //echo 'style="display:none"';
                                                                //echo 'style="display:inline-block"';
                                                            }
                                                        }
                                                        ?>>
                                                            <td colspan="12">

                                            <div class="row count-bot mb-3 impacted-dates-row">

                                                        <div class="row" id="dateRangeContainerMain_<?php echo $dateRangeContainerMain; ?>">
                                                            <div class="col-md-12">
                                                                <p class="date-title">You have selected <span id="selectedDatesCount">0</span> out of 10 days</p>
                                                            </div>

                                                            <div class="datecontainer">

                                                                <div id="datePickersContainer" class="row">
                                                                    <?php
                                                                    $date_values_array = []; // Initialize outside the main block

                                                                    if ($m == 1) {
                                                                        $d = 0;
                                                                        for ($i = 1; $i <= $_2020_Q2_Q3_Q4_count; $i++) {
                                                                            $date_field = "2020_Q2_Q3_Q4_$i";
                                                                            $date_value = isset($date_values[$date_field]) ? $date_values[$date_field] : '';

                                                                            if ($date_value !== '') {
                                                                                $date_values_array[] = $date_value;
                                                                            }
                                                                        }
                                                                    } elseif ($m == 2) {
                                                                        $d = 1;
                                                                        for ($i = 1; $i <= $_2021_Q1_count; $i++) {
                                                                            $date_field = "2021_Q1_$i";
                                                                            $date_value = isset($date_values[$date_field]) ? $date_values[$date_field] : '';

                                                                            if ($date_value !== '') {
                                                                                $date_values_array[] = $date_value;
                                                                            }
                                                                        }
                                                                    } elseif ($m == 3) {
                                                                        $d = 2;
                                                                        for ($i = 1; $i <= $_2021_Q2_Q3_count; $i++) {
                                                                            $date_field = "2021_Q2_Q3_$i";
                                                                            $date_value = isset($date_values[$date_field]) ? $date_values[$date_field] : '';

                                                                            if ($date_value !== '') {
                                                                                $date_values_array[] = $date_value;
                                                                            }
                                                                        }
                                                                    }

                                                               
                                                                    $all_dates_0 = [];
                                                                    $all_dates_1 = [];
                                                                    $all_dates_2 = [];
                                                                    
                                                                    $all_dates_var_name = 'all_dates_' . $d;
$all_dates_varaible = $$all_dates_var_name; // Using variable variables

                                                                    //  echo '<pre>';
                                                                    //  print_r($impacted_days_grouped_data);
                                                                    //  echo '</pre>';
                                                                    if (isset($impacted_days_grouped_data[$d])) {
                                                                        foreach ($impacted_days_grouped_data[$d] as $item) {
                                                                            $containerId = 'dateRangeContainer_' . $item->doc_key . '_' . $item->date_range_index;
                                                                            $inputId = 'selectedDateRangeInput_' . $item->doc_key . '_' . $item->date_range_index;
                                                                            $startDate = !empty($item->start_date) ? $item->start_date : '';
                                                                            $endDate = !empty($item->end_date) ? $item->end_date : '';
                                                                            $dateRangeValue = $startDate && $endDate ? $startDate . ' - ' . $endDate : '';
                                                                            if ($startDate && $endDate) {
                                                                                $all_dates_varaible = array_merge($all_dates_varaible, getDatesInRange($startDate, $endDate));
                                                                            }
                                                                            ?>

                                                                            <div class="date-range-container col-md-4" id="<?php echo $containerId; ?>">
                                                                                <input type="text" id="<?php echo $inputId; ?>" name="<?php echo $inputId; ?>" class="date-range-input" placeholder="Start Date - End Date" autocomplete="off" value="<?php echo $dateRangeValue; ?>">
                                                                                <i class="fa-regular fa-calendar-days calenday-icon"></i>
                                                                                <p class="remove-date">Remove Date <i class="fa-regular fa-trash-can trash-icon" data-index="<?php echo $item->date_range_index; ?>"></i></p>
                                                                            </div>

                                                                            <?php
                                                                        }
                                                                    } else {
                                                                        ?>

                                                                        <div class="date-range-container col-md-4" id="dateRangeContainer_0">
                                                                            <input type="text" id="selectedDateRangeInput_<?php echo $d; ?>_0" name="selectedDateRangeInput_<?php echo $d; ?>_0" class="date-range-input" placeholder="Start Date - End Date" autocomplete="off">
                                                                            <i class="fa-regular fa-calendar-days calenday-icon"></i>
                                                                        </div>


                                                                    <?php }
                                                                    ?>

                                                                </div>
                                                            </div>






                                                            <!--undertaking row-->


                                                            <?php
                                                            $undertaking_checked = '';
                                                            if ($m == 1) {
                                                                $self_time_off_days_april_2020_undertaking = '';
                                                                if ($self_impacted_days1->undertaking == 1) {
                                                                    $self_time_off_days_april_2020_undertaking = 'checked';
                                                                }
                                                                ?>
                                                                <div class="d-flex addmore-checkbox datecontainer">
                                                                    <button id="addMoreDateBtn" class="add-more-date"><i class="fa-solid fa-plus"></i> Add More Dates</button>

                                                                    <div class="form-check checkbox-type">

                                                                        <input class="form-check-input" type="checkbox" value="" name="undertaking_self_time_off_days_april_2020" id="flexCheckDefault1" <?php echo $self_time_off_days_april_2020_undertaking; ?> >
                                                                        <label class="form-check-label" for="flexCheckDefault1">
                                                                            I did not claim unemployment benefits on these dates
                                                                        </label>
                                                                    </div>

                                                                </div>
                                                            <?php
                                                            }else if ($m == 2) {
                                                                $self_time_off_days_january_2021_undertaking = '';
                                                                if ($self_impacted_days2->undertaking == 1) {
                                                                    $self_time_off_days_january_2021_undertaking = 'checked';
                                                                }
                                                                ?>

                                                                <div class="d-flex addmore-checkbox datecontainer">
                                                                    <button id="addMoreDateBtn" class="add-more-date"><i class="fa-solid fa-plus"></i> Add More Dates</button>

                                                                    <div class="form-check checkbox-type">

                                                                        <input class="form-check-input" type="checkbox" value="" name="undertaking_self_time_off_days_january_2021" id="flexCheckDefault2" <?php echo $self_time_off_days_january_2021_undertaking; ?> >
                                                                        <label class="form-check-label" for="flexCheckDefault2">
                                                                            I did not claim unemployment benefits on these dates
                                                                        </label>
                                                                    </div>

                                                                </div>
                                                            <?php
                                                            }else if ($m == 3) {
                                                                $self_time_off_days_april_2021_undertaking = '';
                                                                if ($self_impacted_days3->undertaking == 1) {
                                                                    $self_time_off_days_april_2021_undertaking = 'checked';
                                                                }
                                                                ?>

                                                                <div class="d-flex addmore-checkbox datecontainer">
                                                                    <button id="addMoreDateBtn" class="add-more-date"><i class="fa-solid fa-plus"></i> Add More Dates</button>

                                                                    <div class="form-check checkbox-type">

                                                                        <input class="form-check-input" type="checkbox" value="" name="undertaking_self_time_off_days_april_2021" id="flexCheckDefault3" <?php echo $self_time_off_days_april_2021_undertaking; ?> >
                                                                        <label class="form-check-label" for="flexCheckDefault3">
                                                                            I did not claim unemployment benefits on these dates
                                                                        </label>
                                                                    </div>

                                                                </div>
                                                    <?php }
                                                    ?>
                                                  
     <input type="hidden" id="selectedDateRanges" value="<?php echo implode(',', $all_dates_varaible); ?>">

                                                        </div>  
                                                    </div>
                                                            </td>
                                                    </tr>

                                                                 <?php $n++;}
                                                                $m++;
                                                                }
                                                                ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <h5 class="mt-3 mb-3 title_text"><b>Impacted days for others (Note: Please upload any supporting documents for the period if available.)</b></h5>
                                                    <div class="row">
                                                        <div class="col-md-12 table-responsive">
                                                            <table class="table com_doc_display payroll_sec">
                                                                <thead>
                                                                <tr>
                                                                    <th style="text-align: left;">Documents</th>
                                                                    <th>Period<br>(No. of Days)</th>
<!--                                                                    <th class="">Unemployment benefit availed?</th>-->
                                                                    <th>Supporting Documents(Optional)</th>
                                                                    <th>Status</th>
                                                                    <th>Comments</th>
                                                                </tr>
                                                                </thead>
                                                                <tbody>
                                                                <?php
                                                                $m = 1;
                                                                foreach ($company_docs as $key => $value) {
                                                                if ($m >= 4 && $m <= 6) {
                                                                    $doc_type_id = $value->doc_type_id;
                                                                    $doc_key = $value->doc_key;
                                                                    $doc_label = $value->doc_label;
                                                                    ?>
                                                                    <tr>
                                                                        <?php
                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                        $file_url = $result[0]->uploded_documents;
                                                                        $file_name = $result[0]->local_path_filename;
                                                                        $doc_id = $result[0]->id;
                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));

                                                                        $self_impacted_days4 = $wpdb->get_row($wpdb->prepare("SELECT * FROM $leads_impacted_days where `lead_id` = %d  AND type = %s AND time_of_days = %s", $lead_id, 'others', 'other_time_off_days_april_2020'));
                                                                        $self_impacted_days5 = $wpdb->get_row($wpdb->prepare("SELECT * FROM $leads_impacted_days where `lead_id` = %d  AND type = %s AND time_of_days = %s", $lead_id, 'others', 'other_time_off_days_january_2021'));
                                                                        $self_impacted_days6 = $wpdb->get_row($wpdb->prepare("SELECT * FROM $leads_impacted_days where `lead_id` = %d  AND type = %s AND time_of_days = %s", $lead_id, 'others', 'other_time_off_days_april_2021'));

                                                                        ?>
                                                                        <td class="first_column">
            <label for="" class="questions_Q"><?php 

                if ($m == 4) {
                    echo "Total number of time off days(max 50) between April 1, 2020, and December 31, 2020";
                }
                if ($m == 5) {
                    echo "Total number of time off days(max 50) between January 1, 2021, and March 31, 2021";
                }
                if ($m == 6) {
                    echo "Total number of time off days(max 60) between April 1, 2021, and September 30, 2021";
                }
              


            //echo $doc_label; ?></label>
                                                                        </td>
                                                                        <td><?php
            if ($m == 4) {
                                                                    
//echo '<pre>';
//print_r($self_impacted_days4->no_of_days);
//echo '</pre>';
                                                                    echo '   <input type="hidden" name="other_time_of_days[]" value="other_time_off_days_april_2020">
                                            <input type="hidden" name="other_title[]" value="Time off Days between April 1, 2020, and December 31, 2020">
                                        <input type="number" name="other_no_of_days[]" value="' . $self_impacted_days4->no_of_days . '" class="self-no-days no-of-days" id="days-274" data-max-days="274">';
                                                                }
                                                                if ($m == 5) {
                                                                    echo '    <input type="hidden" name="other_time_of_days[]" value="other_time_off_days_january_2021">
                                            <input type="hidden" name="other_title[]" value="Time off Days between January 1, 2021, and March 31, 2021">
                                        <input type="number" name="other_no_of_days[]" value="' . $self_impacted_days5->no_of_days . '" class="self-no-days no-of-days" id="days-90" data-max-days="90">';
                                                                }
                                                                if ($m == 6) {
                                                                    echo '    <input type="hidden" name="other_time_of_days[]" value="other_time_off_days_april_2021">
                                            <input type="hidden" name="other_title[]" value="Time off Days between April 1, 2021, and September 30, 2021">
                                        <input type="number" name="other_no_of_days[]" value="' . $self_impacted_days6->no_of_days . '" class="self-no-days no-of-days" id="days-182" data-max-days="182">';
                                                                }
            ?></td>
                                                                        
<!--                                                                        <td class="unemp_benefit_availed_col" style="text-align: center;"><?php
                                                            
                                                              if ($m == 4) {
                                                              echo ucfirst($self_impacted_days4->unemp_benefit_claimed);
                                                              }
                                                              if ($m == 5) {
                                                              echo ucfirst($self_impacted_days5->unemp_benefit_claimed);
                                                              }
                                                              if ($m == 6) {
                                                              echo ucfirst($self_impacted_days6->unemp_benefit_claimed);
                                                              } 
                                                            ?></td>-->
                                                                        
                                                                        <?php if ($file_url != '') {
            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
            $file_status = $status_data[0]->doc_status;
            ?>
            <td style="text-align: center;">
                <label class="file_remove_label">
                <span class="material-symbols-outlined">download_done</span>
                <a class="text_overflow_ellipse"
                   <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                            class="custom_file"><?php echo $file_name; ?>
                </a> &nbsp;<span
                        class="material-symbols-outlined cancel_icon"
                        data-doc_id="<?php echo $doc_id; ?>"
                        data-folder_name="<?php echo $parent_folder; ?>"
                        data-lead_id="<?php echo $lead_id; ?>"
                        data-doc_key="<?php echo $doc_key; ?>"
                        data-file_name="<?php echo $file_url; ?>"
                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                        style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                </label>
            </td>
            <td>
                <select name="change_document_status"
                        id="change_stc_document_status" data-index="<?php echo $n;?>"
                        class="change_doc change_doc_<?php echo $n;?>" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                    <option value="">- Select Status -</option>
                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                            data-doc_id="<?php echo $doc_id; ?>"
                            data-doc_type_id="<?php echo $doc_type_id; ?>">In
                        review
                    </option>
                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                            data-doc_id="<?php echo $doc_id; ?>"
                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                            data-doc_id="<?php echo $doc_id; ?>"
                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                </select>
            </td>
            <td style="text-align: center;">
                <?php if (!empty($comment_data)) { ?>
                    <?php if($is_affiliate == 0){?>
                        <span class="add_stc_comment_btn"
                              data-doc_id="<?php echo $doc_id; ?>"
                              data-doc_type_id="<?php echo $doc_type_id; ?>"
                              title="Add">
                            <i class="fa-solid fa-comment-medical"></i>
                        </span>
                    <?php } ?>
                    <span class="view_stc_comment"
                          data-doc_type_id="<?php echo $doc_type_id; ?>"
                           data-doc_id="<?php echo $doc_id; ?>"
                          title="View">
                          <i class="fa-regular fa-comment"></i>
                    </span>
                <?php } ?>

            </td>
                                                                        <?php } else { ?>
            <td>
                <?php if($file_url == ''){
                    ?>
                    <label class="custom-file-upload" >
                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                    </label>
                    <label class="file_remove_label" style="display:none;">
                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                    </label>
                <?php }else{
                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                    $file_status = $status_data[0]->doc_status;
                    ?>
                    <label class="custom-file-upload" style="display:none;">
                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="stc_custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-lead_id="<?php echo $lead_id ;?>" data-product_id="<?php echo $project->product_id;?>">Upload
                    </label>
                    <label class="file_remove_label" >
                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_affiliate == 0){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                    </label>
                <?php } ?>
            </td>
            
            <td style="text-align: center;">'
                <span class="not_uploaded">Yet to upload</span>
                <select name="change_document_status" id="change_stc_document_status" data-index="<?php echo $n;?>" class="change_doc change_doc_<?php echo $n;?>" style="display: none;">
                    <option value="">- Select Status -</option>
                    <option value="In review" data-doc_id="" data-doc_type_id="">In review</option>
                    <option value="Approved" data-doc_id="" data-doc_type_id="">Approve</option>
                    <option value="Rejected" data-doc_id="" data-doc_type_id="">Reject</option>
                </select>
            </td>
            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                    <span class="view_stc_comment"
                          data-doc_type_id="<?php echo $doc_type_id; ?>"
                           data-doc_id="<?php echo $doc_id; ?>"
                          title="View">
                      <i class="fa-regular fa-comment"></i>
                      </span>
                <?php } ?>   
            </td>
                                                                        <?php } ?>
                                                                    </tr>
                                                                    
                                                                    <tr <?php
                                                    if ($m == 4) {

                                                      if (esc_html($self_impacted_days4->unemp_benefit_claimed) == 'yes') {
                                                      //echo 'style="display:inline-block"';
                                                      }else{
                                                      echo 'style="display:none"';
                                                      }

                                                      }
                                                      if ($m == 5) {

                                                      if (esc_html($self_impacted_days5->unemp_benefit_claimed) == 'yes') {
                                                      //echo 'style="display:inline-block"';
                                                      }else{
                                                      echo 'style="display:none"';
                                                      }
                                                      }
                                                      if ($m == 6) {

                                                      if (esc_html($self_impacted_days6->unemp_benefit_claimed) == 'yes') {
                                                      //echo 'style="display:inline-block"';
                                                      }else{
                                                      echo 'style="display:none"';
                                                      }
                                                      } 
                                                    ?>>
                <td class="first_column">
                    <label class="questions_Q">Number of days for which Unemployment Benefit was availed for the above mentioned time period.</label>
                </td>
                <td>
                    
                                                    <?php 
                                                      if ($m == 4) {
                                                      echo $self_impacted_days4->no_of_availed_days;
                                                      }
                                                      if ($m == 5) {
                                                      echo $self_impacted_days5->no_of_availed_days;
                                                      }
                                                      if ($m == 6) {
                                                      echo $self_impacted_days6->no_of_availed_days;
                                                      }
                                                    ?>
                </td>
                <td></td>
                <td></td>
                <td></td>
                <td></td>
            </tr>
            
                                                                    <!--others undertaking-->
                                                        <tr>
                                                                <?php
                                                                if ($m == 6) {
                                                                    $other_time_off_days_april_2020_undertaking = '';
                                                                    if ($self_impacted_days4->undertaking == 1) {
                                                                        $other_time_off_days_april_2020_undertaking = 'checked';
                                                                    }
                                                                    ?>
<td class="datetimepicker10 2020_Q2_Q3_Q4_row" colspan="6">
                                                                    <div class="d-flex addmore-checkbox datecontainer">
                                                              <button id="addMoreDateBtn" class="add-more-date" style="display:contents"><i class="fa-solid fa-plus"></i> Add More Dates</button>

                                                                        <div class="form-check checkbox-type">

                                                                            <input class="form-check-input" type="checkbox" value="" name="undertaking_other_time_off_days_april_2020" id="flexCheckDefault4" <?php echo $other_time_off_days_april_2020_undertaking; ?> >
                                                                            <label class="form-check-label" for="flexCheckDefault4">
                                                                                I did not claim unemployment benefits on these dates
                                                                            </label>
                                                                        </div>

                                                                    </div>
                                                                    </td>
                                                                <?php }
                                                                ?>

                                                            
                                                        </tr>
                                                        <!--others undertaking end-->
                                                                <?php $n++; }
                                                                $m++;
                                                                }
                                                                ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <!-- For OPS user table END -->
                                                </div>
                                            <?php if(isset($lead_id)) { ?>
                                                            <div class="button_next_prev">
                                                                <input type="submit" name="selfdocumentsubmit_impacted" id="selfdocumentsubmit" class="associatetsubmit nxt_btn" value="Submit">
                                                            </div>
                                                        <?php } ?>
                                            </form><!-- comment -->
                                            </div>
                                        
                                        </div>
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                        
                                    </div>
                                    
                                </div>

                                <div class="row">
                                    <?php 
                                    if ($project->contact_id > 0) {
                                        // Replace 'your_table_name' with the actual table name where your data is stored
                                    
                                        $contact_id = $project->contact_id;

                                        $query_new = $wpdb->prepare("
                                            SELECT 
                                                c.id AS contact_id,
                                                cl.lead_id,
                                                CONCAT(c.first_name, ' ', c.last_name) AS name,
                                                c.title,
                                                c.contact_type,
                                                (
                                                    SELECT 
                                                        p.phone
                                                    FROM 
                                                        {$wpdb->prefix}op_phone_contact AS pc
                                                    INNER JOIN 
                                                        {$wpdb->prefix}op_phone AS p ON pc.phone_id = p.id
                                                    WHERE 
                                                        pc.contact_id = c.id
                                                    ORDER BY 
                                                        p.created_date DESC
                                                    LIMIT 1
                                                ) AS phone,
                                                (
                                                    SELECT 
                                                        e.email
                                                    FROM 
                                                        {$wpdb->prefix}op_email_contact AS ec
                                                    INNER JOIN 
                                                        {$wpdb->prefix}op_emails AS e ON ec.email_id = e.id
                                                    WHERE 
                                                        ec.contact_id = c.id
                                                    ORDER BY 
                                                        e.created_date DESC
                                                    LIMIT 1
                                                ) AS email,
                                                c.trash 
                                            FROM 
                                                {$wpdb->prefix}op_contacts AS c
                                            LEFT JOIN 
                                                {$wpdb->prefix}op_contact_lead AS cl ON c.id = cl.contact_id
                                            WHERE 
                                                c.id = %d
                                            ORDER BY 
                                                c.trash ASC, c.id DESC
                                        ", $contact_id);

                                        $query_t = $wpdb->get_results($query_new);

                                        if (!empty ($query_t)) {
                                            $con_data = $query_t[0];

                                            echo do_shortcode('[CommLog_activity_by_lead lead_id="0" lead_phone="' . $con_data->phone . '" lead_email="' . $con_data->email . '" ] ');
                                        }

                                    }
                                    ?>
                                    <div class="custom-opportunity-notes">
                                        <div class="col-sm-12">
                                            <h5>Notes
                                                <?php if($is_affiliate == 0){?>
                                                    <a href="javascript:void(0)" class="opp-add-notes"><i class="fa fa-plus-circle create-note-btn"></i></a>
                                                <?php } ?>
                                            </h5>
                                        </div>
                                        <div class="col-sm-12 custom-opp-notes-scroll">
                                            <div class="notes-listing">
                                                <?php
                                                $i = 1;
                                                $from = 'UTC';
                                                $to = 'America/New_York';
                                                $format = 'Y-m-d h:i:s A';
                                                foreach ($all_notes as $n_key => $n_value) {
                                                    $date = $n_value->created; //UTC time
                                                    date_default_timezone_set($from);
                                                    $newDatetime = strtotime($date);
                                                    date_default_timezone_set($to);
                                                    $newDatetime = date($format, $newDatetime);
                                                    date_default_timezone_set('UTC');
                                                    $datetime = date_create($newDatetime);
                                                    $time = date_format($datetime, "h:ia");
                                                    $day = date_format($datetime, " D ");
                                                    $month = date_format($datetime, " M ");
                                                    $date = date_format($datetime, "dS,");
                                                    $year = date_format($datetime, " Y");
                                                    $actual_date = $time . " on " . $day . $month . $date . $year;
                                                    $notes = $n_value->note;
                                                ?>
                                                    <div class="note-listing-div shadow">
                                                        <p class="notes" id="<?= $n_value->id; ?>"><?= $notes; ?></p>
                                                        <p class="date-time">(<?= $actual_date; ?>)</p>
                                                    </div>
                                                <?php $i++;
                                                } ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                            </div>

                            <div class="col-md-4">
                                <div class="custom-opportunity-view-sidebar">
                                    <?php if($is_affiliate == 0){?>
                                        <div class="row edit-icon-milestone">
                                            <a href="javascript:void(0)" title="Edit"><i class="fa-regular fa-pen-to-square edit_project_milestone" style="font-weight: 700"></i></a>
                                        </div>
                                    <?php } ?>
                                    <div class="show_message_milestone"></div>
                                    <div class="d-flex opp-owner mb-3 ">
                                        <label><b>Milestone:</b></label>
                                        <p id="showMilestone"><?php echo ucwords($project->milestone_name);
                                        $MilestoneIssue = '';
                                        if($project->milestoneActiveStatus == 'inactive'){
                                            $MilestoneIssue = 'Inactive';
                                        }
                                        if($project->milestone_deletedat != ''){
                                            $MilestoneIssue = 'Deleted';
                                        }
                                        $milestoneMmap = unserialize($project->milestoneMap);
                                        if(empty($milestoneMmap)){
                                            $MilestoneIssue = 'Unassigned';
                                        }elseif(!in_array("project", $milestoneMmap)){
                                            $MilestoneIssue = 'Unassigned';
                                        }
                                        if(!empty($MilestoneIssue)){
                                            echo '('.$MilestoneIssue.')';
                                        }
                                        ?> 
                                        </p>
                                        <select class="form-control custom-mile-status select-milestone" id="Milestone" on='23' style="display: none;">
                                            <?php
                                            if (count($all_milestones) > 0) {
                                                foreach ($all_milestones as $all_milestone) {
                                                    $milestone_id = $all_milestone->milestone_id;
                                                    $MilestoneIssue = '';
                                                    if($all_milestone->status == 'inactive'){
                                                        $MilestoneIssue = 'Inactive';
                                                    }
                                                    if($all_milestone->deleted_at !== null){
                                                        $MilestoneIssue = 'Deleted';
                                                    }
                                                    $milestoneMmap = unserialize($all_milestone->map);

                                                    if(empty($milestoneMmap)){
                                                        $MilestoneIssue = 'Unassigned';
                                                    }elseif(!in_array("project", $milestoneMmap)){
                                                        $MilestoneIssue = 'Unassigned';
                                                    }

                                                    $milestone_name = '';
                                                    $MilestoneID = '';
                                                    if(!empty($MilestoneIssue)){
                                                        $milestone_name = $all_milestone->milestone_name.'('.$MilestoneIssue.')';
                                                        $MilestoneID = '';
                                                    }else{
                                                        $milestone_name = $all_milestone->milestone_name;
                                                        $MilestoneID = $all_milestone->milestone_id;
                                                    }
                                                    $sel = '';
                                                    if ($project->milestone_id == $milestone_id) {
                                                        $sel = 'selected';
                                                    } else {
                                                        $sel = '';
                                                    }
                                                    echo '<option value="' . $MilestoneID . '" ' . $sel . '>' . ucwords($milestone_name) . '</option>';
                                                }
                                            } else {
                                            ?>
                                                <option value="">Select Milestone</option>
                                            <?php
                                            }
                                            ?>
                                        </select>
                                    </div>

                                    <div class="d-flex opp-owner">
                                        <label><b>Stage:</b></label>
                                        <p id="showMilestoneStage">
                                            <?php 
                                                echo ucwords($project->milestoneStatus);
                                                $StageIssue = '';
                                                if($project->StageActiveStatus == 'inactive'){
                                                    $StageIssue = 'Inactive';
                                                }
                                                if($project->StageDeleteStatus != ''){
                                                    $StageIssue = 'Deleted';
                                                }
                                                if(!empty($StageIssue)){
                                                    echo '('.$StageIssue.')';
                                                }    
                                            ?> 
                                        </p>
                                        <select class="form-control custom-mile-status" id="MilestoneStage" name='milestone_status-23' style="display: none;">
                                            <?php
                                            if (count($all_milestone_status) > 0) {
                                                foreach ($all_milestone_status as $allmilestonestatus) {
                                                    $milestone_stage_id = $allmilestonestatus->milestone_stage_id;
                                                    $sel = '';
                                                    if($allmilestonestatus->status == 'inactive'){
                                                        $StageIssue = 'Inactive';
                                                    }
                                                    if($allmilestonestatus->deleted_at !== null){
                                                        $StageIssue = 'Deleted';
                                                    }
                                                    $stageID = '';
                                                    $stage_name = '';
                                                    if(!empty($StageIssue)){
                                                        $stage_name = $allmilestonestatus->stage_name.'('.$StageIssue.')';
                                                        $stageID = '';
                                                    }else{
                                                        $stage_name = $allmilestonestatus->stage_name;
                                                        $stageID = $allmilestonestatus->milestone_stage_id;
                                                    }
                                                    if ($project->milestone_stage_id == $milestone_stage_id) {
                                                        $sel = 'selected';
                                                    } else {
                                                        $sel = '';
                                                    }
                                                    echo '<option value="' . $milestone_stage_id . '" ' . $sel . '>' . ucwords($stage_name) . '</option>';
                                                }
                                            } else {
                                            ?>
                                                <option value="">Select Stage</option>
                                            <?php
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <?php if($is_affiliate == 0){?>
                                    <div class="row">
                                        <div class="button-container milestone_button_container" style="display: none;">
                                            <button type="submit" class="create_product_btn update_milestone" data-projectID="<?php echo $project->project_id; ?>"  data-milestoneid = "<?php echo $project->milestone_id; ?>"  data-milestonestageid = "<?php echo $project->milestone_stage_id; ?>">Update</button>
                                            <button type="button" class="create_product_btn cancel_milestone">Cancel</button>
                                        </div>
                                    </div>
                                <?php } ?>
                                </div>
                                <div class="custom-opportunity-view-sidebar assign_collaborators_section">  
                                    <?php if($is_affiliate == 0){?>  
                                        <div class="row edit-icon-owner">
                                            <a href="javascript:void(0)" title="Edit"><i class="fa-regular fa-pen-to-square edit_project_collaborator" style="font-weight: 700"></i></a>
                                        </div>
                                    <?php } ?>
                                     <input type="hidden" id="show_collaborators" value="0">
                                    <div class="d-flex opp-owner">
                                        <label><b>Assigned Collaborators:</b></label>   
                                    </div>
                                    <div class="all_assigned_users">
                                        <?php
                                        $unsigned_user = [];
                                        foreach ($collaborators as $collaborator) {
                                            $user_data = get_user_by('id',$collaborator->user_id); 
                                            $user_roles = $user_data->roles;
                                            $user_role = '';
                                            if(isset($user_roles[0])){
                                                $userrole = $user_roles[0];
                                                if($userrole == 'echeck_staff'){
                                                    $user_role = ' (Echeck Staff)';
                                                }
                                                elseif($userrole == 'echeck_admin'){
                                                    $user_role = ' (Echeck Admin)';
                                                }
                                                elseif($userrole == 'master_ops'){
                                                    $user_role = ' (Master Ops)';
                                                }
                                                elseif($userrole == 'master_sales'){
                                                    $user_role = ' (Master Sales)';
                                                }
                                                elseif($userrole == 'iris_sales_agent'){
                                                    $user_role = ' (Sales Agent)';
                                                }
                                                elseif($userrole == 'iris_sales_agent_rep'){
                                                    $user_role = ' (Sales Agent Rep)';
                                                }
                                                elseif($userrole == 'iris_affiliate_users'){
                                                    $user_role = ' (Affiliate)';
                                                }
                                                elseif($userrole == 'iris_employee'){
                                                    $user_role = ' (Employee)';
                                                }
                                            }
                                            $unsigned_user[count($unsigned_user)] = $collaborator->user_id;
                                            ?>
                                            <p class='col-form-label assign_users' id="assign-users<?= $collaborator->user_id ?>"><?php echo $user_data->data->display_name.$user_role; ?> 
                                                <?php if($is_affiliate == 0){?>
                                                    <span unassign-user="<?= $collaborator->user_id ?>" unassign-user-name="<?= $user_data->data->display_name ?>" class="unassign-user glyphicon glyphicon-minus-sign" style="padding: 0 5px;
                                                    cursor: pointer;"></span>
                                                <?php } ?>
                                            </p>
                                            <?php
                                        }
                                        ?>
                                        <div class="show_message_collaborator"></div> 
                                        <select <?= $disabled ?> class="user-select form-control" name="assigned_user_id" id="assign_collaborator" style="display: none;" >
                                            <option value="">Select Collaborator to Assign</option>
                                            <?php foreach ($listed_users as $user) {
                                                if (in_array($user->ID,$unsigned_user)) {
                                                } else {?>
                                                <option id="user-option<?= $user->ID ?>" value="<?= $user->ID ?>"><?= $user->display_name; ?></option>;  
                                            <?php }} ?>
                                        </select>
                                    </div>
                                    <?php if($is_affiliate == 0){?>
                                    <div class="row">
                                        <div class="button-container collaborator_button_container" style="display: none;">
                                            <button type="button" class="create_product_btn assign-user-btn" id="assign-user-1">Assign Collaborator</button>
                                            <button type="button" class="create_product_btn cancel_collaborator">Cancel</button>
                                        </div>
                                    </div>
                                    <?php } ?>
                                    <!-- <button type="button" class="btn btn-primary assign-user-btn" id="assign-user-1" style="display:none;">Assign Collaborator</button> -->
                                </div>

                                <div class="custom-opportunity-view-sidebar">
                                    <?php if($is_affiliate == 0){?>
                                        <div class="row edit-icon-owner">
                                            <a href="javascript:void(0)" title="Edit"><i class="fa-regular fa-pen-to-square edit_project_contact" style="font-weight: 700"></i></a>
                                        </div>
                                    <?php } ?>
                                    <div class="show_message_contact"></div>
                                    <div class="d-flex opp-owner">
                                        <label><b>Select Contact:</b></label>
                                        <p id="showContactList"><?php echo ucwords($primary_contact); ?></p>
                                        <select class="form-control custom-contactList" style="display: none;" id="ContactList">
                                            <?php
                                            if (count($all_contacts) > 0) {
                                                foreach ($all_contacts as $all_contact) {
                                                    $contact_id = $all_contact->id;
                                                    $sel = '';
                                                    if ($project->contact_id == $contact_id) {
                                                        $sel = 'selected';
                                                    } else {
                                                        $sel = '';
                                                    }
                                                    $contact_name = $all_contact->first_name . ' ' . $all_contact->last_name;
                                                    echo '<option value="' . $contact_id . '" ' . $sel . '>' . $contact_name . '</option>';
                                                }
                                            } else {
                                            ?>
                                                <option value="">Select Contact</option>
                                            <?php
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <?php if($is_affiliate == 0){?>
                                    <div class="row">
                                        <div id="userDetails" data-id='<?php echo get_current_user_id();  ?>' data-name='<?php echo $login_user_name; ?>' style="display:none;"></div>
                                        <div class="button-container contact_button_container" style="display: none;">
                                            <button type="submit" class="create_product_btn update_contacts" data-projectID="<?php echo $project->project_id; ?>" data-contactid = "<?php echo $project->contact_id; ?>">Update</button>
                                            <button type="button" class="create_product_btn cancel_contact">Cancel</button>
                                        </div>
                                    </div>
                                    <?php } ?>
                                </div>
                            </div>

                        </div>

                        
                    </div>
                </div>
            </div>
        </div>
    </div><!-- .site-main -->
</div><!-- .content-area -->
<div class="modal fade" id="exampleModal_otherdoc_adding_comment" tabindex="-1" role="dialog"
     aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Comments</h5>
                <button type="button" class="close-popup-appr-reject close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <input type="hidden" name="doc_id_comment" id="doc_id_comment" class="doc_id_comment">
                        <input type="hidden" id="index" value="">
                        <input type="hidden" name="doc_type_id_comment" id="doc_type_id_comment"
                               class="doc_type_id_comment">
                        <textarea name="comment" id="other-comment-textarea"
                                  data-doc_id="<?php echo $result[0]->doc_id; ?>" maxlength="100"
                                  placeholder="Only 100 characters are allowed"
                                  style="width: 100%;height: 150px;resize:none"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" id="stc-submit-comment-btn" class="btn btn-primary">Submit</button>
            </div>
        </div>
    </div>
</div>
<div class="modal opportunity-add-new-notes" id="add-new-opp-notes" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-comment"></i> Notes</h5>
                <button type="button" class="close-popup-notes close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="show_notes_message">
                    <p class="note-response" style="display: none;"></p>
                    <p class="error-response" style="display: none;">Notes is required.</p>
                </div>

                <div class="row">
                    <div class="floating col-md-12">
                        <label>Notes:</label>
                        <textarea id="notes-input" class="form-control" rows="5" maxlength="300"></textarea>
                    </div>
                </div>
                <div class="buttion_next_prev">
                    <button type="button" class="nxt_btn" id="create-note">Submit</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Modal -->
<div class="modal fade" id="exampleModal_ercdoc_view" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Comments</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height:200px; overflow-y:auto;">
                <input type="hidden" name="view_stc_comment_id" class="view_stc_comment_id" id="view_stc_comment_id">
                <span class="comment_username" style="font-size:14px">User Name</span>
                <p class="mt-2">consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
                    aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                    commodo consequat.</p>
                <span class="comment_date" style="float: right; font-size:12px">Comment Date:- 05-12-2023</span>
                <br>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-warning">View Log</button>
            </div>
        </div>
    </div>
</div>
<div class="modal taxnow-signup-doc-popup" data-bs-backdrop="static" data-bs-keyboard="false" role="dialog"
     tabindex="-1"
     role="dialog" aria-labelledby="confirm-modal-label" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">TaxNow Information</h5>
                <button type="button" class="close-popup-taxnow close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>

            <div class="modal-body">
                <p class="taxnow-signp">What is TaxNow? <a
                            href="<?php echo get_site_url(); ?>/wp-content/plugins/doc_upload/public/pdf/What_is_TaxNow.pdf"
                            style="color: #007bff;text-decoration: underline;font-weight: 600;" target="_blank"><span
                                class="add-taxnow-btn"><i class="fa fa-info-circle"></i></span></a></p>
                <p class="taxnow-signp">TaxNow Registration Steps <a
                            href="<?php echo get_site_url(); ?>/wp-content/plugins/doc_upload/public/pdf/TaxNow_SignUp_Steps.pdf"
                            style="color: #007bff;text-decoration: underline;font-weight: 600;" target="_blank"><span
                                class="add-taxnow-btn"><i class="fa fa-info-circle"></i></span></a></p>
                <p class="taxnow-signp">Client Guide <a
                            href="<?php echo get_site_url(); ?>/wp-content/plugins/doc_upload/public/pdf/Taxnow_New_Client_Guide.pdf"
                            style="color: #007bff;text-decoration: underline;font-weight: 600;" target="_blank"><span
                                class="add-taxnow-btn"><i class="fa fa-info-circle"></i></span></a></p>
            </div>
        </div>
    </div>
</div>
<style type="text/css">
    /* .modal-header .close-popup-appr-reject {
      padding: 0px !important;
      margin: 0px !important;
      background-color:transparent;
      border: 0;
      font-size: 20px !important;
      border-radius: 10px !important;
      font-weight: 700 !important;
      height: 32px !important;
      opacity: 1 !important;
      line-height: 30px !important;
      float: right;
      color: #000;
      text-shadow: 0 1px 0 #fff;
    } */
    #exampleModal_otherdoc_adding_comment .modal-title, #exampleModal_ercdoc_view .modal-title {
        margin-top: 5px;
        font-weight: 700;
        color: #1261ab;
        font-size: 17px;
    }
    #exampleModal_ercdoc_view .modal-header .close, #exampleModal_otherdoc_adding_comment .modal-header .close {
        position: absolute;
        right: -15px;
        top: -15px;
        background: red;
        text-align: center;
        width: 28px;
        height: 28px !important;
        cursor: pointer;
        border-radius: 50% !important;
        line-height: 10px !important;
        color: #fff;
        opacity: 1 !important;
        padding: 0 !important;
        margin: 0 !important;
        text-shadow: none !important;
    }
    #exampleModal_otherdoc_adding_comment .modal-header .close span, #exampleModal_ercdoc_view .modal-header .close span {
        display: block;
        margin-top: -4px;
    }
</style>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"
        integrity="sha384-9/reFTGAW83EW2RDu2S0VKaIzap3H66lZH81PoYlFhbGU+6BZp6G7niu735Sk7lN"
        crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/js/bootstrap.min.js"
        integrity="sha384-w1Q4orYjBQndcko6MimVbzY0tgp4pWB4lZ7lr30WKz0vr/aWKhXdBNmNb5D92v7s"
        crossorigin="anonymous"></script>
        
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-daterangepicker/3.0.5/daterangepicker.min.js"></script>

<script>
    jQuery(document).ready(function() {
        function getFileFormatsString(fileExtension) {
            var formats = fileExtension.slice(0, -1).join(', ');
            var lastFormat = fileExtension[fileExtension.length - 1];
            return formats + (fileExtension.length > 1 ? ', and ' + lastFormat : lastFormat);
        }
        function validateSize(input, fileExtension) {
            var file = input.files[0];
            var size = file.size / 1024;
            var ext = input.value.split('.').pop().toLowerCase();
            var file_name = file.name;
            var file_length = file_name.length;
            if (file_length > 50) {
                swal("Error", "File name should not exceed 50 characters", "error");
                return false;
            }
            if ($.inArray(ext, fileExtension) == -1) {
                swal("Error", "Only " + getFileFormatsString(fileExtension) + " file formats are accepted", "error");
                input.value = "";
                return false;
            }
            if (size > 20240) {
                swal("Error", "Please upload a file up to 20 MB.", "error");
                input.value = "";
                return false;
            }
            return true;
        }
        $('.stc_custom_button').on('change', function () {
            var input = $(this);
            var name = this.name;
            var doc_type_id = $(this).data('doc_type_id');
            var product_id = $(this).data('product_id');
            var lead_id = $(this).data('lead_id');
            var parent_folder = $(this).data('parent_folder');
            var file = this.files[0];

            var formdata = new FormData();
            formdata.append('file', file);
            formdata.append('action', 'handle_stc__file_upload');
            formdata.append('doc_key', name);
            formdata.append('parent_folder', parent_folder);
            formdata.append('doc_type_id', doc_type_id);
            formdata.append('lead_id', lead_id);
            formdata.append('product_id', product_id);


            var file_type = $(this).data('file_type');

            if (file_type == 'excel') {
                var fileExtension = ['xls', 'xlsx'];
            } else if (file_type == 'word') {
                var fileExtension = ['jpeg', 'jpg', 'png', 'pdf', 'zip', 'doc', 'docx'];
            } else {
                var fileExtension = ['jpeg', 'jpg', 'png', 'pdf', 'zip', 'doc', 'docx','xls', 'xlsx'];
            }


            if (!validateSize(this, fileExtension)) {
                return false;
            } else {
                $(input).closest(".custom-file-upload").prepend('<i aria-hidden="true" class="fa fa-spinner fa-spin"></i>');
                $(input).closest(".custom-file-upload").find('.material-symbols-outlined').css('display', 'none');
                $.ajax({
                    url: doc_upload.ajaxurl,
                    type: 'POST',
                    data: formdata,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        var data = JSON.parse(response);
                        var uploadok = data.uploadok;

                        if (uploadok == 'false') {
                            swal("Error", data.error, "error").then(function () {
                                location.reload();
                            });
                        } else {

                            var message = data.message;
                            var file_url = data.file_url;
                            var pdf_filename = data.pdf_filename;
                            var parent_folder = data.parent_folder;
                            var lead_id = data.lead_id;
                            var doc_key = data.doc_key;
                            var doc_id = data.doc_id;
                            var doc_type_id = data.doc_type_id;
                            var onedrive_link = data.onedrive_link
                            // code to change next label by class html
                            $(input).closest(".custom-file-upload").css('display', 'none');
                            $(input).closest("td").next("td").find('span.progress_status').css('display', 'block');
                            $(input).closest("td").next("td").find('span.not_uploaded').css('display', 'none');
                            
                            $(input).closest(".custom-file-upload").find('.fa-spinner').css('display', 'none');
                            $(input).closest(".custom-file-upload").find('.material-symbols-outlined').css('display', 'block');
                            $(input).closest(".custom-file-upload").next(".file_remove_label").css('display', 'flex');
                            $(input).closest(".custom-file-upload").next(".file_remove_label").html('<span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="' + file_url + '" target="_blank"><span class="custom_file">' + pdf_filename + '</span></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-folder_name="' + parent_folder + '" data-lead_id="' + lead_id + '"  data-doc_key="' + doc_key + '" data-doc_id="' + doc_id + '" data-doc_type_id="' + doc_type_id + '" data-file_name="' + pdf_filename + '" data-onedrive_link="' + onedrive_link + '" style="display:none">cancel</span>');
                            swal("Success", "Document uploaded successfully", "success").then(function () {
                                $(input).closest("td").next("td").find('.change_doc').removeAttr('style');
                                $(input).closest("td").next("td").find('.change_doc option').attr('data-doc_id',doc_id);
                                $(input).closest("td").next("td").find('.change_doc option').attr('data-doc_type_id',doc_type_id);
                                $(input).closest("td").next("td").find('.change_doc option[value="In review"]').attr('selected','selected');
                                //location.reload();
                            });
                        }
                    }
                });
            }
        });
        jQuery(document).on('change', '#change_stc_document_status', function () {
            var input = $(this);
            var value = $(this).val();
            var input = $(this);
            var index = $(this).data('index');
            var doc_id = $(this).find("option:selected").data('doc_id');
            var doc_type_id = $(this).find("option:selected").data('doc_type_id');
            var label = $(this).closest('tr').find('label.questions_Q').text().trim();
            var reason = '';
            if (value == 'Approved') {

                var text = "Are you sure you want to approve this document? You will not be able to reject it later!";
                var icon = "success";

            } else if (value == 'Rejected') {

                var text = "Are you sure you want to reject this document?";
                var icon = "error";

            } else if (value == 'In review') {
                var text = "Are you sure you want to change the status of this document to In review again?";
                var icon = "warning";
            } else if (value == '' || value == 'Select Status') {
                swal("Error", "Please select a status", "error");
                return false;
            }
            if (value == 'In review') {
                //pop up
                swal({
                    title: "",
                    text: text,
                    icon: icon,
                    buttons: true,
                    dangerMode: true,
                })
                    .then((willDelete) => {
                        
                        if (willDelete) {

                            $.ajax({
                                url: doc_upload.ajaxurl,
                                type: 'POST',
                                data: {
                                    action: 'update_stc_document_status',
                                    value: value,
                                    doc_id: doc_id,
                                    doc_type_id: doc_type_id,
                                    label: label,
                                    reason: reason
                                },
                                success: function (response) {
                                    swal("Success", "Document status updated successfully", "success").then(function () {
                                        //location.reload();
                                    });
                                }
                            });

                        } else {

                            swal("Document status not changed!").then(function () {
                                //location.reload();
                            });
                        }
                    });
            }

            if (value == 'Rejected' || value == 'Approved') {
                $.ajax({
                    url: doc_upload.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'check_rejected_reason',
                        doc_id: doc_id,
                        doc_type_id: doc_type_id,
                        value: value,
                    },
                    success: function (response) {

                        var data = JSON.parse(response);
                        var uploadok = data.uploadok;

                        if (uploadok == 'false') {

                            $(input).closest('tr').find('span.comment_btn').trigger('click');

                            if (value == 'Rejected') {

                                var modal_title = 'Please add a comment before rejecting the document.';
                                var modal_btn = 'Reject';
                            }
                            else if (value == 'Approved') {

                                var modal_title = 'Please add a comment before approving the document.';
                                var modal_btn = 'Approve';
                            }

                            $('#exampleModal_otherdoc_adding_comment .modal-header h5').html(modal_title);
                            $("#exampleModal_otherdoc_adding_comment #doc_id_comment").val(doc_id);
                            $("#exampleModal_otherdoc_adding_comment #doc_type_id_comment").val(doc_type_id);
                            $('#exampleModal_otherdoc_adding_comment #index').val(index);
                            $('#exampleModal_otherdoc_adding_comment .modal-footer').html('<input type="hidden" class="comment_status" value="' + value + '"></input> <button type="button" class="btn btn-secondary ts-close-modal" data-dismiss="modal">Close</button><button type="button" class="btn btn-primary add_approved_project_rejected_comment_btn">' + modal_btn + '</button>');
                            $("#exampleModal_otherdoc_adding_comment").addClass('in');
                            $("#exampleModal_otherdoc_adding_comment").modal('show');
                            $('.ts-close-modal').on('click', function () {
                                //location.reload();
                                $("#exampleModal_otherdoc_adding_comment").modal('hide');
                            });
                            return true;

                        } else {
                            swal({
                                title: "",
                                text: text,
                                icon: icon,
                                buttons: true,
                                dangerMode: true,
                            })
                                .then((willDelete) => {
                                    if (willDelete) {

                                        $.ajax({
                                            url: doc_upload.ajaxurl,
                                            type: 'POST',
                                            data: {
                                                action: 'update_stc_document_status',
                                                value: value,
                                                doc_id: doc_id,
                                                doc_type_id: doc_type_id,
                                                label: label,
                                                reason: reason
                                            },
                                            success: function (response) {
                                                swal("Success", "Document status updated successfully", "success").then(function () {
                                                    $(this).attr('disabled',true);
                                                    //location.reload();
                                                });
                                            }
                                        });

                                    } else {

                                        swal("Document status not changed!").then(function () {
                                            //location.reload();
                                        });
                                    }
                                });
                            reason = data.reason;
                        }

                    }
                });
            }
            $('#exampleModal_otherdoc_adding_comment .close').on('click', function () {
                $("#exampleModal_otherdoc_adding_comment").modal('hide');
                //location.reload();
            });
        });
        $(document).on('click', '.add_approved_project_rejected_comment_btn', function () {
            var btn_value = $(this).text();
            var comment = jQuery('#other-comment-textarea').val();
            var doc_id = jQuery('#doc_id_comment').val();
            var doc_type_id = jQuery('#doc_type_id_comment').val();
            var index = jQuery('#index').val();
            var comment_status = jQuery('.comment_status').val();
            if (comment == '' || comment == ' ' || comment == null) {
                jQuery("#other-comment-textarea").css("border", "1px solid red");
                jQuery("#other-comment-textarea").focus();
                jQuery("#other-comment-textarea").attr("placeholder", "Please enter comment");
                jQuery("#other-comment-textarea").next().remove();
                jQuery("#other-comment-textarea").after('<span class="error">Please enter comment</span>');
                return false;
            }
            $(this).text('Please wait..');
            $(this).attr('disabled',true);
            $.ajax({
                url: doc_upload.ajaxurl,
                type: 'POST',
                data: {
                    action: 'approved_rejected_comment_data',
                    comment: comment,
                    doc_id: doc_id,
                    doc_type_id: doc_type_id,
                    comment_status: comment_status
                },
                success: function (response) {
                    var data = JSON.parse(response);
                    var uploadok = data.uploadok;
                    var reason = data.reason;
                    var label = data.label;
                    if (uploadok == 'true') {
                        $.ajax({
                            url: doc_upload.ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'update_stc_document_status',
                                value: comment_status,
                                doc_id: doc_id,
                                doc_type_id: doc_type_id,
                                label: label,
                                reason: reason
                            },
                            success: function (response) {
                                swal("Success", "Document status updated successfully", "success").then(function () {
                                    if(btn_value == 'Approve'){
                                        $(".change_doc_"+index+" option[value='Approved']").text('Approved');
                                    }else{
                                        $(".change_doc_"+index+" option[value='Rejected']").text('Rejected');
                                    }
                                    $(".change_doc_"+index).attr('disabled',true);
                                    $(".add_approved_project_rejected_comment_btn").text(btn_value);
                                    $("#exampleModal_otherdoc_adding_comment").modal('hide');
                                    //location.reload();
                                });
                            }
                        });
                    };
                }
            });
        });
        $(document).on('click', '#stc-submit-comment-btn', function () {
            var comment = $("#other-comment-textarea").val().trim()
            if (comment == '' || comment == ' ' || comment == null) {
                jQuery("#other-comment-textarea").css("border", "1px solid red");
                jQuery("#other-comment-textarea").focus();
                jQuery("#other-comment-textarea").attr("placeholder", "Please enter comment");
                jQuery("#other-comment-textarea").next().remove();
                jQuery("#other-comment-textarea").after('<span class="error">Please enter comment</span>');

                return false;
            }
            var doc_id = $("#doc_id_comment").val();
            var doc_type_id = $("#doc_type_id_comment").val();
            var label = $("#exampleModal_otherdoc_adding_comment").data('label');
            $.ajax({
                url: doc_upload.ajaxurl,
                type: 'POST',
                data: {
                    action: 'project_doc_comment_data',
                    comment: comment,
                    doc_id: doc_id,
                    doc_type_id: doc_type_id,
                    label: label
                },
                success: function (response) {
                    swal("Success", "Comment added successfully", "success");
                    location.reload();
                }
            });
        });
        jQuery(document).on('click', '.view_stc_comment', function () {
            var doc_type_id = jQuery(this).data('doc_type_id');
            var doc_id = jQuery(this).data('doc_id');
            //var doc_id = jQuery(this).closest('td').find('.add_stc_comment_btn').data('doc_id');
            jQuery('#exampleModal_ercdoc_view .modal-body').html('<div style="text-align-center;"></div><i aria-hidden="true" class="fa fa-spinner fa-spin"></i></div>');
            $.ajax({
                url: doc_upload.ajaxurl,
                type: 'POST',
                data: {
                    action: 'view_stc_comments',
                    doc_type_id: doc_type_id,
                    doc_id: doc_id
                },
                success: function (response) {
                    var json_response = JSON.parse(response);
                    jQuery('#exampleModal_ercdoc_view .modal-body').html('');
                    var inc = 0;
                    jQuery.each(json_response, function (index, value) {
                        if (index != 0) {
                            var prepare_html_op = "<div style='display:none;' class='iris_comment_view_all iris_comment_" + index + "'>" + value + "</div>";
                            jQuery('#exampleModal_ercdoc_view .modal-body').append(prepare_html_op);
                        }
                        else {
                            var prepare_html_op = "<div class='iris_comment_view_all iris_comment_" + index + "'>" + value + "</div>";
                            jQuery('#exampleModal_ercdoc_view .modal-body').append(prepare_html_op);
                        }
                        inc++;
                    });
                    if (inc == 1) {
                        jQuery('#exampleModal_ercdoc_view .modal-footer .btn').hide();
                    }
                    else {
                        jQuery('#exampleModal_ercdoc_view .modal-footer .btn').show();
                    }
                    jQuery("#exampleModal_ercdoc_view").addClass('in');
                    jQuery("#exampleModal_ercdoc_view").modal('show');
                }
            });
            jQuery(document).on('click', '#exampleModal_ercdoc_view .modal-footer .btn', function () {
                jQuery('.iris_comment_view_all').show();
                jQuery('#exampleModal_ercdoc_view .modal-footer .btn').hide();
            });
        });
        $('#exampleModal_ercdoc_view .close').on('click', function () {
            location.reload();
        });
        $('#exampleModal_otherdoc_adding_comment .close').on('click', function () {
            location.reload();
        });
        $(document).on('click', '.add_stc_comment_btn', function () {
            $("#other-comment-textarea").val("");
            var doc_id = $(this).data('doc_id');
            var doc_type_id = $(this).data('doc_type_id');
            var label = $(this).closest('tr').find('label.questions_Q').text().trim();
            $("#exampleModal_otherdoc_adding_comment #doc_id_comment").val(doc_id);
            $("#exampleModal_otherdoc_adding_comment #doc_type_id_comment").val(doc_type_id);
            $("#exampleModal_otherdoc_adding_comment").attr('data-label', label);
            $("#exampleModal_otherdoc_adding_comment").addClass('in');
            $("#exampleModal_otherdoc_adding_comment").modal('show');
        });
        jQuery(".user-select").on('select2:select', function(e) {
            jQuery("#assign-user-1").show();
        });
        jQuery(".user-select").select2({
            placeholder: "Select Collaborator to Assign",
            allowClear: true
        });
        jQuery(document).on('click', '.unassign-user', function() {
            jQuery(".show_message_collaborator").hide();
            unassign_user_id = jQuery(this).attr("unassign-user");
            unassign_user_name = jQuery(this).attr("unassign-user-name");
            var project_id = "<?php echo $project_id; ?>";
            var data = {
                'action': 'assign_collaborators',
                'project_id':project_id,
                'user_id': unassign_user_id,
                'type': 'unassign-user'
            };
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'post',
                data: data,
                success(response) {
                    console.log(response);
                    var name = jQuery('#userDetails').attr('data-name');
                    var noteMessage = name + ' added a comment: ' + name + ' unassign collaborator to ' + unassign_user_name;
                    saveProject(noteMessage);
                    jQuery("#assign-users" + unassign_user_id).css("display", "none");
                }
            }).done(function() {
                console.log("completed");
            });
        });
        jQuery(document).on('click', '.assign-user-btn', function() {
            jQuery(".show_message_collaborator").hide();
            
            var selected_user = jQuery(".user-select :selected").val();
            if(selected_user != '' && selected_user > 0){
                jQuery('.assign-user-btn').html("Please wait ..");
                var project_id = "<?php echo $project_id; ?>";
                var assignedUsers = [];
                jQuery(".all_assigned_users p").each(function() {
                    assignedUsers.push(jQuery(this).attr("id"))
                });
                if (jQuery.inArray(selected_user, assignedUsers) == -1) {
                        var data = {
                            'action': 'assign_collaborators',
                            'user_id': selected_user,
                            'project_id' : project_id,
                            'type': 'assign-user'
                        };
                        jQuery.ajax({
                            url: '<?php echo admin_url("admin-ajax.php"); ?>',
                            method: 'post',
                            data: data,
                            success(response) {
                                var selected_user_name = jQuery(".user-select :selected").text();
                                /*update note start*/
                                var name = jQuery('#userDetails').attr('data-name');
                                var noteMessage = name + ' added a comment: ' + name + ' assign collaborator to ' + selected_user_name;
                                saveProject(noteMessage);
                                /*update note end*/
                                
                                var selected_user_id = jQuery(".user-select :selected").val();
                                var Dynamic_user = '<p class="col-form-label" id="assign-users' + selected_user_id + '">' + selected_user_name + ' <span unassign-user="' + selected_user_id + '" class="unassign-user glyphicon glyphicon-minus-sign"></span></p>';
                                jQuery(".all_assigned_users").prepend(Dynamic_user);
                                jQuery(".user-select").val('').change();
                                jQuery('.assign-user-btn').html("Assign User");
                            }
                        }).done(function() {
                            //jQuery(".assign-user-btn").css("display", "none");
                            console.log("completed");
                        });
                    
                } else {
                    console.log("user already assigned");
                }
            }else{
                jQuery(".show_message_collaborator").html('Please select collaborator');
            }
        })
        $(".opp-add-notes").click(function() {
            $('.note-response').html("");
            $('.note-response').css('display', 'none');
            $('.error-response').css('display', 'none');
            $("#add-new-opp-notes").modal('show');

        });
        $('.close-popup-notes').click(function() {
            $('.note-response').css('display', 'none');
            $('.error-response').css('display', 'none');
            $("#add-new-opp-notes").modal('hide');
        });
        $('.close-popup-appr-reject').click(function() {
            $('#other-comment-textarea').val('');
            $("#exampleModal_otherdoc_adding_comment").modal('hide');
        });
    });
</script>
<script type="text/javascript">
    $(document).ready(function() {
        jQuery(document).on('change', '.select-milestone', function() {
            var no = jQuery(this).data('no');
            var val = jQuery(this).val();
            var product_name = $(this).find(":selected").html();
            console.log(product_name);
            var url = "<?php echo site_url() . '/wp-json/productsplugin/v1/milestone-status'; ?>";
            jQuery.ajax({
                type: "POST",
                url: url,
                data: {
                    id: val
                },
                beforeSend: function() {
                    $('#MilestoneStage').html('<option value="">Loading Stages...</option>');
                    $('.show_message_milestone').html('');
                },
                success: function(response) {
                    jQuery('#MilestoneStage').html('');

                    if (response.length != 0) {
                        jQuery.each(response, function(indexes, values) {
                            var optionHTML = `<option value="${values.milestone_stage_id}"> ${values.stage_name} </option>`;
                            jQuery('#MilestoneStage').append(optionHTML);
                        });
                    } else {

                        jQuery('#MilestoneStage').html(`<option value="">Select Stage</option>`);
                    }
                }
            });
        });
        $(document).on('click', '.update_project', function() {
            $('.show_message_popup').html('');
            $(this).text('Please wait..');
            $(this).attr('disabled',true);
            let lead_id = "<?php echo $lead_id;?>";
            let projectID = $(this).attr('data-projectid');
            let project_name = $('#project_name').val();
            let project_fee = $('#project_fee').val();
            let maximum_credit = $('#maximum_credit').val();
            let estimated_fee = $('#estimated_fee').val();
            let self_time_off_days_april_2020 = $("#self_time_off_days_april_2020").val();
            let self_time_off_days_january_2021 = $("#self_time_off_days_january_2021").val();
            let self_time_off_days_april_2021 = $("#self_time_off_days_april_2021").val();
            let other_time_off_days_april_2020 = $("#other_time_off_days_april_2020").val();
            let other_time_off_days_january_2021 = $("#other_time_off_days_january_2021").val();
            let other_time_off_days_april_2021 = $("#other_time_off_days_april_2021").val();
            $('.show_message_popup').html('<p class="alert_message">Please Wait</p>');
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-project-optional-field',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    project_name: project_name,
                    project_fee: project_fee,
                    maximum_credit: maximum_credit,
                    estimated_fee: estimated_fee,
                    self_time_off_days_april_2020: self_time_off_days_april_2020,
                    self_time_off_days_january_2021: self_time_off_days_january_2021,
                    self_time_off_days_april_2021: self_time_off_days_april_2021,
                    other_time_off_days_april_2020: other_time_off_days_april_2020,
                    other_time_off_days_january_2021: other_time_off_days_january_2021,
                    other_time_off_days_april_2021: other_time_off_days_april_2021
                },
                success(res) {
                    console.log(res);
                    $(".update_project").text('Update');
                    $(".update_project").attr('disabled',false);
                    if (res.status == true) {
                        $('.show_message_popup').html('<p class="success_message">Project successfully updated</p>');
                        setTimeout(function() {
                            $('.show_message_popup').html('');

                        }, 3000)
                    } else {
                        $('.show_message_popup').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    $(".update_project").text('Update');
                    $(".update_project").attr('disabled',false);
                    $('.show_message_popup').html('<p class="warning_message">Something went worng.</p>');
                }
            });
        });
        $(document).on('click', '.update_milestone', function() {
            let lead_id = "<?php echo $lead_id;?>";
            let projectID = $(this).attr('data-projectID');
            let oldmilestoneid = $(this).attr('data-milestoneid');
            let oldmilestonestageid = $(this).attr('data-milestonestageid');
            let project_name = $('#project_name').val();
            let project_fee = $('#project_fee').val();
            let maximum_credit = $('#maximum_credit').val();
            let estimated_fee = $('#estimated_fee').val();
            let self_time_off_days_april_2020 = $("#self_time_off_days_april_2020").val();
            let self_time_off_days_january_2021 = $("#self_time_off_days_january_2021").val();
            let self_time_off_days_april_2021 = $("#self_time_off_days_april_2021").val();
            let other_time_off_days_april_2020 = $("#other_time_off_days_april_2020").val();
            let other_time_off_days_january_2021 = $("#other_time_off_days_january_2021").val();
            let other_time_off_days_april_2021 = $("#other_time_off_days_april_2021").val();
            let Milestone = $('#Milestone').val();
            let MilestoneStage = $('#MilestoneStage').val();
            
            if (Milestone == '') {
                $('.show_message_milestone').html('<p class="warning_message">Please select the milestone.</p>');
                return;
            }
            if (MilestoneStage == '') {
                $('.show_message_milestone').html('<p class="warning_message">Please select the stage.</p>');
                return;
            }
            
            var milestone_stage_name = jQuery("#MilestoneStage :selected").text();
            var user_id = "<?php echo get_current_user_id(); ?>";
            
            $('.show_message_milestone').html('<p class="alert_message">Please Wait</p>');
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-project-optional-field',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    project_name: project_name,
                    project_fee: project_fee,
                    maximum_credit: maximum_credit,
                    estimated_fee: estimated_fee,
                    self_time_off_days_april_2020: self_time_off_days_april_2020,
                    self_time_off_days_january_2021: self_time_off_days_january_2021,
                    self_time_off_days_april_2021: self_time_off_days_april_2021,
                    other_time_off_days_april_2020: other_time_off_days_april_2020,
                    other_time_off_days_january_2021: other_time_off_days_january_2021,
                    other_time_off_days_april_2021: other_time_off_days_april_2021,
                    Milestone: Milestone,
                    MilestoneStage: MilestoneStage,
                    milestone_stage_name:milestone_stage_name,
                    user_id:user_id,
                },
                success(res) {
                    console.log(res);
                    if (res.status == true) {
                        $('.show_message_milestone').html('<p class="success_message">Successfully updated</p>');
                        $('#showMilestone').html(res.data.milestoneName);
                        $('#showMilestoneStage').html(res.data.milestoneStatus);
                        var name = jQuery('#userDetails').attr('data-name');
                        
                        if(Milestone != oldmilestoneid && MilestoneStage != oldmilestonestageid){
                            var noteMessage = name + ' added a comment: ' + name + ' changed milestone to ' + res.data.milestoneName + ' and changed stage to ' + res.data.milestoneStatus;
                            $(".update_milestone").attr('data-milestoneid',Milestone);
                            $(".update_milestone").attr('data-milestonestageid',MilestoneStage);
                            saveProject(noteMessage);
                        }
                        else if(Milestone != oldmilestoneid){
                            var noteMessage = name + ' added a comment: ' + name + ' changed milestone to ' + res.data.milestoneName;
                            $(".update_milestone").attr('data-milestoneid',Milestone);
                            saveProject(noteMessage);
                        }
                        else if(MilestoneStage != oldmilestonestageid){
                            var noteMessage = name + ' added a comment: ' + name + ' changed stage to ' + res.data.milestoneStatus;
                            $(".update_milestone").attr('data-milestonestageid',MilestoneStage);
                            saveProject(noteMessage);
                        }
                        $('.show_message_milestone').html('');
                        $('.show_message_popup').html('');
                        $('.milestone_button_container').hide();
                        $('#Milestone').hide();
                        $('#MilestoneStage').hide();
                        $('#showMilestone').show();
                        $('#showMilestoneStage').show();
                    } else {
                        $('.show_message_milestone').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    $('.show_message_milestone').html('<p class="warning_message">Something went worng.</p>');
                }
            });
        });
        $(document).on('click', '.update_owner', function() {
            let lead_id = "<?php echo $lead_id;?>";
            let projectID = $(this).attr('data-projectID');
            let project_name = $('#project_name').val();
            let project_fee = $('#project_fee').val();
            let maximum_credit = $('#maximum_credit').val();
            let estimated_fee = $('#estimated_fee').val();
            let self_time_off_days_april_2020 = $("#self_time_off_days_april_2020").val();
            let self_time_off_days_january_2021 = $("#self_time_off_days_january_2021").val();
            let self_time_off_days_april_2021 = $("#self_time_off_days_april_2021").val();
            let other_time_off_days_april_2020 = $("#other_time_off_days_april_2020").val();
            let other_time_off_days_january_2021 = $("#other_time_off_days_january_2021").val();
            let other_time_off_days_april_2021 = $("#other_time_off_days_april_2021").val();
            let OwnerList = $('#OwnerList').val();
            $('.show_message_owner').html('<p class="alert_message">Please Wait</p>');
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-project-optional-field',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    project_name: project_name,
                    project_fee: project_fee,
                    maximum_credit: maximum_credit,
                    estimated_fee: estimated_fee,
                    self_time_off_days_april_2020: self_time_off_days_april_2020,
                    self_time_off_days_january_2021: self_time_off_days_january_2021,
                    self_time_off_days_april_2021: self_time_off_days_april_2021,
                    other_time_off_days_april_2020: other_time_off_days_april_2020,
                    other_time_off_days_january_2021: other_time_off_days_january_2021,
                    other_time_off_days_april_2021: other_time_off_days_april_2021,
                    OwnerList: OwnerList,
                },
                success(res) {
                    console.log(res);
                    if (res.status == true) {
                        $('.show_message_owner').html('<p class="success_message">Successfully updated</p>');
                        $('#showOwnerList').html(res.data.ownerName);
                        var name = jQuery('#userDetails').attr('data-name');
                        var noteMessage = name + ' added a comment: ' + name + ' changed owener to ' + res.data.ownerName;
                        saveProject(noteMessage);
                        $('.show_message_milestone').html('');
                        $('.show_message_popup').html('');
                        $('.show_message_owner').html('');
                        $('.owner_button_container').hide();
                        $('#OwnerList').hide();
                        $('#showOwnerList').show();
                    } else {
                        $('.show_message_owner').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    $('.show_message_owner').html('<p class="warning_message">Something went worng.</p>');
                }
            });
        });
        $(document).on('click', '.update_contacts', function() {
            let lead_id = "<?php echo $lead_id;?>";
            let projectID = $(this).attr('data-projectID');
            let old_contact_id = $(this).attr('data-contactid');
            let project_name = $('#project_name').val();
            let project_fee = $('#project_fee').val();
            let maximum_credit = $('#maximum_credit').val();
            let estimated_fee = $('#estimated_fee').val();
            let self_time_off_days_april_2020 = $("#self_time_off_days_april_2020").val();
            let self_time_off_days_january_2021 = $("#self_time_off_days_january_2021").val();
            let self_time_off_days_april_2021 = $("#self_time_off_days_april_2021").val();
            let other_time_off_days_april_2020 = $("#other_time_off_days_april_2020").val();
            let other_time_off_days_january_2021 = $("#other_time_off_days_january_2021").val();
            let other_time_off_days_april_2021 = $("#other_time_off_days_april_2021").val();
            let ContactList = $('#ContactList').val();
            $('.show_message_contact').html('<p class="alert_message">Please Wait</p>');
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-project-optional-field',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    project_name: project_name,
                    project_fee: project_fee,
                    maximum_credit: maximum_credit,
                    estimated_fee: estimated_fee,
                    self_time_off_days_april_2020: self_time_off_days_april_2020,
                    self_time_off_days_january_2021: self_time_off_days_january_2021,
                    self_time_off_days_april_2021: self_time_off_days_april_2021,
                    other_time_off_days_april_2020: other_time_off_days_april_2020,
                    other_time_off_days_january_2021: other_time_off_days_january_2021,
                    other_time_off_days_april_2021: other_time_off_days_april_2021,
                    ContactList: ContactList
                },
                success(res) {
                    console.log(res);
                    if (res.status == true) {
                        $('.show_message_contact').html('<p class="success_message">Successfully updated</p>');
                        $('#showContactList').html(res.data.ContactName);
                        var name = jQuery('#userDetails').attr('data-name');
                        var noteMessage = name + ' added a comment: ' + name + ' changed contact to ' + res.data.ContactName;
                        if(old_contact_id != ContactList){
                            $(".update_contacts").attr('data-contactid',ContactList);
                            saveProject(noteMessage);
                        }
                        $('.show_message_milestone').html('');
                        $('.show_message_popup').html('');
                        $('.show_message_contact').html('');
                        $('.contact_button_container').hide();
                        $('#ContactList').hide();
                        $('#showContactList').show();
                    } else {
                        $('.show_message_contact').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    $('.show_message_contact').html('<p class="warning_message">Something went worng.</p>');
                }
            });
        });
        $(document).on('click', '.cancel_project', function() {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.button_container').hide();
        });
        $(document).on('click', '.edit_project_milestone', function() {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.milestone_button_container').toggle();
            $('#Milestone').toggle();
            $('#MilestoneStage').toggle();
            $('#showMilestone').toggle();
            $('#showMilestoneStage').toggle();
        });
        $(document).on('click', '.cancel_milestone', function() {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.milestone_button_container').hide();
            $('#Milestone').hide();
            $('#MilestoneStage').hide();
            $('#showMilestone').show();
            $('#showMilestoneStage').show();
        });
        $(document).on('click', '.edit_project_owner', function() {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.show_message_owner').html('');
            $('.owner_button_container').toggle();
            $('#OwnerList').toggle();
            $('#showOwnerList').toggle();
        });
        $(document).on('click', '.cancel_owner', function() {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.show_message_owner').html('');
            $('.owner_button_container').hide();
            $('#OwnerList').hide();
            $('#showOwnerList').show();
        });
        $(document).on('click', '.edit_project_contact', function() {
            $('.show_message_popup').html('');
            $('.show_message_contact').html('');
            $('.contact_button_container').toggle();
            $('#ContactList').toggle();
            $('#showContactList').toggle();
        });
        $(document).on('click', '.edit_project_collaborator', function () {
            var show_collaborators = $("#show_collaborators").val();
            $('.show_message_popup').html('');
            $('.show_message_collaborator').html('');
            $('.collaborator_button_container').toggle();
            if(show_collaborators == 0){
                $("#show_collaborators").val(1);
                $('#assign_collaborator').css('display','inline-block');
                $(".assign_collaborators_section .select2-container").css('display', 'inline-block');
            }else{
                $("#show_collaborators").val(0);
                $('#assign_collaborator').css('display','none');
                $(".assign_collaborators_section .select2-container").css('display', 'none');
            }
            
            //$(".assign_collaborators_section .select2-container").css('display', 'inline-block');
            //$('#showContactList').toggle();
        });
        $(document).on('click', '.cancel_contact', function() {
            $('.show_message_popup').html('');
            $('.show_message_contact').html('');
            $('.contact_button_container').hide();
            $('#ContactList').hide();
            $('#showContactList').show();
        });
        $(document).on('click', '.cancel_collaborator', function() {
            $('.show_message_popup').html('');
            $('.show_message_collaborator').html('');
            $('.collaborator_button_container').hide();
            $('#assign_collaborator').hide();
            $('.assign_collaborators_section .select2-container').hide();
        });
        // ---------- load notes jquery functionality --
        jQuery(document).on('click', '#load-more-notes', function() {
            jQuery(this).html('Loading..');
            jQuery(this).attr('disabled', true);
            var offset = jQuery('#note-offset').val();
            var new_offset = parseInt(offset) + parseInt(10);
            var total_notes_c = <?php echo $total_notes_count; ?>;
            var project_id = <?php echo $project_id; ?>;
            jQuery('#note-offset').val(new_offset);
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/fetch_project_notes',
                method: 'post',
                data: {
                    offset: offset,
                    project_id: project_id
                },
                success(response) {
                    if (new_offset >= total_notes_c) {
                        jQuery('#load-more-notes').css('display', 'none');
                    }
                    jQuery('.notes-listing').append(response);
                    jQuery('#load-more-notes').html('M');
                    jQuery('#load-more-notes').attr('disabled', false);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log('Something went worng.');
                }

            });
        });
        //close pop up
        $(document).on('click', '.close_statuspopup', function() {
            jQuery('#notes_box').hide();
        });
        jQuery(document).on('keyup', '#notes-input', function() {
            $('.error-response').css('display', 'none');
            $('.note-response').css('display', 'none');
        });
        //create project note
        jQuery(document).on('click', '#create-note', function() {
            var noteInput = jQuery('#notes-input').val();
            if (noteInput == '') {
                $('.error-response').css('display', 'block');
                $('.note-response').css('display', 'none');
                return false;
            } else {
                jQuery(this).html('Creating..');
                jQuery(this).prop('disabled', true);
                var project_id = <?php echo $project_id; ?>;
                var name = jQuery('#userDetails').attr('data-name');
                var note = name + ' added a comment: ' + noteInput;
                var user_id = <?php echo get_current_user_id();  ?>;
                jQuery.ajax({
                    url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/create_project_notes',
                    method: 'post',
                    data: {
                        note: note,
                        project_id: project_id,
                        user_id: user_id
                    },
                    success(response) {
                        jQuery('.notes-listing').prepend(response);
                        var offset = jQuery('#note-offset').val();
                        var new_offset = parseInt(offset) + parseInt(1);
                        jQuery('#note-offset').val(new_offset);
                        <?php
                        $date = date("Y-m-d H:i:s");
                        $datetime = date_create($date);
                        $time = date_format($datetime, "h:ia");
                        $day = date_format($datetime, " D ");
                        $month = date_format($datetime, " M ");
                        $date = date_format($datetime, "dS,");
                        $year = date_format($datetime, " Y ");
                        $actual_date = $time . " on " . $day . $month . $date . $year;
                        ?>
                        var curr_date = "<?php echo $actual_date; ?>";
                        var not_div = '<div class="note-listing-div"><p class="notes">' + note + '</p><p class="date-time">' + curr_date + '</p></div>';
                        jQuery('#notes-input').val('');
                        jQuery('.note-response').css('display', 'block');
                        jQuery('.note-response').html("Notes created successfully.");
                        jQuery('#create-note').html('Submit').attr('disabled', false);
                        jQuery('.error-response').css('display', 'none');
                        setTimeout(function() {
                            jQuery('#add-new-opp-notes').modal('hide')
                        }, 2000);
                    }
                });
            }
        });
    });
    function saveProject(noteMessage) {
        var project_id = <?php echo $project_id; ?>;
        var id = jQuery('#userDetails').attr('data-id');
        var name = jQuery('#userDetails').attr('data-name'); 
        var note = noteMessage;
        var user_id = <?php echo get_current_user_id();  ?>;
        jQuery.ajax({
            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/create_project_notes',
            method: 'post',
            data: {
                note: note,
                project_id: project_id,
                user_id: user_id
            },
            success(response) {
                jQuery('.notes-listing').prepend(response);
                var offset = jQuery('#note-offset').val();
                var new_offset = parseInt(offset) + parseInt(1);
                jQuery('#note-offset').val(new_offset);
            }
        });
    }
</script>
<script type="text/javascript">
$(document).ready(function () {
$('.unemployment-claimed-radio').change(function () {
    var row = $(this).closest('tr');

    var targetRowSelector = $(this).data('target-row');
    var otherRow = $(targetRowSelector);

    if ($(this).val() === 'yes') {
        otherRow.show();
    } else {
        otherRow.hide();
    }
});
});
</script>
<script>
    //----- For TaxNow signup Document section
    //Master Ops
    jQuery(document).on('change', '.taxnow_signup_masterops_doc_section_required', function () {
        var doc_type_id = jQuery(this).data('doc_type_id');
        var clicked_doc_section = jQuery(this).data('section');
        var value = jQuery(this).val();

        var docTypeIds = [];
        var isvalid = [];
        isvalid = [doc_type_id];
        docTypeIds = [jQuery(this).data('doc_type_id')]
        if (value == 'Yes') {
            var text = "Are you sure you want to confirm the TaxNow Sign-up is completed?";
        } else if (value == 'No') {
            var text = "Are you sure you want to confirm the TaxNow Sign-up is not completed?";
        }
        var icon = "warning";
        //pop up
        swal({
            title: "",
            text: text,
            icon: icon,
            dangerMode: true,
            buttons: ["No", "Yes"]
        })
            .then((willDelete) => {
                if (willDelete) {
                    $.ajax({
                        url: doc_upload.ajaxurl,
                        type: 'POST',
                        data: {
                            action: 'taxnow_signup_confirmation_required',
                            value: value,
                            docTypeIds: docTypeIds,
                            doc_ids: isvalid,
                        },
                        success: function (response) {

                            swal("Success", "Changed successfully", "success").then(function () {
                                //location.reload()
                            });
                            // location.reload();
                        }
                    });

                } else {

                    // swal("Status not changed!").then(function() {
                    //location.reload();
                    // });
                }
            });
    });
    //----- For TaxNow signup Document section
</script>



<script>

    $(document).ready(function () {
        var data = <?php echo json_encode($impacted_days_grouped_data); ?>;

        // Iterate through each doc_key group
        Object.keys(data).forEach(function (docKey) {
            data[docKey].forEach(function (item) {
                var inputId = '#selectedDateRangeInput_' + item.doc_key + '_' + item.date_range_index;
                console.log(inputId);
                if (item.doc_key == 0) {
                    var startDate_val = '04/01/2020';
                    var endDate_val = '12/31/2020';
                    var minDate_val = '04/01/2020';
                    var maxDate_val = '12/31/2020';

                } else if (item.doc_key == 1) {

                    var startDate_val = '01/01/2021';
                    var endDate_val = '03/31/2021';
                    var minDate_val = '01/01/2021';
                    var maxDate_val = '03/31/2021';

                } else if (item.doc_key == 2) {

                    var startDate_val = '04/01/2021';
                    var endDate_val = '09/30/2021';
                    var minDate_val = '04/01/2021';
                    var maxDate_val = '09/30/2021';

                }

                $(inputId).daterangepicker({
                    autoUpdateInput: false,

                    minDate: minDate_val,
                    maxDate: maxDate_val,
                    locale: {
                        cancelLabel: 'Clear'
                    }
                });

                $(inputId).on('apply.daterangepicker', function (ev, picker) {
                    $(this).val(picker.startDate.format('MM/DD/YYYY') + ' - ' + picker.endDate.format('MM/DD/YYYY'));
                });

                $(inputId).on('cancel.daterangepicker', function (ev, picker) {
                    $(this).val('');
                });

                // Set the selected date range if start_date and end_date are not empty
                if (item.start_date && item.end_date) {
                    $(inputId).data('daterangepicker').setStartDate(item.start_date);
                    $(inputId).data('daterangepicker').setEndDate(item.end_date);
                    $(inputId).val(item.start_date + ' - ' + item.end_date);
                }
            });
        });

        // Add click event for trash icons to remove date ranges
        $(document).on('click', '.trash-icon', function () {
            var index = $(this).data('index');
            $(this).closest('.date-range-container').remove();
        });
    });





    $(document).ready(function () {
        let selectedDates0 = []; // Array to store selected dates for container 0
        let inputCounter0 = 1; // Counter for input fields for container 0
        const containerID0 = 'dateRangeContainerMain_0'; // ID of the main container

        function updateSelectedDatesCount0() {
            $(`#${containerID0} #selectedDatesCount`).text(`${selectedDates0.length}`);

            $(`#self-no-days0`).val(`${selectedDates0.length}`);

            const selfNoDaysInput = $(`#self-no-days0`);
            const maxDays = parseInt(selfNoDaysInput.val(), 10); // Parse to integer

            if (selectedDates0.length > maxDays) {
                $('#warningMessage0').text(`Selected dates exceed the No. of Days.`);
            } else {
                $('#warningMessage0').text(''); // Clear the warning message if within limits
            }
        }

        // Function to check if all date range containers are filled for container 0
        function validateDateRangeContainers0() {
            let allFilled = true;
            $(`#${containerID0} .date-range-container`).each(function (index, container) {
                const dateRangeInput = $(container).find('.date-range-input');
                const dates = dateRangeInput.val();
                if (!dates) {
                    allFilled = false;
                    return false; // Exit each loop early if any container is empty
                }
            });
            return allFilled;
        }

        // Function to initialize daterangepicker on a given input field for container 0
        function initializeDateRangePicker0(inputField) {
            $(inputField).daterangepicker({
                opens: 'right',
                autoUpdateInput: false,
                startDate: '04/01/2020',
                endDate: '12/31/2020',
                minDate: '04/01/2020',
                maxDate: '12/31/2020',
                isInvalidDate: function (date) {
                    const selectedDate = date.format('MM/DD/YYYY');
                    return selectedDates0.includes(selectedDate);
                }
            }, function (start, end, label) {
                const startDate = start.format('MM/DD/YYYY');
                const endDate = end.format('MM/DD/YYYY');

                // Generate an array of dates in the selected range
                let range = [];
                let currentDate = moment(start);
                while (currentDate <= moment(end)) {
                    range.push(currentDate.format('MM/DD/YYYY'));
                    currentDate.add(1, 'days');
                }

                // Check if adding these dates will exceed the limit
                if (selectedDates0.length + range.length > 10) {
                    alert('You can select a maximum of 10 dates.');
                    return;
                }

                // Add the new dates to selectedDates0
                selectedDates0 = selectedDates0.concat(range);

                // Update the hidden input with the new list of dates
                updateHiddenInput0(selectedDates0);

                // Update the visible input field with the selected range
                $(inputField).val(`${startDate} - ${endDate}`);

                // Update all date pickers with the new list of disabled dates
                updateDatePickers0();

                // Add trash icon if not already present and if the input has a value
                if ($(inputField).val()) {
                    const container = $(inputField).closest('.date-range-container');
                    if (!container.find('.trash-icon').length) {
                        const index = container.attr('id').split('_')[1];
                        container.append(`<p class="remove-date">Remove Date <i class="fa-regular fa-trash-can trash-icon" data-index="${index}"></i></p>`);
                    }
                }

                updateSelectedDatesCount0();
            });
        }

        // Function to update the hidden input with the current selected dates for container 0
        function updateHiddenInput0(selectedDates0) {
            $(`#${containerID0} #selectedDateRanges`).val(selectedDates0.join(','));
        }

        // Function to update all date pickers with disabled dates for container 0
        function updateDatePickers0() {
            $(`#${containerID0} .date-range-input`).each(function (index, element) {
                if ($(element).data('daterangepicker')) {
                    const dp = $(element).data('daterangepicker');
                    dp.isInvalidDate = function (date) {
                        const selectedDate = date.format('MM/DD/YYYY');
                        return selectedDates0.includes(selectedDate);
                    };
                    dp.updateView();
                }
            });
        }

        // Initialize the first date range picker input for container 0
        initializeDateRangePicker0(`#${containerID0} #selectedDateRangeInput_0_0`);

        // Function to read initial value from hidden input if exists for container 0
        function readInitialSelectedDates0() {
            const initialSelectedDates0 = $(`#${containerID0} #selectedDateRanges`).val();
            if (initialSelectedDates0) {
                selectedDates0 = initialSelectedDates0.split(',');
                updateDatePickers0(); // Update disabled dates in all date pickers
            }
        }

        // Call readInitialSelectedDates0() on document ready
        readInitialSelectedDates0();

        // Handler to add new date picker input field for container 0
        $(`#${containerID0} #addMoreDateBtn`).click(function (e) {
            e.preventDefault();

            // Check if adding another date range will exceed the limit
            if (selectedDates0.length >= 10) {
                alert('You can select a maximum of 10 dates.');
                return;
            }

            // Validate if all date range containers are filled
            if (!validateDateRangeContainers0()) {
                alert('Please fill all empty date range fields.');
                return;
            }

            // Get the current count of date range containers
            inputCounter0 = $(`#${containerID0} .date-range-container`).length;
            const newIndex = inputCounter0++;
            const newInput = `
            <div class="date-range-container col-md-4" id="dateRangeContainer_0_${newIndex}">
                <input type="text" id="selectedDateRangeInput_0_${newIndex}" name="selectedDateRangeInput_0_${newIndex}" class="date-range-input" placeholder="Start Date - End Date" autocomplete="off">
                <i class="fa-regular fa-calendar-days calenday-icon"></i>
                <p class="remove-date">Remove Date <i class="fa-regular fa-trash-can trash-icon" data-index="${newIndex}"></i></p>
            </div>
        `;
            $(`#${containerID0} #datePickersContainer`).append(newInput);
            initializeDateRangePicker0(`#selectedDateRangeInput_0_${newIndex}`);
            updateSelectedDatesCount0();
        });

        // Event delegation for handling trash icon click for container 0
        $(`#${containerID0} #datePickersContainer`).on('click', '.trash-icon', function () {
            const index = $(this).data('index');

            // Find the input field corresponding to the clicked trash icon
            const dateRangeInput = $(`#selectedDateRangeInput_0_${index}`);
            // Get the date range value from the input field
            const dateRangeValue = dateRangeInput.val();

            let range = [];
            if (dateRangeValue) {
                const [startDate, endDate] = dateRangeValue.split(' - ');

                // Generate an array of dates in the selected range
                let currentDate = moment(startDate, 'MM/DD/YYYY');
                const endMoment = moment(endDate, 'MM/DD/YYYY');
                while (currentDate <= endMoment) {
                    range.push(currentDate.format('MM/DD/YYYY'));
                    currentDate.add(1, 'days');
                }
            }

            // Remove the dates from selectedDates0
            selectedDates0 = selectedDates0.filter(date => !range.includes(date));

            // Update hidden input with updated dates directly
            updateHiddenInput0(selectedDates0);

            // Remove the entire date range container
            $(`#dateRangeContainer_0_${index}`).remove();

            // Reset the indices of remaining date pickers
            $(`#${containerID0} .date-range-container`).each(function (idx, container) {
                $(container).attr('id', `dateRangeContainer_0_${idx}`);
                $(container).find('.date-range-input').attr('id', `selectedDateRangeInput_0_${idx}`);
                $(container).find('.trash-icon').data('index', idx);
            });

            updateSelectedDatesCount0();
        });

        // Read initial value from hidden input if exists for container 0
        const initialSelectedDates0 = $(`#${containerID0} #selectedDateRanges`).val();
        if (initialSelectedDates0) {
            selectedDates0 = initialSelectedDates0.split(',');
            updateDatePickers0(); // Update disabled dates in all date pickers
            updateSelectedDatesCount0();
        }

        // Event listener for calendar icon click for container 0
        $(document).on('click', '.calenday-icon', function () {
            $(this).siblings('.date-range-input').click(); // Simulate a click on the input field
        });
    });



//widget 1


    $(document).ready(function () {
        // For dateRangeContainerMain_1
        let selectedDates1 = []; // Array to store selected dates for container 1
        let inputCounter1 = 1; // Counter for input fields for container 1
        const containerID1 = 'dateRangeContainerMain_1'; // ID of the main container

        function updateSelectedDatesCount1() {
            $(`#${containerID1} #selectedDatesCount`).text(`${selectedDates1.length}`);

            $(`#self-no-days1`).val(`${selectedDates1.length}`);


            const selfNoDaysInput = $(`#self-no-days1`);
            const maxDays = parseInt(selfNoDaysInput.val(), 10); // Parse to integer

            if (selectedDates1.length > maxDays) {
                $('#warningMessage1').text(`Selected dates exceed the No. of Days.`);
            } else {
                $('#warningMessage1').text(''); // Clear the warning message if within limits
            }

        }

        // Function to check if all date range containers are filled for container 1
        function validateDateRangeContainers1() {
            let allFilled = true;
            $(`#${containerID1} .date-range-container`).each(function (index, container) {
                const dateRangeInput = $(container).find('.date-range-input');
                const dates = dateRangeInput.val();
                if (!dates) {
                    allFilled = false;
                    return false; // Exit each loop early if any container is empty
                }
            });
            return allFilled;
        }

        // Function to initialize daterangepicker on a given input field for container 1
        function initializeDateRangePicker1(inputField) {
            $(inputField).daterangepicker({
                opens: 'right',
                autoUpdateInput: false,
                startDate: '01/01/2021',
                endDate: '03/31/2021',
                minDate: '01/01/2021',
                maxDate: '03/31/2021',
                isInvalidDate: function (date) {
                    const selectedDate = date.format('MM/DD/YYYY');
                    return selectedDates1.includes(selectedDate);
                }
            }, function (start, end, label) {
                const startDate = start.format('MM/DD/YYYY');
                const endDate = end.format('MM/DD/YYYY');

                // Generate an array of dates in the selected range
                let range = [];
                let currentDate = moment(start);
                while (currentDate <= moment(end)) {
                    range.push(currentDate.format('MM/DD/YYYY'));
                    currentDate.add(1, 'days');
                }

                // Check if adding these dates will exceed the limit
                if (selectedDates1.length + range.length > 10) {
                    alert('You can select a maximum of 10 dates.');
                    return;
                }

                // Add the new dates to selectedDates1
                selectedDates1 = selectedDates1.concat(range);

                // Update the hidden input with the new list of dates
                updateHiddenInput1(selectedDates1);

                // Update the visible input field with the selected range
                $(inputField).val(`${startDate} - ${endDate}`);

                // Update all date pickers with the new list of disabled dates
                updateDatePickers1();

                // Add trash icon if not already present and if the input has a value
                if ($(inputField).val()) {
                    const container = $(inputField).closest('.date-range-container');
                    if (!container.find('.trash-icon').length) {
                        const index = container.attr('id').split('_')[1];
                        container.append(`<p class="remove-date">Remove Date <i class="fa-regular fa-trash-can trash-icon" data-index="${index}"></i></p>`);
                    }
                }

                updateSelectedDatesCount1();
            });
        }

        // Function to update the hidden input with the current selected dates for container 1
        function updateHiddenInput1(selectedDates1) {
            $(`#${containerID1} #selectedDateRanges`).val(selectedDates1.join(','));
        }

        // Function to update all date pickers with disabled dates for container 1
        function updateDatePickers1() {
            $(`#${containerID1} .date-range-input`).each(function (index, element) {
                if ($(element).data('daterangepicker')) {
                    const dp = $(element).data('daterangepicker');
                    dp.isInvalidDate = function (date) {
                        const selectedDate = date.format('MM/DD/YYYY');
                        return selectedDates1.includes(selectedDate);
                    };
                    dp.updateView();
                }
            });
        }

        // Initialize the first date range picker input for container 1
        initializeDateRangePicker1(`#${containerID1} #selectedDateRangeInput_1_0`);

        // Function to read initial value from hidden input if exists for container 1
        function readInitialSelectedDates1() {
            const initialSelectedDates1 = $(`#${containerID1} #selectedDateRanges`).val();
            if (initialSelectedDates1) {
                selectedDates1 = initialSelectedDates1.split(',');
                updateDatePickers1(); // Update disabled dates in all date pickers
            }
        }

        // Call readInitialSelectedDates1() on document ready
        readInitialSelectedDates1();

        // Handler to add new date picker input field for container 1
        $(`#${containerID1} #addMoreDateBtn`).click(function (e) {
            e.preventDefault();

            // Check if adding another date range will exceed the limit
            if (selectedDates1.length >= 10) {
                alert('You can select a maximum of 10 dates.');
                return;
            }

            // Validate if all date range containers are filled
            if (!validateDateRangeContainers1()) {
                alert('Please fill all empty date range fields.');
                return;
            }

            // Get the current count of date range containers
            inputCounter1 = $(`#${containerID1} .date-range-container`).length;
            const newIndex = inputCounter1++;
            const newInput = `
            <div class="date-range-container col-md-4" id="dateRangeContainer_1_${newIndex}">
                <input type="text" id="selectedDateRangeInput_1_${newIndex}" name="selectedDateRangeInput_1_${newIndex}" class="date-range-input" placeholder="Start Date - End Date" autocomplete="off">
                <i class="fa-regular fa-calendar-days calenday-icon"></i>
                <p class="remove-date">Remove Date <i class="fa-regular fa-trash-can trash-icon" data-index="${newIndex}"></i></p>
            </div>
        `;
            $(`#${containerID1} #datePickersContainer`).append(newInput);
            initializeDateRangePicker1(`#selectedDateRangeInput_1_${newIndex}`);
            updateSelectedDatesCount1();
        });

        // Event delegation for handling trash icon click for container 1
        $(`#${containerID1} #datePickersContainer`).on('click', '.trash-icon', function () {
            const index = $(this).data('index');

            // Find the input field corresponding to the clicked trash icon
            const dateRangeInput = $(`#selectedDateRangeInput_1_${index}`);
            // Get the date range value from the input field
            const dateRangeValue = dateRangeInput.val();

            let range = [];
            if (dateRangeValue) {
                const [startDate, endDate] = dateRangeValue.split(' - ');

                // Generate an array of dates in the selected range
                let currentDate = moment(startDate, 'MM/DD/YYYY');
                const endMoment = moment(endDate, 'MM/DD/YYYY');
                while (currentDate <= endMoment) {
                    range.push(currentDate.format('MM/DD/YYYY'));
                    currentDate.add(1, 'days');
                }
            }

            // Remove the dates from selectedDates1
            selectedDates1 = selectedDates1.filter(date => !range.includes(date));

            // Update hidden input with updated dates directly
            updateHiddenInput1(selectedDates1);

            // Remove the entire date range container
            $(`#dateRangeContainer_1_${index}`).remove();

            // Reset the indices of remaining date pickers
            $(`#${containerID1} .date-range-container`).each(function (idx, container) {
                $(container).attr('id', `dateRangeContainer_1_${idx}`);
                $(container).find('.date-range-input').attr('id', `selectedDateRangeInput_1_${idx}`);
                $(container).find('.trash-icon').data('index', idx);
            });

            updateSelectedDatesCount1();
        });

        // Read initial value from hidden input if exists for container 1
        const initialSelectedDates1 = $(`#${containerID1} #selectedDateRanges`).val();
        if (initialSelectedDates1) {
            selectedDates1 = initialSelectedDates1.split(',');
            updateDatePickers1(); // Update disabled dates in all date pickers
            updateSelectedDatesCount1();
        }

        // Event listener for calendar icon click for container 1
        $(document).on('click', '.calenday-icon', function () {
            $(this).siblings('.date-range-input').click(); // Simulate a click on the input field
        });

    });





//widget 2
    $(document).ready(function () {
        let selectedDates2 = []; // Array to store selected dates for container 2
        let inputCounter2 = 1; // Counter for input fields for container 2
        const containerID2 = 'dateRangeContainerMain_2'; // ID of the main container

        function updateSelectedDatesCount2() {
            $(`#${containerID2} #selectedDatesCount`).text(`${selectedDates2.length}`);

            $(`#self-no-days2`).val(`${selectedDates2.length}`);


            const selfNoDaysInput = $(`#self-no-days2`);
            const maxDays = parseInt(selfNoDaysInput.val(), 10); // Parse to integer

            if (selectedDates2.length > maxDays) {
                $('#warningMessage2').text(`Selected dates exceed the No. of Days.`);
            } else {
                $('#warningMessage2').text(''); // Clear the warning message if within limits
            }
        }

        // Function to check if all date range containers are filled for container 2
        function validateDateRangeContainers2() {
            let allFilled = true;
            $(`#${containerID2} .date-range-container`).each(function (index, container) {
                const dateRangeInput = $(container).find('.date-range-input');
                const dates = dateRangeInput.val();
                if (!dates) {
                    allFilled = false;
                    return false; // Exit each loop early if any container is empty
                }
            });
            return allFilled;
        }

        // Function to initialize daterangepicker on a given input field for container 2
        function initializeDateRangePicker2(inputField) {
            $(inputField).daterangepicker({
                opens: 'right',
                autoUpdateInput: false,
                startDate: '04/01/2021',
                endDate: '09/30/2021',
                minDate: '04/01/2021',
                maxDate: '09/30/2021',
                isInvalidDate: function (date) {
                    const selectedDate = date.format('MM/DD/YYYY');
                    return selectedDates2.includes(selectedDate);
                }
            }, function (start, end, label) {
                const startDate = start.format('MM/DD/YYYY');
                const endDate = end.format('MM/DD/YYYY');

                // Generate an array of dates in the selected range
                let range = [];
                let currentDate = moment(start);
                while (currentDate <= moment(end)) {
                    range.push(currentDate.format('MM/DD/YYYY'));
                    currentDate.add(1, 'days');
                }

                // Check if adding these dates will exceed the limit
                if (selectedDates2.length + range.length > 10) {
                    alert('You can select a maximum of 10 dates.');
                    return;
                }

                // Add the new dates to selectedDates2
                selectedDates2 = selectedDates2.concat(range);

                // Update the hidden input with the new list of dates
                updateHiddenInput2(selectedDates2);

                // Update the visible input field with the selected range
                $(inputField).val(`${startDate} - ${endDate}`);

                // Update all date pickers with the new list of disabled dates
                updateDatePickers2();

                // Add trash icon if not already present and if the input has a value
                if ($(inputField).val()) {
                    const container = $(inputField).closest('.date-range-container');
                    if (!container.find('.trash-icon').length) {
                        const index = container.attr('id').split('_')[1];
                        container.append(`<p class="remove-date">Remove Date <i class="fa-regular fa-trash-can trash-icon" data-index="${index}"></i></p>`);
                    }
                }

                updateSelectedDatesCount2();
            });
        }

        // Function to update the hidden input with the current selected dates for container 2
        function updateHiddenInput2(selectedDates2) {
            $(`#${containerID2} #selectedDateRanges`).val(selectedDates2.join(','));

        }

        // Function to update all date pickers with disabled dates for container 2
        function updateDatePickers2() {
            $(`#${containerID2} .date-range-input`).each(function (index, element) {
                if ($(element).data('daterangepicker')) {
                    const dp = $(element).data('daterangepicker');

                    dp.isInvalidDate = function (date) {
                        const selectedDate = date.format('MM/DD/YYYY');
                        return selectedDates2.includes(selectedDate);
                    };
                    dp.updateView();
                }
            });
        }

        // Initialize the first date range picker input for container 2
        initializeDateRangePicker2(`#${containerID2} #selectedDateRangeInput_2_0`);

        // Function to read initial value from hidden input if exists for container 2
        function readInitialSelectedDates2() {
            const initialSelectedDates2 = $(`#${containerID2} #selectedDateRanges`).val();
            if (initialSelectedDates2) {
                selectedDates2 = initialSelectedDates2.split(',');
                updateDatePickers2(); // Update disabled dates in all date pickers
            }
        }

        // Call readInitialSelectedDates2() on document ready
        readInitialSelectedDates2();

        // Handler to add new date picker input field for container 2
        $(`#${containerID2} #addMoreDateBtn`).click(function (e) {
            e.preventDefault();

            // Check if adding another date range will exceed the limit
            if (selectedDates2.length >= 10) {
                alert('You can select a maximum of 10 dates.');
                return;
            }

            // Validate if all date range containers are filled
            if (!validateDateRangeContainers2()) {
                alert('Please fill all empty date range fields.');
                return;
            }

            // Get the current count of date range containers
            inputCounter2 = $(`#${containerID2} .date-range-container`).length;
            const newIndex = inputCounter2++;
            const newInput = `
            <div class="date-range-container col-md-4" id="dateRangeContainer_2_${newIndex}">
                <input type="text" id="selectedDateRangeInput_2_${newIndex}" name="selectedDateRangeInput_2_${newIndex}" class="date-range-input" placeholder="Start Date - End Date" autocomplete="off">
                <i class="fa-regular fa-calendar-days calenday-icon"></i>
                <p class="remove-date">Remove Date <i class="fa-regular fa-trash-can trash-icon" data-index="${newIndex}"></i></p>
            </div>
        `;
            $(`#${containerID2} #datePickersContainer`).append(newInput);
            initializeDateRangePicker2(`#selectedDateRangeInput_2_${newIndex}`);
            updateSelectedDatesCount2();
        });

        // Event delegation for handling trash icon click for container 2
        $(`#${containerID2} #datePickersContainer`).on('click', '.trash-icon', function () {
            const index = $(this).data('index');

            // Find the input field corresponding to the clicked trash icon
            const dateRangeInput = $(`#selectedDateRangeInput_2_${index}`);
            // Get the date range value from the input field
            const dateRangeValue = dateRangeInput.val();

            let range = [];
            if (dateRangeValue) {
                const [startDate, endDate] = dateRangeValue.split(' - ');

                // Generate an array of dates in the selected range
                let currentDate = moment(startDate, 'MM/DD/YYYY');
                const endMoment = moment(endDate, 'MM/DD/YYYY');
                while (currentDate <= endMoment) {
                    range.push(currentDate.format('MM/DD/YYYY'));
                    currentDate.add(1, 'days');
                }
            }

            // Remove the dates from selectedDates2
            selectedDates2 = selectedDates2.filter(date => !range.includes(date));

            // Update hidden input with updated dates directly
            updateHiddenInput2(selectedDates2);

            // Remove the entire date range container
            $(`#dateRangeContainer_2_${index}`).remove();

            // Reset the indices of remaining date pickers
            $(`#${containerID2} .date-range-container`).each(function (idx, container) {
                $(container).attr('id', `dateRangeContainer_2_${idx}`);
                $(container).find('.date-range-input').attr('id', `selectedDateRangeInput_2_${idx}`);
                $(container).find('.trash-icon').data('index', idx);
            });

            updateSelectedDatesCount2();
        });

        // Read initial value from hidden input if exists for container 2
        const initialSelectedDates2 = $(`#${containerID2} #selectedDateRanges`).val();
        if (initialSelectedDates2) {
            selectedDates2 = initialSelectedDates2.split(',');
            updateDatePickers2(); // Update disabled dates in all date pickers
            updateSelectedDatesCount2();
        }

        // Event listener for calendar icon click for container 2
        $(document).on('click', '.calenday-icon', function () {
            $(this).siblings('.date-range-input').click(); // Simulate a click on the input field
        });
    });


    $(document).ready(function () {
        // Event handler for changes in self_no_of_days input for index 0
        $('#self-no-days0').on('change', function () {
            const maxDays = parseInt($(this).val()); // Get maximum allowed days
            const warningElement = $(this).siblings('#warningMessage0');

            // Get the value of the hidden input field for index 0
            const selectedDatesValue = $('#dateRangeContainerMain_0 #selectedDateRanges').val();

            // Split the value into an array of dates
            const selectedDatesArray = selectedDatesValue.split(',');

            // Count the number of dates
            const dateCount = selectedDatesArray.length;

            // Calculate the number of days in the selected date range
            if (dateCount > maxDays) {
                warningElement.show(); // Show warning message if more dates than allowed
            } else {
                warningElement.hide(); // Hide warning message if within allowed limits
            }
        });

        // Event handler for changes in self_no_of_days input for index 1
        $('#self-no-days1').on('change', function () {
            const maxDays = parseInt($(this).val()); // Get maximum allowed days
            const warningElement = $(this).siblings('#warningMessage1');

            // Get the value of the hidden input field for index 1
            const selectedDatesValue = $('#dateRangeContainerMain_1 #selectedDateRanges').val();

            // Split the value into an array of dates
            const selectedDatesArray = selectedDatesValue.split(',');

            // Count the number of dates
            const dateCount = selectedDatesArray.length;

            // Calculate the number of days in the selected date range
            if (dateCount > maxDays) {
                warningElement.show(); // Show warning message if more dates than allowed
            } else {
                warningElement.hide(); // Hide warning message if within allowed limits
            }
        });

        // Event handler for changes in self_no_of_days input for index 2
        $('#self-no-days2').on('change', function () {
            const maxDays = parseInt($(this).val()); // Get maximum allowed days
            const warningElement = $(this).siblings('#warningMessage2');

            // Get the value of the hidden input field for index 2
            const selectedDatesValue = $('#dateRangeContainerMain_2 #selectedDateRanges').val();

            // Split the value into an array of dates
            const selectedDatesArray = selectedDatesValue.split(',');

            // Count the number of dates
            const dateCount = selectedDatesArray.length;

            // Calculate the number of days in the selected date range
            if (dateCount > maxDays) {
                warningElement.show(); // Show warning message if more dates than allowed
            } else {
                warningElement.hide(); // Hide warning message if within allowed limits
            }
        });
    });




// --= validation for numbers =--
    $(document).ready(function () {
// Function to validate a single input
        function validateInput(inputId, maxDays) {
            let $input = $('#' + inputId);
            let currentValue = parseInt($input.val(), 10);
            let $errorMsg = $('#error-' + inputId);

            if (isNaN(currentValue)) {
                $errorMsg.text('Please enter a valid number.');
                return false;
            } else if (currentValue > maxDays) {
                $errorMsg.text(`Value must be up to ${maxDays} days only.`);
                return false;
            } else {
                $errorMsg.text(''); // Clear the error message when valid
                return true;
            }
        }

// Validate 'availed days' inputs against their corresponding 'no of days'
        function validateAvailedDays() {
            let isValid = true;
            $('.availed-days').each(function () {
                let availedDaysId = $(this).attr('id');
                let correspondingDaysId = $(this).data('section');
                let maxDays = parseInt($('#' + correspondingDaysId).val(), 10);
                let currentValue = $(this).val(); // Keep it as string to check for empty value
                let $errorMsg = $('#error-' + availedDaysId);

                if (currentValue === '') {
                    $errorMsg.text(''); // Clear the error message when input is empty
                } else if (isNaN(parseInt(currentValue, 10)) || parseInt(currentValue, 10) > maxDays) {
                    if (isNaN(parseInt(currentValue, 10))) {
                        $errorMsg.text('Please enter a valid number.');
                    } else {
                        $errorMsg.text(`Availed days cannot exceed ${maxDays} days.`);
                    }
                    isValid = false;
                } else {
                    $errorMsg.text(''); // Clear the error message when valid
                }
            });
            return isValid;
        }

// Trigger validation on input for '.no-of-days' and '.availed-days'
        $('.no-of-days, .availed-days').on('input', function () {
            let inputId = $(this).hasClass('no-of-days') ? $(this).attr('id') : $(this).data('section');
            let maxDays = $('#' + inputId).data('max-days');
            validateInput(inputId, maxDays);
            validateAvailedDays();
        });

// Handle form submission
        $('#impact-days-form-display').on('submit', function (e) {
            let isFormValid = true; // Assume the form is valid initially

// Validate 'no of days' inputs
            $('.no-of-days').each(function () {
                let inputId = $(this).attr('id');
                let maxDays = $(this).data('max-days');
                if (!validateInput(inputId, maxDays)) {
                    isFormValid = false;
                }
            });

// Validate 'availed days'
            if (!validateAvailedDays()) {
                isFormValid = false;
            }

// Check for any remaining error messages
            $('.error-msg').each(function () {
                if ($(this).text() !== '') {
                    isFormValid = false; // Found an error message, form is not valid
                }
            });

// Prevent form submission if not valid
            if (!isFormValid) {
                e.preventDefault(); // Stop form from submitting
                console.log("Form validation failed. Submission prevented.");
            } else {
                console.log("Form is valid. Proceed with further actions or submission.");
            }
        });
    });
// --= validation for numbers =--

    $(document).ready(function () {


        $('#calender-switcher').change(function () {
            var selected = $(this).val(); // Get the selected value from the dropdown

            if (selected == 2) { // Use == for comparison, not =
                $('#show-date-picker').show(); // Show the date picker if selected value is 2
            } else {
                $('#show-date-picker').hide(); // Hide the date picker for other values
            }
        });

        $('.unemployment-claimed-radio').change(function () {
            var row = $(this).closest('tr');

            var targetRowSelector = $(this).data('target-row');
            var otherRow = $(targetRowSelector);

            if ($(this).val() === 'yes') {
// row.find('.custom-file-upload').show();
// row.find('.hideshow_onupload').show();
// Show/hide elements in otherRow
                otherRow.show();
            } else {
// row.find('.custom-file-upload').hide();
// row.find('.hideshow_onupload').hide();
                otherRow.hide();
// Clear input values in the closest tr element
                otherRow.find('input').val('');
            }
        });

        function recurssiveFuncitonsJS() {


            $('.datetimepicker-input').datetimepicker({
                format: 'm/d/Y',
                formatDate: 'm/d/Y',
                autoclose: true,
                orientation: 'bottom',
                timepicker: false,
                defaultDate: '04/01/2020',
                minDate: '04/01/2020',
                maxDate: '12/31/2020',
                scrollMonth: false,
                scrollInput: false,
            });


            $('.datetimepicker-input1').datetimepicker({

                format: 'm/d/Y',
                formatDate: 'm/d/Y',
                autoclose: true,
                orientation: 'bottom',
                timepicker: false,
                defaultDate: '01/01/2021',
                minDate: '01/01/2021', //yesterday is minimum date(for today use 0 or -1970/01/01) April 1, 2020
                maxDate: '03/31/2021', //tomorrow is maximum date calendar December 31, 2020
                scrollMonth: false,
                scrollInput: false,

            });

            $('.datetimepicker-input2').datetimepicker({
                scrollInput: false,
                format: 'm/d/Y',
                formatDate: 'm/d/Y',
                autoclose: true,
                orientation: 'bottom',
                timepicker: false,
                defaultDate: '04/01/2021',
                minDate: '04/01/2021', //yesterday is minimum date(for today use 0 or -1970/01/01) April 1, 2020
                maxDate: '09/30/2021'//tomorrow is maximum date calendar December 31, 2020

            });


//$('.2020_Q2_Q3_Q4_row .fa-calendar').on('click', function() {
//$(this).closest('.input-group-append').prev('.datetimepicker-input').trigger('click');
//});




            $('.input-group-append').on('click', function () {
                $(this).closest('.input-group').find('.datetimepicker-input').trigger('focus');
            });



            $('.input-group-append-second').on('click', function () {
                $(this).closest('.input-group').find('.datetimepicker-input1').trigger('focus');
            });


            $('.input-group-append-third').on('click', function () {
                $(this).closest('.input-group').find('.datetimepicker-input2').trigger('focus');
            });

//jQuery('.add-remove-col input[type="text"]').on('input dp.change', function() {
//        var $container = $(this).closest('.row');
//
//        var $errorMessage = $container.find('.error-message');
//
//        if ($(this).val() !== '') {
//
//            console.log("error message hide");
//            $errorMessage.hide();
//        }else{
//            console.log("error message show");
//        }
//    });
//

        }


        recurssiveFuncitonsJS();





// Function to update the remaining columns after removing a column
        function updateColumns() {
            var count = 1;
            $('#2020_Q2_Q3_Q4Row .add-remove-col').each(function () {
// Update label for="dateX"
//$(this).find('label').attr('for', 'date' + count).text('Date ' + count + ':');

                $(this).find('.col-md-4 label').attr('for', 'date' + count).text('Date ' + count + ':');


                $(this).find('.col-md-6 label').attr('for', 'unemployment_benefit' + count);

// Update input name="2020_Q2_Q3_Q4_X"
                $(this).find('input[type="text"]').attr('name', '2020_Q2_Q3_Q4_' + count);

// Update input id="dateX"
                $(this).find('input[type="text"]').attr('id', 'date' + count);

// Update input id="dateXpicker"
                $(this).find('.input-group.date').attr('id', 'date' + count + 'picker');
                $(this).find('.input-group.date').attr('data-target', '#date' + count + 'picker');

                $(this).find('.input-group.date').attr('data-target', '#date' + count + 'picker');


// Update checkbox id="unemployment_benefitX"
                $(this).find('input[type="checkbox"]').attr('id', 'unemployment_benefit' + count);
                $(this).find('input[type="checkbox"]').attr('name', '2020_Q2_Q3_Q4_' + count + '_un_benefit');

                count++;
            });
        }



        function updateColumns2022Q1() {
            var count = 1;
            $('#2021_Q1Row .add-remove-col').each(function () {
// Update label for="dateX"
//$(this).find('label').attr('for', 'date' + count).text('Date ' + count + ':');

                $(this).find('.col-md-4 label').attr('for', 'date' + count).text('Date ' + count + ':');

// Update input name="2021_Q1_X"
                $(this).find('input[type="text"]').attr('name', '2021_Q1_' + count);

// Update input id="dateX"
                $(this).find('input[type="text"]').attr('id', 'date' + count);

// Update input id="dateXpicker"
                $(this).find('.input-group.date').attr('id', 'date' + count + 'picker');
                $(this).find('.input-group.date').attr('data-target', '#date' + count + 'picker');

// Update checkbox id="unemployment_benefitX"
                $(this).find('input[type="checkbox"]').attr('id', '2021_Q1_' + count + '_un_benefit');
                $(this).find('input[type="checkbox"]').attr('name', '2021_Q1_' + count + '_un_benefit');

                $(this).find('.uncheckbox label').attr('for', '2021_Q1_' + count + '_un_benefit');


                count++;
            });
        }



        function updateColumns2021Q2Q3() {
            var count = 1;
            $('#2021_Q2_Q3Row .add-remove-col').each(function () {
// Update label for="dateX"
//$(this).find('label').attr('for', 'date' + count).text('Date ' + count + ':');

                $(this).find('.col-md-4 label').attr('for', 'date' + count).text('Date ' + count + ':');

// Update input name="2021_Q1_X"
                $(this).find('input[type="text"]').attr('name', '2021_Q2_Q3_' + count);

// Update input id="dateX"
                $(this).find('input[type="text"]').attr('id', 'date' + count);

// Update input id="dateXpicker"
                $(this).find('.input-group.date').attr('id', 'date' + count + 'picker');
                $(this).find('.input-group.date').attr('data-target', '#date' + count + 'picker');

// Update checkbox id="unemployment_benefitX"
                $(this).find('input[type="checkbox"]').attr('id', '2021_Q2_Q3_' + count + '_un_benefit');
                $(this).find('input[type="checkbox"]').attr('name', '2021_Q2_Q3_' + count + '_un_benefit');

                $(this).find('.uncheckbox label').attr('for', '2021_Q2_Q3_' + count + '_un_benefit');

                count++;
            });
        }



// Add row functionality
        jQuery(document).on("click", "#2020_Q2_Q3_Q4Row .add-row", function () {

            let numberOfRow = $("#2020_Q2_Q3_Q4Row").find(".add-remove-col").length;
            numberOfRow = parseInt(numberOfRow) + 1;
            console.log(numberOfRow);
//var lastOwnerNumber = jQuery("#ownership_table_sub_row .owner_number:last").text() || 0;
            if (numberOfRow <= 10) {
//var no = updated_no(lastOwnerNumber);
//lastOwnerNumber = no;
//var get_index = get_index_no(lastOwnerNumber);
//console.log(get_index);
                var newRow = jQuery('<div class="col-md-6 add-remove-col"><div class="row mb-3 align-items-center"><div class="col-md-4"><label for="date1">Date ' + numberOfRow + ':</label><div class="input-group date" id="date' + numberOfRow + 'picker" data-target-input="nearest"><input type="text" name="2020_Q2_Q3_Q4_' + numberOfRow + '" id="date' + numberOfRow + '" class="form-control datetimepicker-input" data-target="#date' + numberOfRow + 'picker" value="" autocomplete="off" readonly=""><div class="input-group-append" data-target="#date' + numberOfRow + 'picker" data-toggle="datetimepicker"><div class="input-group-text"><i class="fa fa-calendar"></i></div></div></div></div><div class="col-md-6"><div class="uncheckbox" data-target-input="nearest"><input type="checkbox" id="unemployment_benefit' + numberOfRow + '" name="2020_Q2_Q3_Q4_' + numberOfRow + '_un_benefit" value="1"><label for="unemployment_benefit' + numberOfRow + '" style="padding-left: 15px;">Unemployment benefit availed?</label></div></div><div class="col-md-2"><div class="icon-action"><i class="fa fa-minus-circle remove-row mr-1" aria-hidden="true"></i><i class="fa fa-plus-circle add-row" aria-hidden="true"></i></div></div><div class="error-message" style="display: none;">Please select a date before marking unemployment benefit.</div></div></div>');

                newRow.appendTo("#2020_Q2_Q3_Q4Row");
                $('#2020_Q2_Q3_Q4Row .remove-row').show();

            }

            recurssiveFuncitonsJS();
// Hide all add-row buttons
            $('#2020_Q2_Q3_Q4Row .add-row').hide();
// Show add-row button only on the last row
            $('#2020_Q2_Q3_Q4Row .add-remove-col:last .add-row').show();
            $('#2020_Q2_Q3_Q4Row .remove-row').show();
//$('#2020_Q2_Q3_Q4Row .add-remove-col:first .remove-row').hide();
            if (numberOfRow >= 10) {
                $('#2020_Q2_Q3_Q4Row .add-row').hide();
            }

        });



// Add row functionality 2021_Q1Row
        jQuery(document).on("click", "#2021_Q1Row .add-row", function () {

            let numberOfRow = $("#2021_Q1Row").find(".add-remove-col").length;
            numberOfRow = parseInt(numberOfRow) + 1;
            console.log(numberOfRow);
//var lastOwnerNumber = jQuery("#ownership_table_sub_row .owner_number:last").text() || 0;
            if (numberOfRow <= 10) {
//var no = updated_no(lastOwnerNumber);
//lastOwnerNumber = no;
//var get_index = get_index_no(lastOwnerNumber);
//console.log(get_index);
                var newRow = jQuery('<div class="col-md-6 add-remove-col"><div class="row mb-3 align-items-center"><div class="col-md-4"><label for="date1">Date ' + numberOfRow + ':</label><div class="input-group date" id="date' + numberOfRow + 'picker" data-target-input="nearest"><input type="text" name="2021_Q1_' + numberOfRow + '" id="date' + numberOfRow + '" class="form-control datetimepicker-input1"  data-target="#date' + numberOfRow + 'picker" value="" autocomplete="off" readonly><div class="input-group-append-second" data-target="#date' + numberOfRow + '>picker" data-toggle="datetimepicker"><div class="input-group-text"><i class="fa fa-calendar"></i></div></div></div></div><div class="col-md-6"><div class="uncheckbox" data-target-input="nearest"><input type="checkbox" id="2021_Q1_' + numberOfRow + '_un_benefit" name="2021_Q1_' + numberOfRow + '_un_benefit" value="1"><label for="2021_Q1_' + numberOfRow + '_un_benefit" style="padding-left: 15px;">Unemployment benefit availed?</label></div></div><div class="col-md-2"><div class="icon-action"><i class="fa fa-minus-circle remove-row mr-1" aria-hidden="true"></i><i class="fa fa-plus-circle add-row" aria-hidden="true"></i></div></div><div class="error-message" style="display: none;">Please select a date before marking unemployment benefit.</div></div></div>');

                newRow.appendTo("#2021_Q1Row");
                $('#2021_Q1Row .remove-row').show();

            }


            recurssiveFuncitonsJS();
// Hide all add-row buttons
            $('#2021_Q1Row .add-row').hide();
// Show add-row button only on the last row
            $('#2021_Q1Row .add-remove-col:last .add-row').show();
            $('#2021_Q1Row .remove-row').show();

            if (numberOfRow >= 10) {
                $('#2021_Q1Row .add-row').hide();
            }
//$('#2021_Q1Row .add-remove-col:first .remove-row').hide();

        });



// Add row functionality
        jQuery(document).on("click", "#2021_Q2_Q3Row .add-row", function () {

            let numberOfRow = $("#2021_Q2_Q3Row").find(".add-remove-col").length;
            numberOfRow = parseInt(numberOfRow) + 1;
            console.log(numberOfRow);
//var lastOwnerNumber = jQuery("#ownership_table_sub_row .owner_number:last").text() || 0;
            if (numberOfRow <= 10) {
//var no = updated_no(lastOwnerNumber);
//lastOwnerNumber = no;
//var get_index = get_index_no(lastOwnerNumber);
//console.log(get_index);
                var newRow = jQuery('<div class="col-md-6 add-remove-col"><div class="row mb-3 align-items-center"><div class="col-md-4"><label for="date' + numberOfRow + '">Date ' + numberOfRow + ':</label><div class="input-group date" id="date' + numberOfRow + 'picker" data-target-input="nearest"><input type="text" name="2021_Q2_Q3_' + numberOfRow + '" id="date' + numberOfRow + '" class="form-control datetimepicker-input2"  data-target="#date' + numberOfRow + 'picker" value="" autocomplete="off" readonly><div class="input-group-append-third" data-target="#date' + numberOfRow + 'picker" data-toggle="datetimepicker"><div class="input-group-text"><i class="fa fa-calendar"></i></div></div></div></div><div class="col-md-6"><div class="uncheckbox" data-target-input="nearest"><input type="checkbox" id="2021_Q2_Q3_' + numberOfRow + '_un_benefit" name="2021_Q2_Q3_' + numberOfRow + '_un_benefit" value="1"><label for="2021_Q2_Q3_' + numberOfRow + '_un_benefit" style="padding-left: 15px;">Unemployment benefit availed?</label></div></div><div class="col-md-2"><div class="icon-action"><i class="fa fa-minus-circle remove-row mr-1" aria-hidden="true"></i><i class="fa fa-plus-circle add-row" aria-hidden="true"></i></div></div><div class="error-message" style="display: none;">Please select a date before marking unemployment benefit.</div></div></div>');

                newRow.appendTo("#2021_Q2_Q3Row");
                $('#2021_Q2_Q3Row .remove-row').show();

            }


            recurssiveFuncitonsJS();
// Hide all add-row buttons
            $('#2021_Q2_Q3Row .add-row').hide();
// Show add-row button only on the last row
            $('#2021_Q2_Q3Row .add-remove-col:last .add-row').show();
            $('#2021_Q2_Q3Row .remove-row').show();
//$('#2021_Q2_Q3Row .add-remove-col:first .remove-row').hide();
            if (numberOfRow >= 10) {
                $('#2021_Q2_Q3Row .add-row').hide();
            }

        });



// Remove row functionality
        jQuery(document).on("click", "#2020_Q2_Q3_Q4Row .remove-row", function () {
            console.log("#2020_Q2_Q3_Q4Row .remove-row");
            var $rowToRemove = jQuery(this).closest("#2020_Q2_Q3_Q4Row .add-remove-col");
            var rowsCount = jQuery("#2020_Q2_Q3_Q4Row .remove-row").length;
            console.log(rowsCount);
            if (rowsCount > 1) {
                $rowToRemove.remove();
                rowsCount--; // Update rowsCount after removal
                if (rowsCount === 1) {
// If only one row remains, hide the remove icon
                    jQuery("#2020_Q2_Q3_Q4Row .remove-row").hide();
                } else {
// If more than one row, ensure the remove icon is visible
                    jQuery("#2020_Q2_Q3_Q4Row .remove-row").show();
                }

                if (rowsCount < 10) {
                    $('#2020_Q2_Q3_Q4Row .add-row').show();
                }
            }

            updateColumns();
            $('#2020_Q2_Q3_Q4Row .add-row').hide(); // Hide all add-row buttons
            $('#2020_Q2_Q3_Q4Row .add-remove-col:last .add-row').show(); // Show on last row only

        });

        jQuery(document).on("click", "#2021_Q1Row .remove-row", function () {
            console.log("#2021_Q1Row .remove-row");
            var $rowToRemove = jQuery(this).closest("#2021_Q1Row .add-remove-col");
            var rowsCount = jQuery("#2021_Q1Row .remove-row").length;
            console.log(rowsCount);
            if (rowsCount > 1) {
                $rowToRemove.remove();
                rowsCount--; // Decrement rowsCount after removal
// Hide the remove-row icon if only one row is left
                if (rowsCount === 1) {
                    jQuery("#2021_Q1Row .remove-row").hide();
                } else {
                    jQuery("#2021_Q1Row .remove-row").show();
                }
                if (rowsCount < 10) {
                    $('#2021_Q1Row .add-row').show();
                }
            }
// Additional logic to update columns if needed
            updateColumns2022Q1();
            $('#2021_Q1Row .add-row').hide(); // Hide all add-row buttons
            $('#2021_Q1Row .add-remove-col:last .add-row').show();
        });


        jQuery(document).on("click", "#2021_Q2_Q3Row .remove-row", function () {
            console.log("#2021_Q2_Q3Row .remove-row");
            var $rowToRemove = jQuery(this).closest("#2021_Q2_Q3Row .add-remove-col");
            var rowsCount = jQuery("#2021_Q2_Q3Row .remove-row").length;
            console.log(rowsCount);
            if (rowsCount > 1) {
                $rowToRemove.remove();
                rowsCount--; // Decrement rowsCount after removal
// Hide the remove-row icon if only one row is left
                if (rowsCount === 1) {
                    jQuery("#2021_Q2_Q3Row .remove-row").hide();
                } else {
                    jQuery("#2021_Q2_Q3Row .remove-row").show();
                }
                if (rowsCount < 10) {
                    $('#2021_Q2_Q3Row .add-row').show();
                }
            }
// Additional logic to update columns if needed
            updateColumns2021Q2Q3();
            $('#2021_Q2_Q3Row .add-row').hide(); // Hide all add-row buttons
            $('#2021_Q2_Q3Row .add-remove-col:last .add-row').show();
        });

// Hide all add-row buttons for 2020_Q2_Q3_Q4Row and show on the last row only
        $('#2020_Q2_Q3_Q4Row .add-row').hide();
        $('#2020_Q2_Q3_Q4Row .add-remove-col:last .add-row').show();

// Hide all add-row buttons for 2021_Q1Row and show on the last row only
        $('#2021_Q1Row .add-row').hide();
        $('#2021_Q1Row .add-remove-col:last .add-row').show();

// Hide all add-row buttons for 2021_Q2_Q3Row
        $('#2021_Q2_Q3Row .add-row').hide();
// Show the add-row button on the last row
        $('#2021_Q2_Q3Row .add-remove-col:last .add-row').show();

        if ($('#2020_Q2_Q3_Q4Row .add-remove-col').length >= 10) {
            $('#2020_Q2_Q3_Q4Row .add-row').hide();
        }
        if ($('#2021_Q1Row .add-remove-col').length >= 10) {
            $('#2021_Q1Row .add-row').hide();
        }
        if ($('#2021_Q2_Q3Row .add-remove-col').length >= 10) {
            $('#2021_Q2_Q3Row .add-row').hide();
        }
    });
</script>
