<?php

    // $table_name = $wpdb->prefix . 'audit_logs';
    // $wpdb->query("DELETE FROM $table_name WHERE FieldID=1621 OR FieldID=9023");

?>
<style>
.custom_opp_tab {
    padding: 0px 0px 12px 0px;
    border: 1px solid #d8d8d882;
    box-shadow: 5px 5px 10px 0px rgb(178 178 178 / 40%) !important;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 20px;
    border-bottom: 2px solid #ff5c00;
}
 
.custom_opp_tab .custom_opp_tab_header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #d5d5d5;
    padding-bottom: 10px;
    padding-top: 10px;
    align-items: baseline;
}
.custom_opp_tab .custom_opp_tab_header h5 {
    font-weight: 600;
    font-size: 15px;
    width: 90%;
}
 
.opp_edit_dlt_btn.projects-iris {
    width: auto !important;
    display: flex;
    align-items: center;
}
 
.opp_edit_dlt_btn.projects-iris select {
    height: 25px;
    width: 160px;
}
 
.custom_opp_tab .lead_des {
    margin-top: 10px;
}
.custom_opp_tab .lead_des p {
    color: #000;
    font-weight: 400;
    margin-bottom: 5px;
}
 
.expand_pp_div {
    margin-top: 5px;
    margin-bottom: 5px;
    font-weight: 600;
}
 
.expand_pp_div i {
    padding-left: 5px;
    vertical-align: middle;
}
 
.opp_edit_dlt_btn .edit_opportunity {
    background: #ff5c00 !important;
    border: 0 !important;
    padding: 5px;
    font-weight: 600;
    font-size: 12px;
    color: #fff !important;
    border-radius: 5px;
    width: 25px;
    height: 25px;
    display: inline-block;
    text-align: center;
    margin-left: 10px;
}
 
.nav-pills .nav-link.active {
    padding: 7px 25px !important;
}

.total-payment-invoice {
    border: 1px solid #b1c9db;
    margin-bottom: 7px !important;
    border-radius: 5px;
    background: rgba(220, 233, 244, 0.50);
    display: flex;
    padding: 12px 10px;
    align-items: center;
    justify-content: space-between;
}

.total-payment-invoice h4 {
    margin-bottom: 0;
    color: #333333;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}

.total-payment-invoice p {
    margin-bottom: 0;
    color: #333333;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    margin-right: 60px;
}

.view-partially tr th {
  background: var(--gray-100-e-5-e-6-e-6, #E5E6E6);
  padding: 12px 10px !important;
  color: var(--gray-800333333, #333333 - GRAY 800);
  font-size: 13px !important;
  font-style: normal;
  font-weight: 600 !important;
  line-height: normal !important;
  border-bottom: 0 !important;
}

.view-partially tr td {
  background: rgba(242, 242, 242, 0.50);
  color: var(--gray-800333333, #333333 - GRAY 800);
  font-style: normal;
  font-weight: 400;
  line-height: normal !important;
  word-break: break-all;
}


.view-partially tr th:first-child {
  width: 140px;
}

.view-partially tr th:nth-child(2) {
  width: 160px;
}

.view-partially tr th:nth-child(3) {
  width: 160px;
}

.view-partially tr th:nth-child(4) {
  width: 190px;
}

.view-partially tr th:nth-child(5) {
  width: 170px;
}

.view-partially tr th:nth-child(6) {
  width: 200px;
}

.swal2-actions .btn-cancel{
    background: #d9534f !important;
}
</style>
<?php

$additional_table = $wpdb->prefix . 'erc_iris_leads_additional_info';
$business_info_table = $wpdb->prefix . 'erc_business_info';
$product_table = $wpdb->prefix . 'crm_products';
$milestone_table = $wpdb->prefix . 'milestones';
$milestone_status_table = $wpdb->prefix . 'milestone_stages';

// $notes_table = $wpdb->prefix . 'erc_project_notes';
// $wpdb->query("ALTER TABLE $notes_table ADD `confidential_notes` INT(11) NOT NULL DEFAULT 0 AFTER `note`");
$users = get_users( array( 
    'role__in' => array('master_ops','echeck_staff','echeck_admin','echeck_client','iris_affiliate_users','master_sales','iris_sales_agent','iris_sales_agent_rep' ),
    'orderby' => 'display_name',
    'order' => 'ASC'  ) );
// -------- confidence notes code -------
     $confidence_user = 0;
     
     $confidence_check = '';
     if(get_current_user_id() == 44019){ // nedeen id 
        $confidence_check = 'checked';
     }
     $option_table = $wpdb->prefix.'onedrive_options';
      $selected_user = $wpdb->get_var("SELECT meta_value FROM $option_table WHERE meta_key='notes_confidence_users' ");
      if(!empty($selected_user)){
        $selected_users = explode(",",$selected_user);
        $current_user_id = get_current_user_id();
        if(in_array($current_user_id,$selected_users)){
            $confidence_user = 1;
        }
     }

$project_id = $_REQUEST['id'];
$project = $wpdb->get_row("SELECT {$wpdb->prefix}projects.*,{$wpdb->prefix}erc_business_info.business_legal_name,{$wpdb->prefix}erc_business_info.doing_business_as,{$wpdb->prefix}erc_business_info.business_category,{$wpdb->prefix}erc_business_info.website_url,{$wpdb->prefix}erc_business_info.authorized_signatory_name,{$wpdb->prefix}erc_business_info.business_phone,{$wpdb->prefix}erc_business_info.business_email,{$wpdb->prefix}erc_business_info.business_title,{$wpdb->prefix}erc_business_info.zip,{$wpdb->prefix}erc_business_info.street_address,{$wpdb->prefix}erc_business_info.state,{$wpdb->prefix}erc_business_info.city,{$wpdb->prefix}erc_business_info.identity_document_type,{$wpdb->prefix}erc_business_info.identity_document_number,{$wpdb->prefix}erc_business_info.business_entity_type,{$wpdb->prefix}erc_business_info.registration_number,{$wpdb->prefix}erc_business_info.registration_date,{$wpdb->prefix}erc_business_info.state_of_registration,{$wpdb->prefix}crm_products.Title as product_name,{$wpdb->prefix}milestones.milestone_name, {$wpdb->prefix}milestones.status as milestoneActiveStatus, {$wpdb->prefix}milestones.map as milestoneMap,{$wpdb->prefix}milestone_stages.stage_name as milestoneStatus,{$wpdb->prefix}milestone_stages.status as StageActiveStatus,{$wpdb->prefix}milestone_stages.deleted_at as StageDeleteStatus,{$wpdb->prefix}erc_erc_intake.fee_type
                                                                    FROM {$wpdb->prefix}projects 
                                                                    JOIN {$wpdb->prefix}erc_business_info 
                                                                    ON {$wpdb->prefix}projects.lead_id = {$wpdb->prefix}erc_business_info.lead_id
                                                                    JOIN {$wpdb->prefix}crm_products 
                                                                    ON {$wpdb->prefix}projects.product_id = {$wpdb->prefix}crm_products.ProductID 
                                                                    LEFT JOIN {$wpdb->prefix}milestones 
                                                                    ON {$wpdb->prefix}projects.milestone_id = {$wpdb->prefix}milestones.milestone_id 
                                                                    LEFT JOIN {$wpdb->prefix}milestone_stages
                                                                    ON {$wpdb->prefix}projects.milestone_stage_id = {$wpdb->prefix}milestone_stages.milestone_stage_id 
                                                                    LEFT JOIN {$wpdb->prefix}erc_erc_intake
                                                                    ON {$wpdb->prefix}projects.lead_id = {$wpdb->prefix}erc_erc_intake.lead_id 
                                                                    WHERE {$wpdb->prefix}projects.deleted_at IS NULL 
                                                                    AND {$wpdb->prefix}projects.project_id = ".$project_id." ");
$contact_id = $project->contact_id;
$lead_id = $project->lead_id;
if($contact_id > 0){
    $contactdata = $wpdb->get_row("SELECT first_name,last_name,title FROM {$wpdb->prefix}op_contacts WHERE id = ".$contact_id."");
    $full_name = $contactdata->first_name.' '.$contactdata->last_name;
    $contact_title = $contactdata->title;
}else{
    $full_name = '';
    $contact_title = '';
}
$phonedata = $wpdb->get_row("SELECT {$wpdb->prefix}op_phone.phone 
                                    FROM {$wpdb->prefix}op_phone_contact 
                                    JOIN {$wpdb->prefix}op_phone ON {$wpdb->prefix}op_phone_contact.phone_id = {$wpdb->prefix}op_phone.id
                                    WHERE {$wpdb->prefix}op_phone_contact.contact_id = ".$contact_id."");
if(!empty($phonedata)){
    $phone = $phonedata->phone;
}else{
    $phone = '';
}

$emaildata = $wpdb->get_row("SELECT {$wpdb->prefix}op_emails.email 
                                    FROM {$wpdb->prefix}op_email_contact 
                                    JOIN {$wpdb->prefix}op_emails ON {$wpdb->prefix}op_email_contact.email_id = {$wpdb->prefix}op_emails.id
                                    WHERE {$wpdb->prefix}op_email_contact.contact_id = ".$contact_id."");
if(!empty($emaildata)){
    $email = $emaildata->email;
}else{
    $email = '';
}

/*$addressdata = $wpdb->get_row("SELECT {$wpdb->prefix}op_address.primary_address_postalcode,{$wpdb->prefix}op_address.primary_address_street,{$wpdb->prefix}op_address.primary_address_city,{$wpdb->prefix}op_address.primary_address_state 
                                    FROM {$wpdb->prefix}op_address_contact 
                                    JOIN {$wpdb->prefix}op_address ON {$wpdb->prefix}op_address_contact.address_id = {$wpdb->prefix}op_address.id
                                    WHERE {$wpdb->prefix}op_address_contact.contact_id = ".$contact_id."");
if(!empty($addressdata)){
    $zip = $addressdata->primary_address_postalcode;
    $street_address = $addressdata->primary_address_street;
    $city = $addressdata->primary_address_city;
    $state = $addressdata->primary_address_state;
}else{
    $zip = '';
    $street_address = '';
    $city = '';
    $state = '';
}*/


$where_milestone = ' 1=1 ';
if (isset($project->product_id) && !empty($project->product_id)) {
    $where_milestone .= ' AND FIND_IN_SET(' . $project->product_id . ',' . $milestone_table . '.product_id) ';
}
$all_milestones =  $wpdb->get_results("SELECT $milestone_table.milestone_id,$milestone_table.milestone_name, $milestone_table.map,$milestone_table.status,$milestone_table.deleted_at FROM $milestone_table WHERE $where_milestone  AND $milestone_table.status = 'active' AND $milestone_table.deleted_at IS NULL AND $milestone_table.map LIKE '%\"project\"%'");
$where_stage = ' 1=1 ';
if (isset($project->milestone_id) && !empty($project->milestone_id)) {
    $where_stage .= ' AND ' . $milestone_status_table . '.milestone_id = ' . $project->milestone_id . ' ';
}
$all_milestone_status =  $wpdb->get_results("SELECT $milestone_status_table.milestone_stage_id,$milestone_status_table.stage_name,$milestone_status_table.status,$milestone_status_table.deleted_at FROM $milestone_status_table WHERE $where_stage AND $milestone_status_table.status = 'active' AND $milestone_status_table.deleted_at IS NULL");
$user_details = get_user_by('id', $project->created_by);
$owner_name = $user_details->display_name;

$login_user_details = get_user_by('id', get_current_user_id());
$login_user_name = $login_user_details->display_name;

$all_users = get_users(array('role__in' => array('Master_Sales', 'Iris_Sales_Agent')));
$active_sales_agents = $wpdb->get_results("SELECT userid,full_name FROM {$wpdb->prefix}erc_sales_team WHERE active_sales_agent = 1");
$contacts_table = $wpdb->prefix . 'op_contacts';
if ($project->contact_id > 0) {
    $contact_data = $wpdb->get_row("SELECT first_name,last_name,trash FROM $contacts_table WHERE id = " . $project->contact_id . "");
    if (!empty($contact_data)) {

        if ($contact_data->trash == 1) {
            $primary_contact = $contact_data->first_name . ' ' . $contact_data->last_name . ' (disabled)';
        } else {
            $primary_contact = $contact_data->first_name . ' ' . $contact_data->last_name;
        }
    } else {
        $primary_contact = '';
    }
} else {
    $primary_contact = '';
}

$lead_id = $project->lead_id;
$table_name = $wpdb->prefix . 'op_contacts';
$query = $wpdb->prepare("SELECT $table_name.id,$table_name.first_name,$table_name.last_name, $table_name.trash FROM $table_name WHERE $table_name.report_to_id = $lead_id AND $table_name.active = 1 AND $table_name.trash = 0");
$all_contacts = $wpdb->get_results($query);

$project_id = $project->project_id;
$business_legal_name = $project->business_legal_name;
$doing_business_as = $project->doing_business_as;
$business_category = $project->business_category;
$website_url = $project->website_url;
$authorized_signatory_name = $project->authorized_signatory_name;
$business_phone = $project->business_phone;
$business_email = $project->business_email;
$business_title = $project->business_title;
$street_address = $project->street_address;
$city = $project->city;
$state = $project->state;
$zip = $project->zip;
$identity_document_type = $project->identity_document_type;
$identity_document_number = $project->identity_document_number;
$business_entity_type = $project->business_entity_type;
$registration_number = $project->registration_number;
$registration_date = $project->registration_date;
$state_of_registration = $project->state_of_registration;
$notes_table = $wpdb->prefix . 'erc_project_notes';
$total_notes = $wpdb->get_results("SELECT id FROM $notes_table WHERE project_id='" . $project_id . "' ORDER BY id DESC ");
$total_notes_count = count($total_notes);

$note_where = "";
if($confidence_user==0){
    $note_where = " AND confidential_notes=0";
}
$all_notes = $wpdb->get_results("SELECT * FROM $notes_table WHERE project_id='" . $project_id . "'". $note_where." ORDER BY id DESC LIMIT 10 OFFSET 0");

//collaborators
$collaborators = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}collaborators WHERE project_id = ".$project_id."");
$collaborator_users = array( 
                        'role__in' => array( 'iris_sales_agent', 'iris_sales_agent_rep', 'master_sales', 'echeck_staff', 'echeck_admin', 'master_ops', 'iris_affiliate_users' ),
                        'orderby' => 'display_name',
                        'order' => 'ASC' 
                    );
$listed_users = get_users( $collaborator_users );
$current_user_id = get_current_user_id();
$user_data = get_user_by('id', $current_user_id);
$user_roles = $user_data->roles;
if(in_array("iris_affiliate_users", $user_roles) || in_array("iris_employee", $user_roles) || in_array("account_manager", $user_roles)){
    $readonly = "readonly";
    $disabled = "disabled";
    $is_affiliate = 1;
}else{
    $readonly = "";
    $disabled = "";
    $is_affiliate = 0;
}
?>
<style type="text/css">
    .manage_project_sub_heading{
        background: transparent linear-gradient(89deg, #1658A5 0%, #0089C2 100%) 0% 0% no-repeat padding-box !important;
    margin-top: 0px !important;
    padding: 10px;
    border-radius: 10px;
    margin: auto 14px;
    }
    .manage_project_sub_heading .nav-link {
        padding: 7px 24px !important;
    }
    .modal-backdrop.fade{
        opacity: 0 !important;
        z-index: 21;
    }
    .modal-dialog{
        top: 54px !important;
    }

  .erc_comment_btn{
    color: #fff;
    padding: 0.5rem !important;
    background: #ff5c00;
    text-align: center;
    margin: 0 auto;
    width: 32px;
    display: block;
    height: 32px;
    cursor: pointer;
    border-radius: 50% !important;
    line-height: 17px;
    display: inline-block;
  }
    
 .view_erc_comment{
    color: #fff;
    padding: 0.5rem !important;
    background: transparent linear-gradient(89deg, #ff5c00 0%, #ff5c00 100%) 0% 0% no-repeat padding-box;
    text-align: center;
    margin: 0 auto;
    width: 32px;
    display: block;
    height: 32px;
    cursor: pointer;
    border-radius: 50% !important;
    line-height: 17px;
    display: inline-block;
}
.view_erc_comment i{
    color: #fff;
}
.add_erc_comment_btn{
    color: #fff;
    padding: 0.5rem !important;
    background: #ff5c00;
    text-align: center;
    margin: 0 auto;
    width: 32px;
    display: block;
    height: 32px;
    cursor: pointer;
    border-radius: 50% !important;
    line-height: 17px;
    display: inline-block;
}
   .close, #close-doc-com{ 
    position: absolute;
    right: -15px;
    top: -15px;
    background: red !important;
    text-align: center;
    width: 28px;
    height: 28px !important;
    cursor: pointer;
    border-radius: 50% !important;
    line-height: 10px !important;
    color: #fff !important;
    opacity: 1 !important;
    padding: 0 !important;
    margin: 0 !important;
    border:none;
}

.view-comment, .stc-submit-comment-btn, .add_approved_project_rejected_comment_btn{
        background-color: #ff5c00 !important;
        border:none;
        color: #fff!important;
    }
.edit-icon-lead a {
      display: flex;
      justify-content: end;
      padding-bottom: 0px;
      font-size: 13px;
      color: #000 !important;
    }

    .confidential-notes-div{
        background: #FFA500;
    }
    .confidential-notes-div .date-time , .confidential-notes-div .edit_self_notes, .confidential-notes-div .delete_self_notes{
        color: #000 !important;   
    }
  .edit_self_notes , .delete_self_notes{
    font-size: 18px;
    color: #ff5c00 !important;
  }    
  .edit_self_notes{
    padding-right:5px;
  }
  .swal-footer{
    text-align: center;
  }
 .confidential-notes-div .edit_self_notes,.confidential-notes-div .delete_self_notes{
    display: block!important;
}
.note-listing-div .edit_self_notes,.note-listing-div .delete_self_notes{
    display: none;
} 

.owner-main-div div{
    display: none !important;
}
 .custom-ownership{
    min-width: 100%;   
 }

.custom-ownership .dropdown-toggle{
    font-size: 14px;
    line-height: 2;
    color: #32373c;
    border-color: #7e8993;
    box-shadow: none;
    border-radius: 3px;
    padding: 0 24px 0 8px;
    min-height: 40px;
    max-width: 25rem;
    -webkit-appearance: none;
    background: #fff url(data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23555%22%2F%3E%3C%2Fsvg%3E) no-repeat right 5px top 55%;
    background-size: 16px 16px;
    cursor: pointer;
    vertical-align: middle;
}

</style>


<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.8.1/css/bootstrap-select.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.8.1/js/bootstrap-select.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>



<link rel='stylesheet' id='dataTable_Style-css'  href='https://cdn.datatables.net/1.10.22/css/jquery.dataTables.min.css?ver=5.4' media='all' />
<script src='https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js?ver=1.0.0'></script>

<div class="main_content_iner">
    <div class="container-fluid p-0">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <div class="white_card card_height_100 mb_30">
                    <div class="white_card_header">
                        <div class="box_header m-0 new_report_header">
                            <div class="title_img">
                                <img src="<?php echo get_site_url(); ?>/wp-content/plugins/oc-crm/assets/img/opportunity-icon.png" class="page-title-img" alt="">
                                <h4>Manage ERC Project</h4>
                            </div>
                        </div>
                        <div class="d-flex">
                            <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                <li class="nav-item active">
                                    <a class="nav-link tab-btn" id="eleve-project-tab" data-toggle="tab" href="#eleve-project" role="tab" aria-controls="first" aria-selected="true">Project</a>
                                </li>
                                <?php if (in_array("echeck_staff", $user_roles) || in_array("echeck_admin", $user_roles) || in_array("master_ops", $user_roles) || in_array("echeck_client", $user_roles) || in_array("administrator", $user_roles)) {?>
                                    <li class="nav-item">
                                        <a class="nav-link tab-btn" id="pills-bank" data-toggle="pill" href="#pills-bank-info" role="tab" aria-controls="pills-bank-info" aria-selected="false">Bank Info</a>
                                    </li>
                                <?php } ?>
                                <li class="nav-item">
                                    <a class="nav-link tab-btn" id="pills-intake" data-toggle="pill" href="#pills-intke" role="tab" aria-controls="pills-intke" aria-selected="false">Intake</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link tab-btn" id="pills-fees" data-toggle="pill" href="#pills-fee" role="tab" aria-controls="pills-fee" aria-selected="false">Fees</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link tab-btn" id="pills-documents" data-toggle="pill" href="#pills-document" role="tab" aria-controls="pills-document" aria-selected="false">Documents</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link tab-btn" id="eleve-invoice-tab" data-toggle="tab" href="#eleve-invoices" role="tab" aria-controls="pills-invoices" aria-selected="true">Invoices</a>
                                </li>								
                                <li class="nav-item">
                                    <a class="nav-link tab-btn" id="pills-audit-logs" data-toggle="pill" href="#pills-logs" role="tab" aria-controls="pills-logs" aria-selected="false">Audit Logs</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div class="white_card_body p-15">
                        <div class="row">
                            <div class="col-md-9" id="left_section">
                                <div class="show_message_popup"></div>
                                <form action="<?= admin_url("admin-post.php") ?>" id="lead_update_form" method="post">
                                    <input type="hidden" name="action" value="update_erc_project_info">
                                    <input type="hidden" name="lead_id" value="<?php echo $lead_id;?>">
                                    <input type="hidden" name="product_id" value="<?php echo $project->product_id;?>">
                                    <input type="hidden" name="project_id" value="<?php echo $project_id;?>">
                                    <div class="tab-content" id="pills-tabContent">
                                        <div class="tab-pane active erc-project-scroll" id="eleve-project" role="tabpanel" aria-labelledby="eleve-project-tab">
                                            <div class="erc-project-view">
                                                <fieldset>
                                                    <legend>Project Details</legend>
                                                    <div class="row mb-3 align-items-center">
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Name</label>
                                                            <input type="text" id="project_name" name="project_name" class="crm-erp-field form-control" value="<?php echo $project->project_name;?>" readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Business</label>
                                                            <a href="<?php echo get_site_url();  ?>/wp-admin/admin.php?page=iris-fields-v1.php&lead_id=<?php echo $lead_id;  ?>" target="_blank" class="btn btn-primary view_business">View</a>
                                                            <input type="text" class="crm-erp-field form-control" value="<?php echo $project->business_legal_name;?>" readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Products</label>
                                                            <input type="text" class="crm-erp-field form-control" value="<?php echo $project->product_name;?>" readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Fee</label>
                                                            <input type="text" id="project_fee" class="crm-erp-field form-control" value="<?php echo $project->fee_type;?>" readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Review Status</label>
                                                          <?php
                                                            $review_status = $project->review_status;
                                                          ?>
                                                            <select class="crm-erp-field form-control" name="review_status" id="review_status">
                                                                <option value="" <?php if($review_status == ''){echo "selected";}?>>Select Review Status</option>
                                                                <option value="pending" <?php if($review_status == 'pending'){echo "selected";}?>>Pending</option>
                                                                <option value="completed" <?php if($review_status == 'completed'){echo "selected";}?>>Completed</option>
                                                            </select>
                                                        </div>

                                                        <div class="floating col-sm-4 mb-3 review_link" style="<?php if($review_status == 'completed'){echo "display:block;";}else{echo "display:none;";}?>">
                                                            <label>Review link</label>
                                                            <input type="text" id="review_link" class="crm-erp-field form-control" name="review_link" value="<?php echo $project->review_link;?>">
                                                            <p class="review_link_error" style="color:#ff0010;"></p>
                                                        </div>
                                                        <!-- <div class="floating col-sm-4 mb-3">
                                                            <label>Maximum Credit</label>
                                                            <input type="text" id="maximum_credit" class="crm-erp-field form-control" value="<?php echo $project->maximum_credit;?>" <?php echo $readonly;?>>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Estimated Fee</label>
                                                            <input type="text" id="estimated_fee" class="crm-erp-field form-control" value="<?php echo $project->estimated_fee;?>" <?php echo $readonly;?>>
                                                        </div> -->
                                                    </div>
                                                </fieldset>
                                            </div>

                                            <div class="erc-project-view">
                                            <fieldset>
                                                <legend>Account Info</legend>
                                                <div class="row edit-icon-lead">
                                                    <a href="javascript:void(0)" title="Edit"><i class="fa-regular fa-pen-to-square edit_lead_info" style="font-weight: 700"></i></a>
                                                </div>
                                                <div class="row mb-3">
                                                    <div class="col-sm-12 erc-project-view-title">
                                                        <h2>Personal Info</h2>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Full Name</label>
                                                        <input type="text" class="crm-erp-field form-control" name="full_name" id="full_name" value="<?php echo $authorized_signatory_name;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Contact No.</label>
                                                        <input type="text" class="crm-erp-field form-control" name="contact_no" id="contact_no" value="<?php echo $business_phone;?>" readonly>
                                                    </div>
                                                     <div class="floating col-sm-4 mb-3">
                                                        <label>Email</label>
                                                        <input type="text" class="crm-erp-field form-control" name="email" id="email"  value="<?php echo $business_email;?>" readonly>
                                                    </div>
                                                     <div class="floating col-sm-4 mb-3">
                                                        <label>Title</label>
                                                        <input type="text" class="crm-erp-field form-control" name="title" id="title" value="<?php echo $business_title;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Zip</label>
                                                        <input type="text" class="crm-erp-field form-control" name="zip" id="zip" value="<?php echo $zip;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Street Address</label>
                                                        <input type="text" class="crm-erp-field form-control" name="street_address" id="street_address" value="<?php echo $street_address;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>City</label>
                                                        <input type="text" class="crm-erp-field form-control" name="city" id="city" value="<?php echo $city;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>State</label>
                                                        <input type="text" class="crm-erp-field form-control" name="state" id="state" value="<?php echo $state;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Identity Document Type</label>
                                                        <select class="crm-erp-field form-control" name="identity_document_type" id="identity_document_type" disabled>
                                                            <option value="N/A">N/A</option>
                                                            <option value="SSN" <?php if($identity_document_type == 'SSN'){echo "selected";}?>>SSN</option>
                                                            <option value="EIN" <?php if($identity_document_type == 'EIN'){echo "selected";}?>>EIN</option>
                                                            <option value="Driver's License" <?php if($identity_document_type == esc_attr("Driver's License")){echo "selected";}?>>Driver's License</option>
                                                            <option value="Others"  <?php if($identity_document_type == 'Others'){echo "selected";}?>>Others</option>
                                                        </select>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Document Number</label>
                                                        <input type="text" class="crm-erp-field form-control" name="identity_document_number" id="identity_document_number" value="<?php echo $identity_document_number;?>" readonly>
                                                    </div>
                                                </div>
                                                <div class="row mb-3">
                                                    <div class="col-sm-12 erc-project-view-title">
                                                        <h2>Business Info</h2>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Business Legal Name</label>
                                                        <input type="text" class="crm-erp-field form-control" name="business_legal_name" id="business_legal_name" value="<?php echo $business_legal_name;?>" readonly>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Doing Business As</label>
                                                        <input type="text" class="crm-erp-field form-control" name="doing_business_as" id="doing_business_as" value="<?php echo $doing_business_as;?>" readonly>
                                                    </div>
                                                     <div class="floating col-sm-4 mb-3">
                                                        <label>Business Category</label>
                                                        <input type="text" class="crm-erp-field form-control" name="business_category" id="business_category"  value="<?php echo $business_category;?>" readonly>
                                                    </div>
                                                     <div class="floating col-sm-4 mb-3">
                                                        <label>Website URL</label>
                                                        <input type="text" class="crm-erp-field form-control" name="website_url" id="website_url" value="<?php echo $website_url;?>" readonly>
                                                    </div>
                                                </div>
                                                <div class="row mb-3">
                                                    <div class="col-sm-12 erc-project-view-title">
                                                        <h2>Business Legal Info</h2>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Business Entity Type</label>
                                                        <select class="crm-erp-field form-control" name="business_entity_type" id="business_entity_type" disabled>
                                                            <option value="1">N/A</option>
                                                            <option value="4" <?php if($business_entity_type == 4){echo "selected";}?>>Sole Proprietorship</option>
                                                            <option value="3" <?php if($business_entity_type == 3){echo "selected";}?>>Partnership</option>
                                                            <option value="2" <?php if($business_entity_type == 2){echo "selected";}?>>Limited Liability (LLC)</option>
                                                            <option value="6" <?php if($business_entity_type == 6){echo "selected";}?>>Corporation (S,C,B,etc)</option>
                                                            <option value="7" <?php if($business_entity_type == 7){echo "selected";}?>>Trust</option>
                                                            <option value="5" <?php if($business_entity_type == 5){echo "selected";}?>>Other</option>
                                                        </select>
                                                    </div>
                                                    <div class="floating col-sm-4 mb-3">
                                                        <label>Registration Number</label>
                                                        <input type="text" class="crm-erp-field form-control" name="registration_number" id="registration_number" value="<?php echo $registration_number;?>" readonly>
                                                    </div>
                                                     <div class="floating col-sm-4 mb-3">
                                                        <label>Registration Date</label>
                                                        <input type="text" class="crm-erp-field form-control" name="registration_date" id="registration_date" value="<?php echo $registration_date;?>" placeholder="MM/DD/YYYY" readonly disabled="disabled">
                                                    </div>
                                                     <div class="floating col-sm-4 mb-3">
                                                        <label>State of Registration</label>
                                                        <input type="text" class="crm-erp-field form-control" name="state_of_registration" id="state_of_registration" value="<?php echo $state_of_registration;?>" readonly>
                                                    </div>
                                                </div>
                                            </fieldset>
                                            </div>
                                        </div>
                                        <?php if (in_array("echeck_staff", $user_roles) || in_array("echeck_admin", $user_roles) || in_array("master_ops", $user_roles) || in_array("echeck_client", $user_roles) || in_array("administrator", $user_roles)) {?>
                                            <div class="tab-pane fade erc-project-scroll" id="pills-bank-info" role="tabpanel" aria-labelledby="pills-bank-info">
                                                <div class="erc-project-view bank_data">
                                                    <img src="<?php echo get_site_url().'/wp-content/plugins/wp-iris/page-templates/loader.gif';  ?>" style="width:80px;margin-left: 35%;" />
                                                </div>
                                            </div>
                                        <?php } ?>
                                        <div class="tab-pane fade erc-project-scroll" id="pills-intke" role="tabpanel" aria-labelledby="pills-intke">
                                            <div class="erc-project-view intake_data">
                                                <img src="<?php echo get_site_url().'/wp-content/plugins/wp-iris/page-templates/loader.gif';  ?>" style="width:80px;margin-left: 35%;" />
                                            </div>
                                        </div>
                                        <div class="tab-pane fade erc-project-scroll" id="pills-fee" role="tabpanel" aria-labelledby="pills-fee">
                                            <div class="erc-project-view fee_data">
                                                <img src="<?php echo get_site_url().'/wp-content/plugins/wp-iris/page-templates/loader.gif';  ?>" style="width:80px;margin-left: 35%;" />
                                            </div>
                                        </div>
                                        <div class="tab-pane fade erc-project-scroll" id="pills-document" role="tabpanel" aria-labelledby="pills-document">
                                            <div class="erc-project-view documents_data">
                                                <img src="<?php echo get_site_url().'/wp-content/plugins/wp-iris/page-templates/loader.gif';  ?>" style="width:80px;margin-left: 35%;" />   
                                            </div>
                                        </div>
										<!-- invoice tab start -->
										<div class="tab-pane erc-project-scroll" id="eleve-invoices" role="tabpanel" aria-labelledby="eleve-invoice-tab">
											<!-- Invoice card -->
											<?php

											// Fetch invoices
											$crm_erp_admin = new CRM_ERP_Admin_Interface();
											$lead_id_project = $lead_id;
											$product_ids = $project->product_id; 
											$invoicesCard = $crm_erp_admin->get_invoices_by_lead_and_product($lead_id_project, $product_ids);
                                            
											// Display invoices
											$crm_erp_admin->display_invoices_data($invoicesCard);
											echo do_shortcode("[productpage_invoice_modals]");											
											?>
											<!--/ Invoice card -->
										</div> 
										<!--/ invoice tab end -->										
										<!-- Audit logs tab start -->
                                        <div class="container tab-pane scroll-common fade" id="pills-logs" role="tabpanel" aria-labelledby="pills-audit-logs">
                                            <img src="<?php echo get_site_url().'/wp-content/plugins/wp-iris/page-templates/loader.gif';  ?>" style="width:80px;margin-left: 35%;" />
                                        </div>
                                        <!-- Audit logs tab end -->

                                        <?php //if($is_affiliate == 0){?>
                                        <div class="">
                                            <div class="button-container button_container" style="">
                                                <button type="submit" class="create_project_btn update_project" data-projectid="<?php echo $project_id;?>">Update</button>
                                            </div>
                                        </div>
                                        <?php //} ?>
                                    </div>
                                </form>
                            <div class="row">
                                <?php echo do_shortcode('[CommLog_activity_by_lead lead_id="' . $lead_id . '"]'); ?>
                                <div class="custom-opportunity-notes">
                                    <div class="col-sm-12">
                                        <h5>ERC Notes
                                            <?php if($is_affiliate == 0){?> 
                                            <a href="javascript:void(0)" class="opp-add-notes"><i class="fa fa-plus-circle create-note-btn"></i></a>
                                            <?php } ?>
                                        </h5>
                                    </div>
                                    <div class="col-sm-12 custom-opp-notes-scroll">
                                        <div class="notes-listing">
                                        <?php
                                        $i = 1;
                                        $from = 'UTC';
                                        $to = 'America/New_York';
                                        $format = 'Y-m-d h:i:s A';
                                        foreach ($all_notes as $n_key => $n_value) {
                                            $confidence_notes_access = $n_value->confidential_notes;
                                            $date = $n_value->created; //UTC time
                                            date_default_timezone_set($from);
                                            $newDatetime = strtotime($date);
                                            date_default_timezone_set($to);
                                            $newDatetime = date($format, $newDatetime);
                                            date_default_timezone_set('UTC');
                                            $datetime = date_create($newDatetime);
                                            $time = date_format($datetime, "h:ia");
                                            $day = date_format($datetime, " D ");
                                            $month = date_format($datetime, " M ");
                                            $date = date_format($datetime, "dS,");
                                            $year = date_format($datetime, " Y");
                                            $actual_date = $time . " on " . $day . $month . $date . $year;
                                            $notes = $n_value->note;

                                            $conf_class ='';
                                                if($confidence_notes_access){ 
                                                    $conf_class='confidential-notes-div';    
                                                }

                                        ?>
                                            <div class="note-listing-div shadow <?php echo $conf_class;?>" id="note-list-<?= $n_value->id; ?>">
                                                <p class="notes" id="note-<?= $n_value->id; ?>" data-confidece="<?php echo $confidence_notes_access;?>"><?= $notes; ?></p>
                                                <p class="date-time">(<?= $actual_date;?>)</p>
                                                <?php 
                                                    // check confidence_user and self notes
                                                    if($confidence_user == 1 && $current_user_id == $n_value->created_by){ ?>  
                                                        <a href="javascript:void(0)" title="Edit Notes" class="edit_self_notes" data-note_id="<?= $n_value->id; ?>">
                                                            <i class="fa-regular fa-pen-to-square"></i>
                                                        </a>
                                                        <a href="javascript:void(0)" title="Delete Notes" class="delete_self_notes" data-note_id="<?= $n_value->id; ?>">
                                                            <i class="fa fa-trash" aria-hidden="true"></i>
                                                        </a>
                                                <?php } ?>    
                                                </div>
                                            <?php $i++;
                                            } ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>        


                    </div><!---col-md-9--->

                            <div class="col-md-3" id="right_section">
                                <div class="custom-opportunity-view-sidebar">
                                    <div class="row edit-icon-milestone">
                                        <?php if($is_affiliate == 0){?>
                                            <a href="javascript:void(0)" title="Edit"><i class="fa-regular fa-pen-to-square edit_project_milestone" style="font-weight: 700"></i></a>
                                        <?php } ?>
                                    </div>
                                    <div class="show_message_milestone"></div>
                                    <div class="d-flex opp-owner mb-3 ">
                                        <label><b>Milestone:</b></label>
                                        <p id="showMilestone"><?php echo ucwords($project->milestone_name);
                                        $MilestoneIssue = '';
                                        if($project->milestoneActiveStatus == 'inactive'){
                                            $MilestoneIssue = 'Inactive';
                                        }
                                        if($project->milestone_deletedat != ''){
                                            $MilestoneIssue = 'Deleted';
                                        }
                                        $milestoneMmap = unserialize($project->milestoneMap);
                                        if(empty($milestoneMmap)){
                                            $MilestoneIssue = 'Unassigned';
                                        }elseif(!in_array("project", $milestoneMmap)){
                                            $MilestoneIssue = 'Unassigned';
                                        }
                                        if(!empty($MilestoneIssue)){
                                            echo '('.$MilestoneIssue.')';
                                        }
                                        ?> 
                                        </p>
                                        <select class="form-control custom-mile-status select-milestone" id="Milestone" on='23' style="display: none;">
                                            <?php
                                            if (count($all_milestones) > 0) {
                                                foreach ($all_milestones as $all_milestone) {
                                                    $milestone_id = $all_milestone->milestone_id;
                                                    $MilestoneIssue = '';
                                                    if($all_milestone->status == 'inactive'){
                                                        $MilestoneIssue = 'Inactive';
                                                    }
                                                    if($all_milestone->deleted_at !== null){
                                                        $MilestoneIssue = 'Deleted';
                                                    }
                                                    $milestoneMmap = unserialize($all_milestone->map);

                                                    if(empty($milestoneMmap)){
                                                        $MilestoneIssue = 'Unassigned';
                                                    }elseif(!in_array("project", $milestoneMmap)){
                                                        $MilestoneIssue = 'Unassigned';
                                                    }

                                                    $milestone_name = '';
                                                    $MilestoneID = '';
                                                    if(!empty($MilestoneIssue)){
                                                        $milestone_name = $all_milestone->milestone_name.'('.$MilestoneIssue.')';
                                                        $MilestoneID = '';
                                                    }else{
                                                        $milestone_name = $all_milestone->milestone_name;
                                                        $MilestoneID = $all_milestone->milestone_id;
                                                    }
                                                    $sel = '';
                                                    if ($project->milestone_id == $milestone_id) {
                                                        $sel = 'selected';
                                                    } else {
                                                        $sel = '';
                                                    }
                                                    echo '<option value="' . $MilestoneID . '" ' . $sel . '>' . ucwords($milestone_name) . '</option>';
                                                }
                                            } else {
                                            ?>
                                                <option value="">Select Milestone</option>
                                            <?php
                                            }
                                            ?>
                                        </select>
                                    </div>

                                    <div class="d-flex opp-owner">
                                        <label><b>Stage:</b></label>
                                        <p id="showMilestoneStage">
                                            <?php 
                                                echo ucwords($project->milestoneStatus);
                                                $StageIssue = '';
                                                if($project->StageActiveStatus == 'inactive'){
                                                    $StageIssue = 'Inactive';
                                                }
                                                if($project->StageDeleteStatus != ''){
                                                    $StageIssue = 'Deleted';
                                                }
                                                if(!empty($StageIssue)){
                                                    echo '('.$StageIssue.')';
                                                }    
                                            ?> 
                                        </p>
                                        <select class="form-control custom-mile-status" id="MilestoneStage" name='milestone_status-23' style="display: none;">
                                            <?php
                                            if (count($all_milestone_status) > 0) {
                                                foreach ($all_milestone_status as $allmilestonestatus) {
                                                    $milestone_stage_id = $allmilestonestatus->milestone_stage_id;
                                                    $sel = '';
                                                    if($allmilestonestatus->status == 'inactive'){
                                                        $StageIssue = 'Inactive';
                                                    }
                                                    if($allmilestonestatus->deleted_at !== null){
                                                        $StageIssue = 'Deleted';
                                                    }
                                                    $stageID = '';
                                                    $stage_name = '';
                                                    if(!empty($StageIssue)){
                                                        $stage_name = $allmilestonestatus->stage_name.'('.$StageIssue.')';
                                                        $stageID = '';
                                                    }else{
                                                        $stage_name = $allmilestonestatus->stage_name;
                                                        $stageID = $allmilestonestatus->milestone_stage_id;
                                                    }
                                                    if ($project->milestone_stage_id == $milestone_stage_id) {
                                                        $sel = 'selected';
                                                    } else {
                                                        $sel = '';
                                                    }
                                                    echo '<option value="' . $milestone_stage_id . '" ' . $sel . '>' . ucwords($stage_name) . '</option>';
                                                }
                                            } else {
                                            ?>
                                                <option value="">Select Stage</option>
                                            <?php
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <div class="row">
                                        <div class="button-container milestone_button_container" style="display: none;">
                                            <button type="submit" class="create_product_btn update_milestone" data-projectID="<?php echo $project->project_id; ?>"  data-milestoneid = "<?php echo $project->milestone_id; ?>"  data-milestonestageid = "<?php echo $project->milestone_stage_id; ?>">Update</button>
                                            <button type="button" class="create_product_btn cancel_milestone">Cancel</button>
                                        </div>
                                    </div>

                                </div>
                                <div class="custom-opportunity-view-sidebar assign_collaborators_section">  
                                    <?php if($is_affiliate == 0){?>  
                                        <div class="row edit-icon-owner">
                                            <a href="javascript:void(0)" title="Edit"><i class="fa-regular fa-pen-to-square edit_project_collaborator" style="font-weight: 700"></i></a>
                                        </div>
                                    <?php } ?>
                                     <input type="hidden" id="show_collaborators" value="0">
                                    <div class="d-flex opp-owner">
                                        <label><b>Assigned Collaborators:</b></label>   
                                    </div>
                                    <div class="all_assigned_users">
                                        <?php
                                        $unsigned_user = [];
                                        foreach ($collaborators as $collaborator) {
                                            $user_data = get_user_by('id',$collaborator->user_id); 
                                            $user_roles = $user_data->roles;
                                            $user_role = '';
                                            if(isset($user_roles[0])){
                                                $userrole = $user_roles[0];
                                                if($userrole == 'echeck_staff'){
                                                    $user_role = ' (Echeck Staff)';
                                                }
                                                elseif($userrole == 'echeck_admin'){
                                                    $user_role = ' (Echeck Admin)';
                                                }
                                                elseif($userrole == 'master_ops'){
                                                    $user_role = ' (Master Ops)';
                                                }
                                                elseif($userrole == 'master_sales'){
                                                    $user_role = ' (Master Sales)';
                                                }
                                                elseif($userrole == 'iris_sales_agent'){
                                                    $user_role = ' (Sales Agent)';
                                                }
                                                elseif($userrole == 'iris_sales_agent_rep'){
                                                    $user_role = ' (Sales Agent Rep)';
                                                }
                                                elseif($userrole == 'iris_affiliate_users'){
                                                    $user_role = ' (Affiliate)';
                                                }
                                                elseif($userrole == 'iris_employee'){
                                                    $user_role = ' (Employee)';
                                                }
                                            }
                                            $unsigned_user[count($unsigned_user)] = $collaborator->user_id;
                                            ?>
                                            <p class='col-form-label assign_users' id="assign-users<?= $collaborator->user_id ?>"><?php echo $user_data->data->display_name.$user_role; ?> 
                                                <?php if($is_affiliate == 0){?> <span unassign-user="<?= $collaborator->user_id ?>" unassign-user-name="<?= $user_data->data->display_name ?>" class="unassign-user glyphicon glyphicon-minus-sign" style="padding: 0 5px;
                                            cursor: pointer;"></span> <?php } ?></p>
                                            <?php
                                        }
                                        ?>
                                        <div class="show_message_collaborator"></div> 
                                        <select <?= $disabled ?> class="user-select form-control" name="assigned_user_id" id="assign_collaborator" style="display: none;" >
                                            <option value="">Select Collaborator to Assign</option>
                                            <?php foreach ($listed_users as $user) {
                                                if (in_array($user->ID,$unsigned_user)) {
                                                } else {?>
                                                <option id="user-option<?= $user->ID ?>" value="<?= $user->ID ?>"><?= $user->display_name; ?></option>;  
                                            <?php }} ?>
                                        </select>
                                    </div>
                                    <div class="row">
                                        <div class="button-container collaborator_button_container" style="display: none;">
                                            <button type="button" class="create_product_btn assign-user-btn" id="assign-user-1">Assign Collaborator</button>
                                            <button type="button" class="create_product_btn cancel_collaborator">Cancel</button>
                                        </div>
                                    </div>
                                </div>

                                <!-- select owner -->
                                <div class="custom-opportunity-view-sidebar">
                                    <div class="row edit-icon-owner">
                                        <a href="javascript:void(0)" title="Edit" ><i class="fa-regular fa-pen-to-square edit_opportunity_owner" style="font-weight: 700"></i></a>
                                    </div>
                                    <div class="show_message_owner"></div>
                                    <div class="d-flex opp-owner">
                                        <label><b>Select Owner:</b></label>
                                        <p id="showOwnerList"><?php  echo $owner_name; ?></p>
                                    </div>
                                    <div class="d-flex1 opp-owner owner-main-div" id="owner-div-id">
                                        <select  class="ewc-filter-sub selectpicker custom-ownership" data-live-search="true" title="Select Owner" id="OwnerList" style="display: none;">
                                        <?php    
                                        $groups = [];
                                        foreach ($users as $user) {
                                            $userid = $user->ID;
                                            $display_name = $user->display_name;
                                            if(isset($user->roles[0])){
                                                $user_role = $user->roles[0];
                                            }else{
                                                foreach ($user->roles as $key => $value) {
                                                        $user_role = $value;
                                                }    
                                            }  

                                           $selected = '';
                                           if($project->created_by == $userid){
                                                       $selected='selected';
                                                    }
                                                
                                                //'echeck_staff','echeck_admin','echeck_client','iris_affiliate_users','master_sales','iris_sales_agent','iris_sales_agent_rep'
                                                
                                                if($user_role=='master_ops'){
                                                    $user_role = 'Master OPS';
                                                }else if($user_role=='echeck_staff' || $user_role=='echeck_admin'){
                                                    $user_role = 'OPS';
                                                }else if($user_role=='echeck_client'){
                                                    $user_role = 'Finance';    
                                                }else if($user_role=='iris_affiliate_users'){
                                                    $user_role = 'Affiliate';
                                                }else if($user_role=='master_sales'){
                                                    $user_role = 'Master Sales';
                                                }else if($user_role=='iris_sales_agent' || $user_role=='iris_sales_agent_rep'){
                                                    $user_role = 'Sales';
                                                }

                                               // Create an array to store options for each group
                                                 if (!isset($groups[$user_role])) {
                                                    $groups[$user_role] = [];
                                                 }
                                                 $test_display_name = ' '.$display_name;
                                                 if(strpos($test_display_name,"Test") || strpos($test_display_name,"test")){

                                                 }else{
                                                    if(trim($display_name)!=''){
                                                        $option = "<option value='{$userid}' {$selected}>{$display_name}</option>";
                                                        $groups[$user_role][] = $option;
                                                    }  
                                        }        }

                                            // Generate the HTML for each optgroup
                                            foreach ($groups as $group => $options) {
                                                echo "<optgroup label='{$group}'>";
                                            foreach ($options as $option) {
                                                echo $option;
                                            }
                                            echo "</optgroup>";
                                            }
                                            
                                        ?>
                                        </select>
                                    </div>
                                    <div class="row">
                                        <div class="button-container owner_button_container mt-0" style="display: none;">
                                                <button type="submit" class="create_product_btn update_owner" data-projectID="<?php echo $project->project_id; ?>">Update</button>
                                                <button type="button" class="create_product_btn cancel_owner" >Cancel</button>
                                        </div>
                                    </div>
                                </div>
                                <!-- select owner end -->    

                                <div class="custom-opportunity-view-sidebar">
                                    <?php if($is_affiliate == 0){?> 
                                        <div class="row edit-icon-owner">
                                            <a href="javascript:void(0)" title="Edit"><i class="fa-regular fa-pen-to-square edit_project_contact" style="font-weight: 700"></i></a>
                                        </div>
                                    <?php } ?>
                                    <div class="show_message_contact"></div>
                                    <div class="d-flex opp-owner">
                                        <label><b>Select Contact:</b></label>
                                        <p id="showContactList"><?php echo ucwords($primary_contact); ?></p>
                                        <select class="form-control custom-contactList" style="display: none;" id="ContactList">
                                            <?php
                                            if (count($all_contacts) > 0) {
                                                foreach ($all_contacts as $all_contact) {
                                                    $contact_id = $all_contact->id;
                                                    $sel = '';
                                                    if ($project->contact_id == $contact_id) {
                                                        $sel = 'selected';
                                                    } else {
                                                        $sel = '';
                                                    }
                                                    $contact_name = $all_contact->first_name . ' ' . $all_contact->last_name;
                                                    echo '<option value="' . $contact_id . '" ' . $sel . '>' . $contact_name . '</option>';
                                                }
                                            } else {
                                            ?>
                                                <option value="">Select Contact</option>
                                            <?php
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <div class="row">
                                        <div id="userDetails" data-id='<?php echo get_current_user_id();  ?>' data-name='<?php echo $login_user_name; ?>' style="display:none;"></div>
                                        <div class="button-container contact_button_container" style="display: none;">
                                            <button type="submit" class="create_product_btn update_contacts" data-projectID="<?php echo $project->project_id; ?>" data-contactid = "<?php echo $project->contact_id; ?>">Update</button>
                                            <button type="button" class="create_product_btn cancel_contact">Cancel</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- .site-main -->
</div><!-- .content-area -->

<div class="modal opportunity-add-new-notes" id="add-new-opp-notes" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-comment"></i> Notes</h5>
                <button type="button" class="close-popup-notes close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="show_notes_message">
                    <p class="note-response" style="display: none;"></p>
                    <p class="error-response" style="display: none;">Notes is required.</p>
                </div>

                <div class="row">
                    <div class="floating col-md-12">
                        <label>Notes:</label>
                        <textarea id="notes-input" class="form-control" rows="5" maxlength="1000"></textarea>
                        <?php if($confidence_user==1){?>
                            Mark As Confidential  <input type="checkbox" name="confidence_user_check" id="confidence_user_check" value="1" style="margin-top:0px;" <?php echo $confidence_check; ?>> 
                        <?php } ?>
                        <p class="remaining-msg" id="remaining-msg" style="float:right;">1000/1000 characters remaining.</p>
                    </div>
                </div>
                <div class="buttion_next_prev">
                    <button type="button" class="nxt_btn" id="create-note">Submit</button>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- edit notes popup -->
 <div class="modal opportunity-add-new-notes" id="edit-new-opp-notes" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-comment"></i> Notes</h5>
                <button type="button" class="close-popup-notes close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="show_message_popup">
                    <p class="note-response" style="display: none;"></p>
                    <p class="error-response" style="display: none;">Notes is required.</p>
                </div>
                
                <div class="row">
                    <div class="floating col-md-12">
                        <label>Notes:</label>
                        <textarea id="edit-notes-input" maxlength="1000" style="resize:none;margin-bottom:2%;" class="form-control" rows="5"></textarea>
                        <?php if($confidence_user==1){?>
                                Mark As Confidential  <input type="checkbox" name="confidence_user_check" id="edit_confidence_user_check" value="1" style="margin-top:0px;"> 
                        <?php } ?>
                         <p class="edit-remaining-msg" id="edit-remaining-msg" style="float:right;">1000/1000 characters remaining.</p>
                    </div>
                </div>
                <div class="buttion_next_prev">
                    <button type="button" class="nxt_btn" id="update-note" data-note_id="">Submit</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="exampleModal_otherdoc_adding_comment" tabindex="-1" role="dialog"
     aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Comments</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <input type="hidden" name="doc_id_comment" id="doc_id_comment" class="doc_id_comment">
                        <input type="hidden" name="doc_type_id_comment" id="doc_type_id_comment"
                               class="doc_type_id_comment">
                        <textarea name="comment" id="other-comment-textarea"
                                  data-doc_id="<?php echo $result[0]->doc_id; ?>" maxlength="100"
                                  placeholder="Only 100 characters are allowed"
                                  style="width: 100%;height: 150px;"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" id="stc-submit-comment-btn" class="stc-submit-comment-btn btn btn-primary">Submit</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="exampleModal_ercdoc_view" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Comments</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height:200px; overflow-y:auto;">
                <input type="hidden" name="view_stc_comment_id" class="view_stc_comment_id" id="view_stc_comment_id">
                <span class="comment_username" style="font-size:14px">User Name</span>
                <p class="mt-2">consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
                    aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                    commodo consequat.</p>
                <span class="comment_date" style="float: right; font-size:12px">Comment Date:- 05-12-2023</span>
                <br>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="view-comment btn btn-warning">View Log</button>
            </div>
        </div>
    </div>
</div>

<?php //require plugin_dir_path(__FILE__) . 'iris-send-erc-agreement.php';  ?>
<script>
    jQuery(document).ready(function() {
        $(".opp-add-notes").click(function() {
            $('.note-response').html("");
            $('.note-response').css('display', 'none');
            $('.error-response').css('display', 'none');
            $("#add-new-opp-notes").modal('show');

        });
        $('.close-popup-notes').click(function() {
            $('.note-response').css('display', 'none');
            $('.error-response').css('display', 'none');
            $("#add-new-opp-notes").modal('hide');
            $("#edit-new-opp-notes").modal('hide');
        });
    });
	$(document).ready(function() {
		$("#registration_date").datepicker({
			dateFormat: "mm/dd/yy",
		});
		
	});
</script>
<script type="text/javascript">
    $(document).ready(function() {
        var timeout; 
        $("#zip").keyup(function () {
            clearTimeout(timeout); // Clear the previous timeout.
            var zipError = $("#zip-error"); // Select the zip error message element.
            zipError.hide(); // Initially hide the error message.
            timeout = setTimeout(() => {
                var zip = $(this).val();
                if (zip.length === 5) { // Ensure API call only when zip code is 5 digits.
                    $.ajax({
                        url: 'https://portal.occamsadvisory.com/portal/wp-json/v1/get_location_by_zipcode',
                        method: 'POST',
                        data: { zipcode: zip },
                        success: function (response) {
                            if (response && !response.error) {
                                $('#city').val(response[0].city); // Assuming response is an array of results
                                $('#city').next('.error-message').remove();
                                //console.log("State response:", response[0].state);
                                var stateValue = response[0].state;
                                // Try setting the value and triggering the change event
                                $('#state').val(stateValue).trigger('change.select2');
                                $('#state').siblings('.error-message').remove();
                                zipError.hide(); // Hide error message in case of successful location fetch.
                            } else {
                                // Show error message if no location found
                                $('#city').val('');
                                $('#city').next('.error-message').remove();
                                $('#state').val('');
                                $('#state').siblings('.error-message').remove();
                                zipError.text(response.error).show(); // Show error message.
                            }
                        },
                        error: function (xhr, status, error) {
                            // Reset fields on AJAX error
                            $('#city').val('');
                            $('#state').val('').trigger('change');
                            $('#state').siblings('.error-message').remove();
                            zipError.text("Invalid zipcode.").show(); // Show error message on AJAX error.
                        }
                    });
                } else {
                    // Reset fields and hide error message if zip code is not 5 digits
                    $('#city').val('');
                    $('#state').val('');
                    $('#state').siblings('.error-message').remove();
                    zipError.hide();
                }
            }, 500); // Wait for 500ms before making the API call
        });
        var lead_id = "<?php echo $lead_id; ?>";
        jQuery(".user-select").on('select2:select', function(e) {
            jQuery("#assign-user-1").show();
        });
        jQuery(".user-select").select2({
            placeholder: "Select Collaborator to Assign",
            allowClear: true
        });
        jQuery(document).on('click', '.unassign-user', function() {
            jQuery(".show_message_collaborator").hide();
            unassign_user_id = jQuery(this).attr("unassign-user");
            unassign_user_name = jQuery(this).attr("unassign-user-name");
            var project_id = "<?php echo $project_id; ?>";
            var data = {
                'action': 'assign_collaborators',
                'project_id':project_id,
                'user_id': unassign_user_id,
                'type': 'unassign-user'
            };
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'post',
                data: data,
                success(response) {
                    console.log(response);
                    var name = jQuery('#userDetails').attr('data-name');
                    var noteMessage = name + ' added a comment: ' + name + ' unassign collaborator to ' + unassign_user_name;
                    saveProject(noteMessage);
                    jQuery("#assign-users" + unassign_user_id).css("display", "none");
                }
            }).done(function() {
                console.log("completed");
            });
        });
        jQuery(document).on('click', '.assign-user-btn', function() {
            jQuery(".show_message_collaborator").hide();
            
            var selected_user = jQuery(".user-select :selected").val();
            if(selected_user != '' && selected_user > 0){
                jQuery('.assign-user-btn').html("Please wait ..");
                var project_id = "<?php echo $project_id; ?>";
                var assignedUsers = [];
                jQuery(".all_assigned_users p").each(function() {
                    assignedUsers.push(jQuery(this).attr("id"))
                });
                if (jQuery.inArray(selected_user, assignedUsers) == -1) {
                        var data = {
                            'action': 'assign_collaborators',
                            'user_id': selected_user,
                            'project_id' : project_id,
                            'type': 'assign-user'
                        };
                        jQuery.ajax({
                            url: '<?php echo admin_url("admin-ajax.php"); ?>',
                            method: 'post',
                            data: data,
                            success(response) {
                                var selected_user_name = jQuery(".user-select :selected").text();
                                /*update note start*/
                                var name = jQuery('#userDetails').attr('data-name');
                                var noteMessage = name + ' added a comment: ' + name + ' assign collaborator to ' + selected_user_name;
                                saveProject(noteMessage);
                                /*update note end*/
                                
                                var selected_user_id = jQuery(".user-select :selected").val();
                                var Dynamic_user = '<p class="col-form-label" id="assign-users' + selected_user_id + '">' + selected_user_name + ' <span unassign-user="' + selected_user_id + '" class="unassign-user glyphicon glyphicon-minus-sign"></span></p>';
                                jQuery(".all_assigned_users").prepend(Dynamic_user);
                                jQuery(".user-select").val('').change();
                                jQuery('.assign-user-btn').html("Assign User");
                            }
                        }).done(function() {
                            //jQuery(".assign-user-btn").css("display", "none");
                            console.log("completed");
                        });
                    
                } else {
                    console.log("user already assigned");
                }
            }else{
                jQuery(".show_message_collaborator").html('Please select collaborator');
            }
        })
        jQuery(document).on('click', '.update_project', function() {
            var form = '#lead_update_form';
            var valid = checkValidation();
            if(valid){
                console.log('valid'+valid);
            var form_data = jQuery(form).serialize();
            var form_data_val = form_data;
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'post',
                data: form_data_val,
                success(response) {
                    var res = JSON.parse(response);
                    jQuery(".update_project").html('Save');
                    jQuery(".update_project").attr('disabled', false);
                    if(res.status == 200){
                        alert("Project saved successfully");
                    }else{
                        alert(res.message);
                    }
                }
            });
          }
        })
        jQuery("#pills-bank").click(function(){
            jQuery(".update_project").show();
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                data: { 
                    action: "erc_project_bank_info", 
                    lead_id: lead_id
                },
                success: function(response) {
                    jQuery("#pills-bank-info .bank_data").html(response);
                },
                error: function () {
                    console.log("Error in getting bank data");
                }
            });
        })
        jQuery("#pills-intake").click(function(){
            jQuery(".update_project").show();
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                data: { 
                    action: "erc_project_intake_data", 
                    lead_id: lead_id
                },
                success: function(response) {
                    jQuery("#pills-intke .intake_data").html(response);
                },
                error: function () {
                    console.log("Error in getting intake data");
                }
            });
        })
        
        jQuery("#eleve-project-tab").click(function(){
            jQuery(".update_project").show();
        });    
        
        jQuery("#pills-fees").click(function(){
            jQuery(".update_project").show();
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                data: { 
                    action: "erc_project_fee_data", 
                    lead_id: lead_id
                },
                success: function(response) {
                    jQuery("#pills-fee .fee_data").html(response);
                },
                error: function () {
                    console.log("Error in getting fee data");
                }
            });
        })

        jQuery(document).on("click","#pills-documents",function(){
            jQuery(".update_project").hide();
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'post',
                data: { 
                    action: "erc_company_document_data", 
                    lead_id: lead_id
                },
                success: function(response) {
                    jQuery("#pills-document .documents_data").html(response);
                },
                error: function () {
                    console.log("Error in getting document data");
                }
            });
        })


        jQuery(document).on("click","#pills-audit-logs",function(){
            var project_id = "<?php echo $project_id; ?>";
            var product_id = "<?php echo $project->product_id; ?>";
            var lead_id = "<?php echo $lead_id; ?>";

            jQuery(".update_project").hide();
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'post',
                data: { 
                    action: "projects_audit_log", 
                    project_id: project_id,
                    product_id:product_id,
                    lead_id:lead_id,
                },
                success: function(response) {
                    jQuery("#pills-logs").html(response);
                    jQuery("#project_audit_log_table").DataTable({"ordering": false});
                    jQuery("#project_milestone_log_table").DataTable({"ordering": false});
                    jQuery("#project_invoice_log_table").DataTable({"ordering": false});
                    jQuery("#project_document_log_table").DataTable({"ordering": false});
                    jQuery("#lead_log_table").DataTable({"ordering": false});
                },
                error: function () {
                    console.log("Error in getting document data");
                }
            });
        })

        // on tab click audit log hide right section and comment
        jQuery(document).on('click', '.tab-btn', function(){
            var id = jQuery(this).attr('id');
            if(id =='pills-audit-logs'){
                jQuery("#right_section").hide();
                jQuery("#comm-log-activity-by-lead").hide();
                jQuery(".custom-opportunity-notes").hide();
                jQuery("#left_section").removeClass('col-md-9');
                jQuery("#left_section").addClass('col-md-12');
            }else{
                jQuery("#right_section").show();
                jQuery("#comm-log-activity-by-lead").show();
                jQuery(".custom-opportunity-notes").show();
                jQuery("#left_section").removeClass('col-md-12');
                jQuery("#left_section").addClass('col-md-9');
            }
        });
        
        jQuery(document).on('change', '.select-milestone', function() {
            var no = jQuery(this).data('no');
            var val = jQuery(this).val();
            var product_name = $(this).find(":selected").html();
            console.log(product_name);
            var url = "<?php echo site_url() . '/wp-json/productsplugin/v1/milestone-status'; ?>";
            jQuery.ajax({
                type: "POST",
                url: url,
                data: {
                    id: val
                },
                beforeSend: function() {
                    $('#MilestoneStage').html('<option value="">Loading Stages...</option>');
                    $('.show_message_milestone').html('');
                },
                success: function(response) {
                    jQuery('#MilestoneStage').html('');

                    if (response.length != 0) {
                        jQuery.each(response, function(indexes, values) {
                            var optionHTML = `<option value="${values.milestone_stage_id}"> ${values.stage_name} </option>`;
                            jQuery('#MilestoneStage').append(optionHTML);
                        });
                    } else {

                        jQuery('#MilestoneStage').html(`<option value="">Select Stage</option>`);
                    }
                }
            });
        });
        $(document).on('click', '.update_project', function() {
            var valid = checkValidation();
            console.log('valid=='+valid);
            if(valid){
            $('.show_message_popup').html('');
            $(this).text('Please wait..');
            $(this).attr('disabled',true);
            let lead_id = "<?php echo $lead_id;?>";
            let projectID = $(this).attr('data-projectid');
            let project_name = $('#project_name').val();
            let project_fee = $('#project_fee').val();
            let maximum_credit = $('#maximum_credit').val();
            let estimated_fee = $('#estimated_fee').val();
            let self_time_off_days_april_2020 = $("#self_time_off_days_april_2020").val();
            let self_time_off_days_january_2021 = $("#self_time_off_days_january_2021").val();
            let self_time_off_days_april_2021 = $("#self_time_off_days_april_2021").val();
            let other_time_off_days_april_2020 = $("#other_time_off_days_april_2020").val();
            let other_time_off_days_january_2021 = $("#other_time_off_days_january_2021").val();
            let other_time_off_days_april_2021 = $("#other_time_off_days_april_2021").val();
            let full_name = $("#full_name").val();
            let contact_no = $("#contact_no").val();
            let email = $("#email").val();
            let title = $("#title").val();
            let zip = $("#zip").val();
            let street_address = $("#street_address").val();
            let city = $("#city").val();
            let state = $("#state").val();
            let identity_document_type = $("#identity_document_type").val();
            let identity_document_number = $("#identity_document_number").val();
            let business_legal_name = $("#business_legal_name").val();
            let doing_business_as = $("#doing_business_as").val();
            let business_category = $("#business_category").val();
            let website_url = $("#website_url").val();
            let business_entity_type = $("#business_entity_type").val();
            let registration_number = $("#registration_number").val();
            let registration_date = $("#registration_date").val();
            let state_of_registration = $("#state_of_registration").val();
            $('.show_message_popup').html('<p class="alert_message">Please Wait</p>');
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-project-optional-field',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    project_name: project_name,
                    project_fee: project_fee,
                    maximum_credit: maximum_credit,
                    estimated_fee: estimated_fee,
                    self_time_off_days_april_2020: self_time_off_days_april_2020,
                    self_time_off_days_january_2021: self_time_off_days_january_2021,
                    self_time_off_days_april_2021: self_time_off_days_april_2021,
                    other_time_off_days_april_2020: other_time_off_days_april_2020,
                    other_time_off_days_january_2021: other_time_off_days_january_2021,
                    other_time_off_days_april_2021: other_time_off_days_april_2021,
                    full_name: full_name,
                    contact_no: contact_no,
                    email: email,
                    title: title,
                    zip: zip,
                    street_address: street_address,
                    city: city,
                    state: state,
                    identity_document_type: identity_document_type,
                    identity_document_number: identity_document_number,
                    business_legal_name: business_legal_name,
                    doing_business_as: doing_business_as,
                    business_category: business_category,
                    website_url: website_url,
                    business_entity_type: business_entity_type,
                    registration_number: registration_number,
                    registration_date: registration_date,
                    state_of_registration: state_of_registration
                },
                success(res) {
                    console.log(res);
                    $(".update_project").text('Update');
                    $(".update_project").attr('disabled',false);
                    if (res.status == true) {
                        $('.show_message_popup').html('<p class="success_message">Project successfully updated</p>');
                        setTimeout(function() {
                            $('.show_message_popup').html('');

                        }, 3000)
                        $("#full_name").attr('readonly',true);
                        $("#contact_no").attr('readonly',true);
                        $("#email").attr('readonly',true);
                        $("#title").attr('readonly',true);
                        $("#zip").attr('readonly',true);
                        $("#street_address").attr('readonly',true);
                        $("#city").attr('readonly',true);
                        $("#state").attr('readonly',true);
                        $("#identity_document_type").attr('disabled',true);
                        $("#identity_document_number").attr('readonly',true);
                        $("#business_legal_name").attr('readonly',true);
                        $("#doing_business_as").attr('readonly',true);
                        $("#business_category").attr('readonly',true);
                        $("#website_url").attr('readonly',true);
                        $("#business_entity_type").attr('disabled',true);
                        $("#registration_number").attr('readonly',true);
                        $("#registration_date").attr('readonly',true);
                        $("#registration_date").attr('disabled',true);
                        $("#state_of_registration").attr('readonly',true);
                    } else {
                        $('.show_message_popup').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    $(".update_project").text('Update');
                    $(".update_project").attr('disabled',false);
                    $('.show_message_popup').html('<p class="warning_message">Something went worng.</p>');
                }
            });
            }else{//validation check
                return false;
            }    
        });
        $(document).on('click', '.update_milestone', function() {
            let lead_id = "<?php echo $lead_id;?>";
            let projectID = $(this).attr('data-projectID');
            let oldmilestoneid = $(this).attr('data-milestoneid');
            let oldmilestonestageid = $(this).attr('data-milestonestageid');
            let project_name = $('#project_name').val();
            let project_fee = $('#project_fee').val();
            let maximum_credit = $('#maximum_credit').val();
            let estimated_fee = $('#estimated_fee').val();
            let self_time_off_days_april_2020 = $("#self_time_off_days_april_2020").val();
            let self_time_off_days_january_2021 = $("#self_time_off_days_january_2021").val();
            let self_time_off_days_april_2021 = $("#self_time_off_days_april_2021").val();
            let other_time_off_days_april_2020 = $("#other_time_off_days_april_2020").val();
            let other_time_off_days_january_2021 = $("#other_time_off_days_january_2021").val();
            let other_time_off_days_april_2021 = $("#other_time_off_days_april_2021").val();
            let Milestone = $('#Milestone').val();
            let MilestoneStage = $('#MilestoneStage').val();
            if (Milestone == '') {
                $('.show_message_milestone').html('<p class="warning_message">Please select the milestone.</p>');
                return;
            }
            if (MilestoneStage == '') {
                $('.show_message_milestone').html('<p class="warning_message">Please select the stage.</p>');
                return;
            }
            $('.show_message_milestone').html('<p class="alert_message">Please Wait</p>');
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-project-optional-field',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    project_name: project_name,
                    project_fee: project_fee,
                    maximum_credit: maximum_credit,
                    estimated_fee: estimated_fee,
                    self_time_off_days_april_2020: self_time_off_days_april_2020,
                    self_time_off_days_january_2021: self_time_off_days_january_2021,
                    self_time_off_days_april_2021: self_time_off_days_april_2021,
                    other_time_off_days_april_2020: other_time_off_days_april_2020,
                    other_time_off_days_january_2021: other_time_off_days_january_2021,
                    other_time_off_days_april_2021: other_time_off_days_april_2021,
                    Milestone: Milestone,
                    MilestoneStage: MilestoneStage
                },
                success(res) {
                    console.log(res);
                    if (res.status == true) {
                        $('.show_message_milestone').html('<p class="success_message">Successfully updated</p>');
                        $('#showMilestone').html(res.data.milestoneName);
                        $('#showMilestoneStage').html(res.data.milestoneStatus);
                        var name = jQuery('#userDetails').attr('data-name');
                        
                        if(Milestone != oldmilestoneid && MilestoneStage != oldmilestonestageid){
                            var noteMessage = name + ' added a comment: ' + name + ' changed milestone to ' + res.data.milestoneName + ' and changed stage to ' + res.data.milestoneStatus;
                            $(".update_milestone").attr('data-milestoneid',Milestone);
                            $(".update_milestone").attr('data-milestonestageid',MilestoneStage);
                            saveProject(noteMessage);
                        }
                        else if(Milestone != oldmilestoneid){
                            var noteMessage = name + ' added a comment: ' + name + ' changed milestone to ' + res.data.milestoneName;
                            $(".update_milestone").attr('data-milestoneid',Milestone);
                            saveProject(noteMessage);
                        }
                        else if(MilestoneStage != oldmilestonestageid){
                            var noteMessage = name + ' added a comment: ' + name + ' changed stage to ' + res.data.milestoneStatus;
                            $(".update_milestone").attr('data-milestonestageid',MilestoneStage);
                            saveProject(noteMessage);
                        }
                        $('.show_message_milestone').html('');
                        $('.show_message_popup').html('');
                        $('.milestone_button_container').hide();
                        $('#Milestone').hide();
                        $('#MilestoneStage').hide();
                        $('#showMilestone').show();
                        $('#showMilestoneStage').show();
                    } else {
                        $('.show_message_milestone').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    $('.show_message_milestone').html('<p class="warning_message">Something went worng.</p>');
                }
            });
        });
/*
        $(document).on('click', '.update_owner', function() {
            let lead_id = "<?php echo $lead_id;?>";
            let projectID = $(this).attr('data-projectID');
            let project_name = $('#project_name').val();
            let project_fee = $('#project_fee').val();
            let maximum_credit = $('#maximum_credit').val();
            let estimated_fee = $('#estimated_fee').val();
            let self_time_off_days_april_2020 = $("#self_time_off_days_april_2020").val();
            let self_time_off_days_january_2021 = $("#self_time_off_days_january_2021").val();
            let self_time_off_days_april_2021 = $("#self_time_off_days_april_2021").val();
            let other_time_off_days_april_2020 = $("#other_time_off_days_april_2020").val();
            let other_time_off_days_january_2021 = $("#other_time_off_days_january_2021").val();
            let other_time_off_days_april_2021 = $("#other_time_off_days_april_2021").val();
            let OwnerList = $('#OwnerList').val();
            $('.show_message_owner').html('<p class="alert_message">Please Wait</p>');
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-project-optional-field',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    project_name: project_name,
                    project_fee: project_fee,
                    maximum_credit: maximum_credit,
                    estimated_fee: estimated_fee,
                    self_time_off_days_april_2020: self_time_off_days_april_2020,
                    self_time_off_days_january_2021: self_time_off_days_january_2021,
                    self_time_off_days_april_2021: self_time_off_days_april_2021,
                    other_time_off_days_april_2020: other_time_off_days_april_2020,
                    other_time_off_days_january_2021: other_time_off_days_january_2021,
                    other_time_off_days_april_2021: other_time_off_days_april_2021,
                    OwnerList: OwnerList,
                },
                success(res) {
                    console.log(res);
                    if (res.status == true) {
                        $('.show_message_owner').html('<p class="success_message">Successfully updated</p>');
                        $('#showOwnerList').html(res.data.ownerName);
                        var name = jQuery('#userDetails').attr('data-name');
                        var noteMessage = name + ' added a comment: ' + name + ' changed owener to ' + res.data.ownerName;
                        saveProject(noteMessage);
                        $('.show_message_milestone').html('');
                        $('.show_message_popup').html('');
                        $('.show_message_owner').html('');
                        $('.owner_button_container').hide();
                        $('#OwnerList').hide();
                        $('#showOwnerList').show();
                    } else {
                        $('.show_message_owner').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    $('.show_message_owner').html('<p class="warning_message">Something went worng.</p>');
                }
            });
        });
        */

        $(document).on('click', '.update_contacts', function() {
            let lead_id = "<?php echo $lead_id;?>";
            let projectID = $(this).attr('data-projectID');
            let old_contact_id = $(this).attr('data-contactid');
            let project_name = $('#project_name').val();
            let project_fee = $('#project_fee').val();
            let maximum_credit = $('#maximum_credit').val();
            let estimated_fee = $('#estimated_fee').val();
            let self_time_off_days_april_2020 = $("#self_time_off_days_april_2020").val();
            let self_time_off_days_january_2021 = $("#self_time_off_days_january_2021").val();
            let self_time_off_days_april_2021 = $("#self_time_off_days_april_2021").val();
            let other_time_off_days_april_2020 = $("#other_time_off_days_april_2020").val();
            let other_time_off_days_january_2021 = $("#other_time_off_days_january_2021").val();
            let other_time_off_days_april_2021 = $("#other_time_off_days_april_2021").val();
            let ContactList = $('#ContactList').val();
            $('.show_message_contact').html('<p class="alert_message">Please Wait</p>');
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-project-optional-field',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    project_name: project_name,
                    project_fee: project_fee,
                    maximum_credit: maximum_credit,
                    estimated_fee: estimated_fee,
                    self_time_off_days_april_2020: self_time_off_days_april_2020,
                    self_time_off_days_january_2021: self_time_off_days_january_2021,
                    self_time_off_days_april_2021: self_time_off_days_april_2021,
                    other_time_off_days_april_2020: other_time_off_days_april_2020,
                    other_time_off_days_january_2021: other_time_off_days_january_2021,
                    other_time_off_days_april_2021: other_time_off_days_april_2021,
                    ContactList: ContactList
                },
                success(res) {
                    console.log(res);
                    if (res.status == true) {
                        $('.show_message_contact').html('<p class="success_message">Successfully updated</p>');
                        $('#showContactList').html(res.data.ContactName);
                        var name = jQuery('#userDetails').attr('data-name');
                        var noteMessage = name + ' added a comment: ' + name + ' changed contact to ' + res.data.ContactName;
                        if(old_contact_id != ContactList){
                            $(".update_contacts").attr('data-contactid',ContactList);
                            saveProject(noteMessage);
                        }
                        $('.show_message_milestone').html('');
                        $('.show_message_popup').html('');
                        $('.show_message_contact').html('');
                        $('.contact_button_container').hide();
                        $('#ContactList').hide();
                        $('#showContactList').show();
                    } else {
                        $('.show_message_contact').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                    }
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    $('.show_message_contact').html('<p class="warning_message">Something went worng.</p>');
                }
            });
        });
        $(document).on('click', '.edit_project_collaborator', function () {
            var show_collaborators = $("#show_collaborators").val();
            $('.show_message_popup').html('');
            $('.show_message_collaborator').html('');
            $('.collaborator_button_container').toggle();
            if(show_collaborators == 0){
                $("#show_collaborators").val(1);
                $('#assign_collaborator').css('display','inline-block');
                $(".assign_collaborators_section .select2-container").css('display', 'inline-block');
            }else{
                $("#show_collaborators").val(0);
                $('#assign_collaborator').css('display','none');
                $(".assign_collaborators_section .select2-container").css('display', 'none');
            }
            
            //$(".assign_collaborators_section .select2-container").css('display', 'inline-block');
            //$('#showContactList').toggle();
        });
        $(document).on('click', '.cancel_collaborator', function() {
            $('.show_message_popup').html('');
            $('.show_message_collaborator').html('');
            $('.collaborator_button_container').hide();
            $('#assign_collaborator').hide();
            $('.assign_collaborators_section .select2-container').hide();
        });
        $(document).on('click', '.cancel_project', function() {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.button_container').hide();
        });
        $(document).on('click', '.edit_lead_info', function() {
            const full_name = document.querySelector('#full_name');
            const contact_no = document.querySelector('#contact_no');
            const email = document.querySelector('#email');
            const title = document.querySelector('#title');
            const zip = document.querySelector('#zip');
            const street_address = document.querySelector('#street_address');
            const city = document.querySelector('#city');
            const state = document.querySelector('#state');
            const identity_document_type = document.querySelector('#identity_document_type');
            const identity_document_number = document.querySelector('#identity_document_number');
            const business_legal_name = document.querySelector('#business_legal_name');
            const doing_business_as = document.querySelector('#doing_business_as');
            const business_category = document.querySelector('#business_category');
            const website_url = document.querySelector('#website_url');
            const business_entity_type = document.querySelector('#business_entity_type');
            const registration_number = document.querySelector('#registration_number');
            const registration_date = document.querySelector('#registration_date');
            const state_of_registration = document.querySelector('#state_of_registration');
            if(full_name.hasAttribute('readonly')){
                $("#full_name").removeAttr('readonly');
                $("#contact_no").removeAttr('readonly');
                $("#email").removeAttr('readonly');
                $("#title").removeAttr('readonly');
                $("#zip").removeAttr('readonly');
                $("#street_address").removeAttr('readonly');
                $("#city").removeAttr('readonly');
                $("#state").removeAttr('readonly');
                $("#identity_document_type").removeAttr('disabled');
                $("#identity_document_number").removeAttr('readonly');
                $("#business_legal_name").removeAttr('readonly');
                $("#doing_business_as").removeAttr('readonly');
                $("#business_category").removeAttr('readonly');
                $("#website_url").removeAttr('readonly');
                $("#business_entity_type").removeAttr('disabled');
                $("#registration_number").removeAttr('readonly');
                $("#registration_date").removeAttr('readonly');
                $("#registration_date").removeAttr('disabled');
                $("#state_of_registration").removeAttr('readonly');
            }else{
                $("#full_name").attr('readonly',true);
                $("#contact_no").attr('readonly',true);
                $("#email").attr('readonly',true);
                $("#title").attr('readonly',true);
                $("#zip").attr('readonly',true);
                $("#street_address").attr('readonly',true);
                $("#city").attr('readonly',true);
                $("#state").attr('readonly',true);
                $("#identity_document_type").attr('disabled',true);
                $("#identity_document_number").attr('readonly',true);
                $("#business_legal_name").attr('readonly',true);
                $("#doing_business_as").attr('readonly',true);
                $("#business_category").attr('readonly',true);
                $("#website_url").attr('readonly',true);
                $("#business_entity_type").attr('disabled',true);
                $("#registration_number").attr('readonly',true);
                $("#registration_date").attr('readonly',true);
                $("#registration_date").attr('disabled',true);
                $("#state_of_registration").attr('readonly',true);
            }
        });
        $(document).on('click', '.edit_project_milestone', function() {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.milestone_button_container').toggle();
            $('#Milestone').toggle();
            $('#MilestoneStage').toggle();
            $('#showMilestone').toggle();
            $('#showMilestoneStage').toggle();
        });
        $(document).on('click', '.cancel_milestone', function() {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.milestone_button_container').hide();
            $('#Milestone').hide();
            $('#MilestoneStage').hide();
            $('#showMilestone').show();
            $('#showMilestoneStage').show();
        });
        /*
        $(document).on('click', '.edit_project_owner', function() {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.show_message_owner').html('');
            $('.owner_button_container').toggle();
            $('#OwnerList').toggle();
            $('#showOwnerList').toggle();
        });
        */
        $(document).on('click', '.edit_project_contact', function() {
            $('.show_message_popup').html('');
            $('.show_message_contact').html('');
            $('.contact_button_container').toggle();
            $('#ContactList').toggle();
            $('#showContactList').toggle();
        });
        $(document).on('click', '.cancel_contact', function() {
            $('.show_message_popup').html('');
            $('.show_message_contact').html('');
            $('.contact_button_container').hide();
            $('#ContactList').hide();
            $('#showContactList').show();
        });
        // ---------- load notes jquery functionality --
        jQuery(document).on('click', '#load-more-notes', function() {
            jQuery(this).html('Loading..');
            jQuery(this).attr('disabled', true);
            var offset = jQuery('#note-offset').val();
            var new_offset = parseInt(offset) + parseInt(10);
            var total_notes_c = <?php echo $total_notes_count; ?>;
            var project_id = <?php echo $project_id; ?>;
            jQuery('#note-offset').val(new_offset);
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/fetch_project_notes',
                method: 'post',
                data: {
                    offset: offset,
                    project_id: project_id
                },
                success(response) {
                    if (new_offset >= total_notes_c) {
                        jQuery('#load-more-notes').css('display', 'none');
                    }
                    jQuery('.notes-listing').append(response);
                    jQuery('#load-more-notes').html('M');
                    jQuery('#load-more-notes').attr('disabled', false);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log('Something went worng.');
                }

            });
        });
        //close pop up
        $(document).on('click', '.close_statuspopup', function() {
            jQuery('#notes_box').hide();
        });
        jQuery(document).on('keyup', '#notes-input', function() {
            $('.error-response').css('display', 'none');
            $('.note-response').css('display', 'none');
        });
        //create project note
        jQuery(document).on('click', '#create-note', function() {
            var noteInput = jQuery('#notes-input').val();
            var notes = jQuery.trim(noteInput);
            if (noteInput == '' || notes.length == 0) {
                $('.error-response').css('display', 'block');
                $('.note-response').css('display', 'none');
                return false;
            } else {
                var confidence_notes_access = jQuery("#confidence_user_check:checked").val();
                if(typeof confidence_notes_access === "undefined"){
                    confidence_notes_access = 0;
                    var note_label = 'comment';
                }else{
                    confidence_notes_access = 1;
                    var note_label = 'CONFIDENTIAL note';
                }
                jQuery(this).html('Creating..');
                jQuery(this).prop('disabled', true);
                var project_id = <?php echo $project_id; ?>;

                var name = jQuery('#userDetails').attr('data-name');
                var note = name+' added a '+ note_label +': '+noteInput;
                var user_id = <?php echo get_current_user_id();  ?>;
                jQuery.ajax({
                    url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/add_edit_notes',
                    method: 'post',
                    dataType: 'Json',
                    data: {note:note,opp_product_id:project_id,user_id: user_id,confidence_notes_access:confidence_notes_access,note_type:'project'},
                    success(response) {
                       var res = JSON.parse(response);
                        console.log(res);
                        var note_id = res.note_id;
                        <?php
                        $from='UTC';
                        $to='America/New_York';
                        $format='Y-m-d h:i:s A';
                        $date = date("Y-m-d H:i:s");
                        date_default_timezone_set($from);
                        $newDatetime = strtotime($date);
                        date_default_timezone_set($to);
                        $newDatetime = date($format, $newDatetime);
                        date_default_timezone_set('UTC');
                        $datetime = date_create($newDatetime);
                        $time = date_format($datetime, "h:ia");
                        $day = date_format($datetime, " D ");
                        $month = date_format($datetime, " M ");
                        $date = date_format($datetime, "dS,");
                        $year = date_format($datetime, " Y");
                        $actual_date = $time . " on " . $day . $month . $date . $year;
                        ?>
                        var curr_date = "<?php echo $actual_date; ?>";

                        if(confidence_notes_access){
                          var conf_class='confidential-notes-div';
                        }else{
                          var conf_class='';  
                        }        
                        
                        var confidence_user = "<?php echo $confidence_user; ?>";
                        
                        if(confidence_user == 1 && confidence_notes_access==1){ 
                            var not_edit_btn = '<a href="javascript:void(0)" title="Edit Notes" class="edit_self_notes" data-note_id="'+note_id+'"><i class="fa-regular fa-pen-to-square"></i></a><a href="javascript:void(0)" title="Delete Notes" class="delete_self_notes" data-note_id="'+note_id+'"><i class="fa fa-trash" aria-hidden="true"></i></a>';
                        }else{
                            var not_edit_btn = '';
                        }

                        var not_div = '<div class="note-listing-div shadow '+conf_class+'" id="note-list-'+note_id+'"><p class="notes" id="note-'+note_id+'" data-confidece="'+confidence_notes_access+'">'+note+'</p><p class="date-time">('+curr_date+')</p>'+not_edit_btn+'</div>';

                        jQuery('.notes-listing').prepend(not_div);

                       /*
                        jQuery('.notes-listing').prepend(response);
                        var offset = jQuery('#note-offset').val();
                        var new_offset = parseInt(offset) + parseInt(1);
                        jQuery('#note-offset').val(new_offset);
                        <?php
                        $date = date("Y-m-d H:i:s");
                        $datetime = date_create($date);
                        $time = date_format($datetime, "h:ia");
                        $day = date_format($datetime, " D ");
                        $month = date_format($datetime, " M ");
                        $date = date_format($datetime, "dS,");
                        $year = date_format($datetime, " Y ");
                        $actual_date = $time . " on " . $day . $month . $date . $year;
                        ?>
                        var curr_date = "<?php echo $actual_date; ?>";
                        var not_div = '<div class="note-listing-div"><p class="notes">' + note + '</p><p class="date-time">' + curr_date + '</p></div>';
                        */

                        jQuery('#notes-input').val('');
                        jQuery("#confidence_user_check").prop('checked',false);
                        jQuery('.note-response').css('display', 'block');
                        jQuery('.note-response').html("Notes created successfully.");
                        jQuery('#create-note').html('Submit').attr('disabled', false);
                        jQuery('.error-response').css('display', 'none');
                        setTimeout(function() {
                            jQuery('#add-new-opp-notes').modal('hide')
                        }, 2000);
                    }
                });
            }
        });
    });
    function saveProject(noteMessage) {
        var project_id = <?php echo $project_id; ?>;
        var id = jQuery('#userDetails').attr('data-id');
        var name = jQuery('#userDetails').attr('data-name');
        var note = noteMessage;
        var user_id = <?php echo get_current_user_id();  ?>;
        jQuery.ajax({
            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/create_project_notes',
            method: 'post',
            data: {
                note: note,
                project_id: project_id,
                user_id: user_id
            },
            success(response) {
                jQuery('.notes-listing').prepend(response);
                var offset = jQuery('#note-offset').val();
                var new_offset = parseInt(offset) + parseInt(1);
                jQuery('#note-offset').val(new_offset);
            }
        });
    }
</script>

<!--------- documents tab -->
<script type="text/javascript">
    jQuery(document).on('click', '.edit_download_sdgr', function () {
        var download_link = jQuery(this).data('file-link');
        var sdgr_form = jQuery(this).data('edit-sdgr');
        jQuery(".edit_sdgr").attr('href', sdgr_form);
        jQuery(".download_sdgr").attr('href', download_link);
        jQuery('#sdgr_info').css('display', 'block');
    })

    function close_sdgr_popup() {
        jQuery("#sdgr_info").css('display', 'none');
    }
</script>

<script> 
    jQuery(document).ready(function() {               
        function getFileFormatsString(fileExtension) {
            var formats = fileExtension.slice(0, -1).join(', ');
            var lastFormat = fileExtension[fileExtension.length - 1];
            return formats + (fileExtension.length > 1 ? ', and ' + lastFormat : lastFormat);
        }
        function validateSize(input, fileExtension) {
            var file = input.files[0];
            var size = file.size / 1024;
            var ext = input.value.split('.').pop().toLowerCase();
            var file_name = file.name;
            var file_length = file_name.length;
            if (file_length > 50) {
                swal("Error", "File name should not exceed 50 characters", "error");
                return false;
            }
            if ($.inArray(ext, fileExtension) == -1) {
                swal("Error", "Only " + getFileFormatsString(fileExtension) + " file formats are accepted", "error");
                input.value = "";
                return false;
            }
            if (size > 20240) {
                swal("Error", "Please upload a file up to 20 MB.", "error");
                input.value = "";
                return false;
            }
            return true;
        }

        jQuery(document).on('change','.erc_custom_button', function () {
            var input = $(this);
            var name = this.name;
            var doc_type_id = $(this).data('doc_type_id');
            var lead_id = $(this).data('lead_id');
            var parent_folder = $(this).data('parent_folder');
            var file = this.files[0];
            var formdata = new FormData();
            formdata.append('file', file);
            
            formdata.append('action', 'handle_erc__file_upload');
            formdata.append('doc_key', name);
            formdata.append('parent_folder', parent_folder);
            formdata.append('doc_type_id', doc_type_id);
            formdata.append('lead_id', lead_id);

            var file_type = $(this).data('file_type');

            if (file_type == 'excel') {
                var fileExtension = ['xls', 'xlsx'];
            } else if (file_type == 'word') {
                var fileExtension = ['jpeg', 'jpg', 'png', 'pdf', 'zip', 'doc', 'docx'];
            } else {
                var fileExtension = ['jpeg', 'jpg', 'png', 'pdf', 'zip'];
            }


            if (!validateSize(this, fileExtension)) {
                return false;
            } else {
                $(input).closest(".custom-file-upload").prepend('<i aria-hidden="true" class="fa fa-spinner fa-spin"></i>');
                $(input).closest(".custom-file-upload").find('.material-symbols-outlined').css('display', 'none');
                $.ajax({
                    url: doc_upload.ajaxurl,
                    type: 'POST',
                    data: formdata,
                    processData: false,
                    contentType: false,
                    success: function (response) {
                        var data = JSON.parse(response);
                        var uploadok = data.uploadok;

                        if (uploadok == 'false') {
                            swal("Error", data.error, "error").then(function () {
                                // location.reload();
                            });
                        } else {

                            var message = data.message;
                            var file_url = data.file_url;
                            var pdf_filename = data.pdf_filename;
                            var parent_folder = data.parent_folder;
                            var lead_id = data.lead_id;
                            var doc_key = data.doc_key;
                            var onedrive_link = data.onedrive_link;
                            var doc_id = data.doc_id;
                            
                            // code to change next label by class html
                            $(input).closest(".custom-file-upload").css('display', 'none');
                            $(input).closest("td").next("td").find('span.progress_status').css('display', 'block');
                            $(input).closest("td").next("td").find('span.not_uploaded').css('display', 'none');
                            $(input).closest(".custom-file-upload").find('.fa-spinner').css('display', 'none');
                            $(input).closest(".custom-file-upload").find('.material-symbols-outlined').css('display', 'block');
                            $(input).closest(".custom-file-upload").next(".file_remove_label").css('display', 'flex');
                            $(input).closest(".custom-file-upload").next(".file_remove_label").html('<span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="' + file_url + '" target="_blank"><span class="custom_file">' + pdf_filename + '</span></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-folder_name="' + parent_folder + '" data-lead_id="' + lead_id + '"  data-doc_key="' + doc_key + '" data-doc_id="' + doc_id + '" data-doc_type_id="' + doc_type_id + '" data-file_name="' + pdf_filename + '" data-onedrive_link="' + onedrive_link + '" style="color: initial;">cancel</span>');
                            swal("Success", "Document uploaded successfully", "success").then(function () {
                                // location.reload();
                            });

                        }


                    }
                });
            }
        });
    });

    jQuery(document).on('change', '.change_doc', function () {
            var input = $(this);
            var value = $(this).val();
            console.log(value);
            var input = $(this);
            var doc_id = $(this).find("option:selected").data('doc_id');
            var doc_type_id = $(this).find("option:selected").data('doc_type_id');
            var label = $(this).closest('tr').find('label.questions_Q').text().trim();
            var reason = '';
            if (value == 'Approved') {

                var text = "Are you sure you want to approve this document? You will not be able to reject it later!";
                var icon = "success";

            } else if (value == 'Rejected') {

                var text = "Are you sure you want to reject this document?";
                var icon = "error";

            } else if (value == 'In review') {
                var text = "Are you sure you want to change the status of this document to In review again?";
                var icon = "warning";
            } else if (value == '' || value == 'Select Status') {
                swal("Error", "Please select a status", "error");
                return false;
            }
            if (value == 'In review') {
                //pop up
                swal({
                    title: "",
                    text: text,
                    icon: icon,
                    buttons: true,
                    dangerMode: true,
                })
                    .then((willDelete) => {
                        
                        if (willDelete) {

                            $.ajax({
                                url: doc_upload.ajaxurl,
                                type: 'POST',
                                data: {
                                    action: 'update_stc_document_status',
                                    value: value,
                                    doc_id: doc_id,
                                    doc_type_id: doc_type_id,
                                    label: label,
                                    reason: reason
                                },
                                success: function (response) {
                                    swal("Success", "Document status updated successfully", "success").then(function () {
                                        // location.reload();
                                    });
                                }
                            });

                        } else {

                            swal("Document status not changed!").then(function () {
                                // location.reload();
                            });
                        }
                    });
            }

            if (value == 'Rejected' || value == 'Approved') {
                $.ajax({
                    url: doc_upload.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'check_rejected_reason',
                        doc_id: doc_id,
                        doc_type_id: doc_type_id,
                        value: value,
                    },
                    success: function (response) {

                        var data = JSON.parse(response);
                        var uploadok = data.uploadok;

                        if (uploadok == 'false') {

                            $(input).closest('tr').find('span.add_erc_comment_btn').trigger('click');

                            if (value == 'Rejected') {

                                var modal_title = 'Please add a comment before rejecting the document.';
                                var modal_btn = 'Reject';
                            }
                            else if (value == 'Approved') {

                                var modal_title = 'Please add a comment before approving the document.';
                                var modal_btn = 'Approve';
                            }

                            $('#exampleModal_otherdoc_adding_comment .modal-header h5').html(modal_title);
                            $("#exampleModal_otherdoc_adding_comment #doc_id_comment").val(doc_id);
                            $("#exampleModal_otherdoc_adding_comment #doc_type_id_comment").val(doc_type_id);
                            $('#exampleModal_otherdoc_adding_comment .modal-footer').html('<input type="hidden" class="comment_status" value="' + value + '"></input> <button type="button" class="btn btn-secondary ts-close-modal" data-dismiss="modal">Close</button><button type="button" class="btn btn-primary add_approved_project_rejected_comment_btn">' + modal_btn + '</button>');
                            $("#exampleModal_otherdoc_adding_comment").addClass('in');
                            $("#exampleModal_otherdoc_adding_comment").modal('show');
                            $('.ts-close-modal').on('click', function () {
                                location.reload();
                            });
                            return true;

                        } else {
                            swal({
                                title: "",
                                text: text,
                                icon: icon,
                                buttons: true,
                                dangerMode: true,
                            })
                                .then((willDelete) => {
                                    if (willDelete) {

                                        $.ajax({
                                            url: doc_upload.ajaxurl,
                                            type: 'POST',
                                            data: {
                                                action: 'update_stc_document_status',
                                                value: value,
                                                doc_id: doc_id,
                                                doc_type_id: doc_type_id,
                                                label: label,
                                                reason: reason
                                            },
                                            success: function (response) {
                                                swal("Success", "Document status updated successfully", "success").then(function () {
                                                    // location.reload();
                                                });
                                            }
                                        });

                                    } else {

                                        swal("Document status not changed!").then(function () {
                                            // location.reload();
                                        });
                                    }
                                });
                            reason = data.reason;
                        }

                    }
                });
            }
            
            // $('#exampleModal_otherdoc_adding_comment .close').on('click', function () {
            //     // location.reload();
            // });
            
        });
        
        $(document).on('click','.close', function () {
            $('#exampleModal_ercdoc_view').removeClass('show');
            $('#exampleModal_ercdoc_view').hide();
            $('#exampleModal_otherdoc_adding_comment').removeClass('show');
            $('#exampleModal_otherdoc_adding_comment').hide();
        });

        jQuery(document).on('click', '.view_erc_comment', function () {
            console.log('view');
            var doc_type_id = jQuery(this).data('doc_type_id');
            // var doc_id = jQuery(this).closest('td').find('.add_erc_comment_btn').data('doc_id');
            var doc_id = jQuery(this).data('doc_id');
            jQuery('#exampleModal_ercdoc_view .modal-body').html('<div style="text-align-center;"></div><i aria-hidden="true" class="fa fa-spinner fa-spin"></i></div>');
            $.ajax({
                url: doc_upload.ajaxurl,
                type: 'POST',
                data: {
                    action: 'view_stc_comments',
                    doc_type_id: doc_type_id,
                    doc_id: doc_id
                },
                success: function (response) {
                    var json_response = JSON.parse(response);
                    jQuery('#exampleModal_ercdoc_view .modal-body').html('');
                    var inc = 0;
                    jQuery.each(json_response, function (index, value) {
                        if (index != 0) {
                            var prepare_html_op = "<div style='display:none;' class='iris_comment_view_all iris_comment_" + index + "'>" + value + "</div>";
                            jQuery('#exampleModal_ercdoc_view .modal-body').append(prepare_html_op);
                        }
                        else {
                            var prepare_html_op = "<div class='iris_comment_view_all iris_comment_" + index + "'>" + value + "</div>";
                            jQuery('#exampleModal_ercdoc_view .modal-body').append(prepare_html_op);
                        }
                        inc++;
                    });
                    if (inc == 1) {
                        jQuery('#exampleModal_ercdoc_view .modal-footer .btn').hide();
                    }
                    else {
                        jQuery('#exampleModal_ercdoc_view .modal-footer .btn').show();
                    }
                    jQuery("#exampleModal_ercdoc_view").addClass('in');
                    jQuery("#exampleModal_ercdoc_view").modal('show');
                }
            });
            jQuery(document).on('click', '#exampleModal_ercdoc_view .modal-footer .btn', function () {
                jQuery('.iris_comment_view_all').show();
                jQuery('#exampleModal_ercdoc_view .modal-footer .btn').hide();
            });
        });

    jQuery(document).on('click', '.add_erc_comment_btn', function () {
            console.log('addd');
            jQuery("#other-comment-textarea").val("");
            var doc_id = jQuery(this).data('doc_id');
            var doc_type_id = jQuery(this).data('doc_type_id');
            var label = jQuery(this).closest('tr').find('label.questions_Q').text().trim();
            jQuery("#exampleModal_otherdoc_adding_comment #doc_id_comment").val(doc_id);
            jQuery("#exampleModal_otherdoc_adding_comment #doc_type_id_comment").val(doc_type_id);
            jQuery("#exampleModal_otherdoc_adding_comment").attr('data-label', label);
            jQuery("#exampleModal_otherdoc_adding_comment").addClass('in');
            jQuery("#exampleModal_otherdoc_adding_comment").modal('show');
    });

    $(document).on('click', '.add_approved_project_rejected_comment_btn', function () {
            var comment = jQuery('#other-comment-textarea').val();
            var doc_id = jQuery('#doc_id_comment').val();
            var doc_type_id = jQuery('#doc_type_id_comment').val();
            var comment_status = jQuery('.comment_status').val();
            if (comment == '' || comment == ' ' || comment == null) {
                jQuery("#other-comment-textarea").css("border", "1px solid red");
                jQuery("#other-comment-textarea").focus();
                jQuery("#other-comment-textarea").attr("placeholder", "Please enter comment");
                jQuery("#other-comment-textarea").next().remove();
                jQuery("#other-comment-textarea").after('<span class="error">Please enter comment</span>');
                return false;
            }
            $(this).text('Please wait..');
            $(this).attr('disabled',true);
            $.ajax({
                url: doc_upload.ajaxurl,
                type: 'POST',
                data: {
                    action: 'approved_rejected_comment_data',
                    comment: comment,
                    doc_id: doc_id,
                    doc_type_id: doc_type_id,
                    comment_status: comment_status
                },
                success: function (response) {
                    var data = JSON.parse(response);
                    var uploadok = data.uploadok;
                    var reason = data.reason;
                    var label = data.label;
                    if (uploadok == 'true') {
                        $.ajax({
                            url: doc_upload.ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'update_stc_document_status',
                                value: comment_status,
                                doc_id: doc_id,
                                doc_type_id: doc_type_id,
                                label: label,
                                reason: reason
                            },
                            success: function (response) {
                                swal("Success", "Document status updated successfully", "success").then(function () {
                                    // location.reload();
                                });
                            }
                        });
                    };
                }
            });
        });

    jQuery(document).on('click', '#stc-submit-comment-btn', function () {
            var comment = jQuery("#other-comment-textarea").val().trim()
            if (comment == '' || comment == ' ' || comment == null) {
                jQuery("#other-comment-textarea").css("border", "1px solid red");
                jQuery("#other-comment-textarea").focus();
                jQuery("#other-comment-textarea").attr("placeholder", "Please enter comment");
                jQuery("#other-comment-textarea").next().remove();
                jQuery("#other-comment-textarea").after('<span class="error">Please enter comment</span>');

                return false;
            }
            jQuery("#stc-submit-comment-btn").html("Please wait ..").prop('disabled',true);
            var doc_id = jQuery("#doc_id_comment").val();
            var doc_type_id = jQuery("#doc_type_id_comment").val();
            var label = jQuery("#exampleModal_otherdoc_adding_comment").data('label');
            jQuery.ajax({
                url: doc_upload.ajaxurl,
                type: 'POST',
                data: {
                    action: 'project_doc_comment_data',
                    comment: comment,
                    doc_id: doc_id,
                    doc_type_id: doc_type_id,
                    label: label
                },
                success: function (response) {
                    swal("Success", "Comment added successfully", "success");
                    // location.reload();
                    jQuery('#exampleModal_otherdoc_adding_comment .close').trigger('click');
                }
            });
        });
</script>

<script type="text/javascript">
    jQuery(document).on('change', '.erc_doc_section_required', function () {
            var index_key = jQuery(this).data('index_key');
            var doc_type_id = jQuery(this).data('doc_type_id');
            var clicked_doc_section = jQuery(this).data('section');
            var value = jQuery(this).val();

            var radioButtons = jQuery('input[type="radio"][data-index_key="' + index_key + '"]');
            // console.log('radioButtons');
            // console.log(radioButtons);
            if (value === 'Yes' && clicked_doc_section !== 'section3') {
                radioButtons = radioButtons.not('[data-section="section3"]');
            } else if (value === 'Yes' && clicked_doc_section === 'section3') {
                var is_uploaded = [];
                radioButtons.each(function () {
                    var doc_id = jQuery(this).data('doc_id');
                    if (doc_id != '' && doc_id != null && doc_id != undefined) {
                        is_uploaded.push(doc_id);
                    }
                });
                if (is_uploaded.length > 0) {
                    radioButtons = radioButtons.filter('[data-section="section3"]');
                }
            }


            if (value === 'No' && clicked_doc_section === 'section3') {

                var is_yes_selected = [];
                radioButtons.each(function () {
                    if (!this.checked) {
                        is_yes_selected.push(this);
                    }
                });

                if (is_yes_selected.length > 0) {
                    radioButtons = radioButtons.filter('[data-section="section3"]');
                }
            }

            // radioButtons.prop('checked', true);

            var docTypeIds = [];
            var isvalid = [];


            radioButtons.each(function () {
                var this_alength = jQuery(this).closest('tr').find('a').length;
                var doc_ids = jQuery(this).data('doc_id');
                if (doc_ids != '' && doc_ids != null && doc_ids != undefined) {
                    isvalid.push(doc_ids);
                }
                if(jQuery(this).data('doc_type_id')!=28 && jQuery(this).data('doc_type_id')!=29 ){
                    docTypeIds.push(jQuery(this).data('doc_type_id'));  
                    jQuery(this).prop('checked', true);
                }    
                jQuery(this).append('<i aria-hidden="true" class="fa fa-spinner fa-spin"></i>')
            });



            if (isvalid.length > 0) {
                if (clicked_doc_section == 'section4' && index_key == '0no') {
                    var text = "You have already uploaded a document for this quarter, Are you sure your company did not apply for PPP Loan in 2020?";
                } else if (clicked_doc_section == 'section4' && index_key == '1no') {
                    var text = "You have already uploaded a document for this quarter, Are you sure your company did not apply for PPP Loan in 2021?";
                } else {
                    var text = "You have already uploaded a document for this quarter, Are you sure you don’t have W2 Employee in this Quarter?";
                }

            } else {
                if (value == 'Yes') {
                    if (clicked_doc_section == 'section4' && index_key == '0yes') {
                        var text = "Are you sure your company applied for PPP Loan in 2020?"
                    } else if (clicked_doc_section == 'section4' && index_key == '1yes') {
                        var text = "Are you sure your company applied for PPP Loan in 2020?";
                    } else {
                        var text = "Are you sure you have W2 Employee in this Quarter?";
                    }
                } else if (value == 'No') {
                    if (clicked_doc_section == 'section4' && index_key == '0no') {
                        var text = "Are you sure your company did not apply for PPP Loan in 2020?";
                    } else if (clicked_doc_section == 'section4' && index_key == '1no') {
                        var text = "Are you sure your company did not apply for PPP Loan in 2021?";
                    } else {
                        var text = "Are you sure you don’t have W2 Employee in this Quarter?";
                    }
                }

            }


            var icon = "warning";
            //pop up
            swal({
                title: "",
                text: text,
                icon: icon,
                buttons: true,
                dangerMode: true,
                buttons: ["No", "Yes"]
            })
                .then((willDelete) => {
                    if (willDelete) {
                        var lead_id = "<?php echo $lead_id;?>";
                        $.ajax({
                            url: doc_upload.ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'update_erc_required_mapping',
                                value: value,
                                docTypeIds: docTypeIds,
                                doc_ids: isvalid,
                                lead_id:lead_id,
                            },
                            success: function (response) {

                                swal("Success", "Changed successfully", "success").then(function () {
                                    // location.reload()
                                });
                                // location.reload();
                            }
                        });

                    } else {

                        // swal("Status not changed!").then(function() {
                       // location.reload();
                        // });
                    }
                });

        });

    jQuery(document).on('change', '.erc_ppp_section_required', function () {
            var index_key = jQuery(this).data('index_key');
            var doc_type_id = jQuery(this).data('doc_type_id');
            var clicked_doc_section = jQuery(this).data('section');
            var value = jQuery(this).val();

            var radioButtons = jQuery('input[type="radio"][data-index_key="' + index_key + '"]');

            if (value === 'Yes' && clicked_doc_section !== 'section3') {
                radioButtons = radioButtons.not('[data-section="section3"]');
            }

            // radioButtons.prop('checked', true);
            
            var docTypeIds = [];
            var isvalid = [];

            docTypeIds.push(jQuery(this).data('doc_type_id'));
            jQuery(this).append('<i aria-hidden="true" class="fa fa-spinner fa-spin"></i>')

            /*
            radioButtons.each(function () {
                var this_alength = jQuery(this).closest('tr').find('a').length;
                var doc_ids = jQuery(this).data('doc_id');
                if (doc_ids != '' && doc_ids != null && doc_ids != undefined) {
                    isvalid.push(doc_ids);
                }
                    docTypeIds.push(jQuery(this).data('doc_type_id'));
                jQuery(this).append('<i aria-hidden="true" class="fa fa-spinner fa-spin"></i>')
            });
            
            console.log('ppp');
            console.log(docTypeIds);
*/
            if (isvalid.length > 0) {
                if (clicked_doc_section == 'section4' && index_key == '0no') {
                    var text = "You have already uploaded a document for this quarter, Are you sure your company did not apply for PPP Loan in 2020?";
                } else if (clicked_doc_section == 'section4' && index_key == '1no') {
                    var text = "You have already uploaded a document for this quarter, Are you sure your company did not apply for PPP Loan in 2021?";
                } else {
                    var text = "You have already uploaded a document for this quarter, Are you sure you don’t have W2 Employee in this Quarter?";
                }

            } else {
                if (value == 'Yes') {
                    if (clicked_doc_section == 'section4' && index_key == '0yes') {
                        var text = "Are you sure your company applied for PPP Loan in 2020?"
                    } else if (clicked_doc_section == 'section4' && index_key == '1yes') {
                        var text = "Are you sure your company applied for PPP Loan in 2020?";
                    } else {
                        var text = "Are you sure you have W2 Employee in this Quarter?";
                    }
                } else if (value == 'No') {
                    if (clicked_doc_section == 'section4' && index_key == '0no') {
                        var text = "Are you sure your company did not apply for PPP Loan in 2020?";
                    } else if (clicked_doc_section == 'section4' && index_key == '1no') {
                        var text = "Are you sure your company did not apply for PPP Loan in 2021?";
                    } else {
                        var text = "Are you sure you don’t have W2 Employee in this Quarter?";
                    }
                }

            }


            var icon = "warning";
            //pop up
            swal({
                title: "",
                text: text,
                icon: icon,
                buttons: true,
                dangerMode: true,
                buttons: ["No", "Yes"]
            })
                .then((willDelete) => {
                    if (willDelete) {
                        var lead_id = "<?php echo $lead_id;?>";
                        $.ajax({
                            url: doc_upload.ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'update_erc_required_mapping',
                                value: value,
                                docTypeIds: docTypeIds,
                                doc_ids: isvalid,
                                lead_id:lead_id,
                            },
                            success: function (response) {

                                swal("Success", "Changed successfully", "success").then(function () {
                                    // location.reload()
                                });
                                // location.reload();
                            }
                        });

                    } else {

                        // swal("Status not changed!").then(function() {
                      //  location.reload();
                        // });
                    }
                });

        });
</script>


<!-- edit delete notes script -->
<script type="text/javascript">
    jQuery(document).ready(function(){
        jQuery('#notes-input').keypress(function (event) {
            var Length = jQuery("#notes-input").val().length;
              maxLen = 1000;
            if (Length >= maxLen) {
                if (event.which != 8) {
                    //swal("Error", "Only 70 characters are allowed", "error");
                    return false;
                }
            }
        });

            const maxLength = 1000;
            $('#notes-input').on('input', function() {
                const remainingChars = maxLength - $(this).val().length;
                $('#remaining-msg').text(remainingChars + '/1000 characters remaining.');
            });

            
            jQuery('#edit-notes-input').keypress(function (event) {
            var Length = jQuery("#edit-notes-input").val().length;
              maxLen = 1000-Length;
            if (Length >= maxLen) {
                if (event.which != 8) {
                    //swal("Error", "Only 70 characters are allowed", "error");
                    return false;
                }
            }
        });

            $('#edit-notes-input').on('input', function() {
                const remainingChars = maxLength - $(this).val().length;
                $('#edit-remaining-msg').text(remainingChars + '/1000 characters remaining.');
            });

    });

    jQuery(document).on('click', '.delete_self_notes', function() {
                // Swal.fire({
                //     title: "Are you sure?",
                //     text: "Do you want to delete this notes?",
                //     icon: "warning",
                //     buttons: {
                //         cancel: "No",
                //         confirm: {
                //           text: "Yes",
                //           value: true,
                //           visible: true,
                //           className: "",
                //           closeModal: true
                //         }
                //       },
                //       dangerMode: true,     
                // }).then((isConfirm) => {
                //     if(isConfirm){
                //     var note_id = jQuery(this).data('note_id');
                //     var ajax_url = '<?php echo admin_url('admin-ajax.php'); ?>';
                //     jQuery.ajax({
                //       url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/delete_notes',
                //       method: 'post',
                //       dataType: 'json',
                //       data: {note_id:note_id,note_type:'project'},
                //       success: function(response) {
                //         jQuery('#note-list-'+note_id).hide();
                //         Swal.fire({
                //           title: 'Success!',
                //           text: 'Notes deleted successfully!',
                //           icon: 'success'
                //         });
                //     }
                //     });
                //     }
                // });
                Swal.fire({
                    title: "Are you sure?",
                    text: "Do you want to delete this notes?",
                    icon: "warning",
                    showCancelButton: true,
                    confirmButtonText: "Yes",
                    cancelButtonText: "No",
                    reverseButtons: true,
                    customClass: {
                        confirmButton: 'btn-success',
                        cancelButton: 'btn-cancel'
                    }
                    // buttonsStyling: false
                    }).then((result) => {
                    if (result.isConfirmed) {
                        var note_id = jQuery(this).data('note_id');
                        jQuery.ajax({
                        url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/delete_notes',
                        method: 'post',
                        dataType: 'json',
                        data: {note_id:note_id, note_type:'project'},
                        success: function(response) {
                            jQuery('#note-list-' + note_id).hide();
                            Swal.fire({
                            title: 'Success!',
                            text: 'Notes deleted successfully!',
                            icon: 'success'
                            });
                        }
                        });
                    }
                });

    });
     
     // on click edit notes
     jQuery(document).on('click', '.edit_self_notes', function() {
        jQuery('.note-response').hide();
        jQuery('.error-response').hide();
        jQuery('#update-note').html('Update');
         jQuery('#update-note').prop('disabled', false);    

        var note_id = jQuery(this).data('note_id');
        var notes = jQuery('#note-'+note_id).html();
        var all_notes = notes.split(':');
        var new_notes = '';
        var note_prefix = '';

        if (typeof all_notes[0] !== 'undefined') {
            note_prefix = all_notes[0];
        }

        if (typeof all_notes[1] !== 'undefined') {
                new_notes = new_notes + all_notes[1];
        }

        if(typeof all_notes[2] !== 'undefined'){
            new_notes = new_notes + all_notes[2];
        }    

        if(new_notes == ''){
            new_notes = notes;
        }    
        
        notes = new_notes;
        
        notes = jQuery.trim( notes );

        var remainingChars = 1000 - notes.length;
        $('#edit-remaining-msg').text(remainingChars + '/1000 characters remaining.');
        
        
        var data_confidece = jQuery('#note-'+note_id).attr('data-confidece');
        console.log(data_confidece);
        jQuery('#update-note').data('note_id',note_id); 
        jQuery("#edit-notes-input").val(notes);
        jQuery("#edit-notes-input").data('note_prefix',note_prefix);
        
        if(data_confidece==1){
            jQuery("#edit_confidence_user_check").prop('checked',true);
        }else{
            jQuery("#edit_confidence_user_check").prop('checked',false);
        }    
        jQuery("#edit-new-opp-notes").modal('show'); 
     });   

     // on submit update notes
     jQuery(document).on('click', '#update-note', function() {
        var note_id = jQuery(this).data('note_id');
        var project_id = <?php echo $project_id; ?>; 
        var note = jQuery('#edit-notes-input').val();

        var notes = jQuery.trim(note);
            
        if (note == '' || notes.length == 0){
            $('.error-response').css('display', 'block');
            $('.note-response').css('display', 'none');
            return false;
    }else{
        
        var note_prefix = jQuery('#edit-notes-input').data('note_prefix');

        var user_id = <?php echo get_current_user_id();  ?>;    
        var confidence_notes_access = jQuery("#edit_confidence_user_check:checked").val();
        if(typeof confidence_notes_access === "undefined"){
            confidence_notes_access = 0;
            var note_type = 'comment';
            note_prefix = note_prefix.replace("added a CONFIDENTIAL note", "added a comment");
        }else{
            confidence_notes_access = 1;
            var note_type = 'CONFIDENTIAL note';
            note_prefix = note_prefix.replace("added a comment","added a CONFIDENTIAL note");
        }


        jQuery(this).html('Updating..');
         jQuery(this).prop('disabled', true);
         note = note_prefix +" : "+ note;
         // var name = jQuery('#userDetails').attr('data-name');
         // var note = name+' added a '+note_type+' : '+note;
        // var data = {action:'edit_self_notes',note_id:note_id,note_val:note,user_id:user_id,confidence_notes_access:confidence_notes_access};
             
        var ajax_url = '<?php echo admin_url('admin-ajax.php'); ?>';
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/add_edit_notes',
                method: 'post',
                dataType: 'Json',
                data: {note:note,opp_product_id:project_id,user_id:user_id,confidence_notes_access:confidence_notes_access,note_type:'project',note_id:note_id},
              success: function(response) {
                
                jQuery('#note-'+note_id).html(note);
                

                if(confidence_notes_access==0){
                    jQuery('#note-list-'+note_id).removeClass('confidential-notes-div');
                    jQuery('#note-'+note_id).attr('data-confidece','0');
                }else{
                    jQuery('#note-list-'+note_id).addClass('confidential-notes-div');
                    jQuery('#note-'+note_id).attr('data-confidece','1');
                }
                    Swal.fire({
                      title: 'Success!',
                      text: 'Notes updated successfully!',
                      icon: 'success'
                    });
                    $('.close-popup-notes').trigger('click');
                    jQuery(this).html('Update');
                    jQuery(this).prop('disabled', false);
              }
        });
       }
    });

</script>


<script type="text/javascript">
           $(document).on('click','.edit_opportunity_owner',function(){
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.show_message_owner').html('');
            $('.owner_button_container').toggle();
            $('#OwnerList').toggle();
            $('#showOwnerList').toggle();
            $('#owner-div-id').removeClass('owner-main-div');
            $('.owner_button_container').show();
        });

        $(document).on('click', '.cancel_owner', function() {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.show_message_owner').html('');
            $('.owner_button_container').hide();
            $('#OwnerList').hide();
            $('#showOwnerList').show();
            $('#owner-div-id').addClass('owner-main-div');
            $('.owner_button_container').hide();
        });

        $(document).on('click','.update_owner',function(){
        var lead_id = "<?php echo $lead_id;?>";
        var projectID = $(this).attr('data-projectID');
        var OwnerList = $('#OwnerList').val();
        var ownerName = $("#OwnerList option:selected").text(); 

        var product_id = <?php echo $project->product_id;?>;
        var user_id = jQuery('#userDetails').attr('data-id');

        $('.show_message_owner').html('<p class="alert_message">Please Wait</p>');
        jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-opportunity-project-owner',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    OwnerList: OwnerList,
                    product_id:product_id,
                    user_id:user_id,
                },
            success(res) {
                console.log(res);
                if(res.status==true){
                    $('.show_message_owner').html('<p class="success_message">Successfully updated</p>');

                    $('#showOwnerList').html(ownerName);
                    var name = jQuery('#userDetails').attr('data-name');
                    var noteMessage = name+' added a comment: '+name+' updated owener to '+ownerName;
                    // var noteMessage = name+' added a comment: Owner updated by '+name;
                    saveNotes(noteMessage);
                    $('.show_message_owner').html('');
                    $('#OwnerList').hide();
                    $('#showOwnerList').show();
                    $('#owner-div-id').addClass('owner-main-div');
                    $('.owner_button_container').hide();
                }else{
                    $('.show_message_owner').html('<p class="warning_message">Something went worng '+res.message+'</p>');
                }
            },

            error: function(jqXHR, textStatus, errorThrown) {        
                    $('.show_message_owner').html('<p class="warning_message">Something went worng.</p>');  
                }
            });
        });   

        function saveNotes(noteMessage){
        var project_id = <?php echo $project->project_id;?>;
        var id = jQuery('#userDetails').attr('data-id');
        var name = jQuery('#userDetails').attr('data-name');
        var note = noteMessage;
        var user_id = <?php echo get_current_user_id();  ?>;
        jQuery.ajax({
            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/create_project_notes',
            method: 'post',
            data: {note:note,project_id:project_id,user_id:user_id},
            success(response) {
                jQuery('.notes-listing').prepend(response);
                var offset = jQuery('#note-offset').val();
                var new_offset = parseInt(offset)+parseInt(1);
                jQuery('#note-offset').val(new_offset);
            }
        });
    }

// ---------
    $('.expand_pp_div').on('click', function() {
            if ($(this).next().hasClass('show')) {
                $(this).next().removeClass("show");
                $(this).next().attr("style", "height: 0px");
            } else {
                $(this).next().addClass("show");
                $(this).next().attr("style", "height: auto !important");
            }
 
        })

    $(document).on('change','#review_status',function(){
        var review_status_val = $(this).val();
        if(review_status_val=='completed'){
            $('.review_link').show();
        }else{
            $('.review_link').hide();
            $('#review_link').val('');
        }
    });

    $(document).on('input','#review_link',function(){
        var review_link_val = $(this).val();
        if(review_link_val.length!=0){
            var validurl = checkValidlink(review_link_val);   
            if(validurl){
                $('.review_link_error').text('');
            }else{
                $('.review_link_error').text('Please enter valid link.');
            }
        }else{
            $('.review_link_error').text('');
        }
    });

    function checkValidlink(website_url){
         var url_regax = /^(https?|s?ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i;

           var return_val = url_regax.test(website_url);
            
            if(return_val==false){
                website_url = 'https://'+website_url;    
            }

            var return_val = url_regax.test(website_url);

            if(return_val==false){
                return false;
            }else{
                return true;
            }
    }

    function checkValidation(){
        var valid = 0;
        var review_link_val = $('#review_link').val();
        if(review_link_val.length!=0){
            var validurl = checkValidlink(review_link_val);   
            if(validurl){
                $('.review_link_error').text('');
                valid = 1;
            }else{
                $('.review_link_error').text('Please enter valid link.');
                valid = 0;
            }
        }else{
            $('.review_link_error').text('');
            valid = 1;
        }
        return valid;
    }
</script>

