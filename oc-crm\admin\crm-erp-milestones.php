<?php
/**
 * Create a new table class that will extend the WP_List_Table
 */
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);
class CRM_ERP_Milestones extends WP_List_Table {
    private $userRole;
    private $limitPerpage;
    function __construct() {
        global $status, $page, $pagenow;
        $this->limitPerpage = 15;
        parent::__construct(array(
            'singular' => 'bulk-delete', //singular name of the listed records
            'plural' => 'bulk-deletes', //plural name of the listed records
            'ajax' => false
                //does this table support ajax?
        ));

        $this->list_table_remove_referer();
    }

    public function list_table_remove_referer() {
        // If we're on an admin page with the referer passed in the QS, prevent it nesting and becoming too long.
        global $pagenow;

        if( 'admin.php' === $pagenow && isset( $_GET['_wp_http_referer'] ) && preg_match( '/_wp_http_referer/', $_GET['_wp_http_referer'] ) ) :
            wp_redirect( remove_query_arg( array( '_wp_http_referer', '_wpnonce' ), wp_unslash( $_SERVER['REQUEST_URI'] ) ) );
            exit;
        endif;
    }

    public function column_default($item, $column_name) {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        switch ($column_name) {
            case 'milestone_id':
                return $item->$column_name;
            case 'milestone_name':
                return ucfirst($item->$column_name);
            case 'product_id':
                return $item->$column_name;
            case 'map':
                return $item->$column_name;
            case 'created_at':
                return date('m/d/Y H:i:s', strtotime($item->$column_name));
            case 'action':
                return $item->$column_name;
            default:
                return print_r($item, true);
        }
    }
    function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" class="chkbox" name="deleteItem[]" value="'.$item->milestone_id.'"/>',

            $this->_args['singular'], //Let's simply repurpose the table's singular label ("plugin")
            /* $2%s */
            $item->milestone_id //The value of the checkbox should be the record's id
        );
    }
    function column_milestone_id($item){
        // Code to create data-map
        $serializedString = $item->map;
        $formattedMap = '';
        // Unserialize the string to get an array
        $arrayData = unserialize($serializedString);

        // Decode HTML entities and remove backslashes
        $milestone_name = htmlspecialchars_decode(stripslashes($item->milestone_name));
    
        // Encode milestone name for HTML attribute
        $encoded_milestone_name = htmlspecialchars($milestone_name, ENT_QUOTES);

        // Check if the unserialization was successful and $arrayData is an array
        if (is_array($arrayData)) {
            // Convert the array values to title case and join them with commas
            $formattedMap = implode(',', $arrayData);
        }

        global $wpdb;
        $all_opportunity_id = '';
        $all_opportunity_name = array();
        $all_opportunity_relation = '';
        $all_oppotunity = $wpdb->get_results("SELECT {$wpdb->prefix}opportunity_products.opportunity_product_id as product_relation_id, {$wpdb->prefix}opportunities.OpportunityID, {$wpdb->prefix}opportunities.OpportunityName  FROM {$wpdb->prefix}opportunity_products left join {$wpdb->prefix}opportunities on {$wpdb->prefix}opportunity_products.opportunity_id = {$wpdb->prefix}opportunities.OpportunityID  WHERE {$wpdb->prefix}opportunity_products.DeletedAt IS NULL AND {$wpdb->prefix}opportunity_products.milestone_id = $item->milestone_id ");
        $opportunity_count = 1;
        $total_opportunity = '';
        if(count($all_oppotunity)>0){
            $total_opportunity = count($all_oppotunity);
            foreach($all_oppotunity as $single_opportunity){
                $all_opportunity_id .= $single_opportunity->OpportunityID.',';
                $one_opportunity_name = str_replace("'","",str_replace('"',"",$single_opportunity->OpportunityName));
                array_push($all_opportunity_name,$one_opportunity_name);
                $all_opportunity_relation .= $single_opportunity->product_relation_id.',';
                $opportunity_count++;
            }

        }

        //Build row actions
        $actions = array(
            'edit'      => "<span class='milestone-id-col' data-produts='".$item->product_ids."' data-mile_name='".$encoded_milestone_name."' data-map='".$formattedMap."' data-status='".$item->status."' data-id='".$item->milestone_id."' data-opportunity_count='".$total_opportunity."'  >Edit</span>",
            'delete'    => sprintf('<span data-mile_id ="'.$item->milestone_id.'"  class="delete_milestone" type="button">Delete</span>',$_REQUEST['page'],'delete',$item->milestone_id),
        );
        
        $milestone_id = $item->milestone_id;

        //Return the title contents
        return sprintf('%1$s %2$s',$milestone_id,$this->row_actions($actions));

    }

    function column_map($item){
        // Serialized string
        $serializedString = $item->map;

        // Unserialize the string to get an array
        $arrayData = unserialize($serializedString);

        // Check if the unserialization was successful and $arrayData is an array
        if (is_array($arrayData)) {
            // Convert the array values to title case and join them with commas
            $formattedString = implode(', ', array_map('ucfirst', $arrayData));

            // Output the formatted string
            return $formattedString;
        }
    }

    function column_milestone_name($item){
        // Build row actions
        $actions = array();
    
        // Decode HTML entities and remove backslashes
        $milestone_name = htmlspecialchars_decode(stripslashes($item->milestone_name));
    
        // Encode milestone name for HTML attribute
        $encoded_milestone_name = htmlspecialchars($milestone_name, ENT_QUOTES);
        
        

        // Build the milestone id element with properly encoded milestone name
        $milestone_id = '<span class="milestone-id-cols" data-produts="'.$item->product_id.'" data-mile_name="'.$encoded_milestone_name.'" data-status="'.$item->status.'" data-id="'.$item->milestone_id.'">' . ucwords($milestone_name) . '</span>';
    
        // Return the title contents
        return sprintf('%1$s %2$s', $milestone_id, $this->row_actions($actions));
    }        
      
    function column_action($item){
        $status = $item->status;
                if($status=='active'){
                    $new_status_name='Deactivate';
                    $custom_class = 'deactivated';
                }else{
                    $new_status_name='Activate';
                    $custom_class = 'activated';
                }
        $action = '<a href="'.admin_url('admin.php?page=crm_erp_milestone_stages&mid='.$item->milestone_id).'" data-milestone_id ="'.$item->milestone_id.'"  class="action_milestone btn button" style="margin-right:10px" type="button" target="_blank">Add/ Remove Stage</a> <button data-milestone_id ="'.$item->milestone_id.'"  class="btn button activate-btn custom-active-inactive-btn ' . $custom_class .'" type="button" data-val="'.$item->status.'" style="margin-top:0px !important" >'.$new_status_name.'</button>';
                $actions = array();
                return sprintf('%1$s %2$s',$action,$this->row_actions($actions));
    }

    function column_status($item){
            $status = $item->status;
            if($status=='active'){
                $status_name='Active';
            }else{
                $status_name='Inactive';
            }
        
        $actions = array();
        //Return the title contents
        return sprintf('%1$s %2$s',$status_name,$this->row_actions($actions));
    }    

    function process_bulk_action() {
        // echo "string";
        // print_r($this->current_action());die();
        if ('delete' === $this->current_action()) {
            $nonce = esc_attr($_REQUEST['_wpnonce']);
            if (isset($_REQUEST['action2']) && $_REQUEST['action2']=='delete'){
                
                if(!empty($_REQUEST['deleteItem'])){
                    $product_status_manager = new CRM_ERP_Milestone_Manager();
                    foreach($_REQUEST['deleteItem'] as $singleProductStatus){
                        $product_status_manager->delete_milestone($singleProductStatus);
                    }
                    wp_redirect('?page=crm_erp_milestones');
                    exit;
                }
                
            }
        } 
    }
    /**
     * Prepare the items for the table to process
     *
     * @return Void
     */
    public function prepare_items() { 
        $this->process_bulk_action(); 
    ?> 
        <form method="get" action="" id="search_all_form">
    <?php
        $this->search_box_new($_GET); 
    ?>
        
        <div class="wrap woocommerce">
            <div class="white_card card_height_100 mb_30">
                <div class="white_card_header">
                    <div class="box_header m-0 new_report_header">
                        <div class="title_img">
                            <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/milestone-icon.png" class="page-title-img" alt="">
                            <h4>Milestones</h4>
                        </div>
                        <div class="invoice_exports">
                            <a class="add-opp-custom-icon" id="add-milestone"><i class="fa-solid fa-plus"></i> New Milestone</a>
                            <a href="javascript:void(0);" class="status_btn_export export_invoice_excel export_milestone">Export</a>
                        </div>
                    </div>
                </div>
                <div class="loader_box" id="loader_box" style="display: none;">
                    <div class="loading">
                        <p class="loading__text">Please Wait. Deleting Milestone.</p>
                        <div class="loading__bar"></div>
                    </div>
                </div>
                <div class="white_card_body custom-crm-erp-milestone-report p-15" id="echeck_report_table_wrap">
                <?php
                    $columns = $this->get_columns();
                    $hidden = $this->get_hidden_columns();
                    $sortable = $this->get_sortable_columns();
                    $this->_column_headers = array(
                        $columns,
                        $hidden,
                        $sortable
                    );
                    //$this->process_bulk_action();
                    $search = $_GET;
                    $data = $this->table_data($search);
                    usort($data, array($this, 'sort_data'));
                    $perPage = $this->limitPerpage;
                    $currentPage = $this->get_pagenum();
                    $totalItems = count($data);
                    $this->set_pagination_args(array(
                        'total_items' => $totalItems,
                        'per_page' => $perPage
                    ));
                    $data = array_slice($data, (($currentPage - 1) * $perPage), $perPage);
                    $this->items = $data;
                    $this->display();
                    
                    ?>
                    </div>
                </div>
            </div>
        </form>
        
        <script type="text/javascript">
            $(document).ready(function() {

                $('#search-created_at').datetimepicker({
                    format: 'm/d/Y',
                    autoclose: true,
                    orientation: 'bottom',
                    timepicker: false,
                    autocomplete: 'off',
                    maxDate: 'now'
                });

                // Check if URL contains search_product_id and is not empty
                var urlParams = new URLSearchParams(window.location.search);
                var searchProductIds = [];
                // Loop through parameters until there are no more values
                for (var i = 0; ; i++) {
                    var paramName = 'search_product_id[' + i + ']';
                    var paramValue = urlParams.get(paramName);
                    if (paramValue === null) {
                        // Break the loop if no more values
                        break;
                    }
                    // Add the value to the searchProductIds array
                    searchProductIds.push(paramValue);
                }

                // Check if searchProductIds is not empty
                if (searchProductIds.length > 0) {
                    $("#search-product_id").val(searchProductIds);
                    $("#search-product_id").trigger('chosen:updated'); // Populating product names in search box
                }

                // Handler for the change event of #agreeCheckbox
                $(document).on('change', '#agreeCheckbox', function() {
                    // Check if #agreeCheckbox is checked
                    if ($(this).prop('checked')) {
                        $(".swal-button--confirm").css("pointer-events", "auto");
                    } else {
                        $(".swal-button--confirm").css("pointer-events", "none");
                    }
                });

                $(document).on('click', '.swal-button-container:has(.swal-button--confirm)', function() {
                    if ($('#agreeCheckbox').prop('checked')) {
                        return true;
                    } else {
                        // Check if error message already exists
                        if (!$('.swal-content + p.error-message').length) {
                            // Append the error message if it doesn't exist
                            $('.swal-content').after('<p class="error-message" style="color: red; margin: 40px;">Please agree to delete the milestone!</p>');
                        }
                    }
                });
            });

            jQuery(document).on("click", ".export_milestone",function(){
                jQuery(this).text('Please wait..');
                jQuery(this).css('pointer-events','none');
                var search_product_id = jQuery("#search-product_id").val();
                var search_milestone_name = jQuery("#search-milestone_name").val();
                var search_status = jQuery("#search-status").val();
                var search_milestone_id = jQuery("#search-milestone_id").val();
                var search_created_at = jQuery("#search-created_at").val();
                
                var search_map_lead = '';
                var search_map_opportunity = '';
                var search_map_account = '';
                
                if (jQuery("#map_lead").prop('checked')==true){ 
                    search_map_lead = jQuery("#map_lead").val();
                }
                if (jQuery("#map_opportunity").prop('checked')==true){ 
                    var search_map_opportunity = jQuery("#map_opportunity").val();
                }
                if (jQuery("#map_account").prop('checked')==true){     
                    var search_map_account = jQuery("#map_account").val();
                }
                
                jQuery.ajax({
                    url:'<?php echo admin_url('admin-ajax.php'); ?>',
                    method:'post',
                   data:{action: 'export_milestone',search_product_id:search_product_id,search_milestone_name: search_milestone_name,search_status:search_status,search_milestone_id:search_milestone_id,search_created_at:search_created_at,search_map_lead:search_map_lead,search_map_opportunity:search_map_opportunity,search_map_account:search_map_account},
                    success(response){
                        jQuery(".export_milestone").text('Export');
                        jQuery(".export_milestone").css('pointer-events','');
                        var downloadLink = document.createElement("a");
                        var responseData = jQuery.trim(response);
                        var fileData = ['\ufeff'+responseData];
                        var blobObject = new Blob(fileData,{
                        type: "text/csv;charset=utf-8;"
                        });
                        var url = URL.createObjectURL(blobObject);
                        downloadLink.href = url;
                        var currentDate = new Date();
                        //var dateString = currentDate.toISOString().slice(0, 10); // YYYY-MM-DD

                        // Extract month, day, and year
                        var month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Month is zero-based
                        var day = String(currentDate.getDate()).padStart(2, '0');
                        var year = currentDate.getFullYear();

                        // Combine into MM/DD/YYYY format
                        var dateString = month + '/' + day + '/' + year;

                        var timeString = currentDate
                            .toLocaleTimeString("en-US", {
                                hour12: false,
                                hour: "2-digit",
                                minute: "2-digit",
                                second: "2-digit",
                            })
                            .replace(/:/g, "_");
                        var filename = "Milestones_" + dateString + "_" + timeString + ".csv";
                        downloadLink.download = filename;
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                    }
                })
            })  
        </script>
        <style type="text/css">
            .swal-footer {
                margin-top: 25px;
            }
        </style>
    <?php
    }
    public function get_columns() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        $columns = array(
            'cb' => '<input type="checkbox" />',
            'milestone_id' => 'Milestone ID',
            'milestone_name' =>'Milestone Name',
            'product_id' => 'Product Name',
            'map' => 'Map',
            'status' => 'Status',
            'created_at' => 'Created Date',
            'action' => 'Action',
        );
        //$columns['action'] = 'Action';
        return $columns;
    }
    public function get_sortable_columns() {
        $sortable = array(
            'milestone_id'     => array('milestone_id',true),
            'milestone_name' => array('milestone_name',true),
            'product_id' => array('product_id',true),
            'map' => array('map',true),
            'status' => array('status',true),
            'created_at' => array('created_at',true));
        return $sortable;
    }
    function get_bulk_actions() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
          if(in_array('administrator', $user_roles) || in_array('echeck_staff', $user_roles) || in_array('echeck_client', $user_roles) || in_array('master_sales', $user_roles)){ 
            return array(
                'delete' => __( 'Delete', ''),
            );
          }else{
            return array();
          }      
    }

    /**
     * Define which columns are hidden
     *
     * @return Array
     */

    public function get_hidden_columns() {
        return array();
    }


    /**

     * Define the sortable columns

     *

     * @return Array

     */
    public function get_display_name($user_id) {
        if (!$user = get_userdata($user_id))
            return false;
        return $user->data->display_name;
    }

    /**

     * Get the table data

     *

     * @return Array

     */

    private function table_data($search) {
        $orderby  = 'milestone_id';
        $order  = 'DESC';

        if(isset($search['orderby'])){
            $orderby  = $_GET['orderby'];
            $order  = $_GET['order'];
        }
        global $wpdb;
        if( (isset($search['search_product_id']) && !empty($search['search_product_id'])) || 
            (isset($search['search_milestone_id']) && $search['search_milestone_id'] != '') ||
            (isset($search['search_milestone_name']) && $search['search_milestone_name'] != '') || 
            (isset($search['search_status']) && $search['search_status'] != '') ||
            (isset($search['search_created_at']) && $search['search_created_at'] != '') ||
            (isset($search['search_map_lead']) && $search['search_map_lead'] == 'on') ||
            (isset($search['search_map_opportunity']) && $search['search_map_opportunity'] == 'on') ||
            (isset($search['search_map_project']) && $search['search_map_project'] == 'on') ||
            (isset($search['search_map_account']) && $search['search_map_account'] == 'on')){
                  
                  $search['orderby'] = $orderby;
                  $search['order'] = $order;

            $product_status_manager = new CRM_ERP_Milestone_Manager();
            $product_status_data = $product_status_manager->search_milestones_Manager($search);
        }else{
                $data['orderby'] = $orderby;
                $data['order'] = $order;

            $product_status_manager = new CRM_ERP_Milestone_Manager();
            $product_status_data = $product_status_manager->get_milestone_all_list($data);
        }
        if(!empty($product_status_data)){
            foreach($product_status_data as $key => $product_status_datas){
                $product_status_datas->product_ids = $product_status_datas->product_id;
                $all_products = explode(",",$product_status_datas->product_id);
                $product_names='';
                foreach ($all_products as $all_key => $all_value) {
                    $milestone_manager = new CRM_ERP_Milestone_Manager();
                    $product_name = $milestone_manager->get_product_name($all_value);
                    if(empty($product_names)){
                        $product_names .= $product_name->Title;
                    }else{
                        $product_names .= ','.$product_name->Title;
                    }
                }
                $product_status_datas->product_id = ucwords($product_names);
            }
        }
        //print_r($product_status_datas);exit;
        return $product_status_data;
    }

    /**
     * Allows you to sort the data by the variables set in the $_GET
     *
     * @return Mixed
     */
    private function sort_data($a, $b) {
        // Set defaults
        $orderby = isset($_GET['orderby']) ? sanitize_text_field($_GET['orderby']) : 'milestone_id';
        $order = isset($_GET['order']) ? sanitize_text_field($_GET['order']) : 'desc';
        
        $result = strnatcmp($a->$orderby, $b->$orderby);
    
        return ($order === 'asc') ? $result : -$result;
    }

    protected function pagination( $which ) {
        if ( empty( $this->_pagination_args ) ) {
            return;
        }
        $total_items     = $this->_pagination_args['total_items'];
        $total_pages     = $this->_pagination_args['total_pages'];
        $infinite_scroll = false;
        if ( isset( $this->_pagination_args['infinite_scroll'] ) ) {
            $infinite_scroll = $this->_pagination_args['infinite_scroll'];
        }
        if ( 'top' === $which && $total_pages > 1 ) {
            $this->screen->render_screen_reader_content( 'heading_pagination' );
        }
        $output = '<span class="displaying-num">' . sprintf(
            /* translators: %s: Number of items. */
            _n( '%s item', '%s items', $total_items ),
            number_format_i18n( $total_items )
        ) . '</span>';
        $current              = $this->get_pagenum();
        $removable_query_args = wp_removable_query_args();
        $current_url = set_url_scheme( 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] );
        $current_url = remove_query_arg( $removable_query_args, $current_url );
        $page_links = array();
        $total_pages_before = '<span class="paging-input">';
        $total_pages_after  = '</span></span>';
        $disable_first = false;
        $disable_last  = false;
        $disable_prev  = false;
        $disable_next  = false;
        if ( 1 == $current ) {
            $disable_first = true;
            $disable_prev  = true;
        }
        if ( 2 == $current ) {
            $disable_first = true;
        }
        if ( $total_pages == $current ) {
            $disable_last = true;
            $disable_next = true;
        }
        if ( $total_pages - 1 == $current ) {
            $disable_last = true;
        }
        if ( $disable_first ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&laquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='first-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( remove_query_arg( 'paged', $current_url ) ),
                __( 'First page' ),
                '&laquo;'
            );
        }
        if ( $disable_prev ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&lsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='prev-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( 'paged', max( 1, $current - 1 ), $current_url ) ),
                __( 'Previous page' ),
                '&lsaquo;'
            );
        }
        if ( 'bottom' === $which ) {
            $html_current_page  = $current;
            $total_pages_before = '<span class="screen-reader-text">' . __( 'Current Page' ) . '</span><span id="table-paging" class="paging-input"><span class="tablenav-paging-text">';
        } else {
            $html_current_page = sprintf(
                "%s<input class='current-page' id='current-page-selector' type='text' name='paged' value='%s' size='%d' aria-describedby='table-paging' /><span class='tablenav-paging-text'>",
                '<label for="current-page-selector" class="screen-reader-text">' . __( 'Current Page' ) . '</label>',
                $current,
                strlen( $total_pages )
            );
        }
        $html_total_pages = sprintf( "<span class='total-pages'>%s</span>", number_format_i18n( $total_pages ) );
        $page_links[]     = $total_pages_before . sprintf(
            _x( '%1$s of %2$s', 'paging' ),
            $html_current_page,
            $html_total_pages
        ) . $total_pages_after;
        if ( $disable_next ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&rsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='next-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=> min( $total_pages, $current + 1 )), $current_url ) ),
                __( 'Next page' ),
                '&rsaquo;'
            );
        }
        if ( $disable_last ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&raquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='last-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=>$total_pages), $current_url ) ),
                __( 'Last page' ),
                '&raquo;'
            );
        }
        $pagination_links_class = 'pagination-links';
        if ( ! empty( $infinite_scroll ) ) {
            $pagination_links_class .= ' hide-if-js';
        }
        $output .= "\n<span class='$pagination_links_class'>" . implode( "\n", $page_links ) . '</span>';
        if ( $total_pages ) {
            $page_class = $total_pages < 2 ? ' one-page' : '';
        } else {
            $page_class = ' no-pages';
        }
        $this->_pagination = "<div class='tablenav-pages{$page_class}'>$output</div>";
        echo $this->_pagination;
    }
    public function search_box_new($search_data)
    {
        global $wpdb;
        $products = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}crm_products WHERE {$wpdb->prefix}crm_products.DeletedAt IS NULL AND {$wpdb->prefix}crm_products.status = 'active' ORDER BY {$wpdb->prefix}crm_products.ProductID DESC");
    ?>
        
        <div id="overlay" onclick="overlay_off()"></div>

            <?php  

                $login_user_details = get_user_by( 'id', get_current_user_id() );
                $login_user_name = $login_user_details->display_name;


            ?>
            <input type="hidden" name="page" value="crm_erp_milestones">
            <input type="hidden" name="user_name" class="current_user_name" value="<?php echo $login_user_name; ?>">
            <div class="row align-items-center mb-3">
                <div class="col-md-3 search_field">
                    <img src="https://design.echeck.money/portal/wp-content/plugins/echeck-theme111/assets/img/icon/Search.svg" alt="" class="icon">
                    <input type="text" placeholder="Search.." id="show_lead_search" autocomplete="off" class="open">
                    <input type="submit" style="visibility: hidden;position:absolute;">
                </div>
            </div>
            <div class="popup-overlay">
                <div class="popup-content" id="popup-content">
                    <div class="search_header">
                        <h4>Search</h4>
                        <span class="close">
                            <i class="fa-solid fa-xmark"></i>
                        </span>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <select id="search-product_id" name="search_product_id[]" class="form-control product_id-class" data-placeholder="Products" multiple>
                                <?php foreach($products as $product): ?>
                                    <option id="<?php echo "product-".$product->ProductID; ?>" value="<?php echo $product->ProductID; ?>"><?php echo $product->Title; ?></option>
                                <?php endforeach; ?>
                            </select>
                            <p class="add-prod-msg mile-err" style="color:red;"></p>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <input type="number" min="1" id="search-milestone_id" name="search_milestone_id" placeholder="ID" value="<?php if (isset($search_data['search_milestone_id'])) {echo $search_data['search_milestone_id'];} ?>" class="search-popup-input-select">
                        </div>
                        <div class="col-md-6">
                            <input type="text" id="search-milestone_name" name="search_milestone_name" autocomplete="off" placeholder="Name" value="<?php if (isset($search_data['search_milestone_name'])) {echo htmlspecialchars_decode(stripslashes( $search_data['search_milestone_name']) );} ?>" class="search-popup-input-select">
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <select id="search-status" name="search_status" class="search-popup-input-select form-control search_status"  placeholder="Status" value="<?php if (isset($search_data['search_status'])) {echo $search_data['search_status'];} ?>">
                                <option value="">Status</option>
                                <option value="active" <?php if (isset($search_data['search_status']) && $search_data['search_status'] === 'active') {echo 'selected';} ?>>Active</option>
                                <option value="inactive" <?php if (isset($search_data['search_status']) && $search_data['search_status'] === 'inactive') {echo 'selected';} ?>>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <input type="text" id="search-created_at" class="search-popup-input-select" value="<?php if (isset($search_data['search_created_at'])) {echo $search_data['search_created_at'];} ?>" name="search_created_at" pattern="\d{2}/\d{2}/\d{4}" placeholder="MM/DD/YYYY" autocomplete="off" onkeydown="return false">
                            <!--<input type="date" id="search-created_at" name="search_created_at" class="search-popup-input-select" placeholder="Created At" autocomplete="off" value="<?php if (isset($search_data['search_created_at'])) {echo $search_data['search_created_at'];} ?>">-->
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <div class="checkbox-wrapper-46">
                                <input class="inp-cbx" name="search_map_lead" id="map_lead" type="checkbox" <?php echo (isset($search_data['search_map_lead']) && $search_data['search_map_lead'] === 'on') ? 'checked' : ''; ?>>
                                <label class="cbx" for="map_lead"><span>
                                    <svg width="12px" height="10px" viewBox="0 0 12 10">
                                    <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                    </svg></span><span>Lead - Mapped</span>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="checkbox-wrapper-46">
                                <input class="inp-cbx" name="search_map_opportunity" id="map_opportunity" type="checkbox" <?php echo (isset($search_data['search_map_opportunity']) && $search_data['search_map_opportunity'] === 'on') ? 'checked' : ''; ?>>
                                <label class="cbx" for="map_opportunity"><span>
                                    <svg width="12px" height="10px" viewBox="0 0 12 10">
                                    <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                    </svg></span><span>Opportunity - Mapped</span>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="checkbox-wrapper-46">
                                <input class="inp-cbx" name="search_map_account" id="map_account" type="checkbox" <?php echo (isset($search_data['search_map_account']) && $search_data['search_map_account'] === 'on') ? 'checked' : ''; ?>>
                                <label class="cbx" for="map_account"><span>
                                    <svg width="12px" height="10px" viewBox="0 0 12 10">
                                    <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                    </svg></span><span>Account - Mapped</span>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="checkbox-wrapper-46">
                                <input class="inp-cbx" name="search_map_project" id="map_project" type="checkbox" <?php echo (isset($search_data['search_map_project']) && $search_data['search_map_project'] === 'on') ? 'checked' : ''; ?>>
                                <label class="cbx" for="map_project"><span>
                                    <svg width="12px" height="10px" viewBox="0 0 12 10">
                                    <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                    </svg></span><span>Project - Mapped</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12 text-center">
                        <input type="submit" id="btn_search_submit" class="search-popup-submit-btn" value="Search" />
                        <input type="button" class="search-popup-reset-btn" value="Clear" id="reset_Form_button" onclick="resetAffForm()"/>
                        </div>
                    </div>  
                </div>
            </div>

            <!-- ------- Milstone popup ----- -->

            <div class="mile-popup-overlay" style="left:17%; top:10%">
                <link href="https://cdn.rawgit.com/harvesthq/chosen/gh-pages/chosen.min.css" rel="stylesheet"/>

                <script src="https://cdn.rawgit.com/harvesthq/chosen/gh-pages/chosen.jquery.min.js"></script>
                <div class="mile-popup-content text-left" id="add-milestone-form">
                    <div class="mile-header">
                        <h4 id="mile-header-h4">New Milestone</h4>
                        <span class="mile-close">
                            <i class="fa-solid fa-xmark"></i>
                        </span>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <input type="hidden" id="popup-milestone_id" name="milestone_id" value="0">
                            <label>Products:</label>
                            <select id="popup-product_id" name="product_id" class="form-control product_id-class" data-placeholder="Select Products" multiple>
                                <?php foreach($products as $product): ?>
                                    <option id="<?php echo "product-".$product->ProductID; ?>" value="<?php echo $product->ProductID; ?>"><?php echo $product->Title; ?></option>
                                <?php endforeach; ?>
                            </select>
                            <p class="add-prod-msg mile-err" style="color:red;"></p>
                        </div>
                    </div>    
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <label>Name*:</label>
                            <input type="text" id="popup-milestone_name" name="milestone_name" autocomplete="off" placeholder="" class="search-popup-input-select">
                            <p class="add-mile-name-msg mile-error" style="color:red;"></p>
                        </div>
                        <div class="col-md-6">
                            <label>Status*:</label>
                            <select id="mile-status" name="mile_status" class="search-popup-input-select form-control">
                                <option value="">Select Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                            <p class="add-mile-status-msg mile-error" style="color:red;"></p>
                        </div>
                    </div>
                    <div class="row mt-2">
                        <label>Map Milestone:</label>
                        <div class="col-md-3">
                            <div class="checkbox-wrapper-46">
                                <input class="inp-cbx" id="lead" type="checkbox">
                                <label class="cbx" for="lead"><span>
                                    <svg width="12px" height="10px" viewBox="0 0 12 10">
                                    <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                    </svg></span><span>Lead</span>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="checkbox-wrapper-46">
                                <input class="inp-cbx" id="opportunity" type="checkbox">
                                <label class="cbx" for="opportunity"><span>
                                    <svg width="12px" height="10px" viewBox="0 0 12 10">
                                    <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                    </svg></span><span>Opportunity</span>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="checkbox-wrapper-46">
                                <input class="inp-cbx" id="account" type="checkbox">
                                <label class="cbx" for="account"><span>
                                    <svg width="12px" height="10px" viewBox="0 0 12 10">
                                    <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                    </svg></span><span>Account</span>
                                </label>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="checkbox-wrapper-46">
                                <input class="inp-cbx" id="project" type="checkbox">
                                <label class="cbx" for="project"><span>
                                    <svg width="12px" height="10px" viewBox="0 0 12 10">
                                    <polyline points="1.5 6 4.5 9 10.5 1"></polyline>
                                    </svg></span><span>Project</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12 text-center">
                        <input type="button" id="btn_mile_submit" class="search-popup-submit-btn" value="Submit" />
                        <input type="button" class="reset-miles" value="Cancel" id="cancel_mile_button" onclick="cancelMiles()"/>
                        <p class="add-mile-msgs" style="color:red;"></p>
                        </div>
                    </div>  
                </div>
            <script>
                jQuery(".product_id-class").chosen({
                        no_results_text: "Oops, nothing found!"
                    });
            </script>
            </div>
            <!-- ---------- -->

        <!-- ------- Milstone warning popup End----- -->


<div class="modal warning-popup-overlay" role="dialog" tabindex="-1" role="dialog" aria-labelledby="confirm-modal-label" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Update Opportunity:</h5>
        <button type="button" class="warning-close close">
            <span aria-hidden="true">×</span>
        </button>
      </div>
      <div class="modal-body">
        
            <div class="row mt-2">
                <div class="col-md-12">
                    <div class="alert alert-warning" role="alert"><b>Warning: </b>Product which are removed is linked to multiple opportunity. Please select new milestone and stage for those opportunities.</div>
                                        
                    <div class="milestone_product_loader" style="display:none;">
                        <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/loader.gif" class="page-title-img" alt="" style="width: 70px;margin: 0 auto;display: block;">
                    </div>
                    <div class="all_opportunity_link">
                    </div>
                    <div class="milestone_product_checkbox" style="display: none;">
                        <label><input type="checkbox" name="milestone_product_mapping" class="milestone_product_mapping"> I want to update the milestone and stage of opportunities related to above product.</label>
                        
                    </div>
                    <div class="milestone_product_error"></div>
                </div>
            </div>      
          <div class="buttion_next_prev updateOpportunity" style="display: none;">
              <input type="button" id="btn_opportunity_submit" class="opportunity-submit-btn nxt_btn" value="Update" >
          </div>
    </div>
</div>
</div>
</div>

        <!-- ------- Milstone waning popup End----- -->
        <script type="text/javascript">
            jQuery(".open").on("click", function() {
              jQuery('.status_box').hide(); 
              jQuery(".popup-overlay, .popup-content").addClass("active");
              jQuery(".search_lead_id").focus();
              jQuery('#overlay').show();
            });

            jQuery(".close").on("click", function() {
              jQuery(".popup-overlay, .popup-content").removeClass("active");
              jQuery('#overlay').hide();
            });

            function overlay_off(){
                $(".close, .mile-close").trigger("click");
            }

            function resetAffForm(){
                jQuery("#reset_Form_button").val('Clearing...');
                var site_url = '<?php  echo get_site_url() ?>';
                window.location.href = site_url+'/wp-admin/admin.php?page=crm_erp_milestones';
            }
            jQuery(document).on("click",'#doaction2', function(e) {
                e.preventDefault();
                var is_check = 0;
                $(".chkbox").each(function(){
                    if($(this).is(":checked")){
                        is_check = 1;
                    }
                })
                if($('#bulk-action-selector-bottom').val()=='delete'){
                    if(is_check == 1){
                        swal({
                            title: "Are you sure?",
                            text: "Yes, I want to delete selected Milestones. Related Stages will delete and Opportunities will no longer be linked to the Milestones.",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                                confirm: "Delete",
                            },
                            content: {
                                element: "input",
                                attributes: {
                                    type: "checkbox",
                                    id: "agreeCheckbox"
                                },
                            },
                            dangerMode: true,
                        })
                        .then((willDelete) => {
                            if (willDelete && $('#agreeCheckbox').prop('checked')) {
                                $(this).html('wait..');
                                $(this).attr('disabled',true);
                                $("#loader_box").show();
                                $('#search_all_form').submit();
                            }
                            $('.swal-modal').removeClass('crm-erp-milestones-delete-swal');
                        });
                        $('.swal-modal').addClass('crm-erp-milestones-delete-swal');
                    }else{
                        swal({
                            title: "Please select atleast one item",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                    }
                }else{ 
                    swal({
                            title: "Please select some action.",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                }
            });

            // ------------ Add Milestone functionality -------
             jQuery(document).on("click", "#add-milestone",function() {
                jQuery('#popup-product_id').find('option').prop('selected', false).end().trigger('chosen:updated');
                jQuery('#mile-header-h4').html('New Milestone');
                jQuery("#btn_mile_submit").val("Submit");
                jQuery("#btn_mile_submit").removeClass("editMilestone");
                jQuery('#popup-milestone_name').val('');
                jQuery('#mile-status').val('');
                jQuery('#popup-milestone_id').val(0);
                $('.mile-popup-overlay .inp-cbx').prop('checked', false); // Uncheck all checkboxes
                $('.mile-err, .mile-error, .add-mile-msgs').html("").hide();

              jQuery('.status_box').hide(); 
              jQuery(".mile-popup-overlay, .mile-popup-content").addClass("active");
              jQuery("input[name=milestone_name]").focus();
              jQuery('#overlay').show();
            });

            jQuery(".mile-close").on("click", function() {
              jQuery(".mile-popup-overlay, .mile-popup-content").removeClass("active");
              jQuery('#overlay').hide();
            });

            function cancelMiles(){ 
                jQuery(".mile-popup-overlay, .mile-popup-content").removeClass("active");
                jQuery('#overlay').hide();
            }
            function arraysAreIdentical(arr1, arr2) {
                notPresent = []
                var arr1 = arr1.toString().split(',').map(Number);
                var arr2 = arr2.toString().split(',').map(Number);
                if (arr1.length !== arr2.length) {
                    notPresent = [];
                        for (var i = 0; i < arr1.length; i++) {
                            if (arr2.indexOf(arr1[i]) === -1) {
                                notPresent.push(arr1[i]);
                            }
                        }
                    return notPresent;
                }else{
                    for (var i = 0; i < arr1.length; i++) {
                        if (arr1.indexOf(arr2[i]) === -1) {
                            notPresent = [];
                            for (var i = 0; i < arr1.length; i++) {
                                if (arr2.indexOf(arr1[i]) === -1) {
                                    notPresent.push(arr1[i]);
                                }
                            }
                            return notPresent;
                        }
                    }
                }
                
                return notPresent;
            }
            jQuery(document).on("click", ".warning-close",function() {
                jQuery('.warning-popup-overlay').hide();
            });
            jQuery(document).on("click", "#btn_mile_submit",function() {
                $('.milestone_product_loader').show();
                $('#btn_opportunity_submit').val('Update');
                $('.all_opportunity_link').html('');
                $('.milestone_product_error').html('');
                var button_txt = jQuery(this).val();
                var products = jQuery('#popup-product_id').val();
                var new_products = jQuery('#popup-product_id').val();
                var old_products = jQuery(this).attr('data-produts');
                var milestone_id = jQuery(this).attr('data-milestoneID');
                if(!$('#opportunity').is(':checked')){
                    new_products = '';
                }

                var mile_name = jQuery('#popup-milestone_name').val();
                var mile_status = jQuery('#mile-status').val();
                var milestone_id = jQuery('#popup-milestone_id').val();
                
                // Create an array to store selected checkbox values
                var selectedCheckboxes = [];

                // Iterate over each checkbox and add its value to the array if checked
                jQuery('.mile-popup-overlay .inp-cbx:checked').each(function() {
                    selectedCheckboxes.push(jQuery(this).attr('id'));
                });
                var mile_map = selectedCheckboxes;
                if(products==null){
                    jQuery('.add-prod-msg').html("Please select Product.").show();
                    return;
                }else if(mile_name.length==0){
                    jQuery('.add-prod-msg').html("").hide();
                    jQuery('.add-mile-name-msg').html("Name is required.").show();  
                    return;  
                }else if(mile_name.length>=51){
                    jQuery('.add-prod-msg').html("").hide();
                    jQuery('.add-mile-name-msg').html("Milestone Name length should not be grater than 50.").show();
                    return;
                }else if(mile_status.length==0){
                    jQuery('.add-prod-msg').html("").hide();
                    jQuery('.add-mile-name-msg').html("").hide();
                    jQuery('.add-mile-status-msg').html("Status is required.").show();
                    return;
                }


                if($(this).hasClass('editMilestone') &&arraysAreIdentical(old_products, new_products).length > 0 && jQuery(this).attr('data-opportunity_count')){
                    removedProductList = arraysAreIdentical(old_products, new_products).toString();
                    jQuery.ajax({
                        url:"<?php echo site_url().'/wp-json/productsplugin/v1/get-milestone-stage-for-product'; ?>",
                        method:'post',
                        data:{
                            removedProductList:removedProductList,
                            new_products:new_products.toString(),
                            old_products:old_products.toString(),
                            milestone_id:milestone_id
                        },
                        success(res){
                            //res = JSON.parse(response);
                            if(res.status==true){
                                $('.all_opportunity_link').html(res.html);
                                $('.updateOpportunity').show();
                                $('.milestone_product_checkbox').show();
                                $('.milestone_product_loader').hide();
                                $('.milestone_product_error').html('');
                            }else{
                                $('.all_opportunity_link').html('No Milestone Found for the product');
                            }// check
                           }//check success 
                    });//check ajax 
                    $('#btn_opportunity_submit').attr('removedProductList',removedProductList);
                    $('#btn_opportunity_submit').attr('new_products',new_products);
                    $('#btn_opportunity_submit').attr('old_products',old_products);
                    $('#btn_opportunity_submit').attr('milestone_id',milestone_id);
                    $('#btn_opportunity_submit').attr('mile_status',mile_status);
                    $('#btn_opportunity_submit').attr('mile_name',mile_name);
                    $('#btn_opportunity_submit').attr('mile_map',mile_map);


                    $(".mile-popup-overlay, .mile-popup-content").removeClass("active");
                    $('#overlay').hide();
                    jQuery('.warning-popup-overlay').show();
                    return;
                }
                


                    jQuery(this).val('Loading..').prop('disabled',true);
                    jQuery('.mile-error').html("").hide();
                    var products = products.join(',');
                    jQuery.ajax({
                        url:"<?php echo site_url().'/wp-json/productsplugin/v1/check-milestone-unique'; ?>",
                        method:'post',
                        data:{product_id:products,milestone_name:mile_name,milestone_id:milestone_id},
                        success(response){
                            if(response.status==400){
                                jQuery('.add-mile-msgs').html("Milestone already created.").show();
                                jQuery('#btn_mile_submit').val(button_txt).prop('disabled',false);
                            }else{
                                create_update_milestone(products,mile_name,mile_status,milestone_id,mile_map);
                            }// check
                           }//check success 
                        });//check ajax 
                    
                });// click 

            jQuery(document).on("click", "#btn_opportunity_submit",function() {
                $('#btn_opportunity_submit').val('Wait...');
                if(!$('.milestone_product_mapping').is(':checked')){
                    $('.milestone_product_error').html('<div class="alert alert-danger" role="alert">Please check the check box.</div>');
                    $('#btn_opportunity_submit').val('Update');
                    return;
                }
                removedProductList = $(this).attr('removedProductList');
                new_products = $(this).attr('new_products');
                old_products = $(this).attr('old_products');
                milestone_id = $(this).attr('milestone_id');
                mile_status = $(this).attr('mile_status');
                mile_name = $(this).attr('mile_name');
                mile_map = $(this).attr('mile_map');
                var removedProductArray = removedProductList.toString().split(',').map(Number);
                let myArray = {};
                myArray['data'] = {};
                let count = 0;
                let errorflag = 1;
                Object.keys(removedProductArray).forEach(function(key) {
                  //console.log(key, removedProductArray[key]);
                  if ($(".milestone"+removedProductArray[key])[0]){//if milestone class exist
                        if ($(".stage"+removedProductArray[key]).val() != ''){
                            myArray['data'][count] = {};
                            myArray['data'][count]['product_id'] = removedProductArray[key];
                            myArray['data'][count]['old_milestone'] = milestone_id;
                            myArray['data'][count]['new_milestone'] = $(".milestone"+removedProductArray[key]).val();
                            myArray['data'][count]['stage'] = $(".stage"+removedProductArray[key]).val();
                            count = 1 + count;
                        }else{
                            $('.milestone_product_error').html('<div class="alert alert-danger" role="alert">Please select the valid stage</div>');
                            $('#btn_opportunity_submit').val('Update');
                            //alert('Please select the stage.');
                            errorflag = 0;
                        }//end of else
                    }//if milestone class exist
                });//foreah loop
                myArray['removedProductArray'] = removedProductArray.toString();
                myArray['new_products'] = new_products;
                myArray['old_products'] = old_products;
                myArray['milestone_id'] = milestone_id;
                myArray['mile_status'] = mile_status;
                myArray['mile_name'] = mile_name;
                myArray['mile_map'] = mile_map;
                myArray['user_name'] = jQuery('.current_user_name').val();
                if(errorflag){
                    //console.log(myArray);
                    //console.log(JSON.stringify(myArray));
                    jQuery.ajax({
                        url:"<?php echo site_url().'/wp-json/productsplugin/v1/update-opportunity-mapping'; ?>",
                        method:'post',
                        dataType: 'json',
                        data: {myArray: JSON.stringify(myArray)},
                        success: function(response) {
                            $('.milestone_product_error').html('');
                            $('#btn_opportunity_submit').val('Update');
                                swal({
                                     title: 'Milestone Successfully Updated.',
                                    //  text: text,
                                     icon: 'success',
                                     type: 'success'
                                });
                                location.reload(true);
                            
                        },//check success 
                        error: function(xhr, status, error) {
                            $('.milestone_product_error').html('');
                            $('#btn_opportunity_submit').val('Update');
                            console.log("Error:", error);
                        }
                    });//check ajax 
                }

            });//click

            jQuery(document).on('change','.select-milestone',function(){
                var product_id = jQuery(this).attr('product_id');
                var val = jQuery(this).val();
                var url = "<?php echo site_url().'/wp-json/productsplugin/v1/milestone-status';?>";
                jQuery.ajax({
                    type: "POST",
                    url: url,
                    data: {id:val},
                    beforeSend: function() {
                        $('.stage'+product_id).html('<option value="">Loading Stages...</option>');
                        //$('.show_message_milestone').html('');
                    },
                    success: function(response) {
                        jQuery('.stage'+product_id).html('');       
                        
                       if(response.length!=0){ 
                        jQuery.each( response, function (indexes, values){
                            var stageIssue = '';
                            if(values.deleted_at!=null){
                                stageIssue = 'Deleted';
                            }
                            if(values.status=='inactive'){
                                stageIssue = 'Inactive';
                            }
                            stageName = '';
                            stageID = '';
                            if(stageIssue){
                                stageName = values.stage_name+'('+stageIssue+')';
                                stageID = ''
                            }else{
                                stageName = values.stage_name;
                                stageID = values.milestone_stage_id;
                            }
                            var optionHTML = `<option value="${stageID}"> ${stageName} </option>`;
                            jQuery('.stage'+product_id).append(optionHTML);       
                        });
                       }else{

                            jQuery('.stage'+product_id).html(`<option value="">Select Stage</option>`);
                       }    
                    }
                });            
            });


            function create_update_milestone(products,mile_name,mile_status,milestone_id,mile_map){
                if(milestone_id.length==0 || milestone_id==0){
                    var title = "Milestone Created Successfully.";
                }else{
                    var title = "Milestone Updated Successfully.";
                }
                jQuery.ajax({
                    url:'<?php echo admin_url('admin-ajax.php'); ?>',
                    method:'post',
                    data:{action: 'add_milestone_action',product_id:products,milestone_name:mile_name,status:mile_status,milestone_id:milestone_id,map:mile_map},
                    success(response){
                        // Hide Add/Edit Product Popup
                        $(".mile-popup-overlay, .mile-popup-content").removeClass("active");
                        $('#overlay').hide();
                        swal({
                             title: title,
                            //  text: text,
                             icon: 'success',
                             type: 'success'
                        });
                        location.reload(true);
                    }
                 });// ajax
            }

            jQuery(document).on("click", ".milestone-id-col",function() {
                $('.mile-err, .mile-error, .add-mile-msgs').html("").hide();
                $('#popup-product_id').find('option').prop('selected', false).end().trigger('chosen:updated');
                jQuery('#mile-header-h4').html('Edit milestone');
                jQuery("#btn_mile_submit").val("Update");
                jQuery("#btn_mile_submit").addClass("editMilestone");

                var id = jQuery(this).data('id');
                var mile_name = jQuery(this).data('mile_name');
                var mile_status = jQuery(this).data('status');
                var mile_produts = jQuery(this).data('produts');
                var mile_map = $(this).attr("data-map");
                $('#btn_mile_submit').attr('data-mile_name',$(this).attr('data-mile_name'));
                $('#btn_mile_submit').attr('data-opportunity_count',$(this).attr('data-opportunity_count'));
                $('#btn_mile_submit').attr('data-produts',$(this).attr('data-produts'));
                $('#btn_mile_submit').attr('data-milestoneID',$(this).attr('data-id'));

                if(mile_produts.length!=0){
                    var searchProductIds = [];
                    mile_produts = mile_produts.toString();
                    var products = mile_produts.split(',');
                    $( products ).each(function( index,value ) {
                        // Add the value to the searchProductIds array
                        searchProductIds.push(value);
                    });
                    // Check if searchProductIds is not empty
                    if (searchProductIds.length > 0) {
                        $("#popup-product_id").val(searchProductIds);
                        $("#popup-product_id").trigger('chosen:updated'); // Populating product names in Products Dropdown
                    } 
                }  
                
                jQuery('#popup-product_id').trigger('chosen:updated');
                jQuery('#popup-milestone_name').val(mile_name);
                jQuery('#mile-status').val(mile_status);
                jQuery('#popup-milestone_id').val(id);
                if(mile_map.length!=0){
                    // Select checkboxes based on map value
                    var checkboxesToSelect = mile_map.split(',').map(function(item) {
                        return '#' + item.trim(); // Assuming IDs are used for checkboxes
                    });
                
                    // Check the selected checkboxes
                    $(checkboxesToSelect.join(',')).prop('checked', true);
                    // Uncheck the remaining checkboxes
                    $('.mile-popup-overlay .inp-cbx').not(checkboxesToSelect.join(',')).prop('checked', false);
                } else {
                    $('.mile-popup-overlay .inp-cbx').prop('checked', false); // Uncheck all checkboxes
                }

                jQuery('.status_box').hide(); 
                jQuery(".mile-popup-overlay, .mile-popup-content").addClass("active");
                jQuery("input[name=milestone_name]").focus();
                jQuery('#overlay').show();
            });    


            // Delete Milestone functionality Start Here ------
            jQuery(document).on('click','.delete_milestone',function(){
                    swal({
                        title: "Are you sure?",
                        text: "Yes, I want to delete this Milestone. Related Stages will delete and Opportunities will no longer be linked to the selected Milestone.",
                        icon: "warning",
                        buttons: {
                            cancel: "Cancel",
                            confirm: "Delete",
                        },
                        content: {
                            element: "input",
                            attributes: {
                                type: "checkbox",
                                id: "agreeCheckbox"
                            },
                        },
                        dangerMode: true,
                    })
                    .then((willDelete) => {
                        if (willDelete && $('#agreeCheckbox').prop('checked')) {
                            $(this).html('wait..');
                            $(this).attr('disabled',true);
                            $("#loader_box").show();
                            var mile_id = jQuery(this).data('mile_id');
                            jQuery.ajax({
                                url:'<?php echo admin_url('admin-ajax.php'); ?>',
                                method:'post',
                                data:{action: 'delete_milestone',milestone_id:mile_id},
                                success(response){
                                    location.reload(true);
                                }
                            });
                        }
                        $('.swal-modal').removeClass('crm-erp-milestones-delete-swal');
                    }); 
                    $('.swal-modal').addClass('crm-erp-milestones-delete-swal');
            }); 

            // Activate Inactivate Milestone functionality Start Here ------
            jQuery(document).on('click','.activate-btn',function(){
                 var val = jQuery(this).data('val');
                 if(val=='active'){
                    var texts = "Do you want to Deactivate this Milestone.";
                 }else if(val=='inactive'){
                    var texts = "Do you want to Activate this Milestone.";
                 }else{
                    var texts = "Do you want to change Status.";
                 }
                 swal({
                          title: "Are you sure?",
                          text: texts,
                          icon: "warning",
                          buttons: {
                            cancel: false,
                            no: {
                              text: "No",
                              value: "no",
                            },
                            yes: {
                              text: "Yes",
                              value: "yes",
                            },
                        },
                }).then((value) => {
                      switch (value) {
                          case "yes":
                        $(this).html('wait..');
                        $(this).attr('disabled',true);
                        var mile_id = jQuery(this).data('milestone_id');
                        jQuery.ajax({
                            url:'<?php echo admin_url('admin-ajax.php'); ?>',
                            method:'post',
                            data:{action: 'change_milestone_status',milestone_id:mile_id,value:val},
                            success(response){
                                // console.log(response);
                                location.reload(true);
                            }
                        });
                          break;
                          default:
                      }
                });    
            }); 
        </script>
    <?php
    }
}
?>