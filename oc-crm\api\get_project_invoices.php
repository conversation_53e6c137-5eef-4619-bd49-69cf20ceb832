<?php

        //  ini_set('display_errors', 1);
        //  ini_set('display_startup_errors', 1);
        //  error_reporting(E_ALL); 

get_project_invoices_handler($request);
    
function get_project_invoices_handler($request) {
    global $wpdb;

    $params = $request->get_json_params();
    $project_id = isset($params['project_id']) ? intval($params['project_id']) : 0;

    if (!$project_id) {
        return new WP_REST_Response(['error' => 'Project ID is required.'], 400);
    }

    $project_row = $wpdb->get_row($wpdb->prepare("
        SELECT lead_id, product_id 
        FROM {$wpdb->prefix}projects 
        WHERE project_id = %d
    ", $project_id));

    if (!$project_row) {
        error_log("Invalid project ID: $project_id");
        return new WP_REST_Response(['error' => 'Invalid project ID.'], 404);
    }

    $lead_id = $project_row->lead_id;
    $product_id = $project_row->product_id;

    $invoices = get_invoices_by_lead_and_product($lead_id, $product_id);

    if (empty($invoices)) {
        error_log("No invoices found for lead_id: $lead_id and product_id: $product_id");
        error_log("Last query: " . $wpdb->last_query);
        error_log("Last error: " . $wpdb->last_error);
    }

    return new WP_REST_Response(['success' => true, 'data' => $invoices], 200);
}


function get_invoices_by_lead_and_product($lead_id_project, $product_id_project) {



   global $wpdb;

   if ($lead_id_project !== "" && $product_id_project !== "") {
  
       $query = "
       SELECT 
           inv.*, 
           
           CASE 
               WHEN prod.product_name <> '' THEN 
                   GROUP_CONCAT(DISTINCT prod.product_name ORDER BY prod.product_name SEPARATOR ', ') 
               ELSE ''
           END as product_names,
           
           COUNT(latest_payment.payment_id) as payment_count,
           latest_payment.payment_mode as payments_mode,
           latest_payment.payment_cleared_date as payments_cleared_date,
           latest_payment.payment_date as payments_date,
           latest_payment.received_amt as received_amts,
           inv_token.token
       FROM {$wpdb->prefix}invoices inv
       LEFT JOIN {$wpdb->prefix}invoices_products prod
           ON inv.customer_invoice_no = prod.invoice_number
       LEFT JOIN 
           (
               SELECT 
                   p1.*
               FROM 
                   {$wpdb->prefix}invoice_payments p1
               INNER JOIN 
                   (
                       SELECT 
                           invoice_id, 
                           MAX(payment_date) as latest_payment_date
                       FROM 
                           {$wpdb->prefix}invoice_payments
                       GROUP BY 
                           invoice_id
                   ) p2 
                   ON p1.invoice_id = p2.invoice_id 
                   AND p1.payment_date = p2.latest_payment_date
           ) latest_payment
       ON inv.id = latest_payment.invoice_id
       LEFT JOIN {$wpdb->prefix}invoice_token inv_token 
       ON inv.customer_invoice_no = inv_token.invoice_id
       WHERE inv.lead_id = %s 
       AND inv.parent_product = %s 
       AND inv.status != 13
       GROUP BY inv.id 
       ORDER BY inv.id DESC
   ";
   
   $query2 = "
       SELECT 
           inv.*, 
           
           CASE 
               WHEN prod.product_name <> '' THEN 
                   GROUP_CONCAT(DISTINCT prod.product_name ORDER BY prod.product_name SEPARATOR ', ') 
               ELSE ''
           END as product_names,
           
           COUNT(latest_payment.payment_id) as payment_count,
           latest_payment.payment_mode as payments_mode,
           COALESCE(erc_fees.payment_cleared_date, latest_payment.payment_cleared_date) as payments_cleared_date,
           COALESCE(erc_fees.payment_date, latest_payment.payment_date) as payments_date,
           latest_payment.received_amt as received_amts,
           inv_token.token
       FROM {$wpdb->prefix}invoices inv
       LEFT JOIN {$wpdb->prefix}invoices_products prod
           ON inv.customer_invoice_no = prod.invoice_number
       LEFT JOIN 
           (
               SELECT 
                   p1.*
               FROM 
                   {$wpdb->prefix}invoice_payments p1
               INNER JOIN 
                   (
                       SELECT 
                           invoice_id, 
                           MAX(payment_date) as latest_payment_date
                       FROM 
                           {$wpdb->prefix}invoice_payments
                       GROUP BY 
                           invoice_id
                   ) p2 
                   ON p1.invoice_id = p2.invoice_id 
                   AND p1.payment_date = p2.latest_payment_date
           ) latest_payment
       ON inv.id = latest_payment.invoice_id
       LEFT JOIN {$wpdb->prefix}invoice_token inv_token 
       ON inv.customer_invoice_no = inv_token.invoice_id
       LEFT JOIN {$wpdb->prefix}erc_erc_fees erc_fees
       ON inv.lead_id = erc_fees.lead_id
       WHERE inv.lead_id = %s 
       AND inv.parent_product = %s 
       AND inv.status != 13
       GROUP BY inv.id 
       ORDER BY inv.id DESC
   ";


    $lead_id_project;
    $product_id_project;
    $query;
   $invoices = $wpdb->get_results(
       $wpdb->prepare($query, $lead_id_project, $product_id_project),
       ARRAY_A
   );




   return display_invoices_data($invoices);

   }

}

function display_invoices_data($invoices) {
    global $wpdb;

    if (!empty($invoices)) {
        foreach ($invoices as $invoice) {
            $invoice_id = $invoice['id'];
            $invoice_type = $invoice['invoice_type'];
            $retainer_type = $invoice['retainer_type'];
            $parent_product = $invoice['parent_product'];
            $customer_invoice_no = $invoice['customer_invoice_no'];
            $lead_id = $invoice['lead_id'];
            $status = $invoice['status'];
            $payment_result = $wpdb->get_results("SELECT * from {$wpdb->prefix}invoice_payments where invoice_id =".$invoice_id."");
            if(isset($payment_result)){
                $payment_count_new = count($payment_result);
            }
            
            $updatedBy_result = $wpdb->get_row("SELECT createdBy from {$wpdb->prefix}invoice_audit_logs where action='update' AND action_type='invoice' AND action_id =".$invoice_id."");
            if(isset($updatedBy_result) && !empty($updatedBy_result)){
                $updatedBy = $updatedBy_result->createdBy;
            }else{
                $updatedBy = '';
            }
            $updatedBy = '';
            if($status==2 || $status==17){

                $payment_dates = get_payment_dates($invoice_id,$customer_invoice_no,$invoice_type,$retainer_type,$parent_product,$lead_id);

                if(!empty($payment_dates)){
                    $invoice['payment_date'] = $payment_dates['payment_date'];
                    $invoice['payment_cleared_date'] = $payment_dates['payment_cleared_date'];
                    $invoice['payment_mode'] = $payment_dates['payment_mode'];
                }
                
               
            }
      
        }


        
            // ✅ Add status keys for each invoice
            foreach ($invoices as &$invoice) {
                $status = $invoice['status'];
                $invoice_id = $invoice['id'];

                $invoice_status = '';
                $invoice_status_class = '';

                switch ($status) {
                    case 1:
                        $invoice_status = 'Unpaid';
                        $invoice_status_class = 'unpaid badge bg-danger';
                        break;
                    case 2:
                        $invoice_status = 'Paid';
                        $invoice_status_class = 'paid_invoice badge bg-success';
                        break;
                    case 3:
                        $invoice_status = 'Cancelled';
                        $invoice_status_class = 'cancel badge bg-cancel';
                        break;
                    case 4:
                        $invoice_status = 'Draft';
                        $invoice_status_class = 'draft';
                        break;
                    case 5:
                        $invoice_status = 'Remind';
                        $invoice_status_class = 'remind';
                        break;
                    case 6:
                        $invoice_status = 'Payment in process';
                        $invoice_status_class = 'payment_in_process badge bg-warning';
                        break;
                    case 13:
                        $invoice_status = 'Delete';
                        $invoice_status_class = 'delete';
                        break;
                    case 17:
                        $invoice_status = 'Partially paid';
                        $invoice_status_class = 'partially_paid badge bg-primary';
                        break;
                    default:
                        $invoice_status = '';
                        $invoice_status_class = '';
                        break;
                }

                $invoice['invoice_status'] = $invoice_status;
                $invoice['invoice_status_class'] = $invoice_status_class;

                if (!empty($invoice['created_user'])) {
                    $created_id = $invoice['created_user'];
                    $created_user = get_user_by('id', $created_id);
                    $invoice['created_user'] = $created_user->display_name;
                }
                
                $payment_methods = array(
                    "occams_initiated_eCheck" => "Occams Initiated - eCheck",
                    "occams_initiated_ach" => "Occams Initiated - ACH",
                    "occams_initiated_wire" => "Client Initiated - Wire",
                    "client_initiated_ach" => "Client Initiated - ACH",
                    "client_initiated_check_mailed" => "Client Initiated - Check Mailed",
                    "credit_card_or_debit_card" => "Credit Card or Debit Card",
                    "credit_card" => "Credit card",
                    "ach" => "Bank transfer",
                    "qb-link" => "QB Payment Link",
                    "ipn" => "PayPal",
                    "other-payment" => "Other Payment",
                );

                $payment_mode = "";
                $payment_date = "";
                $payment_cleared_date = "";


                if (strtotime($invoice['invoice_date']) < strtotime('08/14/2024')) {
                    $payment_mode = $invoice['payment_mode'];
                    $payment_date = $invoice['payment_date'];
                    $payment_cleared_date = $invoice['payment_cleared_date'];
                } else {
                    if ($status == 17 || $invoice['payment_count'] > 1) {
                        $payment_mode = $invoice['payments_mode'];
                        $payment_date = $invoice['payments_date'];
                        $payment_cleared_date = $invoice['payments_cleared_date'];
                    } else {
                        $payment_mode = isset($invoice['payment_mode']) && !empty($invoice['payment_mode']) ? $invoice['payment_mode'] : $invoice['payments_mode'];
                        $payment_date = isset($invoice['payment_date']) && !empty($invoice['payment_date']) ? $invoice['payment_date'] : $invoice['payments_date'];
                        $payment_cleared_date = isset($invoice['payment_cleared_date']) && !empty($invoice['payment_cleared_date']) ? $invoice['payment_cleared_date'] : $invoice['payments_cleared_date'];
                    }
                }

                if ($payment_date != '') {
                    $payment_date = date('m/d/Y', strtotime($payment_date));
                }
                if (!empty($payment_cleared_date)) {
                    $payment_cleared_date = date('m/d/Y', strtotime($payment_cleared_date));
                }

                $payment_method_keys = !empty($payment_mode) ? explode(',', $payment_mode) : [];
                $selected_values = array();

                foreach ($payment_method_keys as $key) {
                    $key = trim($key);
                    if (array_key_exists($key, $payment_methods)) {
                        $selected_values[] = $payment_methods[$key];
                    }
                }

                $payment_mode_string = !empty($selected_values) ? implode(', ', $selected_values) : 'N/A';

                $invoice['payment_mode_string'] = $payment_mode_string;
                $invoice['formatted_payment_date'] = $payment_date;
                $invoice['formatted_payment_cleared_date'] = $payment_cleared_date;

                $invoice['payment_history'] = get_payment_history($invoice_id);
            }
            unset($invoice); // break reference


    }
    
        //    return $invoices;
        // echo json_encode($invoices);

            // return json_encoded array with status and data
            echo json_encode(['status' => 200, 'data' => $invoices]);

            die();
   
}

function get_payment_history($invoice_id) {
    global $wpdb;

    // Fetch payment history from the database
    $payment_history = $wpdb->get_results(
        $wpdb->prepare("SELECT * FROM {$wpdb->prefix}invoice_payments WHERE invoice_id = %d ORDER BY payment_date ASC", $invoice_id)
    );

    // Define payment methods
    $payment_methods = array(
        "occams_initiated_eCheck" => "Occams Initiated - eCheck",
        "occams_initiated_ach" => "Occams Initiated - ACH",
        "occams_initiated_wire" => "Client Initiated - Wire",
        "client_initiated_ach" => "Client Initiated - ACH",
        "client_initiated_check_mailed" => "Client Initiated - Check Mailed",
        "credit_card_or_debit_card" => "Credit Card or Debit Card",
        "credit_card" => "Credit card",
        "ach" => "Bank transfer",
        "qb-link" => "QB Payment Link",
        "ipn" => "PayPal",
        "other-payment" => "Other Payment"
    );

    // Initialize variables
    $formatted_payment_history = array();
    $total_received = 0;

    // Process each payment
    if (!empty($payment_history)) {
        foreach ($payment_history as $payment) {
            // Format the payment data
            $payment_id = ($payment->reference_id == 0) ? '' : $payment->reference_id;
            $payment_date = date('m/d/Y', strtotime($payment->payment_date));
            $payment_cleared_date = date('m/d/Y', strtotime($payment->payment_cleared_date));
            $payment_mode = sanitize_text_field($payment->payment_mode);
            $payment_note = sanitize_text_field($payment->payment_note);
            $received_amt = (float) $payment->received_amt;

            // Increment the total received amount
            $total_received += $received_amt;

            // Convert payment mode to readable string
            $payment_mode_name = isset($payment_methods[$payment_mode]) ? $payment_methods[$payment_mode] : 'N/A';

            // Add the payment data to the formatted history
            $formatted_payment_history[] = array(
                'payment_id' => $payment_id,
                'payment_date' => $payment_date,
                'payment_cleared_date' => $payment_cleared_date,
                'payment_mode' => $payment_mode_name,
                'payment_note' => $payment_note,
                'received_amt' => number_format($received_amt, 2)
            );
        }
    }

    // Return the formatted payment history and total received amount
    return array(
        'formatted_payment_history' => $formatted_payment_history,
        'total_received' => number_format($total_received, 2),
        'overdue_amount' => '0.00', // You can calculate overdue amount here if needed
    );
}

 function get_payment_dates($invoice_id, $customer_invoice_no, $invoice_type, $retainer_type, $parent_product, $lead_id) {
    global $wpdb;

    $payment_dates = null;

    // Helper function to get payment dates based on SQL query and mapping
    $get_payment_data = function ($query, $params, $date_fields) use ($wpdb) {

        $result = $wpdb->get_row($wpdb->prepare($query, ...$params));
        if ($result) {
            return [
                'payment_date' => $result->{$date_fields['payment_date']},
                'payment_cleared_date' => $result->{$date_fields['payment_cleared_date']},
                'payment_mode' => isset($result->{$date_fields['payment_mode']}) ? $result->{$date_fields['payment_mode']} : null
            ];
        }
        return null;
    };

    if ($invoice_type === 'custom_invoice') {
        $payment_dates = $get_payment_data(
            "SELECT payment_date, payment_cleared_date,payment_mode  FROM {$wpdb->prefix}invoice_payments WHERE invoice_id = %d",
            [$invoice_id],
            ['payment_date' => 'payment_date', 'payment_cleared_date' => 'payment_cleared_date' , 'payment_mode' => 'payment_mode']
        );

        if (empty($payment_dates) && $parent_product == 935) {
            $products = $wpdb->get_results("SELECT product_id FROM {$wpdb->prefix}invoices_products WHERE invoice_number = $customer_invoice_no");

            $retainer = false;
            $is_initial_retainer = false;
            $is_balance_retainer = false;

            if (!empty($products)) {
                foreach ($products as $product) {
                    if ($product->product_id == 26) {
                        $retainer = true;
                        $is_initial_retainer = true;
                    } elseif ($product->product_id == 21) {
                        $retainer = true;
                        $is_balance_retainer = true;
                    }
                }
            }
            if($retainer){
                // echo 'test3';
                if ($is_initial_retainer) {
                    // echo 'test4';
                    $payment_dates = $get_payment_data(
                        "SELECT retainer_payment_date, retainer_payment_cleared , retainer_payment_type FROM {$wpdb->prefix}erc_erc_intake WHERE lead_id = %d AND retainer_invoice_no = %d",
                        [$lead_id, $customer_invoice_no],
                        ['payment_date' => 'retainer_payment_date', 'payment_cleared_date' => 'retainer_payment_cleared' , 'payment_mode' => 'retainer_payment_type']
                    );
                }

                if ($is_balance_retainer) {
                    // echo 'test5';
                    $payment_dates = $get_payment_data(
                        "SELECT bal_retainer_pay_date, bal_retainer_clear_date, retainer_payment_type FROM {$wpdb->prefix}erc_erc_intake WHERE lead_id = %d AND bal_retainer_invoice_no = %d",
                        [$lead_id, $customer_invoice_no],
                        ['payment_date' => 'bal_retainer_pay_date', 'payment_cleared_date' => 'bal_retainer_clear_date' , 'payment_mode' => 'retainer_payment_type']
                    );
                }
            }else{
                // echo 'test6 date for succ1';
                $payment_dates = get_success_fee_dates($customer_invoice_no, $lead_id);
                // print_r($payment_dates);
            }
        }
    } elseif ($invoice_type === 'success_fee') {
        // echo 'test7 date for success fee';
        $payment_dates = get_success_fee_dates($customer_invoice_no, $lead_id);
   
        // echo 'test8';
    } elseif ($retainer_type) {
        // echo 'test9';
        if ($retainer_type === 'IR') {
            // echo 'test10';
            $payment_dates = $get_payment_data(
                "SELECT retainer_payment_date, retainer_payment_cleared,retainer_payment_type FROM {$wpdb->prefix}erc_erc_intake WHERE lead_id = %d AND retainer_invoice_no = %d",
                [$lead_id, $customer_invoice_no],
                ['payment_date' => 'retainer_payment_date', 'payment_cleared_date' => 'retainer_payment_cleared', 'payment_mode' => 'retainer_payment_type']
            );
        } elseif ($retainer_type === 'BR') {
            // echo 'test11';
            $payment_dates = $get_payment_data(
                "SELECT bal_retainer_pay_date, bal_retainer_clear_date,retainer_payment_type FROM {$wpdb->prefix}erc_erc_intake WHERE lead_id = %d AND bal_retainer_invoice_no = %d",
                [$lead_id, $customer_invoice_no],
                ['payment_date' => 'bal_retainer_pay_date', 'payment_cleared_date' => 'bal_retainer_clear_date', 'payment_mode' => 'retainer_payment_type']
            );
        }

    }elseif($invoice_type === 'retainer' && empty($retainer_type)){
        $query = "SELECT log_date_time FROM {$wpdb->prefix}invoice_status_log WHERE invoice_id = %d AND status = %s ORDER BY log_date_time DESC LIMIT 1";
        $log_date_time = $wpdb->get_var( $wpdb->prepare( $query, $invoice_id, 'Paid' ) );

           $payment_dates = [
               'payment_date' => $log_date_time,
               'payment_cleared_date' => date('Y-m-d', strtotime($log_date_time . ' +1 day')),
               'payment_mode' => 'N/A'
           ];
    }

    return $payment_dates;
}

function get_success_fee_dates($customer_invoice_no,$lead_id) {
    global $wpdb;

     
     $query = $wpdb->prepare("
        SELECT 
            CASE 
                WHEN i_invoice_no = %d THEN i_invoice_payment_date
                WHEN ii_invoice_no = %d THEN ii_invoice_payment_date
                WHEN iii_invoice_no = %d THEN iii_invoice_payment_date
                WHEN iv_invoice_no = %d THEN iv_invoice_payment_date
            END AS payment_date,
            
            CASE 
                WHEN i_invoice_no = %d THEN i_invoice_pay_cleared
                WHEN ii_invoice_no = %d THEN ii_invoice_pay_cleared
                WHEN iii_invoice_no = %d THEN iii_invoice_pay_cleared
                WHEN iv_invoice_no = %d THEN iv_invoice_pay_cleared
            END AS payment_cleared_date,

            CASE
                WHEN i_invoice_no = %d THEN i_invoice_payment_type
                WHEN ii_invoice_no = %d THEN ii_invoice_payment_type
                WHEN iii_invoice_no = %d THEN iii_invoice_payment_type
                WHEN iv_invoice_no = %d THEN iv_invoice_payment_type
            END AS payment_mode

        FROM 
            {$wpdb->prefix}erc_erc_fees
        WHERE 
            lead_id = %d 
            AND (i_invoice_no = %d OR ii_invoice_no = %d OR iii_invoice_no = %d OR iv_invoice_no = %d);
    ", $customer_invoice_no, $customer_invoice_no, $customer_invoice_no, $customer_invoice_no, $customer_invoice_no, $customer_invoice_no, $customer_invoice_no, $customer_invoice_no, $customer_invoice_no, $customer_invoice_no, $customer_invoice_no, $customer_invoice_no, $lead_id, $customer_invoice_no, $customer_invoice_no, $customer_invoice_no, $customer_invoice_no);

    // Execute the query
    $result = $wpdb->get_row($query, ARRAY_A);
    
    return $result;		

}
