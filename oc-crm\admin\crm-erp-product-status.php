<?php
/**

 * Create a new table class that will extend the WP_List_Table

 */
class CRM_ERP_Product_Status extends WP_List_Table {
    private $userRole;
    private $limitPerpage;
    function __construct() {
        global $status, $page;
        $this->limitPerpage = 15;
        parent::__construct(array(
            'singular' => 'bulk-delete', //singular name of the listed records
            'plural' => 'bulk-deletes', //plural name of the listed records
            'ajax' => false
                //does this table support ajax?
        ));
    }

    public function column_default($item, $column_name) {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        switch ($column_name) {
            case 'product_status_id':
            case 'product_name':
            case 'status':
            case 'Action':
                return $item->$column_name;
            default:
                return print_r($item, true);
        }
    }
    function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" name="deleteItem[]" value="'.$item->product_status_id.'"/>',

            $this->_args['singular'], //Let's simply repurpose the table's singular label ("plugin")
            /* $2%s */
            $item->product_status_id //The value of the checkbox should be the record's id
        );
    }
    function column_product_status_id($item){
        //Build row actions
        $actions = array(
            'edit'      => sprintf('<a href="?page=add_edit_product_status&action=edit&id='.$item->product_status_id.'">Edit</a>',$_REQUEST['page'],'edit',$item->product_status_id),
            'delete'    => sprintf('<a href="?page=add_edit_product_status&action=delete&id='.$item->product_status_id.'">Delete</a>',$_REQUEST['page'],'delete',$item->product_status_id),
        );
        
        //Return the title contents
        return sprintf('%1$s %2$s',$item->product_status_id,$this->row_actions($actions));
    }

    function process_bulk_action() {
        if ('delete' === $this->current_action()) {
            $nonce = esc_attr($_REQUEST['_wpnonce']);
            if (isset($_REQUEST['action2']) && $_REQUEST['action2']=='delete'){
                
                if(!empty($_REQUEST['deleteItem'])){
                    $product_status_manager = new CRM_ERP_Product_Status_Manager();
                    foreach($_REQUEST['deleteItem'] as $singleProductStatus){
                        $product_status_manager->delete_product_status($singleProductStatus);
                    }
                    wp_redirect('?page=crm_erp_product_status');
                    exit;
                }
                
            }
        } 
    }
    /**
     * Prepare the items for the table to process
     *
     * @return Void
     */
    public function prepare_items() { 
        $this->process_bulk_action(); 
    ?> 
        <form method="get" action="" id="search_all_form">
    <?php
        $this->search_box_new($_GET); 
    ?>
        <div class="wrap woocommerce">
            <div class="white_card card_height_100 mb_30">
                <div class="white_card_header">
                    <div class="box_header m-0 new_report_header">
                        <div class="title_img">
                            <img src="<?php echo get_site_url();?>/wp-content/plugins/affiliate_portal/page-templates/img/menu-icon/Knowledge_Ceter_White.svg" class="page-title-img" alt="">
                            <h4 style="color: #fff;">Product Status</h4>
                        </div>
                        <div class="invoice_exports">
                            <a href="admin.php?page=add_edit_product_status&action=add" class="status_btn_export">New Product Status</a>
                            <a href="javascript:void(0);" class="status_btn_export export_invoice_excel export_product_status">Export</a>
                        </div>
                    </div>
                </div>
                <div class="white_card_body" id="echeck_report_table_wrap" style="padding:7px;">
            <?php
            $columns = $this->get_columns();
            $hidden = $this->get_hidden_columns();
            $sortable = $this->get_sortable_columns();
            $this->_column_headers = array(
                $columns,
                $hidden,
                $sortable
            );
            //$this->process_bulk_action();
            $search = $_GET;
            $data = $this->table_data($search);
            $perPage = $this->limitPerpage;
            $currentPage = $this->get_pagenum();
            $totalItems = count($data);
            $this->set_pagination_args(array(
                'total_items' => $totalItems,
                'per_page' => $perPage
            ));
            $data = array_slice($data, (($currentPage - 1) * $perPage), $perPage);
            $this->items = $data;
            $this->display();
            
            ?>
                    </div>
                </div>
            </div>
        </form>
            <script type="text/javascript">
                $(document).ready(function() {
                    // Handler for the change event of #agreeCheckbox
                    $(document).on('change', '#agreeCheckbox', function() {
                        // Check if #agreeCheckbox is checked
                        if ($(this).prop('checked')) {
                            $(".swal-button--confirm").css("pointer-events", "auto");
                        } else {
                            $(".swal-button--confirm").css("pointer-events", "none");
                        }
                    });

                    $(document).on('click', '.swal-button-container:has(.swal-button--confirm)', function() {
                        if ($('#agreeCheckbox').prop('checked')) {
                            return true;
                        } else {
                            // Check if error message already exists
                            if (!$('.swal-content + p.error-message').length) {
                                // Append the error message if it doesn't exist
                                $('.swal-content').after('<p class="error-message" style="color: red; margin: 20px;">Please agree to delete the product status!</p>');
                            }
                        }
                    });
                });

                jQuery(document).on('click','.delete_product_status',function(){
                    swal({
                        title: "Are you sure?",
                        text: "Yes, I want to delete this product status.",
                        icon: "warning",
                        buttons: {
                            cancel: "Cancel",
                            confirm: "Delete",
                        },
                        content: {
                            element: "input",
                            attributes: {
                                type: "checkbox",
                                id: "agreeCheckbox"
                            },
                        },
                        dangerMode: true,
                    })
                    .then((willDelete) => {
                        if (willDelete && $('#agreeCheckbox').prop('checked')) {
                            $(this).html('wait..');
                            $(this).attr('disabled',true);
                            var product_status_id = jQuery(this).data('product_status_id');
                            jQuery.ajax({
                                url:'<?php echo admin_url('admin-ajax.php'); ?>',
                                method:'post',
                                data:{action: 'delete_product_status',product_status_id:product_status_id},
                                success(response){
                                    location.reload(true);
                                }
                            });
                        }
                    });  
                }); 

                jQuery(document).on("click", ".export_product_status",function(){
                    jQuery(this).text('Please wait..');
                    jQuery(this).css('pointer-events','none');
                    var product_id = jQuery("#product_id").val();
                    var status = jQuery("#status").val();
                    jQuery.ajax({
                        url:'<?php echo admin_url('admin-ajax.php'); ?>',
                        method:'post',
                        data:{action: 'export_product_status',product_id: product_id,status: status},
                        success(response){
                            jQuery(".export_product_status").text('Export');
                            jQuery(".export_product_status").css('pointer-events','');
                            var downloadLink = document.createElement("a");
                            var responseData = jQuery.trim(response);
                            var fileData = ['\ufeff'+responseData];
                            var blobObject = new Blob(fileData,{
                            type: "text/csv;charset=utf-8;"
                            });
                            var url = URL.createObjectURL(blobObject);
                            downloadLink.href = url;
                            var currentDate = new Date();
                            var dateString = currentDate.toISOString().slice(0, 10); // YYYY-MM-DD
                            var timeString = currentDate
                                .toLocaleTimeString("en-US", {
                                    hour12: false,
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    second: "2-digit",
                                })
                                .replace(/:/g, "-");
                            var filename = "Product_Status_" + dateString + "_" + timeString + ".csv";
                            downloadLink.download = filename;
                            document.body.appendChild(downloadLink);
                            downloadLink.click();
                            document.body.removeChild(downloadLink);
                        }
                    })
                })  
            </script>
            <style type="text/css">
                .delete_product_status {
                  background: red;
                  border: 0;
                  padding: 7px 12px;
                  color: #fff;
                  border-radius: 10px;
                }
                .swal-content {
                    text-align: left;
                    margin-top: -24px;
/*                    margin-left: 60px;*/
                }
                .swal-text {
                    margin-left: 38px;
                }
                .swal-footer {
                    margin-top: 45px;
                }
                .swal-button--confirm {
                    pointer-events: none;
                }
                .swal-button-container:has(.swal-button--confirm) {
                    cursor: pointer;
                }
            </style>
    <?php
    }

    public function get_columns() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        $columns = array(
            'cb' => '<input type="checkbox" />',
            'product_status_id' => 'Product Status ID',
            'product_name' => 'Product Name',
            'status' => 'Status',
            'Action' => 'Action',
        );
        //$columns['action'] = 'Action';
        return $columns;
    }
    public function get_sortable_columns() {
        $sortable = array(
            'product_status_id'     => array('product_status_id',true));
        return $sortable;
    }
    function get_bulk_actions() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
          if(in_array('administrator', $user_roles) || in_array('echeck_staff', $user_roles) || in_array('echeck_client', $user_roles)){ 
            return array(
                'delete' => __( 'Delete', ''),
            );
          }else{
            return array();
          }      
    }

    /**
     * Define which columns are hidden
     *
     * @return Array
     */

    public function get_hidden_columns() {
        return array();
    }

    /**

     * Define the sortable columns

     *

     * @return Array

     */
    public function get_display_name($user_id) {
        if (!$user = get_userdata($user_id))
            return false;
        return $user->data->display_name;
    }

    /**

     * Get the table data

     *

     * @return Array

     */

    private function table_data($search) {
        global $wpdb;
        if( (isset($search['product_id']) && $search['product_id'] != '') || (isset($search['status']) && $search['status'] != '')){
            $product_status_manager = new CRM_ERP_Product_Status_Manager();
            $product_status_data = $product_status_manager->search_product_status($search);
        }else{
            $product_status_manager = new CRM_ERP_Product_Status_Manager();
            $product_status_data = $product_status_manager->get_product_status_list();
        }
        if(!empty($product_status_data)){
            foreach($product_status_data as $key => $product_status_datas){
                $product_status_datas->Action = '<button data-product_status_id ="'.$product_status_datas->product_status_id.'"  class="delete_product_status" type="button">Delete</button>';
            }
        }
        return $product_status_data;
    }
    /**

     * Allows you to sort the data by the variables set in the $_GET

     *

     * @return Mixed

     */
    
    private function sort_data($a, $b) {

        // Set defaults

        $orderby = 'id';

        $order = 'desc';



        // If orderby is set, use this as the sort column

        if (!empty($_GET['orderby'])) {

            $orderby = $_GET['orderby'];

        }



        // If order is set use this as the order

        if (!empty($_GET['order'])) {

            $order = $_GET['order'];

        }



        $result = strnatcmp($a[$orderby], $b[$orderby]);



        if ($order === 'asc') {

            return $result;

        }



        return -$result;

    }

    protected function pagination( $which ) {
        if ( empty( $this->_pagination_args ) ) {
            return;
        }
        $total_items     = $this->_pagination_args['total_items'];
        $total_pages     = $this->_pagination_args['total_pages'];
        $infinite_scroll = false;
        if ( isset( $this->_pagination_args['infinite_scroll'] ) ) {
            $infinite_scroll = $this->_pagination_args['infinite_scroll'];
        }
        if ( 'top' === $which && $total_pages > 1 ) {
            $this->screen->render_screen_reader_content( 'heading_pagination' );
        }
        $output = '<span class="displaying-num">' . sprintf(
            /* translators: %s: Number of items. */
            _n( '%s item', '%s items', $total_items ),
            number_format_i18n( $total_items )
        ) . '</span>';
        $current              = $this->get_pagenum();
        $removable_query_args = wp_removable_query_args();
        $current_url = set_url_scheme( 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] );
        $current_url = remove_query_arg( $removable_query_args, $current_url );
        $page_links = array();
        $total_pages_before = '<span class="paging-input">';
        $total_pages_after  = '</span></span>';
        $disable_first = false;
        $disable_last  = false;
        $disable_prev  = false;
        $disable_next  = false;
        if ( 1 == $current ) {
            $disable_first = true;
            $disable_prev  = true;
        }
        if ( 2 == $current ) {
            $disable_first = true;
        }
        if ( $total_pages == $current ) {
            $disable_last = true;
            $disable_next = true;
        }
        if ( $total_pages - 1 == $current ) {
            $disable_last = true;
        }
        if ( $disable_first ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&laquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='first-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( remove_query_arg( 'paged', $current_url ) ),
                __( 'First page' ),
                '&laquo;'
            );
        }
        if ( $disable_prev ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&lsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='prev-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( 'paged', max( 1, $current - 1 ), $current_url ) ),
                __( 'Previous page' ),
                '&lsaquo;'
            );
        }
        if ( 'bottom' === $which ) {
            $html_current_page  = $current;
            $total_pages_before = '<span class="screen-reader-text">' . __( 'Current Page' ) . '</span><span id="table-paging" class="paging-input"><span class="tablenav-paging-text">';
        } else {
            $html_current_page = sprintf(
                "%s<input class='current-page' id='current-page-selector' type='text' name='paged' value='%s' size='%d' aria-describedby='table-paging' /><span class='tablenav-paging-text'>",
                '<label for="current-page-selector" class="screen-reader-text">' . __( 'Current Page' ) . '</label>',
                $current,
                strlen( $total_pages )
            );
        }
        $html_total_pages = sprintf( "<span class='total-pages'>%s</span>", number_format_i18n( $total_pages ) );
        $page_links[]     = $total_pages_before . sprintf(
            _x( '%1$s of %2$s', 'paging' ),
            $html_current_page,
            $html_total_pages
        ) . $total_pages_after;
        if ( $disable_next ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&rsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='next-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=> min( $total_pages, $current + 1 )), $current_url ) ),
                __( 'Next page' ),
                '&rsaquo;'
            );
        }
        if ( $disable_last ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&raquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='last-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=>$total_pages), $current_url ) ),
                __( 'Last page' ),
                '&raquo;'
            );
        }
        $pagination_links_class = 'pagination-links';
        if ( ! empty( $infinite_scroll ) ) {
            $pagination_links_class .= ' hide-if-js';
        }
        $output .= "\n<span class='$pagination_links_class'>" . implode( "\n", $page_links ) . '</span>';
        if ( $total_pages ) {
            $page_class = $total_pages < 2 ? ' one-page' : '';
        } else {
            $page_class = ' no-pages';
        }
        $this->_pagination = "<div class='tablenav-pages{$page_class}'>$output</div>";
        echo $this->_pagination;
    }
    public function search_box_new($search_data)
    {
        global $wpdb;
        $products = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}crm_products WHERE {$wpdb->prefix}crm_products.DeletedAt IS NULL AND {$wpdb->prefix}crm_products.status = 'active' ORDER BY {$wpdb->prefix}crm_products.ProductID DESC");
    ?>
        <style type="text/css">
        .search_field img {
            width: 22px !important;
            padding: 0px !important;
            position: absolute !important;
            top: 20px;
            left: 40px;
        }

        #show_ac_search {
            color: #86898E;
            font-size: 14px !important;
            height: 35px !important;
            border: 0 !important;
            background: #FFFFFF;
            border-radius: 6px !important;
            border: 0 !important;
            font-weight: 400 !important;
            padding-left: 3%;
        }

        .popup-overlay.active {
            /*displays pop-up when "active" class is present*/
            visibility: visible;
            text-align: center;
            z-index: 1000;
        }

        .popup-content {
            /*Hides pop-up content when there is no "active" class */
            visibility: hidden;
        }

        .popup-content.active {
            /*Shows pop-up content when "active" class is present */
            visibility: visible;
        }

        .open {
            border: none !important;
            padding-left: 4% !important;
        }

        .close {
            font-size: 20px;
            padding: 0px 10px !important;
            border-radius: 10px !important;
            font-weight: 700;
            height: 32px;
            opacity: 1;
            line-height: 30px;
        }

        .popup-overlay button {
            display: inline-block;
            vertical-align: middle;
            border-radius: 30px;
            margin: .20rem;
            font-size: 1rem;
            color: #666666;
            background: #ffffff;
            border: 1px solid #666666;
        }


        .search_field {
            position: relative;
        }

        .search_field img {
            width: 22px !important;
            padding: 0px !important;
            position: absolute !important;
            top: 9px;
            right: 20px !important;
            left: inherit !important;
        }

        .invoice_date_range {
            text-align: right;
        }

        .invoice_date_filter {
            float: initial !important;
        }

        .open {
            padding: 4px !important;
            width: 100%;
        }

        .invoice_date_filter span {
            color: #000 !important;
            font-weight: 500;
            margin-left: 10px;
        }

        .popup-overlay {
            visibility: hidden;
            position: absolute;
            background: #ffffff;
            border: 0;
            width: 50%;
            height: auto;
            left: 19px;
            top: 15px;
            padding: 10px;
            box-shadow: 5px 5px 5px #00000040 !important;
        }
        #overlay {
            position: fixed;
            display: none;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0,0,0,0.5);
            z-index: 999;
            cursor: pointer;
       }

        #assigned_sales_agent, #assigned_sales_support{
           width:100%;
        }
        .select2-container--default .select2-results>.select2-results__options {
            overflow-x: hidden;
        }
        .select2-container {
            width: inherit !important;
        }
        .select2-container--default .select2-selection--single .select2-selection__rendered {
            text-align: left;
        }

        .select2-container .select2-selection--single {
            height:35px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 35px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 34px !important;
        }
                 
        .phone_call_sms{
            position: relative;
        }
        .msg_icon 
        {
            font-size: 13px;
            color: #ff5c00 !important;
            text-align: center;
            margin-left: 5px;
        }
        .email_icon{
            font-size: 14px;
            color: #ff5c00 !important;
            text-align: center;
            margin-left: 7px;
        }           
        .phone_icon {
            font-size: 13px;
            color: #ff5c00 !important;
            text-align: center;
            margin-left: 5px;
        } 
        select#sms_template, select#reason_for_sms {
            max-width: 100% !important;
        }
        button.btn.btn-primary {
            color: #fff;
            background-color: #1758A8;
            border-color: #e9e5f0;
        }
        #send_an_SMS_Modal label{
            font-weight: 600!important;
        }
        .astrik_class {
            color: red;
        }
        .send_decl_confirm_box_wraper{
            display: block;
            position: fixed;
            top: 20%;
            width: 30%;
            height: auto;
            left: 40%;
            right: auto;
            background: gray;
            border-radius: 10px;
            z-index: 9999;
        }
        .sent_decl_confirm_box_wraper{
            display: block;
            position: fixed;
            top: 20%;
            width: 30%;
            height: auto;
            left: 40%;
            right: auto;
            background: gray;
            border-radius: 10px;
            z-index: 9999;
        }
        .close_send_decl_popup {
            background: red;
            color: #fff;
            font-size: 14px;
            width: 25px;
            height: 25px;
            display: block;
            text-align: center;
            position: absolute;
            right: -10px;
            top: -12px;
            border-radius: 54px;
            line-height: 21px;
            cursor: pointer;
            z-index:9999;
        }
        .close_sent_decl_popup {
            background: red;
            color: #fff;
            font-size: 14px;
            width: 25px;
            height: 25px;
            display: block;
            text-align: center;
            position: absolute;
            right: -10px;
            top: -12px;
            border-radius: 54px;
            line-height: 21px;
            cursor: pointer;
            z-index:9999;
        }
        .decl_btn_yes,.decl_btn_no{float: left;padding: 20px;}
        input.btn.btn-primary {
            color: #fff !important;
            background-color: #1758A8 !important;
            border-color: #e9e5f0 !important;
        }
        .search_header {
          display: flex;
          justify-content: space-between;
          border-bottom: 1px solid #ccc;
          padding-bottom: 0px;
          margin-bottom: 20px;
        }
        .reset {
          font-size: 13px;
          border: transparent !important;
          padding: 1px 15px !important;
          border-radius: 10px !important;
          box-shadow: 0px 0px 5px #0000001a !important;
          font-weight: 700;
          height: 32px;
          opacity: 1;
          float: inherit !important;
          display: inline-block;
          line-height: 28px;
          margin-left: 15px;
          background: red !important;
          color: #fff !important;
        }
        </style>
        
        
            <input type="hidden" name="page" value="crm_erp_product_status">
            <div class="row align-items-center mb-3">
                <div class="col-md-3 search_field">
                    <img src="https://design.echeck.money/portal/wp-content/plugins/echeck-theme111/assets/img/icon/Search.svg" alt="" class="icon">
                    <input type="text" placeholder="Search.." id="show_lead_search" autocomplete="off" class="open">
                    <input type="submit" style="visibility: hidden;position:absolute;">
                </div>
            </div>
            <div class="popup-overlay">
                <div class="popup-content">
                    <div class="search_header">
                        <h4>Search</h4>
                        <span class="close">
                            <i class="fa-solid fa-xmark"></i>
                        </span>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <select id="product_id" name="product_id" style="width: 100% !important;">
                                <option value="">Select Product</option>
                                <?php foreach($products as $product): 
                                        if($product->ProductID == $search_data['product_id']){
                                            $sel = "selected";
                                        }else{
                                            $sel = '';
                                        }
                                ?>
                                    <option value="<?php echo $product->ProductID; ?>" <?php echo $sel;?>><?php echo $product->Title; ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <input type="text" id="status" name="status" style="width: 100% !important;" placeholder="Product Status" value="<?php if (isset($search_data['status'])) {echo $search_data['status'];} ?>" class="search_status">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12 text-center">
                        <input type="submit" id="btn_search_submit" class="btn_submit" value="Search" />
                        <input type="button" class="reset" value="Clear" id="reset_Form_button" onclick="resetAffForm()"/>
                        </div>
                    </div>  
                </div>
            </div>
        <script type="text/javascript">
            jQuery(".open").on("click", function() {
              jQuery('.status_box').hide(); 
              jQuery(".popup-overlay, .popup-content").addClass("active");
              jQuery(".search_lead_id").focus();
              jQuery('#overlay').show();
            });

            jQuery(".close").on("click", function() {
              jQuery(".popup-overlay, .popup-content").removeClass("active");
              jQuery('#overlay').hide();
            });

            function overlay_off(){
              jQuery(".close").trigger("click");
              jQuery('#overlay').hide();
            }
            function resetAffForm(){
                jQuery("#reset_Form_button").val('Clearing...');
                var site_url = '<?php  echo get_site_url() ?>';
                window.location.href = site_url+'/wp-admin/admin.php?page=crm_erp_product_status';
            }
            jQuery(document).on("click",'#doaction2', function(e) {
                e.preventDefault();
                if($('#bulk-action-selector-bottom').val()=='delete'){
                    if (confirm("Are you sure you want to delete the record?")) {
                    $('#search_all_form').submit();
                    } else {
                        return;
                    }
                }else{
                    alert('Please select some option.');
                }
            });
        </script>
    <?php
    }
}
?>