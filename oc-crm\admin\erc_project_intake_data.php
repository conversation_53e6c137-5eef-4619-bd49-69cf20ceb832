<?php
global $wpdb;
$lead_id = $_GET['lead_id'];

$ercin_w2_employees_count = "";
$ercin_initial_retain_fee_amount = "";
$ercin_w2_ee_difference_count = "";
$ercin_balance_retainer_fee = "";
$ercin_total_max_erc_amount = "";
$ercin_total_estimated_fees = "";
$ercin_affiliate_referral_fees = "";
$ercin_avg_emp_count_2019 = "";
$ercin_fee_type = "";
$custom_fee = "";
$ercin_company_folder_link = "";
$ercin_document_folder_link = "";
$ercin_eligible_quarters = "";
$ercin_welcome_email = "";
$ercin_retainer_payment_date = "";
$ercin_retainer_payment_cleared = "";
$ercin_retainer_payment_returned = "";
$ercin_ret_payment_return_reason = "";
$ercin_retainer_refund_date = "";
$ercin_retainer_refund_amount = "";
$ercin_retainer_payment_amount = "";
$ercin_retainer_payment_type = "";
$ercin_bal_retainer_pay_date = "";
$ercin_bal_retainer_clear_date = "";
$ercin_bal_retainer_return_date = "";
$ercin_bal_retainer_return_reaso = "";
$ercin_coi_aoi = "";
$ercin_voided_check = "";
$ercin_2019_tax_return = "";
$ercin_2020_tax_return = "";
$ercin_2021_financials = "";
$ercin_2020_q1 = "";
$ercin_2020_q2 = "";
$ercin_2020_q3 = "";
$ercin_2020_q4 = "";
$ercin_2021_q1 = "";
$ercin_payroll_register_2021_q2 = "";
$ercin_2021_q3 = "";
$ercin_payroll_register_2020_q1 = "";
$ercin_payroll_register_2020_q2 = "";
$ercin_payroll_register_2020_q3 = "";
$ercin_payroll_register_2020_q4 = "";
$ercin_payroll_register_2021_q1 = "";
$ercin_2021_q2 = "";
$ercin_payroll_register_2021_q3 = "";
$f911_status = "";
$ercin_ppp_1_applied = "";
$ercin_ppp_1_date = "";
$ercin_ppp_1_forgiveness_applied = "";
$ercin_ppp_1_forgive_app_date = "";
$ercin_ppp_1_amount = "";
$ercin_ppp_1_wages_allocated = "";
$ercin_ppp_2_applied = "";
$ercin_ppp_2_date = "";
$ercin_ppp_2_forgiveness_applied = "";
$ercin_ppp_2_forgive_app_date = "";
$ercin_ppp_2_amount = "";
$ercin_ppp_2_wages_allocated = "";
$ercin_additional_comments = "";
$ercin_fpso_fees = "";
//$intake_table_name = "eccom_erc_erc_intake";
$intake_table_name = $wpdb->prefix."erc_erc_intake";
$in_sql = $wpdb->prepare("SELECT * FROM `$intake_table_name` WHERE lead_id = ".$lead_id."");
$intake_data = $wpdb->get_results($in_sql);


// $alter = $wpdb->query("ALTER TABLE `$intake_table_name` ADD 'f911_status' VARCHAR(255) NOT NULL DEFAULT '' AFTER 2021_q3_4162 ");

// Iterate over each result
foreach ($intake_data as $in_key => $in_value) {
    $in_value = (array) $in_value;
    //print_r($in_value);
    $ercin_w2_employees_count = $in_value["w2_employees_count"];
    $ercin_initial_retain_fee_amount = $in_value["initial_retain_fee_amount"];
    $ercin_w2_ee_difference_count = $in_value["w2_ee_difference_count"];
    $ercin_balance_retainer_fee = $in_value["balance_retainer_fee"];
    $ercin_total_max_erc_amount = $in_value["total_max_erc_amount"];
    $ercin_total_estimated_fees = $in_value["total_estimated_fees"];
    $ercin_affiliate_referral_fees = $in_value["affiliate_referral_fees"];
    $ercin_avg_emp_count_2019 = $in_value["avg_emp_count_2019"];
    $ercin_fee_type = $in_value["fee_type"];
    $custom_fee = $in_value["fee_type"];
    $ercfee_sdgr = $in_value['sdgr'];
    $ercin_company_folder_link = $in_value["company_folder_link"];
    $ercin_document_folder_link = $in_value["document_folder_link"];
    $ercin_eligible_quarters = $in_value["eligible_quarters"];
    $ercin_retainer_invoice_no = $in_value["retainer_invoice_no"];
    $ercin_welcome_email = $in_value["welcome_email"];
    $ercin_retainer_payment_date = $in_value["retainer_payment_date"];
    $ercin_retainer_payment_cleared = $in_value["retainer_payment_cleared"];
    $ercin_retainer_payment_returned = $in_value["retainer_payment_returned"];
    $ercin_ret_payment_return_reason = $in_value["retpayment_return_reason"];
    $ercin_retainer_refund_date = $in_value["retainer_refund_date"];
    $ercin_retainer_refund_amount = $in_value["retainer_refund_amount"];
    $ercin_retainer_payment_amount = $in_value["retainer_payment_amount"];
    $ercin_retainer_payment_type = $in_value["retainer_payment_type"];
    $ercin_bal_retainer_invoice_no = $in_value["bal_retainer_invoice_no"];
    $ercin_bal_retainer_sent_date = $in_value["bal_retainer_sent_date"];
    $ercin_bal_retainer_pay_date = $in_value["bal_retainer_pay_date"];
    $ercin_bal_retainer_clear_date = $in_value["bal_retainer_clear_date"];
    $ercin_bal_retainer_return_date = $in_value["bal_retainer_return_date"];
    $ercin_bal_retainer_return_reaso = $in_value["bal_retainer_return_reaso"];
    $ercin_coi_aoi = $in_value["coi_aoi"];
    $ercin_voided_check = $in_value["voided_check"];
    $ercin_2019_tax_return = $in_value["2019_tax_return"];
    $ercin_2020_tax_return = $in_value["2020_tax_return"];
    $ercin_2021_financials = $in_value["2021_financials"];
    $ercin_2020_q1 = $in_value["2020_q1_4144"];
    $ercin_2020_q2 = $in_value["2020_q2_4145"];
    $ercin_2020_q3 = $in_value["2020_q3_4146"];
    $ercin_2020_q4 = $in_value["2020_q4_4147"];
    $ercin_2021_q1 = $in_value["2021_q1_4149"];
    $ercin_2021_q2 = $in_value["2021_q2_4151"];
    $ercin_2021_q3 = $in_value["2021_q3_4152"];
    $ercin_payroll_register_2020_q1 = $in_value["2020_q1_4155"];
    $ercin_payroll_register_2020_q2 = $in_value["2020_q2_4156"];
    $ercin_payroll_register_2020_q3 = $in_value["2020_q3_4157"];
    $ercin_payroll_register_2020_q4 = $in_value["2020_q4_4158"];
    $ercin_payroll_register_2021_q1 = $in_value["2021_q1_4160"];
    $ercin_payroll_register_2021_q2 = $in_value["2021_q2_4161"];
    $ercin_payroll_register_2021_q3 = $in_value["2021_q3_4162"];
    $f911_status = $in_value["f911_status"];
    $ercin_ppp_1_applied = $in_value["ppp_1_applied"];
    $ercin_ppp_1_date = $in_value["ppp_1_date"];
    $ercin_ppp_1_forgiveness_applied = $in_value["ppp_1_forgiveness_applied"];
    $ercin_ppp_1_forgive_app_date = $in_value["ppp_1_forgive_app_date"];
    $ercin_ppp_1_amount = $in_value["ppp_1_amount"];
    $ercin_ppp_1_wages_allocated = $in_value["ppp_1_wages_allocated"];
    $ercin_ppp_2_applied = $in_value["ppp_2_applied"];
    $ercin_ppp_2_date = $in_value["ppp_2_date"];
    $ercin_ppp_2_forgiveness_applied = $in_value["ppp_2_forgiveness_applied"];
    $ercin_ppp_2_forgive_app_date = $in_value["ppp_2_forgive_app_date"];
    $ercin_ppp_2_amount = $in_value["ppp_2_amount"];
    $ercin_ppp_2_wages_allocated = $in_value["ppp_2_wages_allocated"];
    $ercin_additional_comments = $in_value["additional_comments"];
    $ercin_attorney_name = $in_value["attorney_name"];
    $ercin_call_date = $in_value["call_date"];
    $ercin_call_time = $in_value["call_time"];
    $ercin_memo_received_date = $in_value["memo_received_date"];
    $ercin_memo_cut_off_date = $in_value["memo_cut_off_date"];
    $opportunity_size = $in_value["opportunity_size"];
    $opportunity_timeline = $in_value["opportunity_timeline"];
    $confidance_label = $in_value["confidance_label"];
    $interest_percentage = $in_value["interest_percentage"];
    $net_no = $in_value["net_no"]; 
	$ercin_fpso_fees = $in_value["fpso_fees"];	
}


if(empty($interest_percentage) || $interest_percentage==0.00){
    $interest_percentage = 15;
}

if(empty($net_no)){
    $net_no = 30;
}
?>


<div class="erc-project-view">
    <fieldset>
        <legend>ERC Basic Details</legend>
        <div class="row mb-3">
        <div class="floating col-sm-4 mb-3">
            <label>W2 Employees Count</label>
            <input name='w2_employees_count' value='<?php echo $ercin_w2_employees_count; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
       <div class="floating col-sm-4 mb-3">
            <label>Initial Retain Fee Amount</label>
            <input name='initial_retain_fee_amount' value='<?php echo $ercin_initial_retain_fee_amount; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>W2 EE Difference Count</label>
            <input class="crm-erp-field form-control" type="text" name='w2_ee_difference_count' value='<?php echo $ercin_w2_ee_difference_count; ?>' <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Balance Retainer Fee</label>
            <input class="crm-erp-field form-control" type="text" name='balance_retainer_fee' value='<?php echo $ercin_balance_retainer_fee; ?>' <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Total Max ERC Amount</label>
            <input class="crm-erp-field form-control" type="text" name='total_max_erc_amount' value='<?php echo $ercin_total_max_erc_amount; ?>' <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Total Estimated Fees</label>
            <input class="crm-erp-field form-control" type="text" name='total_estimated_fees' value='<?php echo $ercin_total_estimated_fees; ?>' <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Affiliate Referral Fees</label>
            <input id="email" class="crm-erp-field form-control" type="text" name='affiliate_referral_fees' value='<?php echo $ercin_affiliate_referral_fees; ?>' <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <div class="custom-control custom-checkbox">
                <input type='checkbox' class='custom-control-input' id="customCheck1" name="sdgr" data-name='ercfee_sdgr' value="Yes" 
                <?php echo ($ercfee_sdgr == "Yes") ? "checked" : "test"; ?> >

                <label class="custom-control-label" for="customCheck1">SDGR</label>
            </div>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Average Employee Count in 2019</label>
            <select <?= $disabled ?> class='crm-erp-field form-control' name='avg_emp_count_2019'>
                <option value="0">N/A</option>
                <option value="1" <?php if($ercin_avg_emp_count_2019 == 1){echo 'selected';}?>>Less Than 100</option>
                <option value="2" <?php if($ercin_avg_emp_count_2019 == 2){echo 'selected';}?>>Between 100-500</option>
                <option value="3" <?php if($ercin_avg_emp_count_2019 == 3){echo 'selected';}?>>More Than 500</option>
            </select>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Fee Type</label>
            <select <?= $disabled ?> class='crm-erp-field form-control' name='fee_type'>
                <?php
                $fee_type = [
                    "N/A" => "N/A",
                    "Retainer Fee @$90 Per EE + Success Fee @15%" => "Retainer Fee @$90 Per EE + Success Fee @15%",
                    "Retainer Fee $10k + Upon Completion Fees @12%" => "Retainer Fee $10k + Upon Completion Fees @12%",
                    "Document Fee @$299 + Success Fee @18%" => "Document Fee @$299 + Success Fee @18%",
                    "Retainer Fee @$90 Per EE + Success Fee @12.5%" => "Retainer Fee @$90 Per EE + Success Fee @12.5%",
                    "Retainer Fee @$90 Per EE + Success Fee @12%" => "Retainer Fee @$90 Per EE + Success Fee @12%",
                    "Retainer Fee @$90 Per EE + Success Fee @10%" => "Retainer Fee @$90 Per EE + Success Fee @10%",
                    "Retainer Fee @$90 Per EE + Success Fee @13%" => "Retainer Fee @$90 Per EE + Success Fee @13%",
                    "Retainer Fee @$90 Per EE + Success Fee @20%" => "Retainer Fee @$90 Per EE + Success Fee @20%",
                    "Retainer Fee @$90 Per EE + Success Fee @9.75%" => "Retainer Fee @$90 Per EE + Success Fee @9.75%",
                    "Retainer Fee $10k + Upon Completion Fees @10%" => "Retainer Fee $10k + Upon Completion Fees @10%",
                    "Document Fee @$0 + Success Fee @12.5%" => "Document Fee @$0 + Success Fee @12.5%",
                    "Document Fee @$0 + Success Fee @14.5%" => "Document Fee @$0 + Success Fee @14.5%",
                    "Document Fee @$0 + Success Fee @15%" => "Document Fee @$0 + Success Fee @15%",
                    "Document Fee @$450 OT + Success Fee @12.5%" => "Document Fee @$450 OT + Success Fee @12.5%",
                    "Document Fee @$49 OT + Success Fee @15%" => "Document Fee @$49 OT + Success Fee @15%",
                    "Document Fee @$49 OT + Success Fee @17.5%" => "Document Fee @$49 OT + Success Fee @17.5%",
                    "Document Fee @$99 + Success Fee @18%" => "Document Fee @$99 + Success Fee @18%",
                    "Document Fee @$99 OT + Success Fee @15%" => "Document Fee @$99 OT + Success Fee @15%",
                    "Document Fee @$99 OT + Success Fee @15.75%" => "Document Fee @$99 OT + Success Fee @15.75%",
                    "Document Fee @$99 OT + Success Fee @20%" => "Document Fee @$99 OT + Success Fee @20%",
                    "Document Fee @$990 OT + Success Fee @15.75%" => "Document Fee @$990 OT + Success Fee @15.75%",
                    "Enrollment 5 EE or less $450 Flat Fee" => "Enrollment 5 EE or less $450 Flat Fee",
                    "Enrollment @$90 Per EE + Success Fee @10%" => "Enrollment @$90 Per EE + Success Fee @10%",
                    "Enrollment @$90 Per EE + Success Fee @12.5%" => "Enrollment @$90 Per EE + Success Fee @12.5%",
                    "Retainer @$90 Per EE - Service Fees @15%" => "Retainer @$90 Per EE - Service Fees @15%",
                    "Retainer @$90 Per EE+Upon Completion Fee @10%" => "Retainer @$90 Per EE+Upon Completion Fee @10%",
                    "Retainer @$90 Per EE+Upon Completion Fee @12%" => "Retainer @$90 Per EE+Upon Completion Fee @12%",
                    "Retainer Fee @ $90 Per EE+Success Fee @ 17.5%" => "Retainer Fee @ $90 Per EE+Success Fee @ 17.5%",
                    "Retainer @$0 Per EE + Service Fees @15%" => "Retainer @$0 Per EE + Service Fees @15%",
                    "Retainer Fee @$90 Per EE + Success Fee @6.25%" => "Retainer Fee @$90 Per EE + Success Fee @6.25%",
                    "Retainer Fee $100 + Success Fee @15%" => "Retainer Fee $100 + Success Fee @15%",
                    "Retainer Fee @$99 + Success Fee @10%" => "Retainer Fee @$99 + Success Fee @10%",
                    "Retainer Fee @$0 + Success Fee @18%" => "Retainer Fee @$0 + Success Fee @18%",
                    "Retainer Fee @$0 + Success Fee @10%" => "Retainer Fee @$0 + Success Fee @10%",
                    "Custom Fee - $37500 - $45000" => "Custom Fee - $37500 - $45000",
                    "Retainer Fee @$3000 + Success Fee @15%" => "Retainer Fee @$3000 + Success Fee @15%",
                    "Retainer Fee @$2000 + Success Fee @20%" => "Retainer Fee @$2000 + Success Fee @20%",
                    "Retainer Fee @$30 Per EE + Success Fee @15%" => "Retainer Fee @$30 Per EE + Success Fee @15%",
                    "Retainer Fee @$0 Per EE + Success Fee @12%" => "Retainer Fee @$0 Per EE + Success Fee @12%",
                    "Retainer Fee @$2500 + Success Fee @20%" => "Retainer Fee @$2500 + Success Fee @20%",
                    "Retainer Fee @$3000 + Success Fee @22%" => "Retainer Fee @$3000 + Success Fee @22%",
                    "Retainer Fee @$12500 + Success Fee @15%" => "Retainer Fee @$12500 + Success Fee @15%",
                    "$90 Per EE Min $2000 + Success Fee @20%" => "$90 Per EE Min $2000 + Success Fee @20%",
                    "Retainer Fee @$0 + Success Fee @20%" => "Retainer Fee @$0 + Success Fee @20%",
                    "Document Fee @$299 + Success Fee @20%" => "Document Fee @$299 + Success Fee @20%",
                    "Completion Fee @10%" => "Completion Fee @10%",
                    "Success Fee @10%" => "Success Fee @10%"
                ];

                foreach ($fee_type as $fkey => $fvalue) {
                    if ($ercin_fee_type == $fkey) {
                        $selected = "selected";
                    } else {
                        $selected = "";
                    } ?>
                    <option value='<?= $fkey ?>' <?= $selected ?>><?php echo $fvalue; ?></option>
                <?php
                }
                ?>
            </select>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Custom Fee</label>
            <input name='custom_fee' value='<?php echo $custom_fee; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Company Folder Link</label>
            <input name='company_folder_link' value='<?php echo $ercin_company_folder_link; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Document Folder Link</label>
            <input name='document_folder_link' value='<?php echo $ercin_document_folder_link; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Eligible Quarters</label>
            <input name='eligible_quarters' value='<?php echo $ercin_eligible_quarters; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Welcome Email</label>
            <input name='welcome_email' value='<?php
            if (
                !empty($ercin_welcome_email)
            ) {
                $ercin_welcome_email = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_welcome_email
                    )
                );
            }
            echo $ercin_welcome_email;
            ?>' class="crm-erp-field form-control date_field" id="welcome_email" type="text" placeholder="MM/DD/YYYY" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Invoice# Initial Retainer</label>
            <input name='retainer_invoice_no' value='<?php echo $ercin_retainer_invoice_no; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Retainer Payment Date</label>
            <input name='retainer_payment_date' value='<?php
            if (
                !empty($ercin_retainer_payment_date)
            ) {
                $ercin_retainer_payment_date = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_retainer_payment_date
                    )
                );
            }
            echo $ercin_retainer_payment_date;
            ?>' class="crm-erp-field form-control date_field" id="retainer_payment_date" type="text" placeholder="MM/DD/YYYY" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Retainer Payment Cleared</label>
            <input name='retainer_payment_cleared' value='<?php
            if (
                !empty($ercin_retainer_payment_cleared)
            ) {
                $ercin_retainer_payment_cleared = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_retainer_payment_cleared
                    )
                );
            }
            echo $ercin_retainer_payment_cleared;
            ?>' class="crm-erp-field form-control" id="retainer_payment_cleared" type="text" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Retainer Payment Returned</label>
            <input name='retainer_payment_returned' value='<?php
            if (
                !empty($ercin_retainer_payment_returned)
            ) {
                $ercin_retainer_payment_returned = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_retainer_payment_returned
                    )
                );
            }
            echo $ercin_retainer_payment_returned;
            ?>' class="crm-erp-field form-control date_field" id="retainer_payment_returned" type="text" placeholder="MM/DD/YYYY" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Ret.Payment Return Reason</label>
            <input name='retpayment_return_reason' size='43' maxlength='-1' value='<?php echo $ercin_ret_payment_return_reason; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Retainer Refund Date</label>
            <input name='retainer_refund_date' value='<?php
            if (
                !empty($ercin_retainer_refund_date)
            ) {
                $ercin_retainer_refund_date = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_retainer_refund_date
                    )
                );
            }
            echo $ercin_retainer_refund_date;
            ?>' class="crm-erp-field form-control date_field" id="retainer_refund_date" type="text" placeholder="MM/DD/YYYY" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Retainer Refund Amount</label>
            <input name='retainer_refund_amount' value='<?php echo $ercin_retainer_refund_amount; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Retainer Payment Amount</label>
            <input name='retainer_payment_amount' value='<?php echo $ercin_retainer_payment_amount; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Retainer Payment Type</label>
            <select <?= $disabled ?> class='crm-erp-field form-control' name='retainer_payment_type'>
                <?php
                $retainer_type = [
                    "1" => "ACH",
                    "2" => "CC/DB Card",
                ];
                foreach ($retainer_type as $rkey => $rvalue) {
                    if ( $ercin_retainer_payment_type == $rkey ) {
                        $selected = "selected";
                    } else {
                        $selected = "";
                    } ?>
                    <option value='<?= $rkey ?>' <?= $selected ?>><?php echo $rvalue; ?></option>
                    <?php
                }
                ?>
            </select>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Bal Retainer Invoice#</label>
            <input name='bal_retainer_invoice_no' value='<?php echo $ercin_bal_retainer_invoice_no; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Bal Retainer Sent Date</label>
            <input name='bal_retainer_sent_date' value='<?php
            if (
                !empty($ercin_bal_retainer_sent_date)
            ) {
                $ercin_bal_retainer_sent_date = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_bal_retainer_sent_date
                    )
                );
            }
            echo $ercin_bal_retainer_sent_date;
            ?>' class="crm-erp-field form-control date_field" type="text" placeholder="MM/DD/YYYY" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Bal Retainer Pay Date</label>
            <input name='bal_retainer_pay_date' value='<?php
            if (
                !empty($ercin_bal_retainer_pay_date)
            ) {
                $ercin_bal_retainer_pay_date = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_bal_retainer_pay_date
                    )
                );
            }
            echo $ercin_bal_retainer_pay_date;
            ?>' class="crm-erp-field form-control date_field" type="text" placeholder="MM/DD/YYYY" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Bal Retainer Clear Date</label>
            <input name='bal_retainer_clear_date' value='<?php
            if (
                !empty($ercin_bal_retainer_clear_date)
            ) {
                $ercin_bal_retainer_clear_date = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_bal_retainer_clear_date
                    )
                );
            }
            echo $ercin_bal_retainer_clear_date;
            ?>' class="crm-erp-field form-control date_field" type="text" placeholder="MM/DD/YYYY" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Bal Retainer Return Date</label>
            <input name='bal_retainer_return_date' value='<?php
            if (
                !empty($ercin_bal_retainer_return_date)
            ) {
                $ercin_bal_retainer_return_date = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_bal_retainer_return_date
                    )
                );
            }
            echo $ercin_bal_retainer_return_date;
            ?>' class="crm-erp-field form-control date_field" type="text" placeholder="MM/DD/YYYY" <?= $readonly ?>>
        </div>
        <div class="floating col-sm-4 mb-3">
            <label>Bal Retainer Return Reason</label>
            <input name='bal_retainer_return_reaso' value='<?php echo $ercin_bal_retainer_return_reaso; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
        </div>
        </div>
    </fieldset>
</div>

<div class="erc-project-view">
    <fieldset>
        <legend>Payment Terms</legend>
        <div class="row mb-3">
        <div class="floating col-sm-4 mb-3">
            <label>Net No.</label>
            <input name='net_no' value='<?php echo $net_no; ?>' class="crm-erp-field form-control" type="number" <?= $readonly ?> min="0">
        </div>
       <div class="floating col-sm-4 mb-3">
            <label>Interest Percentage</label>
            <input name='interest_percentage' value='<?php echo $interest_percentage; ?>' class="crm-erp-field form-control" type="number" <?= $readonly ?> step=".01" min="0" max="100">
        </div>
        </div>
    </fieldset>
</div>

<div class="erc-project-view">
    <fieldset>
        <legend>ERC Documents</legend>
        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>Business Docs</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>COI AOI</label>
                <select <?= $disabled ?> class='crm-erp-field form-control' name='coi_aoi'>
                    <option value="3" <?= ($ercin_coi_aoi == "3") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "1" => "YES",
                        "2" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        // Direct comparison with the key
                        $selected = ($ercin_coi_aoi == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Voided Check</label>

                <select <?= $disabled ?> class='crm-erp-field form-control' name='voided_check'>
                    <option value="3" <?= ($ercin_voided_check == "3") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "1" => "YES",
                        "2" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_voided_check == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>Business Financial Docs</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>2019 Tax Return</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2019_tax_return' id="2019 Tax Return">
                    <option value="3" <?= ($ercin_2019_tax_return == "3") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "1" => "YES",
                        "2" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_2019_tax_return == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>2020 Tax Return</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2020_tax_return' id="2020 Tax Return">
                    <option value="3" <?= ($ercin_2020_tax_return == "3") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "1" => "YES",
                        "2" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_2020_tax_return == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>2021 Financials</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2021_financials' id="2021 Financials">
                    <option value="3" <?= ($ercin_2021_financials == "3") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "1" => "YES",
                        "2" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_2021_financials == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>941's - 2020</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>2020 Q1</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2020_q1_941' id="941 - 2020 Q1">
                    <option value="1" <?= ($ercin_2020_q1 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_2020_q1 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>2020 Q2</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2020_q2_941' id="941 - 2020 Q2">
                    <option value="1" <?= ($ercin_2020_q2 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_2020_q2 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>2020 Q3</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2020_q3_941' id="941 - 2020 Q3">
                    <option value="1" <?= ($ercin_2020_q3 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_2020_q3 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>2020 Q4</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2020_q4_941' id="941 - 2020 Q4">
                    <option value="1" <?= ($ercin_2020_q4 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_2020_q4 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
        </div>


        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>941's - 2021</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>2021 Q1</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2021_q1_941' id="941 - 2021 Q1">
                    <option value="1" <?= ($ercin_2021_q1 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_2021_q1 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>2020 Q1</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2021_q2_941' id="941 - 2021 Q2">
                    <option value="1" <?= ($ercin_2021_q2 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_2021_q2 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>2021 Q3</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2021_q3_941' id="941 - 2021 Q3">
                    <option value="1" <?= ($ercin_2021_q3 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_2021_q3 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>Payroll Register - 2020</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Payroll Register 2020 Q1</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2020_q1_payroll' id="">
                    <option value="1" <?= ($ercin_payroll_register_2020_q1 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_payroll_register_2020_q1 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }               
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Payroll Register 2020 Q2</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2020_q2_payroll' id="">
                    <option value="1" <?= ($ercin_payroll_register_2020_q2 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_payroll_register_2020_q2 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Payroll Register 2020 Q3</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2020_q3_payroll' id="">
                    <option value="1" <?= ($ercin_payroll_register_2020_q3 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_payroll_register_2020_q3 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Payroll Register 2020 Q4</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2020_q4_payroll' id="">
                    <option value="1" <?= ($ercin_payroll_register_2020_q4 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_payroll_register_2020_q4 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>Payroll Register - 2021</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Payroll Register 2021 Q1</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2021_q1_payroll' id="">
                    <option value="1" <?= ($ercin_payroll_register_2021_q1 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_payroll_register_2021_q1 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>2021 Q2</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2021_q2_payroll' id="">
                    <option value="1" <?= ($ercin_2021_q2 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_2021_q2 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Payroll Register 2021 Q3</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='2021_q3_payroll' id="">
                    <option value="1" <?= ($ercin_payroll_register_2021_q3 == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_payroll_register_2021_q3 == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>F911 Status</label>
                <select <?= $disabled ?> class="f911_status crm-erp-field form-control" name='f911_status' id="">
                    <option value="" <?= ($f911_status == "") ? "selected" : "" ?>>N/A</option>
                        <option value='F911 Sent' <?= ($f911_status == "F911 Sent") ? "selected" : "" ?>>F911 Sent</option>
                        <option value='F911 Signed' <?= ($f911_status == "F911 Signed") ? "selected" : "" ?>>F911 Signed</option>
                        <option value='F911 Faxed to TAS' <?= ($f911_status == "F911 Faxed to TAS") ? "selected" : "" ?>>F911 Faxed to TAS</option>
                        <option value='In Progress with TAS' <?= ($f911_status == "In Progress with TAS") ? "selected" : "" ?>>In Progress with TAS</option>
                        <option value='Resolved' <?= ($f911_status == "Resolved") ? "selected" : "" ?>>Resolved</option>    
                        <option value='Closed/Unresolved' <?= ($f911_status == "Closed/Unresolved") ? "selected" : "" ?>>Closed/Unresolved</option>    
                </select>
            </div>
        </div>
    </fieldset>
</div>

<div class="erc-project-view">
    <fieldset>
        <legend>PPP Details</legend>
        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>PPP 2020 Information</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2020 Applied</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='ppp_1_applied' id="ppp1_applied">
                    <option value="1" <?= ($ercin_ppp_1_applied == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_ppp_1_applied == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2020 Start Date</label>
                <input id="bank_state" class="crm-erp-field form-control date_field" type="text" placeholder="MM/DD/YYYY" <?= $readonly ?> name='ppp_1_date' size='10' maxlength='-1' value='<?php
                if (!empty($ercin_ppp_1_date)) {
                    $ercin_ppp_1_date = date("m/d/Y", strtotime($ercin_ppp_1_date));
                }
                echo $ercin_ppp_1_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2020 Forgiveness Applied</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='ppp_1_forgiveness_applied' id="PPP 2020 Forgiveness Applied">
                    <option value="1" <?= ($ercin_ppp_1_forgiveness_applied == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_ppp_1_forgiveness_applied == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2020 End Date</label>
                <input name='ppp_1_forgive_app_date' value='<?php
                if (
                    !empty($ercin_ppp_1_forgive_app_date)
                ) {
                    $ercin_ppp_1_forgive_app_date = date(
                        "m/d/Y",
                        strtotime(
                            $ercin_ppp_1_forgive_app_date
                        )
                    );
                }
                echo $ercin_ppp_1_forgive_app_date;
                ?>' class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2020 Amount</label>
                <input name='ppp_1_amount' value='<?php echo $ercin_ppp_1_amount; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2020 Wages Allocated</label>
                <input name='ppp_1_wages_allocated' value='<?php echo $ercin_ppp_1_wages_allocated; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>PPP 2021 Information</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2021 Applied</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='ppp_2_applied' id="PPP 2021 Applied">
                    <option value="1" <?= ($ercin_ppp_2_applied == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_ppp_2_applied == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2021 Start Date</label>
                <input name='ppp_2_date' value='<?php
                if (
                    !empty($ercin_ppp_2_date)
                ) {
                    $ercin_ppp_2_date = date(
                        "m/d/Y",
                        strtotime(
                            $ercin_ppp_2_date
                        )
                    );
                }
                echo $ercin_ppp_2_date;
                ?>' class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2021 Forgiveness Applied</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name='ppp_2_forgiveness_applied' id="PPP 2021 Forgiveness Applied">
                    <option value="1" <?= ($ercin_ppp_2_forgiveness_applied == "1") ? "selected" : "" ?>>N/A</option>
                    <?php
                    $intake_section = [
                        "2" => "YES",
                        "3" => "NO",
                    ];
                    foreach ($intake_section as $inkey => $invalue) {
                        $selected = ($ercin_ppp_2_forgiveness_applied == $inkey) ? "selected" : "";
                        ?>
                        <option value='<?= $inkey ?>' <?= $selected ?>><?= $invalue ?></option>
                        <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2021 End Date</label>
                <input name='ppp_2_forgive_app_date' value='<?php
                if (
                    !empty($ercin_ppp_2_forgive_app_date)
                ) {
                    $ercin_ppp_2_forgive_app_date = date(
                        "m/d/Y",
                        strtotime(
                            $ercin_ppp_2_forgive_app_date
                        )
                    );
                }
                echo $ercin_ppp_2_forgive_app_date;
                ?>' class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2021 Amount</label>
                <input class="crm-erp-field form-control" type="text" name='ppp_2_amount' value='<?php echo $ercin_ppp_2_amount; ?>' <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>PPP 2021 Wages Allocated</label>
                <input name='ppp_2_wages_allocated' value='<?php echo $ercin_ppp_2_wages_allocated; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Additional Comments</label>
                <input name='additional_comments' value='<?php echo $ercin_additional_comments; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
            </div>
        </div>
    </fieldset>
</div>
<div class="erc-project-view">
    <fieldset>
        <legend>FPSO Details</legend>
            <div class="row mb-3">
                <div class="floating col-sm-4 mb-3">
                    <label>Attorney Name</label>
                    <input name='attorney_name' value='<?php echo $ercin_attorney_name; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Call Date</label>
                    <input name='call_date' value='<?php
            if (
                !empty($ercin_call_date)
            ) {
                $ercin_call_date = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_call_date
                    )
                );
            }
            echo $ercin_call_date;
            ?>' class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Call Time</label>
                    <input name='call_time' value='<?php echo $ercin_call_time; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Memo Received Date</label>
                    <input name='memo_received_date' value='<?php
            if (
                !empty($ercin_memo_received_date)
            ) {
                $ercin_memo_received_date = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_memo_received_date
                    )
                );
            }
            echo $ercin_memo_received_date;
            ?>' class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Memo Cut Off Date</label>
                    <input name='memo_cut_off_date' value='<?php
            if (
                !empty($ercin_memo_cut_off_date)
            ) {
                $ercin_memo_cut_off_date = date(
                    "m/d/Y",
                    strtotime(
                        $ercin_memo_cut_off_date
                    )
                );
            }
            echo $ercin_memo_cut_off_date;
            ?>' class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>>
                </div>
				
				<!--= FPSO Fee start =-->
				<div class="floating col-sm-4 mb-3">
					<label>FPSO Fees</label>
					<input id="fpso_fees" name="fpso_fees" 
						   class="crm-erp-field form-control fpso-fees-field" 
						   type="text" 
						   placeholder="0.00" 
						   value="<?php echo $ercin_fpso_fees; ?>" 
						   <?= $readonly ?>>
				</div>			
				<!--= FPSO Fee end =-->
            </div>  
        </fieldset>
    </div>
</div>      
   <script>
    jQuery(document).ready(function() {
		jQuery(".date_field").datepicker({
			dateFormat: "mm/dd/yy",
		});
	});

	function sanitizeAmount(input) {
		let value = input.value;

		// Remove all except numbers and dot
		value = value.replace(/[^0-9.]/g, "");

		// Prevent multiple dots
		const parts = value.split(".");
		if (parts.length > 2) {
			value = parts[0] + "." + parts.slice(1).join("");
		}

		// Restrict to 2 decimals
		if (parts[1]) {
			parts[1] = parts[1].slice(0, 2);
			value = parts[0] + "." + parts[1];
		}

		// Remove leading zeros (except before decimal)
		if (value.length > 1 && value[0] === "0" && value[1] !== ".") {
			value = value.replace(/^0+/, "");
		}

		input.value = value;
	}

	const fpsoField = document.getElementById("fpso_fees");

	// On typing
	fpsoField.addEventListener("input", function() {
		sanitizeAmount(this);
	});
</script>	
<?php  

exit;

?>