<?php
/**
 * CRM ERP Documents REST API
 *
 * Provides REST API endpoints for audit logs functionality
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class CRM_ERP_Documents_API
{

    public function __construct()
    {
        add_action('rest_api_init', array($this, 'register_api_routes'));

        define("STC_PRODUCT_ID", "937");
        define("TAX_PRODUCT_ID", "936");
        define("ERC_PRODUCT_ID", "935");
        define("AA_PRODUCT_ID", "934");
    }

    public function register_api_routes()
    {
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
        header("Access-Control-Allow-Headers: Content-Type, Authorization");

        register_rest_route('productsplugin/v1', '/get-erc-payroll-documents', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_erc_payroll_documents'),
            'permission_callback' => '__return_true'
        ));

        register_rest_route('productsplugin/v1', '/get-erc-documents', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_erc_documents'),
            'permission_callback' => '__return_true'
        ));

        register_rest_route('productsplugin/v1', '/get-stc-required-documents', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_stc_required_documents'),
            'permission_callback' => '__return_true'
        ));

        register_rest_route('productsplugin/v1', '/get-stc-impacted-days', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_stc_impacted_days'),
            'permission_callback' => '__return_true'
        ));

        register_rest_route('productsplugin/v1', '/get-ta-required-documents', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_ta_required_documents'),
            'permission_callback' => '__return_true'
        ));

        register_rest_route('productsplugin/v1', '/get-ta-additional-documents', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_ta_additional_documents'),
            'permission_callback' => '__return_true'
        ));

        register_rest_route('productsplugin/v1', '/upload_document', array(
            'methods' => 'POST',
            'callback' => array($this, 'upload_document_callback'),
            'permission_callback' => '__return_true'
        ));

        register_rest_route('productsplugin/v1', '/delete_document', array(
            'methods' => 'POST',
            'callback' => array($this, 'delete_document_callback'),
            'permission_callback' => '__return_true'
        ));

        register_rest_route('productsplugin/v1', '/view_comments', array(
            'methods' => 'POST',
            'callback' => array($this, 'view_comments_callback'),
            'permission_callback' => '__return_true'
        ));

        register_rest_route('productsplugin/v1', '/add_comments', array(
            'methods' => 'POST',
            'callback' => array($this, 'add_comments_callback'),
            'permission_callback' => '__return_true'
        ));

        register_rest_route('productsplugin/v1', '/change_document_status', array(
            'methods' => 'POST',
            'callback' => array($this, 'change_document_status_callback'),
            'permission_callback' => '__return_true'
        ));

    }

    public function upload_document_callback(WP_REST_Request $request)
    {
        try {
            $response = $this->handle_file_upload_restapi($request->get_params(), $request->get_file_params());

            return rest_ensure_response(array(
                'success' => true,
                'message' => 'Document uploaded successfully.',
                'data' => $response // Pass any returned data from `handle_file_upload()`
            ));
        } catch (Exception $e) {
            return rest_ensure_response(array(
                'success' => false,
                'message' => 'Failed to upload document. ' . $e->getMessage(),
            ));
        }
    }

    function handle_file_upload_restapi($params, $files)
    {
        global $wp_filesystem;

        if (empty($params['doc_key']) || empty($params['parent_folder'])) {
            throw new Exception("Missing required parameters.");
        }

        $user = wp_get_current_user();
        $user_email = $user->user_email;

        $lead_id = isset($params['lead_id']) ? $params['lead_id'] : '';

        $product_id = $this->get_product_by_lead($params['doc_key'], $lead_id);

        WP_Filesystem();

        $parent_folder = sanitize_text_field($params['parent_folder']);
        $content_directory = $wp_filesystem->wp_content_dir() . 'uploads/';

        $target_dir_location = $content_directory . 'documents_uploads/' . $lead_id . '/' . $parent_folder . '/';
        $wp_filesystem->mkdir($target_dir_location);

        $doc_key = sanitize_text_field($params['doc_key']);
        $doc_type_id = sanitize_text_field($params['doc_type_id']);

        if ($product_id == TAX_PRODUCT_ID || $product_id == STC_PRODUCT_ID || $product_id == AA_PRODUCT_ID) {
            $document_folder_link = $this->get_document_folder_link($lead_id, $product_id);
        } else {
            $document_folder_link = $this->get_document_folder_link_erc($lead_id);
        }

        if (empty($document_folder_link)) {
            throw new Exception("Technical error. Please try again later.");
        }

        if (!empty($files)) {
            $filez = $files;
            if ($product_id == TAX_PRODUCT_ID || $product_id == STC_PRODUCT_ID || $product_id == AA_PRODUCT_ID) {
                $return_array = $this->upload_single_docs_updated_array($filez, $target_dir_location, $lead_id, $parent_folder, $doc_key);
            } else {
                $return_array = $this->upload_single_docs_array($filez, $target_dir_location, $lead_id, $parent_folder, $doc_key);
            }
        }

        if ($return_array['uploadok'] > 0) {
            $pdf_dir = $target_dir_location;
            $pdf_filename = $return_array['uploaded_path']['file'];
            $file_path = site_url() . '/wp-content/uploads/documents_uploads/' . $lead_id . '/' . $parent_folder . '/' . $pdf_filename;

            if (!empty($pdf_filename)) {
                $onedrive_file_id = do_shortcode('[upload_file_onedrive pdf_dir="' . $pdf_dir . '" foldername="' . $parent_folder . '" pdf_filename="' . $pdf_filename . '" document_folder="' . $document_folder_link . '"]');

                global $wpdb;
                $table_name_uploads = $wpdb->prefix . 'leads_document_upload';
                $table_name_status = $wpdb->prefix . 'leads_document_status';

                $wpdb->insert(
                    $table_name_uploads,
                    array(
                        'lead_id' => $lead_id,
                        'parent_folder' => $parent_folder,
                        'uploded_documents' => $file_path,
                        'doc_key' => $doc_key,
                        'doc_type_id' => $doc_type_id,
                        'onedrive_itemid' => $onedrive_file_id,
                        'local_path_filename' => $pdf_filename,
                        'deleted' => 0,
                        'updated_by' => get_current_user_id(),
                        'ip_address' => $_SERVER['REMOTE_ADDR'],
                    )
                );

                $inserted_id = $wpdb->insert_id;

                $wpdb->insert(
                    $table_name_status,
                    array(
                        'doc_id' => $inserted_id,
                        'doc_status' => 'In review',
                        'doc_type_id' => $doc_type_id,
                        'user_id' => get_current_user_id(),
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    )
                );
                $table_name_comments = $wpdb->prefix . 'leads_document_notes';
                $comment = $pdf_filename . ' uploaded';
                $wpdb->insert(
                    $table_name_comments,
                    array(
                        'doc_id' => $inserted_id,
                        'lead_id' => $lead_id,
                        'comments' => $comment,
                        'doc_type_id' => $doc_type_id,
                        'user_id' => get_current_user_id(),
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    )
                );

                return array(
                    'doc_id' => $inserted_id,
                    'uploadok' => true,
                    'message' => "File uploaded successfully",
                    'file_url' => $file_path,
                    'pdf_filename' => $pdf_filename,
                    'parent_folder' => $parent_folder,
                    'lead_id' => $lead_id,
                    'doc_key' => $doc_key,
                    'doc_type_id' => $doc_type_id,
                    'onedrive_file_id' => $onedrive_file_id
                );
            }
        }

        throw new Exception("Upload failed.");
    }

    public function get_product_by_lead($doc_key = '', $d_lead_id = '')
    {
        global $wpdb;

        $table_name_document_mapping = $wpdb->prefix . 'leads_document_mapping';

        $document_mapping = $wpdb->get_row("SELECT form_id FROM " . $table_name_document_mapping . " WHERE doc_key = '$doc_key'  order by doc_type_id desc ");

        if ($document_mapping->form_id == 1 || $document_mapping->form_id == 2 || $document_mapping->form_id == 3 || $document_mapping->form_id == 4 || $document_mapping->form_id == 9) {
            $productIn = ERC_PRODUCT_ID . "," . AA_PRODUCT_ID;
        }
        if ($document_mapping->form_id == 5 || $document_mapping->form_id == 6) {
            $productIn = STC_PRODUCT_ID;
        }
        if ($document_mapping->form_id == 7 || $document_mapping->form_id == 8) {
            $productIn = TAX_PRODUCT_ID;
        }

        if ($d_lead_id) {
            $lead_id = $d_lead_id;
        } else {
            $lead_id = $_SESSION['iris_business_lead_id'];
        }

        $opportunity_product_table = $wpdb->prefix . 'opportunity_products';
        $opportunities_table = $wpdb->prefix . 'opportunities';
        $products_table = $wpdb->prefix . 'crm_products';

        if ($productIn) {
            $lead_sql = $wpdb->prepare("
                SELECT DISTINCT $opportunity_product_table.Product_id as product_id 
                FROM $opportunities_table 
                LEFT JOIN $opportunity_product_table ON $opportunities_table.OpportunityID = $opportunity_product_table.opportunity_id 
                LEFT JOIN $products_table ON $products_table.ProductID = $opportunity_product_table.Product_id 
                WHERE $opportunities_table.LeadID = %d 
                AND $opportunities_table.DeletedAt IS NULL
                AND $products_table.DeletedAt IS NULL
                AND $products_table.Status = 'active'
                AND $opportunity_product_table.Product_id IN ($productIn)
            ", $lead_id);

            $opportunity_ids = $wpdb->get_col($lead_sql);
        }

        if (!empty($opportunity_ids)) {
            // Return the first product_id found
            return $opportunity_ids[0];
        }

        return null; // Return null if no product_id found
    }

    public function get_document_folder_link_only($lead_id, $product_id=''){
        global $wpdb;
        if($product_id){
            $table_lead_folder_mapping = $wpdb->prefix . 'lead_folder_mapping';

            $result = $wpdb->get_results("SELECT * FROM " . $table_lead_folder_mapping . " WHERE lead_id = '$lead_id' and product_id = '$product_id' order by id desc ");
            $document_folder_link = "";
            $prefix_url = "https://occams-my.sharepoint.com/personal/admin_occamsparadigm_com/Documents/Occams/USA/Business%20Segments/Financial%20%26%20Tax%20Advisory%20(FTA)/Tax%20Credits/ERC/ERC%20Client%27s/AutoX/";
            if ($result[0]->company_folder) {
                $document_folder_link = $prefix_url . $result[0]->company_folder;
            }
        }else{
            $table_name_intake = $wpdb->prefix . 'erc_erc_intake';

            $result = $wpdb->get_results("SELECT * FROM " . $table_name_intake . " WHERE lead_id = '$lead_id'  order by id desc ");

            $document_folder_link = $result[0]->company_folder_link;
        }


        return $document_folder_link;
    }

    function get_document_folder_link($lead_id, $product_id)
    {
        global $wpdb;
        $table_lead_folder_mapping = $wpdb->prefix . 'lead_folder_mapping';

        $result = $wpdb->get_results("SELECT * FROM " . $table_lead_folder_mapping . " WHERE lead_id = '$lead_id' and product_id = '$product_id' order by id desc ");

        // Folder Creation
        if (empty($result)) {
            $business_tbl = $wpdb->prefix . 'erc_business_info';
            //get business name by lead id
            $business_name = $wpdb->get_var("SELECT business_legal_name FROM $business_tbl erc_business_info WHERE lead_id = '$lead_id'  order by id desc ");

            $url = 'https://portal.occamsadvisory.com/portal/wp-json/v1/create_dynamic_onedrive_folder';
            $datas['lead_id'] = $lead_id;
            $datas['product_id'] = $product_id;
            $datas['business_name'] = $business_name;
            $folder_data = json_encode($datas);
            $args = array('body' => $folder_data, 'headers' => array('Content-Type' => 'x/json'));

            $folder_response = wp_remote_post($url, $args);
            $response_folder = json_decode(wp_remote_retrieve_body($folder_response));
        }
        //----------

        $result = $wpdb->get_results("SELECT * FROM " . $table_lead_folder_mapping . " WHERE lead_id = '$lead_id' and product_id = '$product_id' order by id desc ");

        $document_folder_link = "";
        $prefix_url = "https://occams-my.sharepoint.com/personal/admin_occamsparadigm_com/Documents/Occams/USA/Business%20Segments/Financial%20%26%20Tax%20Advisory%20(FTA)/Tax%20Credits/ERC/ERC%20Client%27s/AutoX/";
        if ($result[0]->document_folder) {
            $document_folder_link = $prefix_url . $result[0]->document_folder;
        }

        return $document_folder_link;

    }

    function get_document_folder_link_erc($lead_id)
    {

        global $wpdb;
        $table_name_intake = $wpdb->prefix . 'erc_erc_intake';

        $result = $wpdb->get_results("SELECT * FROM " . $table_name_intake . " WHERE lead_id = '$lead_id'  order by id desc ");

        $document_folder_link = $result[0]->document_folder_link;

        return $document_folder_link;

    }

    function upload_single_docs_updated_array($filez, $target_dir_location, $user_id, $parent_folder, $doc_key = '')
    {
        if (!empty($filez)) {
            $count = 0;
            $uploadok = 0;
            $uploaded_path = array();

            foreach ($filez as $key => $files) {
                if (!empty($files['name'])) {
                    $uploaded_path[$key] = array();

                    $name_file = $files['name'];

                    $name_file = preg_replace('/[\[\]\(\)\{\}]+/', '_', $name_file);
                    $name_file = str_replace('_.', '.', $name_file);

                    //$timestamp = time(); // Current timestamp
                    $name_file = pathinfo($name_file, PATHINFO_FILENAME) . '_' . $doc_key . '.' . pathinfo($name_file, PATHINFO_EXTENSION);

                    $tmp_name = $files['tmp_name'];
                    $field_name = $key;

                    // get file type
                    $file_type = wp_check_filetype(basename($name_file), null);
                    $file_type = $file_type['ext'];
                    $file_path = $target_dir_location . $name_file;

                    if (move_uploaded_file($tmp_name, $target_dir_location . $name_file)) {
                        $uploadok = $uploadok + 1;
                        $uploaded_path[$key] = $name_file;
                    }
                }

                $count++;
            }

            $return_array = array(
                'uploadok' => $uploadok,
                'uploaded_path' => $uploaded_path
            );

            return $return_array;
        }

    }

    function upload_single_docs_array($filez, $target_dir_location, $user_id, $parent_folder, $doc_key = '')
    {
        if (!empty($filez)) {
            $count = 0;
            $uploadok = 0;
            $uploaded_path = array();

            foreach ($filez as $key => $files) {
                if (!empty($files['name'])) {
                    $uploaded_path[$key] = array();

                    $name_file = $files['name'];

                    $name_file = preg_replace('/[\[\]\(\)\{\}]+/', '_', $name_file);
                    $name_file = str_replace('_.', '.', $name_file);

                    //$timestamp = time(); // Current timestamp
                    $name_file = pathinfo($name_file, PATHINFO_FILENAME) . '_' . $doc_key . '.' . pathinfo($name_file, PATHINFO_EXTENSION);

                    $tmp_name = $files['tmp_name'];
                    $field_name = $key;

                    // get file type
                    $file_type = wp_check_filetype(basename($name_file), null);
                    $file_type = $file_type['ext'];
                    $file_path = $target_dir_location . $name_file;

                    if (move_uploaded_file($tmp_name, $target_dir_location . $name_file)) {
                        $uploadok = $uploadok + 1;
                        $uploaded_path[$key] = $name_file;
                    }
                }

                $count++;
            }

            $return_array = array(
                'uploadok' => $uploadok,
                'uploaded_path' => $uploaded_path
            );

            return $return_array;
        }

    }

    public function delete_document_callback()
    {

        try {

            $response = $this->handle_file_delete($_POST);

            return rest_ensure_response(array(
                'success' => true,
                'message' => 'Document deleted successfully.'
            ));
        } catch (Exception $e) {

            return rest_ensure_response(array(
                'success' => false,
                'message' => 'Failed to delete document. ' . $e->getMessage(),
            ));
        }
    }

    function handle_file_delete()
    {
        global $wpdb;
        global $wp_filesystem;

        $user = wp_get_current_user();
        $user_email = $user->user_email;
        $product_id = $this->get_product_by_lead($_POST['doc_key'], $_POST['lead_id']);

        WP_Filesystem();
        $content_directory = $wp_filesystem->wp_content_dir() . 'uploads/';
        $user_id = get_current_user_id();
        $lead_id = $_POST['lead_id'];
        $parent_folder = $_POST['folder_name'];
        $file_name = $_POST['file_name'];
        $doc_key = $_POST['doc_key'];
        $doc_id = $_POST['doc_id'];
        $doc_type_id = $_POST['doc_type_id'];


        $table_name_status = $wpdb->prefix . 'leads_document_status';

        $result = $wpdb->get_results("SELECT * FROM " . $table_name_status . " WHERE doc_id = '$doc_id' order by id desc limit 1  ");
        $doc_status = $result[0]->doc_status;

        //  if($doc_status == 'Approved'){
        // 	$return_array = array('uploadok' => 'false', "message" => "This document is already approved.");
        // 	echo json_encode($return_array);
        // 	die();
        //  }

        $table_name_uploads = $wpdb->prefix . 'leads_document_upload';

        $result = $wpdb->get_results("SELECT * FROM " . $table_name_uploads . " WHERE id = '$doc_id' ");

        $pdf_filename = $result[0]->local_path_filename;
        $comment = $pdf_filename . ' deleted';

        $lead_id = isset($_POST['lead_id']) && !empty($_POST['lead_id']) ? $_POST['lead_id'] : $result[0]->lead_id;
        $parent_folder = isset($_POST['folder_name']) && !empty($_POST['folder_name']) ? $_POST['folder_name'] : $result[0]->parent_folder;
        $doc_key = isset($_POST['doc_key']) && !empty($_POST['doc_key']) ? $_POST['doc_key'] : $result[0]->doc_key;
        $doc_type_id = isset($_POST['doc_type_id']) && !empty($_POST['doc_type_id']) ? $_POST['doc_type_id'] : $result[0]->doc_type_id;
        $current_user_id = isset($user_id) && $user_id !== 0 ? $user_id : $result[0]->updated_by;


        if ($product_id == TAX_PRODUCT_ID || $product_id == STC_PRODUCT_ID) {
            $document_folder = $this->get_document_folder_link($lead_id, $product_id);
        } else {
            $document_folder = $this->get_document_folder_link_erc($lead_id);
        }

        if ($document_folder == '') {

            $return_array = array('uploadok' => 'false', 'error' => 'We are experiencing technical difficulties. Please try again later.');
            echo json_encode($return_array);
            die();
        }

        $onedrive_deleted_file_id1 = do_shortcode('[move_file_to_trash_onedrive  foldername="' . $parent_folder . '" pdf_filename="' . $pdf_filename . '" document_folder="' . $document_folder . '"]');
        $onedrive_deleted_file_id = do_shortcode('[delete_file_onedrive   pdf_filename="' . $pdf_filename . '" document_folder="' . $document_folder . '"]');

        $wp_filesystem->delete($content_directory . 'documents_uploads/' . $lead_id . '/' . $parent_folder . '/' . $pdf_filename);


        $table_name_uploads = $wpdb->prefix . 'leads_document_upload';
        $table_name_comments = $wpdb->prefix . 'leads_document_notes';

        $wpdb->update(
            $table_name_uploads,
            array(
                'deleted' => 1,
            ),
            array(
                'id' => $doc_id,
            )
        );

        $wpdb->insert(
            $table_name_comments,
            array(
                'doc_id' => $doc_id,
                'lead_id' => $lead_id,
                'comments' => $comment,
                'user_id' => $current_user_id,
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'doc_type_id' => $doc_type_id
            )
        );

        $return_array = array(
            "message" => "File deleted successfully",
        );

        echo json_encode($return_array);
        die();
    }

    public function view_comments_callback()
    {
        try {
            global $wpdb;
            $response = array();

            $doc_id = $_REQUEST['doc_id'];
            $doc_type_id = $_REQUEST['doc_type_id'];
            //$lead_id = $_GET['lead_id'];
            $table_name_comments = $wpdb->prefix . 'leads_document_notes';

            $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM " . $table_name_comments . " WHERE doc_type_id = '$doc_type_id' AND doc_id = '$doc_id'  order by id desc"));

            foreach ($comment_data as $value) {
                $user = get_user_by('id', $comment_data->user_id);
                $user_name = $user->display_name;
                $response[] = array(
                    'comments' => $value->comments,
                    'commented_by' => $user_name,
                    'commented_at' => $value->update_datetime,
                );
            }

            // Return the response as JSON
            return rest_ensure_response(array(
                'success' => true,
                'message' => 'Comments fetched successfully.',
                'data' => $response
            ));
        } catch (Exception $e) {
            return rest_ensure_response(array(
                'success' => false,
                'message' => 'Failed to fetch comments. ' . $e->getMessage(),
            ));
        }

    }

    public function add_comments_callback()
    {
        global $wpdb;
        try {
            global $wpdb;
            $response = array();

            $comment = $_POST['comment'];
            $doc_id = $_POST['doc_id'];
            $doc_type_id = $_POST['doc_type_id'];
            $lead_id = $_POST['lead_id'];
            $table_name_comments = $wpdb->prefix . 'leads_document_notes';

            $sql = $wpdb->prepare(
                "INSERT INTO {$table_name_comments} (doc_id, doc_type_id, lead_id, comments) VALUES (%d, %d, %d, %s)",
                $doc_id,
                $doc_type_id,
                $lead_id,
                $comment
            );

            $result = $wpdb->query($sql);

            return rest_ensure_response(array(
                'success' => true,
                'message' => 'Comments added successfully.',
            ));
        } catch (Exception $e) {
            return rest_ensure_response(array(
                'success' => false,
                'message' => 'Failed to add comments. ' . $e->getMessage(),
            ));
        }


    }

    public function change_document_status_callback()
    {
        global $wpdb;
        try {
            $doc_status = $_POST['status'];
            $doc_id = $_POST['doc_id'];
            $doc_type_id = $_POST['doc_type_id'];

            $table_name_status = $wpdb->prefix . 'leads_document_status';
            $table_name_comments = $wpdb->prefix . 'leads_document_notes';

            $table_name_uploads = $wpdb->prefix . 'leads_document_upload';

            $result = $wpdb->get_results("SELECT * FROM " . $table_name_uploads . " WHERE id = '$doc_id'  order by id desc ");

            $local_path_filename = $result[0]->local_path_filename;


            if ($doc_status == 'Approved') {
                $comment = $local_path_filename . ' approved';
            } elseif ($doc_status == 'Rejected') {
                $comment = $local_path_filename . ' rejected';
            } elseif ($doc_status == 'In review') {
                $comment = $local_path_filename . ' in review';
            }


            $wpdb->insert(
                $table_name_status,
                array(
                    'doc_id' => $doc_id,
                    'doc_status' => $doc_status,
                    'ip_address' => $_SERVER['REMOTE_ADDR'],
                    'doc_type_id' => $doc_type_id
                )
            );


            $wpdb->insert(
                $table_name_comments,
                array(
                    'doc_id' => $doc_id,
                    'comments' => $comment,
                    'ip_address' => $_SERVER['REMOTE_ADDR'],
                    'doc_type_id' => $doc_type_id
                )
            );


            return rest_ensure_response(array(
                'success' => true,
                'message' => 'Document status changed successfully.',
            ));
        } catch (Exception $e) {
            return rest_ensure_response(array(
                'success' => false,
                'message' => 'Failed to change document status. ' . $e->getMessage(),
            ));
        }


    }

    function get_erc_documents(WP_REST_Request $request)
    {
        global $wpdb;

        $project_id = $request->get_param('project_id');
        $form_id = $request->get_param('form_id');

        if (!$project_id) {
            return new WP_REST_Response(['error' => 'Project ID is required.'], 400);
        }
        if (!$form_id) {
            return new WP_REST_Response(['error' => 'Form ID is required.'], 400);
        }

        $project_row = $wpdb->get_row($wpdb->prepare("
            SELECT lead_id, product_id 
            FROM {$wpdb->prefix}projects 
            WHERE project_id = %d
            ", $project_id));

        if ($project_row) {
            $lead_id = $project_row->lead_id;
            $product_id = $project_row->product_id;
            $erc_product_id = 935;
            //$form_id = 4;

            $cutoff_date = '2024-06-21';

            $opportunities_table = $wpdb->prefix . 'opportunities';
            $opportunity_product_table = $wpdb->prefix . 'opportunity_products';
            $doc_mapping_table = $wpdb->prefix . 'leads_document_mapping';
            $doc_uplod_table = $wpdb->prefix . 'leads_document_upload';
            $doc_uplod_status = $wpdb->prefix . 'leads_document_status';
            $doc_uplod_notes = $wpdb->prefix . 'leads_document_notes';

            // Check for recent agreement
            $last_agreement_sent = $wpdb->get_row($wpdb->prepare(
                "SELECT o.last_agreement_sent 
         FROM $opportunities_table o
         LEFT JOIN $opportunity_product_table op ON o.OpportunityID = op.opportunity_id 
         WHERE o.LeadID = %d 
           AND o.DeletedAt IS NULL 
           AND op.product_id = %d 
           AND o.last_agreement_sent > %s",
                $lead_id, $erc_product_id, $cutoff_date
            ));

            $is_last_agreement = !empty($last_agreement_sent);

            if ($form_id == 1) {
                $doc_keys = ['cert_article_incorporation', 'ein', 'company_additional_field1', 'company_additional_field2', 'tax_now_signup'];
            }/*else if($form_id == 2){
            $doc_keys = array('payroll_2019_average_fte','payroll_additional_field1', 'payroll_additional_field2', );
        }*/ else if ($form_id == 3) {
                $doc_keys = ['ppp_2021', 'quarterly_gross_receipt_csv', 'erc_additional_field1', 'erc_additional_field2'];
            } else if ($form_id == 4) {
                $doc_keys = ['business_tax_returns_2019', 'business_tax_returns_2021', 'other_additional_field1', 'other_additional_field2'];
            }

            $placeholders = implode("','", array_map('esc_sql', $doc_keys));
            if ($is_last_agreement) {
                $other_docs = $wpdb->get_results($wpdb->prepare(
                    "SELECT * FROM $doc_mapping_table WHERE doc_key IN ('$placeholders') AND form_id = %d",
                    $form_id
                ));
            } else {
                $other_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `form_id` = %d", $form_id));
            }

            $response = [];

            foreach ($other_docs as $doc) {
                $doc_type_id = $doc->doc_type_id;
                $doc_key = $doc->doc_key;
                $doc_label = $doc->doc_label;

                // Label override based on last agreement check
                if ($is_last_agreement) {
                    if ($doc_type_id == 1) {
                        $doc_label = "Certificate or Articles of Incorporation";
                    }
                    if ($doc_type_id == 2) {
                        $doc_label = "Tax Id- Employee Identification Number (EIN) Issued by IRS";
                    }
                    if ($doc_type_id == 29) {
                        $doc_label = "PPP Forgiveness Form 3508 for 2021 (if applicable)";
                    }
                    if ($doc_type_id == 30) {
                        $doc_label = "1. Quarterly GROSS RECEIPTS for each calendar quarter in 2019 & 2021( use template)<br>
                        2. Name and ownership % of the owner/s of the business";
                    }
                    if ($doc_type_id == 33) {
                        $doc_label = "2019-Business Tax Returns-Filed Federal Tax Return 1120, 1120-S, or 1065";
                    }
                    if ($doc_type_id == 35) {
                        $doc_label = "2021-Business Tax Return-Filed Federal Tax Return 1120, 1120-S, or 1065";
                    }
                }

                // Get uploaded document
                $uploaded = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $doc_uplod_table 
             WHERE lead_id = %d AND deleted = 0 AND doc_type_id = %d 
             ORDER BY id DESC LIMIT 1",
                    $lead_id, $doc_type_id
                ));

                // Get document status
                $file_status = null;
                if ($uploaded) {
                    $status = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $doc_uplod_status 
                 WHERE doc_id = %d 
                 ORDER BY id DESC LIMIT 1",
                        $uploaded->id
                    ));
                    $file_status = $status->doc_status ?? null;
                }

                // Get comments
                $comments = $wpdb->get_results($wpdb->prepare(
                    "SELECT n.*, u.display_name AS username, DATE_FORMAT(n.update_datetime, '%%m/%%d/%%Y %%h:%%i:%%s') AS update_datetime
                 FROM $doc_uplod_notes n
                 LEFT JOIN {$wpdb->users} u ON n.user_id = u.ID
                 WHERE n.doc_type_id = %d AND n.lead_id = %d",
                    $doc_type_id, $lead_id
                ));

                $response[] = [
                    'lead_id' => $lead_id,
                    'doc_type_id' => $doc_type_id,
                    'doc_key' => $doc_key,
                    'doc_label' => $doc_label,
                    'file_url' => $uploaded->uploded_documents ?? '',
                    'file_name' => $uploaded->local_path_filename ?? '',
                    'status' => $file_status,
                    'comments' => $comments,
                ];
            }

            if($product_id ==  937){
                $document_folder_link = $this->get_document_folder_link_only($lead_id, 937);
            }else{
                $document_folder_link = $this->get_document_folder_link_only($lead_id,'');
            }

            return rest_ensure_response([
                'lead_id' => $lead_id,
                'product_id' => $product_id,
                'view_document' => $document_folder_link,
                'documents' => $response
            ]);
        } else {
            return new WP_REST_Response(['error' => 'Something went wrong with data.'], 400);
        }
    }

    function get_erc_payroll_documents(WP_REST_Request $request)
    {
        global $wpdb;
        //ini_set('display_errors', 1);
        //ini_set('display_startup_errors', 1);
        //error_reporting(E_ALL);
        $project_id = $request->get_param('project_id');

        if (!$project_id) {
            return new WP_REST_Response(['error' => 'Project ID is required.'], 400);
        }

        $project_row = $wpdb->get_row($wpdb->prepare("
            SELECT lead_id, product_id 
            FROM {$wpdb->prefix}projects 
            WHERE project_id = %d
            ", $project_id));

        if ($project_row) {
            $lead_id = $project_row->lead_id;
            $product_id = $project_row->product_id;

            $erc_product_id = 935;
            $form_id = 2;
            $parent_folder = 'Payroll Documents';
            $cutoff_date = '2024-06-21';

            $opportunities_table = $wpdb->prefix . 'opportunities';
            $opportunity_product_table = $wpdb->prefix . 'opportunity_products';
            $doc_mapping_table = $wpdb->prefix . 'leads_document_mapping';
            $doc_uplod_table = $wpdb->prefix . 'leads_document_upload';
            $doc_uplod_status = $wpdb->prefix . 'leads_document_status';
            $doc_uplod_notes = $wpdb->prefix . 'leads_document_notes';

            // Check for recent agreement
            $last_agreement_sent = $wpdb->get_row($wpdb->prepare(
                "SELECT o.last_agreement_sent 
         FROM $opportunities_table o
         LEFT JOIN $opportunity_product_table op ON o.OpportunityID = op.opportunity_id 
         WHERE o.LeadID = %d 
           AND o.DeletedAt IS NULL 
           AND op.product_id = %d 
           AND o.last_agreement_sent > %s",
                $lead_id, $erc_product_id, $cutoff_date
            ));

            $is_last_agreement = !empty($last_agreement_sent);

            // Define queries
            $quat_keys = $is_last_agreement ?
                ['quat_payroll_2021_q1', 'quat_payroll_2021_q2', 'quat_payroll_2021_q3'] :
                ['quat_payroll_2020_q1', 'quat_payroll_2020_q2', 'quat_payroll_2020_q3', 'quat_payroll_2020_q4', 'quat_payroll_2021_q1', 'quat_payroll_2021_q2', 'quat_payroll_2021_q3'];

            $register_keys = $is_last_agreement ?
                ['payroll_registers_2021_q1', 'payroll_registers_2021_q2', 'payroll_registers_2021_q3'] :
                ['payroll_registers_2020_q1', 'payroll_registers_2020_q2', 'payroll_registers_2020_q3', 'payroll_registers_2020_q4', 'payroll_registers_2021_q1', 'payroll_registers_2021_q2', 'payroll_registers_2021_q3'];

            $healthcare_keys = $is_last_agreement ?
                ['payroll_health_care_2021_q1', 'payroll_health_care_2021_q2', 'payroll_health_care_2021_q3'] :
                ['payroll_health_care_2020_q1', 'payroll_health_care_2020_q2', 'payroll_health_care_2020_q3', 'payroll_health_care_2020_q4', 'payroll_health_care_2021_q1', 'payroll_health_care_2021_q2', 'payroll_health_care_2021_q3'];

            $additional_keys = ['payroll_2019_average_fte', 'payroll_additional_field1', 'payroll_additional_field2'];

            // Fetch groups
            $quat_docs = $this->fetch_docs_by_keys($doc_mapping_table, $quat_keys);
            $register_docs = $this->fetch_docs_by_keys($doc_mapping_table, $register_keys);
            $healthcare_docs = $this->fetch_docs_by_keys($doc_mapping_table, $healthcare_keys);
            $additional_docs = $this->fetch_docs_by_keys($doc_mapping_table, $additional_keys, true);

            // Create grouped structure
            $groups = [];

            $groups[] = [
                'heading' => $is_last_agreement ?
                    "Payroll Tax Return 941’s for quarters- 2021: Q1-Q3" :
                    "Quarterly Payroll Tax Return 941’s for Eligible Quarters – 2020: Q1-Q4 & 2021: Q1-Q3",
                'documents' => $quat_docs
            ];

            $groups[] = [
                'heading' => $is_last_agreement ?
                    "Payroll Registers for quarters- 2021: Q1-Q3" :
                    "Payroll Registers for all Eligible Quarters – 2020: Q1-Q4 & 2021: Q1-Q3",
                'documents' => $register_docs
            ];

            $groups[] = [
                'heading' => $is_last_agreement ?
                    "Health Care Expenses per employee per payroll for 2021: Q1-Q3 (if applicable)" :
                    "Total Health Care Expenses per Employee per Payroll for 2020: Q1-Q4 & 2021: Q1-Q3",
                'documents' => $healthcare_docs
            ];

            $groups[] = [
                'heading' => "Payroll Additional Documents",
                'documents' => $additional_docs
            ];

            return rest_ensure_response([
                'lead_id' => $lead_id,
                'product_id' => $product_id,
                'groups' => $groups
            ]);

            $placeholders = implode("','", array_map('esc_sql', $doc_keys));

            if ($is_last_agreement) {
                $company_docs = $wpdb->get_results($wpdb->prepare(
                    "SELECT * FROM $doc_mapping_table WHERE doc_key IN ('$placeholders') AND form_id = %d",
                    $form_id
                ));
            } else {
                $company_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `form_id` = %d", $form_id));
            }

            $response = [];

            foreach ($company_docs as $doc) {
                $doc_type_id = $doc->doc_type_id;
                $doc_key = $doc->doc_key;
                $doc_label = $doc->doc_label;

                // Label override based on last agreement check
                if ($is_last_agreement) {
                    if ($doc_type_id == 1) {
                        $doc_label = "Certificate or Articles of Incorporation";
                    }
                    if ($doc_type_id == 2) {
                        $doc_label = "Tax Id- Employee Identification Number (EIN) Issued by IRS";
                    }
                }

                // Get uploaded document
                $uploaded = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $doc_uplod_table 
             WHERE lead_id = %d AND deleted = 0 AND doc_type_id = %d 
             ORDER BY id DESC LIMIT 1",
                    $lead_id, $doc_type_id
                ));

                // Get document status
                $file_status = null;
                if ($uploaded) {
                    $status = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $doc_uplod_status 
                 WHERE doc_id = %d 
                 ORDER BY id DESC LIMIT 1",
                        $uploaded->id
                    ));
                    $file_status = $status->doc_status ?? null;
                }

                // Get comments
                $comments = $wpdb->get_results($wpdb->prepare(
                    "SELECT n.*, u.display_name AS username, DATE_FORMAT(n.update_datetime, '%%m/%%d/%%Y %%h:%%i:%%s') AS update_datetime
                 FROM $doc_uplod_notes n
                 LEFT JOIN {$wpdb->users} u ON n.user_id = u.ID
                 WHERE n.doc_type_id = %d AND n.lead_id = %d",
                    $doc_type_id, $lead_id
                ));

                $response[] = [
                    'doc_key' => $doc_key,
                    'doc_label' => $doc_label,
                    'file_url' => $uploaded->uploded_documents ?? '',
                    'file_name' => $uploaded->local_path_filename ?? '',
                    'status' => $file_status,
                    'comments' => $comments,
                ];
            }

            if($product_id ==  937){
                $document_folder_link = $this->get_document_folder_link_only($lead_id, 937);
            }else{
                $document_folder_link = $this->get_document_folder_link_only($lead_id,'');
            }

            return rest_ensure_response([
                'lead_id' => $lead_id,
                'product_id' => $product_id,
                'view_document' => $document_folder_link,
                'documents' => $response
            ]);
        } else {
            return new WP_REST_Response(['error' => 'Something went wrong with data.'], 400);
        }
    }

    function fetch_docs_by_keys($table, $keys = [], $lead_id = null, $form_id = null, $is_last_agreement = false, $ordered = false)
    {
        global $wpdb;

        $doc_uplod_table = $wpdb->prefix . 'leads_document_upload';
        $doc_uplod_status = $wpdb->prefix . 'leads_document_status';
        $doc_uplod_notes = $wpdb->prefix . 'leads_document_notes';

        // Base query construction
        $where = [];
        $params = [];

        if (!empty($keys)) {
            $placeholders = implode(',', array_fill(0, count($keys), '%s'));
            $where[] = "`doc_key` IN ($placeholders)";
            $params = array_merge($params, $keys);
        }

        if ($is_last_agreement && $form_id !== null) {
            $where[] = "`form_id` = %d";
            $params[] = $form_id;
        } elseif (!$is_last_agreement && $form_id !== null && empty($keys)) {
            $where[] = "`form_id` = %d";
            $params[] = $form_id;
        }

        $order_clause = '';
        if ($ordered && !empty($keys)) {
            $ordered_keys = "'" . implode("','", array_map('esc_sql', $keys)) . "'";
            $order_clause = "ORDER BY FIELD(`doc_key`, $ordered_keys)";
        }

        $where_clause = !empty($where) ? 'WHERE ' . implode(' AND ', $where) : '';
        $sql = "SELECT * FROM $table $where_clause $order_clause";

        $docs = $wpdb->get_results($wpdb->prepare($sql, $params));

        $response = [];

        foreach ($docs as $doc) {
            $doc_type_id = $doc->doc_type_id;
            $doc_key = $doc->doc_key;
            $doc_label = $doc->doc_label;

            // Get latest uploaded file
            $uploaded = null;
            if ($lead_id !== null) {
                $uploaded = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $doc_uplod_table 
                 WHERE lead_id = %d AND deleted = 0 AND doc_type_id = %d 
                 ORDER BY id DESC LIMIT 1",
                    $lead_id, $doc_type_id
                ));
            }

            // Get status
            $file_status = null;
            if ($uploaded) {
                $status = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $doc_uplod_status 
                 WHERE doc_id = %d 
                 ORDER BY id DESC LIMIT 1",
                    $uploaded->id
                ));
                $file_status = $status->doc_status ?? null;
            }

            // Get comments
            $comments = [];
            if ($lead_id !== null) {
                // Get comments
                $comments = $wpdb->get_results(
                    $wpdb->prepare(
                        "SELECT n.*, u.display_name AS username, DATE_FORMAT(n.update_datetime, '%%m/%%d/%%Y %%h:%%i:%%s') AS update_datetime
                         FROM $doc_uplod_notes n
                         LEFT JOIN $wpdb->users u ON n.user_id = u.ID
                         WHERE n.doc_type_id = %d AND n.lead_id = %d",
                        $doc_type_id,
                        $lead_id
                    )
                );
            }

            $response[] = [
                'doc_key' => $doc_key,
                'doc_label' => $doc_label,
                'file_url' => $uploaded->uploded_documents ?? '',
                'file_name' => $uploaded->local_path_filename ?? '',
                'status' => $file_status,
                'comments' => $comments,
            ];
        }

        return $response;
    }

    function get_stc_required_documents(WP_REST_Request $request)
    {
        global $wpdb;
        $project_id = $request->get_param('project_id');

        if (!$project_id) {
            return new WP_REST_Response(['error' => 'Project ID is required.'], 400);
        }

        $project_row = $wpdb->get_row($wpdb->prepare("
            SELECT lead_id, product_id 
            FROM {$wpdb->prefix}projects 
            WHERE project_id = %d
            ", $project_id));

        if ($project_row) {
            $lead_id = $project_row->lead_id;
            $product_id = $project_row->product_id;
            $form_id = 6;
            $leadArray = [9451, 9432, 9430, 9425, 9407, 9405, 9400, 9399, 9390, 9386, 9380, 9373, 9346, 9345, 9342, 9341, 9339, 9197, 9180, 9149, 8933, 8862, 8816, 8637];

            $doc_mapping_table = $wpdb->prefix . 'leads_document_mapping';
            $doc_upload_table = $wpdb->prefix . 'leads_document_upload';
            $doc_notes_table = $wpdb->prefix . 'leads_document_notes';
            $doc_uplod_status = $wpdb->prefix . 'leads_document_status';
            $doc_required_mapping = $wpdb->prefix . 'leads_document_required_mapping';
            $document_taxnow_status = $wpdb->prefix . 'leads_document_taxnow_status';

            // First group docs
            $doc_type_ids_group1 = in_array($lead_id, $leadArray) ? [39, 40, 41] : [40, 41];
            $documents_group1 = [];

            if (!empty($doc_type_ids_group1)) {
                $placeholders = implode(',', array_fill(0, count($doc_type_ids_group1), '%d'));
                $sql = "SELECT * FROM $doc_mapping_table WHERE form_id = %d AND doc_type_id IN ($placeholders)";
                $params = array_merge([$form_id], $doc_type_ids_group1);
                $query = call_user_func_array([$wpdb, 'prepare'], array_merge([$sql], $params));
                $company_docs = $wpdb->get_results($query);

                foreach ($company_docs as $doc) {
                    $doc_type_id = $doc->doc_type_id;
                    $doc_key = $doc->doc_key;
                    $doc_label = substr($doc->doc_label, strrpos($doc->doc_label, '-') + 1);

                    $upload = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $doc_upload_table WHERE lead_id = %d AND deleted = 0 AND doc_type_id = %d ORDER BY id DESC LIMIT 1",
                        $lead_id,
                        $doc_type_id
                    ));

                    $last_result = $wpdb->get_results($wpdb->prepare(
                        "SELECT * FROM $doc_upload_table WHERE lead_id = %d AND doc_type_id = %d ORDER BY id DESC LIMIT 1",
                        $lead_id,
                        $doc_type_id
                    ));

                    $last_doc_id = !empty($last_result) ? $last_result[0]->id : null;

                    // Fetch status for latest uploaded document
                    $file_status = null;
                    if ($last_doc_id) {
                        $status_data = $wpdb->get_row($wpdb->prepare(
                            "SELECT * FROM $doc_uplod_status WHERE doc_id = %d ORDER BY id DESC LIMIT 1",
                            $last_doc_id
                        ));
                        if ($status_data) {
                            $file_status = $status_data->doc_status;
                        }
                    }

                    // Notes
                    $notes = $wpdb->get_results($wpdb->prepare(
                        "SELECT n.*, u.display_name AS username, DATE_FORMAT(n.update_datetime, '%%m/%%d/%%Y %%h:%%i:%%s') AS update_datetime
                     FROM $doc_notes_table n
                     LEFT JOIN {$wpdb->users} u ON n.user_id = u.ID
                     WHERE n.doc_type_id = %d AND n.lead_id = %d",
                        $doc_type_id,
                        $lead_id
                    ));

                    // Logic flags
                    $is_uploaded = !empty($upload);
                    $can_cancel = $remove_document_access == 1 && in_array($file_status, ['Rejected', 'In review']);
                    $can_change_status = $file_status === 'In review' || ($change_document_status_access == 1 && $is_affiliate == 0);

                    $documents_group1[] = [
                        'doc_type_id' => $doc_type_id,
                        'doc_key' => $doc_key,
                        'doc_label' => $doc_label,
                        'mandatory' => 'Yes',
                        'file_url' => $upload->uploded_documents ?? '',
                        'file_name' => $upload->local_path_filename ?? '',
                        'status' => $file_status,
                        'comments' => $notes,
                    ];
                }

            }

            // Second group docs (example: form_id=6, doc_type_ids 42,43)
            $doc_type_ids_group2 = in_array($lead_id, $leadArray) ? [42, 43] : [43];
            $documents_group2 = [];

            if (!empty($doc_type_ids_group2)) {
                $placeholders = implode(',', array_fill(0, count($doc_type_ids_group2), '%d'));
                $sql = "SELECT * FROM $doc_mapping_table WHERE form_id = %d AND doc_type_id IN ($placeholders)";
                $params = array_merge([$form_id], $doc_type_ids_group2);
                $query = call_user_func_array([$wpdb, 'prepare'], array_merge([$sql], $params));
                $w2_docs = $wpdb->get_results($query);

                foreach ($w2_docs as $doc) {
                    $doc_type_id = $doc->doc_type_id;
                    $doc_key = $doc->doc_key;
                    $doc_label = substr($doc->doc_label, strrpos($doc->doc_label, '-') + 1);

                    // Get the latest document upload (not deleted)
                    $upload = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $doc_upload_table 
                     WHERE lead_id = %d AND deleted = 0 AND doc_type_id = %d 
                     ORDER BY id DESC LIMIT 1",
                        $lead_id, $doc_type_id
                    ));

                    $file_url = $upload->uploded_documents ?? '';
                    $file_name = $upload->local_path_filename ?? '';
                    $doc_id = $upload->id ?? null;

                    $last_result = $wpdb->get_results($wpdb->prepare(
                        "SELECT * FROM $doc_upload_table WHERE lead_id = %d AND doc_type_id = %d ORDER BY id DESC LIMIT 1",
                        $lead_id,
                        $doc_type_id
                    ));

                    $last_doc_id = !empty($last_result) ? $last_result[0]->id : null;

                    // Fetch status for latest uploaded document
                    $file_status = null;
                    if ($last_doc_id) {
                        $status_data = $wpdb->get_row($wpdb->prepare(
                            "SELECT * FROM $doc_uplod_status WHERE doc_id = %d ORDER BY id DESC LIMIT 1",
                            $last_doc_id
                        ));
                        if ($status_data) {
                            $file_status = $status_data->doc_status;
                        }
                    }

                    // Check if document is applicable
                    $isapplicable = $wpdb->get_row($wpdb->prepare(
                        "SELECT is_applicable FROM {$wpdb->prefix}leads_stc_documents_additional_info 
                     WHERE doc_key = %s AND lead_id = %d",
                        $doc_key . '_applicable', $lead_id
                    ));
                    $applicable = $isapplicable->is_applicable ?? 'Yes';

                    // Get comments/notes
                    $notes = $wpdb->get_results($wpdb->prepare(
                        "SELECT n.*, u.display_name AS username, DATE_FORMAT(n.update_datetime, '%%m/%%d/%%Y %%h:%%i:%%s') AS update_datetime 
                     FROM $doc_notes_table n
                     LEFT JOIN {$wpdb->users} u ON n.user_id = u.ID
                     WHERE n.doc_type_id = %d AND n.lead_id = %d",
                        $doc_type_id, $lead_id
                    ));

                    // Add to response array
                    $documents_group2[] = [
                        'doc_type_id' => $doc_type_id,
                        'doc_key' => $doc_key,
                        'doc_label' => $doc_label,
                        'file_status' => $file_status,
                        'applicable' => $applicable,
                        'file_url' => $file_url,
                        'file_name' => $file_name,
                        'comments' => $notes,
                    ];
                }

            }

            // Third group: Documents supporting Covid-19 Illness - Self
            $doc_type_ids_group3 = in_array($lead_id, $leadArray) ? [48, 49] : [49];
            $documents_group3 = [];

            if (!empty($doc_type_ids_group3)) {
                $placeholders = implode(',', array_fill(0, count($doc_type_ids_group3), '%d'));
                $sql = "SELECT * FROM $doc_mapping_table WHERE form_id = %d AND doc_type_id IN ($placeholders)";
                $params = array_merge([$form_id], $doc_type_ids_group3);
                $query = call_user_func_array([$wpdb, 'prepare'], array_merge([$sql], $params));
                $covid_docs = $wpdb->get_results($query);

                foreach ($covid_docs as $doc) {
                    $doc_type_id = $doc->doc_type_id;
                    $doc_key = $doc->doc_key;
                    $doc_label = substr($doc->doc_label, strrpos($doc->doc_label, '-') + 1);

                    // Get uploaded file
                    $upload = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM $doc_upload_table WHERE lead_id = %d AND deleted = 0 AND doc_type_id = %d ORDER BY id DESC LIMIT 1",
                        $lead_id,
                        $doc_type_id
                    ));

                    $last_result = $wpdb->get_results($wpdb->prepare(
                        "SELECT * FROM $doc_upload_table WHERE lead_id = %d AND doc_type_id = %d ORDER BY id DESC LIMIT 1",
                        $lead_id,
                        $doc_type_id
                    ));

                    $last_doc_id = !empty($last_result) ? $last_result[0]->id : null;

                    // Fetch status for latest uploaded document
                    $file_status = null;
                    if ($last_doc_id) {
                        $status_data = $wpdb->get_row($wpdb->prepare(
                            "SELECT * FROM $doc_uplod_status WHERE doc_id = %d ORDER BY id DESC LIMIT 1",
                            $last_doc_id
                        ));
                        if ($status_data) {
                            $file_status = $status_data->doc_status;
                        }
                    }

                    // Notes
                    $notes = $wpdb->get_results($wpdb->prepare(
                        "SELECT n.*, u.display_name AS username, DATE_FORMAT(n.update_datetime, '%%m/%%d/%%Y %%h:%%i:%%s') AS update_datetime
                     FROM $doc_notes_table n
                     LEFT JOIN {$wpdb->users} u ON n.user_id = u.ID
                     WHERE n.doc_type_id = %d AND n.lead_id = %d",
                        $doc_type_id,
                        $lead_id
                    ));


                    // ✅ Applicable check from custom table
                    $isapplicable = $wpdb->get_row($wpdb->prepare(
                        "SELECT * FROM {$wpdb->prefix}leads_stc_documents_additional_info WHERE doc_key = %s AND lead_id = %d",
                        $doc_key . '_applicable',
                        $lead_id
                    ));
                    $applicable = $isapplicable->is_applicable ?? 'Yes';

                    $documents_group3[] = [
                        'doc_type_id' => $doc_type_id,
                        'doc_key' => $doc_key,
                        'doc_label' => $doc_label,
                        'applicable' => $applicable,
                        'file_status' => $file_status,
                        'file_url' => $upload->uploded_documents ?? '',
                        'file_name' => $upload->local_path_filename ?? '',
                        'status' => $file_status,
                        'comments' => $notes,
                    ];
                }

            }

            // Group 4: Self Employment Tax Credit Question
            $group4_question = [];
            $doc_type_id = 139;

            $tax_credit_table = $wpdb->prefix . 'leads_document_claimed_for_tax_credit';

            // Get claimed status from DB
            $tax_credit_data = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM $tax_credit_table WHERE doc_type_id = %d AND lead_id = %d",
                $doc_type_id,
                $lead_id
            ));

            $claimed_for_tax_credit = isset($tax_credit_data->have_you_claimed) ? $tax_credit_data->have_you_claimed : '';
            $amount_2020 = isset($tax_credit_data->amount_2020) ? $tax_credit_data->amount_2020 : '';
            $amount_2021 = isset($tax_credit_data->amount_2021) ? $tax_credit_data->amount_2021 : '';


            // Push to Group 4 array
            $group4_question[] = [
                'doc_type_id' => $doc_type_id,
                'question' => 'Have you claimed for self employment tax credit for 2020 or 2021?',
                'claimed' => $claimed_for_tax_credit,
                'amount_2020' => $amount_2020,
                'amount_2021' => $amount_2021,
            ];

            // Group 5: TaxNow Sign-up
            $group5 = [];
            $doc_type_id = 138;
            // Get latest uploaded doc
            $uploaded = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM $doc_upload_table 
            WHERE lead_id = %d AND deleted = 0 AND doc_type_id = %d 
            ORDER BY id DESC LIMIT 1
        ", $lead_id, $doc_type_id));

            $doc_id = $uploaded->id ?? '';
            $file_name = $uploaded->local_path_filename ?? '';
            $file_url = $uploaded->uploded_documents ?? '';

            // Required status
            $required_map = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM $doc_required_mapping 
            WHERE doc_type_id = %d AND lead_id = %d
        ", $doc_type_id, $lead_id));
            $required_flag = $required_map->required_flag ?? null;

            // TaxNow sign-up status
            $taxnow_data = $wpdb->get_row($wpdb->prepare("
            SELECT * FROM $document_taxnow_status 
            WHERE doc_type_id = %d AND lead_id = %d
        ", $doc_type_id, $lead_id));
            $taxnow_status = $taxnow_data->taxnow_signup_status ?? null;

            // Determine status label
            if ($taxnow_status == '1') {
                $status_label = 'Signed Up';
            } elseif ($taxnow_status == '0' && $required_flag == '0') {
                $status_label = 'Sign Up Pending';
            } elseif ($taxnow_status == '0' && $required_flag == '1') {
                $status_label = 'In Review';
            } elseif ($required_flag == '0') {
                $status_label = 'In Review';
            } elseif ($required_flag == '1') {
                $status_label = 'In Review';
            } else {
                $status_label = 'Pending';
            }

            $documents_group5[] = [
                'doc_label' => 'Initiate Sign-Up (TaxNow)',
                'doc_type_id' => $doc_type_id,
                'signed_up' => $required_flag,
                'doc_id' => $doc_id,
                'file_name' => $file_name,
                'file_url' => $file_url,
                'status' => $status_label,
                'signed_up_taxnow_file' => $taxnow_status, // 1 = Yes, 0 = No
            ];


            $groups = [
                [
                    'heading' => 'Tax Returns - 1040',
                    'documents' => $documents_group1,
                ],
                [
                    'heading' => 'W2 (If Applicable)',
                    'documents' => $documents_group2,
                ],
                [
                    'heading' => 'Form - 1099 G (If Applicable)',
                    'documents' => $documents_group3,
                ],
                [
                    'heading' => 'Have you claimed for Self Employment Tax Credit',
                    'documents' => $group4_question,
                ],
                [
                    'heading' => 'TaxNow Sign-up',
                    'documents' => $documents_group5,
                ]

            ];

            if($product_id ==  937){
                $document_folder_link = $this->get_document_folder_link_only($lead_id, 937);
            }else{
                $document_folder_link = $this->get_document_folder_link_only($lead_id,'');
            }

            return rest_ensure_response([
                'lead_id' => $lead_id,
                'product_id' => $product_id,
                'view_document' => $document_folder_link,
                'groups' => $groups,
            ]);
        } else {
            return new WP_REST_Response(['error' => 'Something went wrong with data.'], 400);
        }
    }

    function get_stc_impacted_days(WP_REST_Request $request){
        global $wpdb;

        $project_id = $request->get_param('project_id');
        if (!$project_id) {
            return new WP_REST_Response(['error' => 'Project ID is required.'], 400);
        }

        $project_row = $wpdb->get_row($wpdb->prepare("
            SELECT lead_id, product_id 
            FROM {$wpdb->prefix}projects 
            WHERE project_id = %d
            ", $project_id));

        if ($project_row) {
            $product_id = $project_row->product_id;
            $lead_id = $project_row->lead_id;
            $lead_id = (int)$lead_id;

            $form_id = 5;
            $leadArray = [9451, 9432, 9430, 9425, 9407, 9405, 9400, 9399, 9390, 9386,
                9380, 9373, 9346, 9345, 9342, 9341, 9339, 9197, 9180, 9149,
                8933, 8862, 8816, 8637];

            $tbl_upload = $wpdb->prefix . 'leads_document_upload';
            $tbl_notes = $wpdb->prefix . 'leads_document_notes';
            $tbl_status = $wpdb->prefix . 'leads_document_status';
            $tbl_days = $wpdb->prefix . 'leads_impacted_days';
            $tbl_mapping = $wpdb->prefix . 'leads_document_mapping';
            $impacted_table = $wpdb->prefix . 'leads_impacted_dates';
            $impacted_days_table = $wpdb->prefix . 'impacted_date_ranges';

            if (in_array($lead_id, $leadArray, true)) {
                $company_docs = $wpdb->get_results(
                    "SELECT * FROM $tbl_mapping WHERE form_id = $form_id AND doc_type_id IN (52,53,54) ORDER BY FIELD(doc_type_id,52,53,54)"
                );
            } else {
                $company_docs = $wpdb->get_results(
                    "SELECT * FROM $tbl_mapping WHERE form_id = $form_id AND doc_type_id IN (53,54) ORDER BY FIELD(doc_type_id,53,54)"
                );
            }

            $row_map_full = [
                0 => ['self_time_off_days_april_2020', 'Total number of time off days between April 1, 2020, and December 31, 2020'],
                1 => ['self_time_off_days_january_2021', 'Total number of time off days between January 1, 2021, and March 31, 2021'],
                2 => ['self_time_off_days_april_2021', 'Total number of time off days between April 1, 2021, and September 30, 2021'],
            ];

            $row_offset = in_array($lead_id, $leadArray, true) ? 0 : 1;

            $date_results = $wpdb->get_results("SELECT * FROM $impacted_table WHERE lead_id = $lead_id");
            $date_values = [];
            foreach ($date_results as $row) {
                $date_values[$row->field_title] = $row->field_value;
                if (strpos($row->field_title, '_un_benefit') === false) {
                    $date_values[$row->field_title . '_checkbox'] = $row->unemp_benefit_claimed;
                }
            }


            $_2020_Q2_Q3_Q4_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $impacted_table
            WHERE lead_id = %s 
            AND field_title REGEXP '^[0-9]{4}Q2_Q3_Q4[0-9]{1,2}$'
            AND field_value <> ''",
                $lead_id
            ));


            $_2021_Q1_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $impacted_table 
             WHERE lead_id = %d 
             AND field_title REGEXP '^2021_Q1_(10|[1-9])$'
             AND field_value <> ''",
                $lead_id
            ));

            $_2021_Q2_Q3_count = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM $impacted_table 
             WHERE lead_id = %d 
             AND field_title REGEXP '^2021_Q2_Q3_(10|[1-9])$'
             AND field_value <> ''",
                $lead_id
            ));

            $quarter_mappings = [
                ['prefix' => '2020_Q2_Q3_Q4_', 'count' => $_2020_Q2_Q3_Q4_count],
                ['prefix' => '2021_Q1_', 'count' => $_2021_Q1_count],
                ['prefix' => '2021_Q2_Q3_', 'count' => $_2021_Q2_Q3_count],
            ];

            $all_dates = [];
            foreach ($quarter_mappings as $map) {
                for ($i = 1; $i <= $map['count']; $i++) {
                    $date_field = $map['prefix'] . $i;
                    if (!empty($date_values[$date_field])) {
                        $all_dates[] = $date_values[$date_field];
                    }
                }
            }

            $impacted_days_results = $wpdb->get_results("SELECT * FROM $impacted_days_table WHERE lead_id = $lead_id AND doc_key > 0");

            $impacted_days_grouped_data = [];
            foreach ($impacted_days_results as $item) {
                $doc_key = $item->doc_key;
                $start = $item->start_date;
                $end = $item->end_date;

                if (!empty($doc_key) && !empty($start) && !empty($end)) {
                    $range_string = date('m/d/Y', strtotime($start)) . ' - ' . date('m/d/Y', strtotime($end));
                    $impacted_days_grouped_data[$doc_key][] = $range_string;
                }
            }


            $self_documents = [];
            $i = 1;
            foreach ($company_docs as $idx => $doc_row) {
                $map_idx = $idx + $row_offset;
                [$time_key, $label] = $row_map_full[$map_idx];

                $doc_type_id = (int)$doc_row->doc_type_id;

                // Match date ranges by doc_type_id
                $matched_dates = isset($impacted_days_grouped_data[$i])
                    ? $impacted_days_grouped_data[$i]
                    : [];

                $imp_row = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $tbl_days WHERE lead_id = %d AND type = %s AND time_of_days = %s LIMIT 1",
                    $lead_id, 'self', $time_key
                ));

                $upload = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $tbl_upload WHERE lead_id = %d AND doc_type_id = %d AND deleted = 0 ORDER BY id DESC LIMIT 1",
                    $lead_id, $doc_type_id
                ));

                $doc_id = $upload->id ?? null;
                $file_url = $upload->uploded_documents ?? '';
                $file_name = $upload->local_path_filename ?? '';

                $status = 'Pending';
                if ($doc_id) {
                    $st = $wpdb->get_var($wpdb->prepare(
                        "SELECT doc_status FROM $tbl_status WHERE doc_id = %d ORDER BY id DESC LIMIT 1",
                        $doc_id
                    ));
                    $status = in_array($st, ['Approved', 'Rejected', 'In review']) ? $st : 'Pending';
                }

                $self_doc = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $tbl_days WHERE lead_id = %d AND type = %s AND time_of_days = %s",
                    $lead_id, 'self', $time_key
                ));

                $comments = $wpdb->get_results($wpdb->prepare(
                    "SELECT n.*, u.display_name AS username, DATE_FORMAT(n.update_datetime, '%%m/%%d/%%Y %%h:%%i:%%s') AS update_datetime
                 FROM $tbl_notes n
                 LEFT JOIN {$wpdb->users} u ON n.user_id = u.ID
                 WHERE n.doc_type_id = %d AND n.lead_id = %d",
                    $doc_type_id,
                    $lead_id
                ));

                $self_documents[] = [
                    'question' => $label,
                    'time_of_days_key' => $time_key,
                    'no_of_days' => (int)($imp_row->no_of_days ?? 0),
                    'undertaking_checked' => isset($self_doc->undertaking) && $self_doc->undertaking == 1,
                    'doc_type_id' => $doc_type_id,
                    'doc_id' => $doc_id,
                    'file_url' => $file_url,
                    'file_name' => $file_name,
                    'status' => $status,
                    'comments' => $comments,
                    'dates' => $matched_dates, // 👈 assign only matched dates
                ];

                $i++;
            }


            /* =====================================================================
               GROUP-2  ──  OTHERS  (unchanged from your last version)
               ===================================================================== */

            if (in_array($lead_id, $leadArray, true)) {
                $company_docs_oth = $wpdb->get_results(
                    "SELECT * FROM $tbl_mapping WHERE form_id = $form_id AND doc_type_id IN (55,56,57)"
                );
            } else {
                $company_docs_oth = $wpdb->get_results(
                    "SELECT * FROM $tbl_mapping WHERE form_id = $form_id AND doc_type_id IN (56,57)"
                );
            }

            $others_time_off_map = [
                55 => [
                    'key' => 'other_time_off_days_april_2020',
                    'label' => 'Total number of time off days(max 50) between April 1, 2020, and December 31, 2020',
                    'max_days' => 274,
                ],
                56 => [
                    'key' => 'other_time_off_days_january_2021',
                    'label' => 'Total number of time off days(max 50) between January 1, 2021, and March 31, 2021',
                    'max_days' => 90,
                ],
                57 => [
                    'key' => 'other_time_off_days_april_2021',
                    'label' => 'Total number of time off days(max 60) between April 1, 2021, and September 30, 2021',
                    'max_days' => 182,
                ],
            ];

            $others_documents = [];
            foreach ($company_docs_oth as $doc_row) {
                $doc_type_id = (int)$doc_row->doc_type_id;
                if (!isset($others_time_off_map[$doc_type_id])) continue;

                $map = $others_time_off_map[$doc_type_id];

                $imp_row = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $tbl_days WHERE lead_id = %d AND type = %s AND time_of_days = %s LIMIT 1",
                    $lead_id, 'others', $map['key']
                ));

                $upload = $wpdb->get_row($wpdb->prepare(
                    "SELECT * FROM $tbl_upload WHERE lead_id = %d AND doc_type_id = %d AND deleted = 0 ORDER BY id DESC LIMIT 1",
                    $lead_id, $doc_type_id
                ));

                $doc_id = $upload->id ?? null;
                $file_url = $upload->uploded_documents ?? '';
                $file_name = $upload->local_path_filename ?? '';

                $status = 'Pending';
                if ($doc_id) {
                    $st = $wpdb->get_var($wpdb->prepare(
                        "SELECT doc_status FROM $tbl_status WHERE doc_id = %d ORDER BY id DESC LIMIT 1",
                        $doc_id
                    ));

                    if ($st === 'In review') {
                        $status = 'In review';
                    } elseif ($st === 'Approved') {
                        $status = 'Approved';
                    } elseif ($st === 'Rejected') {
                        $status = 'Rejected';
                    } else {
                        // If document uploaded but no valid status found
                        $status = 'Pending';
                    }
                } else {
                    $last_status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $tbl_status WHERE doc_id = '$last_doc_id' ORDER BY id DESC limit 1; "));
                    $last_file_status = "";
                    $last_file_status = $last_status_data[0]->doc_status;
                    $status = $last_file_status;
                }

                $comments = $wpdb->get_results($wpdb->prepare(
                    "SELECT n.*, u.display_name AS username, DATE_FORMAT(n.update_datetime, '%%m/%%d/%%Y %%h:%%i:%%s') AS update_datetime
                         FROM $tbl_notes n
                         LEFT JOIN {$wpdb->users} u ON n.user_id = u.ID
                         WHERE n.doc_type_id = %d AND n.lead_id = %d",
                    $doc_type_id,
                    $lead_id
                ));


                $others_documents[] = [
                    'question' => $map['label'],
                    'time_of_days_key' => $map['key'],
                    'max_days' => $map['max_days'],
                    'no_of_days' => (int)($imp_row->no_of_days ?? 0),
                    'doc_type_id' => $doc_type_id,
                    'doc_id' => $doc_id,
                    'file_url' => $file_url,
                    'file_name' => $file_name,
                    'status' => $status,
                    'comments' => $comments
                ];
            }

            /* -------- filter visibility flags -------- */
            $show_self = array_filter($self_documents, fn($d) => $d['no_of_days'] > 0);
            $show_others = array_filter($others_documents, fn($d) => $d['no_of_days'] > 0);

            if($product_id ==  937){
                $document_folder_link = $this->get_document_folder_link_only($lead_id, 937);
            }else{
                $document_folder_link = $this->get_document_folder_link_only($lead_id,'');
            }

            return rest_ensure_response([
                'lead_id' => $lead_id,
                'product_id' => $product_id,
                'view_document'=> $document_folder_link,
                'groups' => [
                    [
                        'group_heading' => 'Impacted days for self (Note: Please upload any supporting documents for the period if available)',
                        'show_group' => !empty($show_self),
                        'documents' => $self_documents,
                    ],
                    [
                        'group_heading' => 'Impacted days for others (Note: Please upload any supporting documents for the period if available)',
                        'show_group' => !empty($show_others),
                        'undertaking_checked' => isset($imp_row->undertaking) && $imp_row->undertaking == 1,
                        'documents' => $others_documents,
                    ],
                ],
            ]);
        }
        else {
            return new WP_REST_Response(['error' => 'Something went wrong with data.'], 400);
        }
    }

    function getDatesInRange($start_date, $end_date)
    {
        $dates = [];

        if (!$start_date || !$end_date) {
            return $dates; // early exit on invalid input
        }

        try {
            $start = new DateTime($start_date);
            $end = new DateTime($end_date);
            $interval = new DateInterval('P1D');
            $period = new DatePeriod($start, $interval, $end->modify('+1 day'));

            foreach ($period as $date) {
                $dates[] = $date->format('Y-m-d');
            }
        } catch (Exception $e) {
            error_log('getDatesInRange error: ' . $e->getMessage());
        }

        return $dates;
    }

    function get_ta_required_documents(WP_REST_Request $request)
    {
        global $wpdb;
        $project_id = $request->get_param('project_id');
        $is_view = $request->get_param('is_view') ?? 0;
        if (!$project_id) {
            return new WP_REST_Response(['error' => 'Project ID is required.'], 400);
        }

        $project_row = $wpdb->get_row($wpdb->prepare("
            SELECT lead_id, product_id 
            FROM {$wpdb->prefix}projects 
            WHERE project_id = %d
            ", $project_id));

        if ($project_row) {
            $lead_id = $project_row->lead_id;

            $form_id = 7;

            $doc_mapping_table = $wpdb->prefix . 'leads_document_mapping';
            $doc_uplod_table = $wpdb->prefix . 'leads_document_upload';
            $doc_uplod_notes = $wpdb->prefix . 'leads_document_notes';
            $doc_uplod_status = $wpdb->prefix . 'leads_document_status';

            $company_docs = $wpdb->get_results(
                $wpdb->prepare("SELECT * FROM $doc_mapping_table WHERE form_id = %d", $form_id)
            );

            $documents_group1 = [];
            $documents_group2 = [];
            $documents_group3 = [];
            $documents_group4 = [];

            $m = 1;
            foreach ($company_docs as $value) {
                if (($m >= 9 && $m <= 10) || ($m >= 11 && $m <= 12) || $m == 13 || $m == 14) {
                    $doc_type_id = $value->doc_type_id;
                    $doc_key = $value->doc_key;
                    $doc_label = $value->doc_label;

                    $year = '';
                    if (strpos($doc_label, '-') !== false) {
                        $year = trim(substr($doc_label, strrpos($doc_label, '-') + 1));
                    }

                    $result = $wpdb->get_results(
                        $wpdb->prepare(
                            "SELECT * FROM $doc_uplod_table WHERE lead_id = %d AND deleted = 0 AND doc_type_id = %d ORDER BY id DESC LIMIT 1",
                            $lead_id,
                            $doc_type_id
                        )
                    );

                    $file_url = !empty($result) ? $result[0]->uploded_documents : '';
                    $file_name = !empty($result) ? $result[0]->local_path_filename : '';
                    $doc_id = !empty($result) ? $result[0]->id : 0;

                    $comment_data = $wpdb->get_results(
                        $wpdb->prepare(
                            "SELECT * FROM $doc_uplod_notes WHERE doc_type_id = %d AND lead_id = %d",
                            $doc_type_id,
                            $lead_id
                        )
                    );

                    $file_status = 'Yet to upload';
                    if ($doc_id) {
                        $status_data = $wpdb->get_results(
                            $wpdb->prepare(
                                "SELECT * FROM $doc_uplod_status WHERE doc_id = %d ORDER BY id DESC LIMIT 1",
                                $doc_id
                            )
                        );
                        if (!empty($status_data)) {
                            $file_status = $status_data[0]->doc_status;
                        }
                    }

                    $doc_entry = [
                        'doc_type_id' => $doc_type_id,
                        'doc_key' => $doc_key,
                        'doc_label' => $doc_label,
                        'year' => $year,
                        'file_url' => $file_url,
                        'file_name' => $file_name,
                        'doc_id' => $doc_id,
                        'file_status' => $file_status,
                        'comments_count' => count($comment_data),
                    ];

                    if ($m >= 9 && $m <= 10) {
                        $documents_group1[] = $doc_entry;
                    } elseif ($m >= 11 && $m <= 12) {
                        $documents_group2[] = $doc_entry;
                    } elseif ($m == 13) {
                        $documents_group3[] = $doc_entry;
                    } elseif ($m == 14) {
                        $documents_group4[] = $doc_entry;
                    }
                }
                $m++;
            }

            $groups = [
                [
                    'heading' => "All 941-X's filed",
                    'documents' => $documents_group1,
                ],
                [
                    'heading' => "All Letters of overpayment received",
                    'documents' => $documents_group2,
                ],
                [
                    'heading' => "Photo ID, preferably driver license, of the authorized signer",
                    'documents' => $documents_group3,
                ],
                [
                    'heading' => "In the case of a joint 1040 filer, the photo ID of the spouse, as well.",
                    'documents' => $documents_group4,
                ]
            ];

            return rest_ensure_response([
                'lead_id' => $lead_id,
                'groups' => $groups,
            ]);
        }else {
            return new WP_REST_Response(['error' => 'Something went wrong with data.'], 400);
        }
    }

    function get_ta_additional_documents(WP_REST_Request $request){
        global $wpdb;

        $form_id = 8;
        $project_id = $request->get_param('project_id');
        $is_view = $request->get_param('is_view') ?? 0;
        if (!$project_id) {
            return new WP_REST_Response(['error' => 'Project ID is required.'], 400);
        }

        $project_row = $wpdb->get_row($wpdb->prepare("
            SELECT lead_id, product_id 
            FROM {$wpdb->prefix}projects 
            WHERE project_id = %d
            ", $project_id));

        if ($project_row) {
            $lead_id = $project_row->lead_id;

            $doc_mapping_table = $wpdb->prefix . 'leads_document_mapping';
            $doc_uplod_table = $wpdb->prefix . 'leads_document_upload';
            $doc_uplod_notes = $wpdb->prefix . 'leads_document_notes';
            $doc_uplod_status = $wpdb->prefix . 'leads_document_status';

            $company_docs = $wpdb->get_results(
                $wpdb->prepare("SELECT * FROM $doc_mapping_table WHERE form_id = %d", $form_id)
            );

            $documents_group1 = [];
            $documents_group2 = [];
            $documents_group3 = [];
            $documents_group4 = [];
            $documents_group5 = [];

            $m = 1;
            foreach ($company_docs as $value) {
                $doc_type_id = $value->doc_type_id;
                $doc_key = $value->doc_key;
                $doc_label = $value->doc_label;

                // Check for year in label
                $year = '';
                if (strpos($doc_label, '-') !== false) {
                    $year = trim(substr($doc_label, strrpos($doc_label, '-') + 1));
                }

                // Uploaded file data
                $result = $wpdb->get_results(
                    $wpdb->prepare(
                        "SELECT * FROM $doc_uplod_table WHERE lead_id = %d AND deleted = 0 AND doc_type_id = %d ORDER BY id DESC LIMIT 1",
                        $lead_id,
                        $doc_type_id
                    )
                );

                $file_url = !empty($result) ? $result[0]->uploded_documents : '';
                $file_name = !empty($result) ? $result[0]->local_path_filename : '';
                $doc_id = !empty($result) ? $result[0]->id : 0;

                // Comments count
                $comment_data = $wpdb->get_results(
                    $wpdb->prepare(
                        "SELECT n.*, u.display_name AS username
                     FROM $doc_uplod_notes n
                     LEFT JOIN $wpdb->users u ON n.user_id = u.ID
                     WHERE n.doc_type_id = %d AND n.lead_id = %d",
                        $doc_type_id,
                        $lead_id
                    )
                );


                // Status
                $file_status = 'Yet to upload';
                if ($doc_id) {
                    $status_data = $wpdb->get_results(
                        $wpdb->prepare(
                            "SELECT * FROM $doc_uplod_status WHERE doc_id = %d ORDER BY id DESC LIMIT 1",
                            $doc_id
                        )
                    );
                    if (!empty($status_data)) {
                        $file_status = $status_data[0]->doc_status;
                    }
                }

                // Get is_applicable for m=8 (group 5)
                $is_applicable = '';
                if ($m == 8) {
                    $applicable_row = $wpdb->get_row(
                        $wpdb->prepare(
                            "SELECT applicable FROM eccom_tax_amendment_additional_documents WHERE document_id = %s AND lead_id = %d",
                            $doc_key,
                            $lead_id
                        )
                    );
                    $is_applicable = $applicable_row ? strtoupper($applicable_row->applicable) : '';
                }

                // Prepare document entry
                $doc_entry = [
                    'doc_type_id' => $doc_type_id,
                    'doc_key' => $doc_key,
                    'doc_label' => $doc_label,
                    'year' => $year,
                    'file_url' => $file_url,
                    'file_name' => $file_name,
                    'doc_id' => $doc_id,
                    'file_status' => $file_status,
                    'comments_count' => count($comment_data),
                ];

                if ($m == 8) {
                    $doc_entry['is_applicable'] = $is_applicable;
                }

                // Assign to proper group
                if ($m >= 1 && $m <= 2) {
                    $documents_group1[] = $doc_entry;
                } else if ($m >= 3 && $m <= 4) {
                    $documents_group2[] = $doc_entry;
                } else if ($m >= 5 && $m <= 6) {
                    $documents_group3[] = $doc_entry;
                } else if ($m == 7) {
                    $documents_group4[] = $doc_entry;
                } else if ($m == 8) {
                    $documents_group5[] = $doc_entry;
                }

                $m++;
            }

            $groups = [
                [
                    'heading' => "Original 1040 filed",
                    'documents' => $documents_group1,
                ],
                [
                    'heading' => "Original K-1 received",
                    'documents' => $documents_group2,
                ],
                [
                    'heading' => "Amended K-1 received",
                    'documents' => $documents_group3,
                ],
                [
                    'heading' => "Photo ID of the authorized signer",
                    'documents' => $documents_group4,
                ],
                [
                    'heading' => "Photo ID of spouse (for joint 1040 filers)",
                    'documents' => $documents_group5,
                ]
            ];

            return rest_ensure_response([
                'lead_id' => $lead_id,
                'groups' => $groups,
            ]);
        }else {
            return new WP_REST_Response(['error' => 'Something went wrong with data.'], 400);
        }
    }

}

// Initialize the API
new CRM_ERP_Documents_API();

