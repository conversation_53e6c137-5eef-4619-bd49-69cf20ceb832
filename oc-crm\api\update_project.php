<?php
global $wpdb;
$data = $request->get_json_params();
$table_name = $wpdb->prefix . 'projects';
$project_id = $data['project_id'];
$user_id = $data['user_id'];

$project = $wpdb->get_row("SELECT {$wpdb->prefix}projects.lead_id
                                                                    FROM {$wpdb->prefix}projects 
                                                                    WHERE {$wpdb->prefix}projects.deleted_at IS NULL 
                                                                AND {$wpdb->prefix}projects.project_id = ".$project_id." ");
$lead_id = 0;
if(!empty($project)){
  $lead_id = $project->lead_id;
}
if($lead_id > 0){
    $project_fee = $data['project_fee'];
    $review_status = $data['review_status'];
    $review_link = $data['review_link'];
    $authorized_signatory_name = $data['authorized_signatory_name'];
    $business_phone = $data['business_phone'];
    $business_email = $data['business_email'];
    $business_title = $data['business_title'];
    $zip = $data['zip'];
    $street_address = $data['street_address'];
    $city = $data['city'];
    $state = $data['state'];
    $identity_document_type = $data['identity_document_type'];
    $identity_document_number = $data['identity_document_number'];
    $business_legal_name = $data['business_legal_name'];
    $doing_business_as = $data['doing_business_as'];
    $business_category = $data['business_category'];
    $website_url = $data['website_url'];
    $business_entity_type = $data['business_entity_type'];
    $registration_number = $data['registration_number'];
    $registration_date = $data['registration_date'];
    $state_of_registration = $data['state_of_registration'];


    $invoiceActions = $data['invoiceActions'];

    if(!empty($invoiceActions)){
        foreach($invoiceActions as $invoice_id => $action_id){
            $wpdb->query("UPDATE {$wpdb->prefix}invoices SET 
                status = '".$action_id."',
                updated_date = '".date('Y-m-d H:i:s')."'
                WHERE id = ".$invoice_id."");
        }
    }

    //Upate Project Info
    $project_info = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}projects WHERE project_id = ".$project_id."");  
    $project_table = $wpdb->prefix.'projects';  
    $new_project_data = array();
    $new_project_data['project_fee'] = $project_fee;
    
    if(isset($data['review_link'])){
        $new_project_data['review_link'] = $data['review_link'];
    }
    
    if(isset($data['review_status'])){
        $new_project_data['review_status'] = $data['review_status'];
    }

    $this->project_log_audit_entry($user_id,$lead_id, $project_info, $new_project_data,$project_table);   
    // $wpdb->query("UPDATE {$wpdb->prefix}projects SET 
    //                                                 project_fee='".$project_fee."' 
    //                                                 WHERE project_id=$project_id");

        
        $result = $wpdb->update($project_table, $new_project_data, array('project_id' => $project_id));

    
    //Update Project Info
    $business_info = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}erc_business_info WHERE lead_id = ".$lead_id."");   
    $new_busines_data = array();
    $new_busines_data['authorized_signatory_name'] = $authorized_signatory_name;
     $new_busines_data['business_phone'] = $business_phone;
     $new_busines_data['business_email'] = $business_email;
     $new_busines_data['business_title'] = $business_title;
     $new_busines_data['zip'] = $zip;
     $new_busines_data['street_address'] = $street_address;
     $new_busines_data['city'] = $city;
     $new_busines_data['state'] = $state;                  
     $new_busines_data['identity_document_type'] = esc_attr($identity_document_type);
     $new_busines_data['identity_document_number'] = $identity_document_number;
     $new_busines_data['business_legal_name'] = $business_legal_name;
     $new_busines_data['doing_business_as'] = $doing_business_as;
     $new_busines_data['business_category'] = $business_category;
     $new_busines_data['website_url'] = $website_url;
     $new_busines_data['business_entity_type'] = $business_entity_type;
     $new_busines_data['registration_number'] = $registration_number;
     $new_busines_data['registration_date'] = $registration_date;
     $new_busines_data['state_of_registration'] = $state_of_registration;

     $buss_info_table = $wpdb->prefix.'erc_business_info'; 
     $this->project_log_audit_entry($user_id,$lead_id, $business_info, $new_busines_data,$buss_info_table);                                            
    $wpdb->query("UPDATE {$wpdb->prefix}erc_business_info SET 
                                                        authorized_signatory_name = '".$authorized_signatory_name."',
                                                        business_phone = '".$business_phone."',
                                                        business_email = '".$business_email."',
                                                        business_title = '".$business_title."',                                                        
                                                        zip = '".$zip."',
                                                        street_address = '".$street_address."',
                                                        city = '".$city."',
                                                        state = '".$state."',
                                                        identity_document_type = '".esc_attr($identity_document_type)."',
                                                        identity_document_number = '".$identity_document_number."',
                                                        business_legal_name = '".$business_legal_name."',
                                                        doing_business_as = '".$doing_business_as."',
                                                        business_category = '".$business_category."',
                                                        website_url = '".$website_url."',
                                                        business_entity_type = '".$business_entity_type."',
                                                        registration_number = '".$registration_number."',
                                                        registration_date = '".$registration_date."',
                                                        state_of_registration = '".$state_of_registration."'
                                                        WHERE lead_id = ".$lead_id."");

    //Update Bank Info
    if(isset($data['bank_name'])){ 
        $bank_info = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}erc_bank_info WHERE lead_id = ".$lead_id."");
        if(!empty($bank_info)){
          $new_bank_data = array();
          $new_bank_data['bank_name'] = $data['bank_name'];
           $new_bank_data['bank_mailing_address'] = $data['bank_mailing_address'];
           $new_bank_data['city'] = $data['bank_city'];
           $new_bank_data['state'] = $data['bank_state'];
           $new_bank_data['zip'] = $data['bank_zip'];
           $new_bank_data['country'] = $data['country'];
           $new_bank_data['bank_phone'] = $data['bank_phone'];
           $new_bank_data['account_holder_name'] = $data['account_holder_name'];                  
           $new_bank_data['account_type'] = $data['account_type'];
           $new_bank_data['other'] = $data['other'];
           $new_bank_data['aba_routing_no'] = $data['aba_routing_no'];
           $new_bank_data['account_number'] = $data['account_number'];
           $new_bank_data['swift'] = $data['swift'];
           $new_bank_data['iban'] = $data['iban'];

           $bank_table = $wpdb->prefix.'erc_bank_info'; 
           $this->project_log_audit_entry($user_id,$lead_id, $bank_info, $new_bank_data,$bank_table);
          $wpdb->query("UPDATE {$wpdb->prefix}erc_bank_info SET 
                               bank_name = '".$data['bank_name']."',
                               bank_mailing_address = '".$data['bank_mailing_address']."',
                               city = '".$data['bank_city']."',
                               state = '".$data['bank_state']."',
                               zip = '".$data['bank_zip']."',
                               country = '".$data['country']."',
                               bank_phone = '".$data['bank_phone']."',
                               account_holder_name = '".$data['account_holder_name']."',                                  
                               account_type = '".$data['account_type']."',
                               other = '".$data['other']."',
                               aba_routing_no = '".$data['aba_routing_no']."',
                               account_number = '".$data['account_number']."',
                               swift = '".$data['swift']."',
                               iban = '".$data['iban']."'
                               WHERE lead_id = ".$lead_id."
                            ");
        }else{
            $wpdb->query("INSERT INTO {$wpdb->prefix}erc_bank_info (lead_id,bank_name,bank_mailing_address,city,state,zip,country,bank_phone,account_holder_name,account_type,other,aba_routing_no,account_number,swift,iban) VALUES (".$lead_id.",'".$data['bank_name']."','".$data['bank_mailing_address']."','".$data['bank_city']."','".$data['bank_state']."','".$data['bank_zip']."','".$data['country']."','".$data['bank_phone']."', '".$data['account_holder_name']."', '".$data['account_type']."', '".$data['other']."','".$data['aba_routing_no']."', '".$data['account_number']."', '".$data['swift']."', '".$data['iban']."')");
        }
    }

    //Update Intake Tab
    $new_intake_data = array();
    if(isset($data['w2_employees_count'])){
        $intake_info = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}erc_erc_intake WHERE lead_id = ".$lead_id."");
        if(!empty($intake_info)){
          if($data['fee_type'] == 'N/A'){
            $fee_type = $data['custom_fee'];
          }else{
            $fee_type = $data['fee_type'];
          }
          $new_intake_data['w2_employees_count'] = $data['w2_employees_count'];
          $new_intake_data['initial_retain_fee_amount'] = $data['initial_retain_fee_amount'];
          $new_intake_data['w2_ee_difference_count'] = $data['w2_ee_difference_count'];
          $new_intake_data['balance_retainer_fee'] = $data['balance_retainer_fee'];
          $new_intake_data['total_max_erc_amount'] = $data['total_max_erc_amount'];
          $new_intake_data['total_estimated_fees'] = $data['total_estimated_fees'];
          $new_intake_data['affiliate_referral_fees'] = $data['affiliate_referral_fees'];
          $new_intake_data['sdgr'] = $data['sdgr'];                   
          $new_intake_data['avg_emp_count_2019'] = $data['avg_emp_count_2019'];
          $new_intake_data['fee_type'] = $fee_type;
          $new_intake_data['company_folder_link'] = $data['company_folder_link'];
          $new_intake_data['document_folder_link'] = $data['document_folder_link'];
          $new_intake_data['eligible_quarters'] = $data['eligible_quarters'];
          $new_intake_data['retainer_invoice_no'] = $data['retainer_invoice_no'];
          $new_intake_data['welcome_email'] = $data['welcome_email'];
          $new_intake_data['retainer_payment_date'] = $data['retainer_payment_date'];
          $new_intake_data['retainer_payment_cleared'] = $data['retainer_payment_cleared'];
          $new_intake_data['retainer_payment_returned'] = $data['retainer_payment_returned'];
          $new_intake_data['retpayment_return_reason'] = $data['retpayment_return_reason'];
          $new_intake_data['retainer_refund_date'] = $data['retainer_refund_date'];
          $new_intake_data['retainer_refund_amount'] = $data['retainer_refund_amount'];
          $new_intake_data['retainer_payment_amount'] = $data['retainer_payment_amount'];
          $new_intake_data['retainer_payment_type'] = $data['retainer_payment_type'];
          $new_intake_data['bal_retainer_invoice_no'] = $data['bal_retainer_invoice_no'];
          $new_intake_data['bal_retainer_sent_date'] = $data['bal_retainer_sent_date'];
          $new_intake_data['bal_retainer_pay_date'] = $data['bal_retainer_pay_date'];
          $new_intake_data['bal_retainer_clear_date'] = $data['bal_retainer_clear_date'];
          $new_intake_data['bal_retainer_return_date'] = $data['bal_retainer_return_date'];
          $new_intake_data['bal_retainer_return_reaso'] = $data['bal_retainer_return_reaso'];
          $new_intake_data['coi_aoi'] = $data['coi_aoi'];
          $new_intake_data['voided_check'] = $data['voided_check'];
          $new_intake_data['2019_tax_return'] = $data['2019_tax_return'];
          $new_intake_data['2020_tax_return'] = $data['2020_tax_return'];
          $new_intake_data['2021_financials'] = $data['2021_financials'];
          $new_intake_data['2020_q1_4144'] = $data['2020_q1_941'];
          $new_intake_data['2020_q2_4145'] = $data['2020_q2_941'];
          $new_intake_data['2020_q3_4146'] = $data['2020_q3_941'];
          $new_intake_data['2020_q4_4147'] = $data['2020_q4_941'];
          $new_intake_data['2021_q1_4149'] = $data['2021_q1_941'];
          $new_intake_data['2021_q2_4151'] = $data['2021_q2_941'];
          $new_intake_data['2021_q3_4152'] = $data['2021_q3_941'];
          $new_intake_data['2020_q1_4155'] = $data['2020_q1_payroll'];
          $new_intake_data['2020_q2_4156'] = $data['2020_q2_payroll'];
          $new_intake_data['2020_q3_4157'] = $data['2020_q3_payroll'];
          $new_intake_data['2020_q4_4158'] = $data['2020_q4_payroll'];
          $new_intake_data['2021_q1_4160'] = $data['2021_q1_payroll'];
          $new_intake_data['2021_q2_4161'] = $data['2021_q2_payroll'];
          $new_intake_data['2021_q3_4162'] = $data['2021_q3_payroll'];
          $new_intake_data['f911_status'] = $data['f911_status'];
          $new_intake_data['ppp_1_applied'] = $data['ppp_1_applied'];
          $new_intake_data['ppp_1_date'] = $data['ppp_1_date'];
          $new_intake_data['ppp_1_forgiveness_applied'] = $data['ppp_1_forgiveness_applied'];
          $new_intake_data['ppp_1_forgive_app_date'] = $data['ppp_1_forgive_app_date'];
          $new_intake_data['ppp_1_amount'] = $data['ppp_1_amount'];
          $new_intake_data['ppp_1_wages_allocated'] = $data['ppp_1_wages_allocated'];
          $new_intake_data['ppp_2_applied'] = $data['ppp_2_applied'];
          $new_intake_data['ppp_2_date'] = $data['ppp_2_date'];
          $new_intake_data['ppp_2_forgiveness_applied'] = $data['ppp_2_forgiveness_applied'];
          $new_intake_data['ppp_2_forgive_app_date'] = $data['ppp_2_forgive_app_date'];
          $new_intake_data['ppp_2_amount'] = $data['ppp_2_amount'];
          $new_intake_data['ppp_2_wages_allocated'] = $data['ppp_2_wages_allocated'];
          $new_intake_data['additional_comments'] = $data['additional_comments'];
          $new_intake_data['attorney_name'] = $data['attorney_name'];
          $new_intake_data['call_date'] = $data['call_date'];
          $new_intake_data['call_time'] = $data['call_time'];
          $new_intake_data['memo_received_date'] = $data['memo_received_date'];
          $new_intake_data['memo_cut_off_date'] = $data['memo_cut_off_date'];
          $new_intake_data['opportunity_size'] = $data['opportunity_size'];
          $new_intake_data['opportunity_timeline'] = $data['opportunity_timeline'];
          $new_intake_data['confidance_label'] = $data['confidance_label'];    
          $new_intake_data['interest_percentage'] = $data['interest_percentage'];
          $new_intake_data['net_no'] = $data['net_no'];    
          $int_table = $wpdb->prefix.'erc_erc_intake';
          $this->project_log_audit_entry($user_id,$lead_id, $intake_info, $new_intake_data, $int_table);   
            $wpdb->query("UPDATE {$wpdb->prefix}erc_erc_intake SET 
                               w2_employees_count = '".$data['w2_employees_count']."',
                               initial_retain_fee_amount = '".$data['initial_retain_fee_amount']."',
                               w2_ee_difference_count = '".$data['w2_ee_difference_count']."',
                               balance_retainer_fee = '".$data['balance_retainer_fee']."',
                               total_max_erc_amount = '".$data['total_max_erc_amount']."',
                               total_estimated_fees = '".$data['total_estimated_fees']."',
                               affiliate_referral_fees = '".$data['affiliate_referral_fees']."',
                               sdgr = '".$data['sdgr']."',                                
                               avg_emp_count_2019 = '".$data['avg_emp_count_2019']."',
                               fee_type = '".$fee_type."',
                               company_folder_link = '".$data['company_folder_link']."',
                               document_folder_link = '".$data['document_folder_link']."',
                               eligible_quarters = '".$data['eligible_quarters']."',
                               retainer_invoice_no = '".$data['retainer_invoice_no']."',
                               welcome_email = '".$data['welcome_email']."',
                               retainer_payment_date = '".$data['retainer_payment_date']."',
                               retainer_payment_cleared = '".$data['retainer_payment_cleared']."',
                               retainer_payment_returned = '".$data['retainer_payment_returned']."',
                               retpayment_return_reason = '".$data['retpayment_return_reason']."',
                               retainer_refund_date = '".$data['retainer_refund_date']."',
                               retainer_refund_amount = '".$data['retainer_refund_amount']."',
                               retainer_payment_amount = '".$data['retainer_payment_amount']."',
                               retainer_payment_type = '".$data['retainer_payment_type']."',
                               bal_retainer_invoice_no = '".$data['bal_retainer_invoice_no']."',
                               bal_retainer_sent_date = '".$data['bal_retainer_sent_date']."',
                               bal_retainer_pay_date = '".$data['bal_retainer_pay_date']."',
                               bal_retainer_clear_date = '".$data['bal_retainer_clear_date']."',
                               bal_retainer_return_date = '".$data['bal_retainer_return_date']."',
                               bal_retainer_return_reaso = '".$data['bal_retainer_return_reaso']."',
                               coi_aoi = '".$data['coi_aoi']."',
                               voided_check = '".$data['voided_check']."',
                               2019_tax_return = '".$data['2019_tax_return']."',
                               2020_tax_return = '".$data['2020_tax_return']."',
                               2021_financials = '".$data['2021_financials']."',
                               2020_q1_4144 = '".$data['2020_q1_941']."',
                               2020_q2_4145 = '".$data['2020_q2_941']."',
                               2020_q3_4146 = '".$data['2020_q3_941']."',
                               2020_q4_4147 = '".$data['2020_q4_941']."',
                               2021_q1_4149 = '".$data['2021_q1_941']."',
                               2021_q2_4151 = '".$data['2021_q2_941']."',
                               2021_q3_4152 = '".$data['2021_q3_941']."',
                               2020_q1_4155 = '".$data['2020_q1_payroll']."',
                               2020_q2_4156 = '".$data['2020_q2_payroll']."',
                               2020_q3_4157 = '".$data['2020_q3_payroll']."',
                               2020_q4_4158 = '".$data['2020_q4_payroll']."',
                               2021_q1_4160 = '".$data['2021_q1_payroll']."',
                               2021_q2_4161 = '".$data['2021_q2_payroll']."',
                               2021_q3_4162 = '".$data['2021_q3_payroll']."',
                               f911_status = '".$data['f911_status']."',
                               ppp_1_applied = '".$data['ppp_1_applied']."',
                               ppp_1_date = '".$data['ppp_1_date']."',
                               ppp_1_forgiveness_applied = '".$data['ppp_1_forgiveness_applied']."',
                               ppp_1_forgive_app_date = '".$data['ppp_1_forgive_app_date']."',
                               ppp_1_amount = '".$data['ppp_1_amount']."',
                               ppp_1_wages_allocated = '".$data['ppp_1_wages_allocated']."',
                               ppp_2_applied = '".$data['ppp_2_applied']."',
                               ppp_2_date = '".$data['ppp_2_date']."',
                               ppp_2_forgiveness_applied = '".$data['ppp_2_forgiveness_applied']."',
                               ppp_2_forgive_app_date = '".$data['ppp_2_forgive_app_date']."',
                               ppp_2_amount = '".$data['ppp_2_amount']."',
                               ppp_2_wages_allocated = '".$data['ppp_2_wages_allocated']."',
                               additional_comments = '".$data['additional_comments']."',
                               attorney_name = '".$data['attorney_name']."',
                               call_date = '".$data['call_date']."',
                               call_time = '".$data['call_time']."',
                               memo_received_date = '".$data['memo_received_date']."',
                               memo_cut_off_date = '".$data['memo_cut_off_date']."',
                               interest_percentage = '".$data['interest_percentage']."',
                               net_no = '".$data['net_no']."'
                               WHERE lead_id = ".$lead_id."
                            ");
        }else{
            $wpdb->query(
            "INSERT INTO {$wpdb->prefix}erc_erc_intake (
                lead_id,w2_employees_count, initial_retain_fee_amount,
                w2_ee_difference_count, balance_retainer_fee, total_max_erc_amount, total_estimated_fees,
                affiliate_referral_fees, sdgr, avg_emp_count_2019, fee_type, company_folder_link,
                document_folder_link, eligible_quarters, welcome_email, retainer_payment_date,
                retainer_payment_cleared, retainer_payment_returned, retpayment_return_reason,
                retainer_refund_date, retainer_refund_amount, retainer_payment_amount, retainer_payment_type,
                bal_retainer_pay_date, bal_retainer_clear_date, bal_retainer_return_date,
                bal_retainer_return_reaso, coi_aoi, voided_check, 2019_tax_return, 2020_tax_return,
                2021_financials, 2020_q1_4144, 2020_q2_4145, 2020_q3_4146, 2020_q4_4147,
                2021_q1_4149, 2021_q2_4151, 2021_q3_4152, 2020_q1_4155, 2020_q2_4156,
                2020_q3_4157, 2020_q4_4158, 2021_q1_4160, 2021_q2_4161, 2021_q3_4162,               
                ppp_1_applied, ppp_1_date, ppp_1_forgiveness_applied, ppp_1_forgive_app_date, ppp_1_amount,
                ppp_1_wages_allocated, ppp_2_applied, ppp_2_date, ppp_2_forgiveness_applied,
                ppp_2_forgive_app_date, ppp_2_amount, ppp_2_wages_allocated, additional_comments, attorney_name, call_date, call_time, memo_received_date, memo_cut_off_date 
            ) VALUES (
                ".$lead_id.",'".$data['w2_employees_count']."', '".$data['initial_retain_fee_amount']."',
                '".$data['w2_ee_difference_count']."', '".$data['balance_retainer_fee']."', '".$data['total_max_erc_amount']."', '".$data['total_estimated_fees']."',
                '".$data['affiliate_referral_fees']."', '".$data['sdgr']."', '".$data['avg_emp_count_2019']."', '".$data['fee_type']."', '".$data['company_folder_link']."',
                '".$data['document_folder_link']."', '".$data['eligible_quarters']."', '".$data['welcome_email']."', '".$data['retainer_payment_date']."',
                '".$data['retainer_payment_cleared']."', '".$data['retainer_payment_returned']."', '".$data['retpayment_return_reason']."',
                '".$data['retainer_refund_date']."', '".$data['retainer_refund_amount']."', '".$data['retainer_payment_amount']."', '".$data['retainer_payment_type']."',
                '".$data['bal_retainer_pay_date']."', '".$data['bal_retainer_clear_date']."', '".$data['bal_retainer_return_date']."',
                '".$data['bal_retainer_return_reaso']."', '".$data['coi_aoi']."', '".$data['voided_check']."', '".$data['2019_tax_return']."', '".$data['2020_tax_return']."', 
                '".$data['2021_financials']."', '".$data['2020_q1_941']."', '".$data['2020_q2_941']."', '".$data['2020_q3_941']."', '".$data['2020_q4_941']."',
                '".$data['2021_q1_941']."', '".$data['2021_q2_941']."', '".$data['2021_q3_941']."', '".$data['2020_q1_payroll']."', '".$data['2020_q2_payroll']."',
                '".$data['2020_q3_payroll']."', '".$data['2020_q4_payroll']."', '".$data['2021_q1_payroll']."', '".$data['2021_q2_payroll']."', '".$data['2021_q3_payroll']."',                
                '".$data['ppp_1_applied']."', '".$data['ppp_1_date']."', '".$data['ppp_1_forgiveness_applied']."', '".$data['ppp_1_forgive_app_date']."', '".$data['ppp_1_amount']."',
                '".$data['ppp_1_wages_allocated']."', '".$data['ppp_2_applied']."', '".$data['ppp_2_date']."', '".$data['ppp_2_forgiveness_applied']."',
                '".$data['ppp_2_forgive_app_date']."', '".$data['ppp_2_amount']."', '".$data['ppp_2_wages_allocated']."', '".$data['additional_comments']."', '".$data['attorney_name']."', '".$data['call_date']."', '".$data['call_time']."', '".$data['memo_received_date']."', '".$data['memo_cut_off_date']."')");
        }
    }
    //Update Fees Tab
    if(isset($data['error_discovered_date'])){
        $fee_info = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}erc_erc_fees WHERE lead_id = ".$lead_id."");
        if(!empty($fee_info)){
          $new_fees_data = array();
          $new_fees_data['error_discovered_date'] = $data['error_discovered_date'];
           $new_fees_data['q2_2020_941_wages'] = $data['q2_2020_941_wages'];
           $new_fees_data['q3_2020_941_wages'] = $data['q3_2020_941_wages'];
           $new_fees_data['q4_2020_941_wages'] = $data['q4_2020_941_wages'];
           $new_fees_data['q1_2021_941_wages'] = $data['q1_2021_941_wages'];
           $new_fees_data['q2_2021_941_wages'] = $data['q2_2021_941_wages'];
           $new_fees_data['q3_2021_941_wages'] = $data['q3_2021_941_wages'];
           $new_fees_data['q4_2021_941_wages'] = $data['q4_2021_941_wages'];
           $new_fees_data['affiliate_name'] = $data['affiliate_name'];
           $new_fees_data['affiliate_percentage'] = $data['affiliate_percentage'];
           $new_fees_data['erc_claim_filed'] = $data['erc_claim_filed'];
           $new_fees_data['erc_amount_received'] = $data['erc_amount_received'];
           $new_fees_data['total_erc_fees'] = $data['total_erc_fees'];
           $new_fees_data['legal_fees'] = $data['legal_fees'];
           $new_fees_data['total_erc_fees_paid'] = $data['total_erc_fees_paid'];
           $new_fees_data['total_erc_fees_pending'] = $data['total_erc_fees_pending'];
           $new_fees_data['total_occams_share'] = $data['total_occams_share'];
           $new_fees_data['total_aff_ref_share'] = $data['total_aff_ref_share'];
           $new_fees_data['retain_occams_share'] = $data['retain_occams_share'];
           $new_fees_data['retain_aff_ref_share'] = $data['retain_aff_ref_share'];
           $new_fees_data['bal_retain_occams_share'] = $data['bal_retain_occams_share'];
           $new_fees_data['bal_retain_aff_ref_share'] = $data['bal_retain_aff_ref_share'];
           $new_fees_data['total_occams_share_paid'] = $data['total_occams_share_paid'];
           $new_fees_data['total_aff_ref_share_paid'] = $data['total_aff_ref_share_paid'];
           $new_fees_data['total_occams_share_pendin'] = $data['total_occams_share_pendin'];
           $new_fees_data['total_aff_ref_share_pend'] = $data['total_aff_ref_share_pend'];
           $new_fees_data['q1_2020_filed_status'] = $data['q1_2020_filed_status'];
           $new_fees_data['filing_date_4267'] = $data['q1_2020_filing_date'];
           $new_fees_data['amount_filed_4263'] = $data['q1_2020_amount_filed'];
           $new_fees_data['q1_2020_benefits'] = $data['q1_2020_benefits'];
           $new_fees_data['q1_2020_eligibility_basis'] = $data['q1_2020_eligibility_basis'];
           $new_fees_data['q2_2020_filed_status'] = $data['q2_2020_filed_status'];
           $new_fees_data['filing_date_4268'] = $data['q2_2020_filing_date'];
           $new_fees_data['amount_filed_4269'] = $data['q2_2020_amount_filed'];
           $new_fees_data['q2_2020_benefits'] = $data['q2_2020_benefits'];
           $new_fees_data['q2_2020_eligibility_basis'] = $data['q2_2020_eligibility_basis'];
           $new_fees_data['q3_2020_filed_status'] = $data['q3_2020_filed_status'];
           $new_fees_data['filing_date_4270'] = $data['q3_2020_filing_date'];
           $new_fees_data['amount_filed_4266'] = $data['q3_2020_amount_filed'];
           $new_fees_data['q3_2020_benefits'] = $data['q3_2020_benefits'];
           $new_fees_data['q3_2020_eligibility_basis'] = $data['q3_2020_eligibility_basis'];
           $new_fees_data['q4_2020_filed_status'] = $data['q4_2020_filed_status'];
           $new_fees_data['filing_date_4272'] = $data['q4_2020_filing_date'];
           $new_fees_data['amount_filed_4273'] = $data['q4_2020_amount_filed'];
           $new_fees_data['q4_2020_benefits'] = $data['q4_2020_benefits'];
           $new_fees_data['q4_2020_eligibility_basis'] = $data['q4_2020_eligibility_basis'];
           $new_fees_data['q1_2021_filed_status'] = $data['q1_2021_filed_status'];
           $new_fees_data['filing_date_4276'] = $data['q1_2021_filing_date'];
           $new_fees_data['amount_filed_4277'] = $data['q1_2021_amount_filed'];
           $new_fees_data['q1_2021_benefits'] = $data['q1_2021_benefits'];
           $new_fees_data['q1_2021_eligibility_basis'] = $data['q1_2021_eligibility_basis'];
           $new_fees_data['q2_2021_filed_status'] = $data['q2_2021_filed_status'];
           $new_fees_data['filing_date_4279'] = $data['q2_2021_filing_date'];
           $new_fees_data['amount_filed_4280'] = $data['q2_2021_amount_filed'];
           $new_fees_data['q2_2021_benefits'] = $data['q2_2021_benefits'];
           $new_fees_data['q2_2021_eligibility_basis'] = $data['q2_2021_eligibility_basis'];
           $new_fees_data['q3_2021_filed_status'] = $data['q3_2021_filed_status'];
           $new_fees_data['filing_date_4282'] = $data['q3_2021_filing_date'];
           $new_fees_data['amount_filed_4283'] = $data['q3_2021_amount_filed'];
           $new_fees_data['q3_2021_benefits'] = $data['q3_2021_benefits'];
           $new_fees_data['q3_2021_eligibility_basis'] = $data['q3_2021_eligibility_basis'];
           $new_fees_data['q4_2021_filed_status'] = $data['q4_2021_filed_status'];
           $new_fees_data['filing_date_4285'] = $data['q4_2021_filing_date'];
           $new_fees_data['amount_filed_4286'] = $data['q4_2021_amount_filed'];
           $new_fees_data['q4_2021_benefits'] = $data['q4_2021_benefits'];
           $new_fees_data['q4_2021_eligibility_basis'] = $data['q4_2021_eligibility_basis'];
           $new_fees_data['q1_2020_loOP'] = $data['q1_2020_loop'];
           $new_fees_data['q1_2020_letter'] = $data['q1_2020_letter'];
           $new_fees_data['q1_2020_check'] = $data['q1_2020_check'];
           $new_fees_data['q1_2020_chq_amt'] = $data['q1_2020_chq_amt'];
           $new_fees_data['q2_2020_loOP'] = $data['q2_2020_loop'];
           $new_fees_data['q2_2020_letter'] = $data['q2_2020_letter'];
           $new_fees_data['q2_2020_check'] = $data['q2_2020_check'];
           $new_fees_data['q2_2020_chq_amt'] = $data['q2_2020_chq_amt'];
           $new_fees_data['q3_2020_loOP'] = $data['q3_2020_loop'];
           $new_fees_data['q3_2020_letter'] = $data['q3_2020_letter'];
           $new_fees_data['q3_2020_check'] = $data['q3_2020_check'];
           $new_fees_data['q3_2020_chq_amt'] = $data['q3_2020_chq_amt'];
           $new_fees_data['q4_2020_loOP'] = $data['q4_2020_loop'];
           $new_fees_data['q4_2020_letter'] = $data['q4_2020_letter'];
           $new_fees_data['q4_2020_check'] = $data['q4_2020_check'];
           $new_fees_data['q4_2020_chq_amt'] = $data['q4_2020_chq_amt'];
           $new_fees_data['q1_2021_loOP'] = $data['q1_2021_loop'];
           $new_fees_data['q1_2021_letter'] = $data['q1_2021_letter'];
           $new_fees_data['q1_2021_check'] = $data['q1_2021_check'];
           $new_fees_data['q1_2021_chq_amt'] = $data['q1_2021_chq_amt'];
           $new_fees_data['q2_2021_loOP'] = $data['q2_2021_loop'];
           $new_fees_data['q2_2021_letter'] = $data['q2_2021_letter'];
           $new_fees_data['q2_2021_check'] = $data['q2_2021_check'];
           $new_fees_data['q2_2021_chq_amt'] = $data['q2_2021_chq_amt'];
           $new_fees_data['q3_2021_loOP'] = $data['q3_2021_loop'];
           $new_fees_data['q3_2021_letter'] = $data['q3_2021_letter'];
           $new_fees_data['q3_2021_check'] = $data['q3_2021_check'];
           $new_fees_data['q3_2021_chq_amt'] = $data['q3_2021_chq_amt'];
           $new_fees_data['q4_2021_loOP'] = $data['q4_2021_loop'];
           $new_fees_data['q4_2021_letter'] = $data['q4_2021_letter'];
           $new_fees_data['q4_2021_check'] = $data['q4_2021_check'];
           $new_fees_data['q4_2021_chq_amt'] = $data['q4_2021_chq_amt'];
           $new_fees_data['i_invoice_no'] = $data['i_invoice_no'];
           $new_fees_data['i_invoice_amount'] = $data['i_invoice_amount'];
           $new_fees_data['i_invoiced_qtrs'] = $data['i_invoiced_qtrs'];
           $new_fees_data['i_invoice_sent_date'] = $data['i_invoice_sent_date'];
           $new_fees_data['i_invoice_payment_type'] = $data['i_invoice_payment_type'];
           $new_fees_data['i_invoice_payment_date'] = $data['i_invoice_payment_date'];
           $new_fees_data['i_invoice_pay_cleared'] = $data['i_invoice_pay_cleared'];
           $new_fees_data['i_invoice_pay_returned'] = $data['i_invoice_pay_returned'];
           $new_fees_data['i_invoice_return_reason'] = $data['i_invoice_return_reason'];
           $new_fees_data['i_invoice_occams_share'] = $data['i_invoice_occams_share'];
           $new_fees_data['i_invoice_aff_ref_share'] = $data['i_invoice_aff_ref_share'];
           $new_fees_data['ii_invoice_no'] = $data['ii_invoice_no'];
           $new_fees_data['ii_invoice_amount'] = $data['ii_invoice_amount'];
           $new_fees_data['ii_invoiced_qtrs'] = $data['ii_invoiced_qtrs'];
           $new_fees_data['ii_invoice_sent_date'] = $data['ii_invoice_sent_date'];
           $new_fees_data['ii_invoice_payment_type'] = $data['ii_invoice_payment_type'];
           $new_fees_data['ii_invoice_payment_date'] = $data['ii_invoice_payment_date'];
           $new_fees_data['ii_invoice_pay_cleared'] = $data['ii_invoice_pay_cleared'];
           $new_fees_data['ii_invoice_pay_returned'] = $data['ii_invoice_pay_returned'];
           $new_fees_data['ii_invoice_return_reason'] = $data['ii_invoice_return_reason'];
           $new_fees_data['ii_invoice_occams_share'] = $data['ii_invoice_occams_share'];
           $new_fees_data['ii_invoice_aff_ref_share'] = $data['ii_invoice_aff_ref_share'];
           $new_fees_data['iii_invoice_no'] = $data['iii_invoice_no'];
           $new_fees_data['iii_invoice_amount'] = $data['iii_invoice_amount'];
           $new_fees_data['iii_invoiced_qtrs'] = $data['iii_invoiced_qtrs'];
           $new_fees_data['iii_invoice_sent_date'] = $data['iii_invoice_sent_date'];
           $new_fees_data['iii_invoice_payment_type'] = $data['iii_invoice_payment_type'];
           $new_fees_data['iii_invoice_payment_date'] = $data['iii_invoice_payment_date'];
           $new_fees_data['iii_invoice_pay_cleared'] = $data['iii_invoice_pay_cleared'];
           $new_fees_data['iii_invoice_pay_returned'] = $data['iii_invoice_pay_returned'];
           $new_fees_data['iii_invoice_return_reason'] = $data['iii_invoice_return_reason'];
           $new_fees_data['iii_invoice_occams_share'] = $data['iii_invoice_occams_share'];
           $new_fees_data['iii_invoice_aff_ref_share'] = $data['iii_invoice_aff_ref_share'];
           $new_fees_data['iv_invoice_no'] = $data['iv_invoice_no'];
           $new_fees_data['iv_invoice_amount'] = $data['iv_invoice_amount'];
           $new_fees_data['iv_invoiced_qtrs'] = $data['iv_invoiced_qtrs'];
           $new_fees_data['iv_invoice_sent_date'] = $data['iv_invoice_sent_date'];
           $new_fees_data['iv_invoice_payment_type'] = $data['iv_invoice_payment_type'];
           $new_fees_data['iv_invoice_payment_date'] = $data['iv_invoice_payment_date'];
           $new_fees_data['iv_invoice_pay_cleared'] = $data['iv_invoice_pay_cleared'];
           $new_fees_data['iv_invoice_pay_returned'] = $data['iv_invoice_pay_returned'];
           $new_fees_data['iv_invoice_return_reason'] = $data['iv_invoice_return_reason'];
           $new_fees_data['iv_invoice_occams_share'] = $data['iv_invoice_occams_share'];
           $new_fees_data['iv_invoice_aff_ref_share'] = $data['iv_invoice_aff_ref_share'];
           $new_fees_data['q1_2021_max_erc_amount'] = $data['q1_2021_max_erc_amount'];
           $new_fees_data['q2_2021_max_erc_amount'] = $data['q2_2021_max_erc_amount'];
           $new_fees_data['q3_2021_max_erc_amount'] = $data['q3_2021_max_erc_amount'];
           $new_fees_data['q4_2021_max_erc_amount'] = $data['q4_2021_max_erc_amount'];
           $new_fees_data['q1_2020_max_erc_amount'] = $data['q1_2020_max_erc_amount'];
           $new_fees_data['q2_2020_max_erc_amount'] = $data['q2_2020_max_erc_amount'];
           $new_fees_data['q3_2020_max_erc_amount'] = $data['q3_2020_max_erc_amount'];
           $new_fees_data['q4_2020_max_erc_amount'] = $data['q4_2020_max_erc_amount'];

           $fees_table = $wpdb->prefix.'erc_erc_fees'; 
           $this->project_log_audit_entry($user_id,$lead_id, $fee_info, $new_fees_data,$fees_table);
            $wpdb->query("UPDATE {$wpdb->prefix}erc_erc_fees SET 
                           error_discovered_date = '".$data['error_discovered_date']."',
                           q2_2020_941_wages = '".$data['q2_2020_941_wages']."',
                           q3_2020_941_wages = '".$data['q3_2020_941_wages']."',
                           q4_2020_941_wages = '".$data['q4_2020_941_wages']."',
                           q1_2021_941_wages = '".$data['q1_2021_941_wages']."',
                           q2_2021_941_wages = '".$data['q2_2021_941_wages']."',
                           q3_2021_941_wages = '".$data['q3_2021_941_wages']."',
                           q4_2021_941_wages = '".$data['q4_2021_941_wages']."',   
                           affiliate_name = '".$data['affiliate_name']."',
                           affiliate_percentage = '".$data['affiliate_percentage']."',
                           erc_claim_filed = '".$data['erc_claim_filed']."',
                           erc_amount_received = '".$data['erc_amount_received']."',
                           total_erc_fees = '".$data['total_erc_fees']."',
                           legal_fees = '".$data['legal_fees']."',
                           total_erc_fees_paid = '".$data['total_erc_fees_paid']."',
                           total_erc_fees_pending = '".$data['total_erc_fees_pending']."',
                           total_occams_share = '".$data['total_occams_share']."',
                           total_aff_ref_share = '".$data['total_aff_ref_share']."',
                           retain_occams_share = '".$data['retain_occams_share']."',
                           retain_aff_ref_share = '".$data['retain_aff_ref_share']."',
                           bal_retain_occams_share = '".$data['bal_retain_occams_share']."',
                           bal_retain_aff_ref_share = '".$data['bal_retain_aff_ref_share']."',
                           total_occams_share_paid = '".$data['total_occams_share_paid']."',
                           total_aff_ref_share_paid = '".$data['total_aff_ref_share_paid']."',
                           total_occams_share_pendin = '".$data['total_occams_share_pendin']."',
                           total_aff_ref_share_pend = '".$data['total_aff_ref_share_pend']."',
                           q1_2020_filed_status = '".$data['q1_2020_filed_status']."',
                           filing_date_4267 = '".$data['q1_2020_filing_date']."',
                           amount_filed_4263 = '".$data['q1_2020_amount_filed']."',
                           q1_2020_benefits = '".$data['q1_2020_benefits']."',
                           q1_2020_eligibility_basis = '".$data['q1_2020_eligibility_basis']."',
                           q2_2020_filed_status = '".$data['q2_2020_filed_status']."',
                           filing_date_4268 = '".$data['q2_2020_filing_date']."',
                           amount_filed_4269 = '".$data['q2_2020_amount_filed']."',
                           q2_2020_benefits = '".$data['q2_2020_benefits']."',
                           q2_2020_eligibility_basis = '".$data['q2_2020_eligibility_basis']."',
                           q3_2020_filed_status = '".$data['q3_2020_filed_status']."',
                           filing_date_4270 = '".$data['q3_2020_filing_date']."',
                           amount_filed_4266 = '".$data['q3_2020_amount_filed']."',
                           q3_2020_benefits = '".$data['q3_2020_benefits']."',
                           q3_2020_eligibility_basis = '".$data['q3_2020_eligibility_basis']."',
                           q4_2020_filed_status = '".$data['q4_2020_filed_status']."',
                           filing_date_4272 = '".$data['q4_2020_filing_date']."',
                           amount_filed_4273 = '".$data['q4_2020_amount_filed']."',
                           q4_2020_benefits = '".$data['q4_2020_benefits']."',
                           q4_2020_eligibility_basis = '".$data['q4_2020_eligibility_basis']."',
                           q1_2021_filed_status = '".$data['q1_2021_filed_status']."',
                           filing_date_4276 = '".$data['q1_2021_filing_date']."',
                           amount_filed_4277 = '".$data['q1_2021_amount_filed']."',
                           q1_2021_benefits = '".$data['q1_2021_benefits']."',
                           q1_2021_eligibility_basis = '".$data['q1_2021_eligibility_basis']."',
                           q2_2021_filed_status = '".$data['q2_2021_filed_status']."',
                           filing_date_4279 = '".$data['q2_2021_filing_date']."',
                           amount_filed_4280 = '".$data['q2_2021_amount_filed']."',
                           q2_2021_benefits = '".$data['q2_2021_benefits']."',
                           q2_2021_eligibility_basis = '".$data['q2_2021_eligibility_basis']."',
                           q3_2021_filed_status = '".$data['q3_2021_filed_status']."',
                           filing_date_4282 = '".$data['q3_2021_filing_date']."',
                           amount_filed_4283 = '".$data['q3_2021_amount_filed']."',
                           q3_2021_benefits = '".$data['q3_2021_benefits']."',
                           q3_2021_eligibility_basis = '".$data['q3_2021_eligibility_basis']."',
                           q4_2021_filed_status = '".$data['q4_2021_filed_status']."',
                           filing_date_4285 = '".$data['q4_2021_filing_date']."',
                           amount_filed_4286 = '".$data['q4_2021_amount_filed']."',
                           q4_2021_benefits = '".$data['q4_2021_benefits']."',
                           q4_2021_eligibility_basis = '".$data['q4_2021_eligibility_basis']."'
                           ,q1_2020_loOP = '".$data['q1_2020_loop']."'
                           ,q1_2020_letter = '".$data['q1_2020_letter']."'
                           ,q1_2020_check = '".$data['q1_2020_check']."'
                           ,q1_2020_chq_amt = '".$data['q1_2020_chq_amt']."'
                           ,q2_2020_loOP = '".$data['q2_2020_loop']."'
                           ,q2_2020_letter = '".$data['q2_2020_letter']."'
                           ,q2_2020_check = '".$data['q2_2020_check']."'
                           ,q2_2020_chq_amt = '".$data['q2_2020_chq_amt']."'
                           ,q3_2020_loOP = '".$data['q3_2020_loop']."'
                           ,q3_2020_letter = '".$data['q3_2020_letter']."'
                           ,q3_2020_check = '".$data['q3_2020_check']."'
                           ,q3_2020_chq_amt = '".$data['q3_2020_chq_amt']."'
                           ,q4_2020_loOP = '".$data['q4_2020_loop']."'
                           ,q4_2020_letter = '".$data['q4_2020_letter']."'
                           ,q4_2020_check = '".$data['q4_2020_check']."'
                           ,q4_2020_chq_amt = '".$data['q4_2020_chq_amt']."'
                           ,q1_2021_loOP = '".$data['q1_2021_loop']."'
                           ,q1_2021_letter = '".$data['q1_2021_letter']."'
                           ,q1_2021_check = '".$data['q1_2021_check']."'
                           ,q1_2021_chq_amt = '".$data['q1_2021_chq_amt']."'
                           ,q2_2021_loOP = '".$data['q2_2021_loop']."'
                           ,q2_2021_letter = '".$data['q2_2021_letter']."'
                           ,q2_2021_check = '".$data['q2_2021_check']."'
                            ,q2_2021_chq_amt = '".$data['q2_2021_chq_amt']."'
                           ,q3_2021_loOP = '".$data['q3_2021_loop']."'
                           ,q3_2021_letter = '".$data['q3_2021_letter']."'
                           ,q3_2021_check = '".$data['q3_2021_check']."'
                           ,q3_2021_chq_amt = '".$data['q3_2021_chq_amt']."'
                           ,q4_2021_loOP = '".$data['q4_2021_loop']."'
                           ,q4_2021_letter = '".$data['q4_2021_letter']."'
                           ,q4_2021_check = '".$data['q4_2021_check']."'
                           ,q4_2021_chq_amt = '".$data['q4_2021_chq_amt']."'
                           ,i_invoice_no = '".$data['i_invoice_no']."'
                           ,i_invoice_amount = '".$data['i_invoice_amount']."'
                           ,i_invoiced_qtrs = '".$data['i_invoiced_qtrs']."'
                          ,i_invoice_sent_date = '".$data['i_invoice_sent_date']."'
                          ,i_invoice_payment_type = '".$data['i_invoice_payment_type']."'
                          ,i_invoice_payment_date = '".$data['i_invoice_payment_date']."'
                          ,i_invoice_pay_cleared = '".$data['i_invoice_pay_cleared']."'
                          ,i_invoice_pay_returned = '".$data['i_invoice_pay_returned']."'
                          ,i_invoice_return_reason = '".$data['i_invoice_return_reason']."'
                          ,i_invoice_occams_share = '".$data['i_invoice_occams_share']."'
                          ,i_invoice_aff_ref_share = '".$data['i_invoice_aff_ref_share']."'
                          ,ii_invoice_no = '".$data['ii_invoice_no']."'
                          ,ii_invoice_amount = '".$data['ii_invoice_amount']."'
                          ,ii_invoiced_qtrs = '".$data['ii_invoiced_qtrs']."'
                          ,ii_invoice_sent_date = '".$data['ii_invoice_sent_date']."'
                          ,ii_invoice_payment_type = '".$data['ii_invoice_payment_type']."'
                          ,ii_invoice_payment_date = '".$data['ii_invoice_payment_date']."'
                          ,ii_invoice_pay_cleared = '".$data['ii_invoice_pay_cleared']."'
                          ,ii_invoice_pay_returned = '".$data['ii_invoice_pay_returned']."'
                          ,ii_invoice_return_reason = '".$data['ii_invoice_return_reason']."'
                          ,ii_invoice_occams_share = '".$data['ii_invoice_occams_share']."'
                          ,ii_invoice_aff_ref_share = '".$data['ii_invoice_aff_ref_share']."'
                          ,iii_invoice_no = '".$data['iii_invoice_no']."'
                          ,iii_invoice_amount = '".$data['iii_invoice_amount']."'
                          ,iii_invoiced_qtrs = '".$data['iii_invoiced_qtrs']."'
                          ,iii_invoice_sent_date = '".$data['iii_invoice_sent_date']."'
                          ,iii_invoice_payment_type = '".$data['iii_invoice_payment_type']."'
                          ,iii_invoice_payment_date = '".$data['iii_invoice_payment_date']."'
                          ,iii_invoice_pay_cleared = '".$data['iii_invoice_pay_cleared']."'
                          ,iii_invoice_pay_returned = '".$data['iii_invoice_pay_returned']."'
                          ,iii_invoice_return_reason = '".$data['iii_invoice_return_reason']."'
                          ,iii_invoice_occams_share = '".$data['iii_invoice_occams_share']."'
                          ,iii_invoice_aff_ref_share = '".$data['iii_invoice_aff_ref_share']."'
                          ,iv_invoice_no = '".$data['iv_invoice_no']."'
                          ,iv_invoice_amount = '".$data['iv_invoice_amount']."'
                          ,iv_invoiced_qtrs = '".$data['iv_invoiced_qtrs']."'
                          ,iv_invoice_sent_date = '".$data['iv_invoice_sent_date']."'
                          ,iv_invoice_payment_type = '".$data['iv_invoice_payment_type']."'
                          ,iv_invoice_payment_date = '".$data['iv_invoice_payment_date']."'
                          ,iv_invoice_pay_cleared = '".$data['iv_invoice_pay_cleared']."'
                          ,iv_invoice_pay_returned = '".$data['iv_invoice_pay_returned']."'
                          ,iv_invoice_return_reason = '".$data['iv_invoice_return_reason']."'
                          ,iv_invoice_occams_share = '".$data['iv_invoice_occams_share']."'
                          ,iv_invoice_aff_ref_share = '".$data['iv_invoice_aff_ref_share']."'
                          ,q1_2021_max_erc_amount = '".$data['q1_2021_max_erc_amount']."'
                          ,q2_2021_max_erc_amount = '".$data['q2_2021_max_erc_amount']."'
                          ,q3_2021_max_erc_amount = '".$data['q3_2021_max_erc_amount']."'
                          ,q4_2021_max_erc_amount = '".$data['q4_2021_max_erc_amount']."'
                          ,q1_2020_max_erc_amount = '".$data['q1_2020_max_erc_amount']."'
                          ,q2_2020_max_erc_amount = '".$data['q2_2020_max_erc_amount']."'
                          ,q3_2020_max_erc_amount = '".$data['q3_2020_max_erc_amount']."'
                          ,q4_2020_max_erc_amount = '".$data['q4_2020_max_erc_amount']."'
                           WHERE lead_id = ".$lead_id."
                        ");
        }else{
            $wpdb->query(
            "INSERT INTO {$wpdb->prefix}erc_erc_fees (
                lead_id,
                error_discovered_date,
                q2_2020_941_wages,
                q3_2020_941_wages,
                q4_2020_941_wages,
                q1_2021_941_wages,
                q2_2021_941_wages,
                q3_2021_941_wages,
                q4_2021_941_wages,
                affiliate_name,
                affiliate_percentage,
                erc_claim_filed,
                erc_amount_received,
                total_erc_fees,
                legal_fees,
                total_erc_fees_paid,
                total_erc_fees_pending,
                total_occams_share,
                total_aff_ref_share,
                retain_occams_share,
                retain_aff_ref_share,
                bal_retain_occams_share,
                bal_retain_aff_ref_share,
                total_occams_share_paid,
                total_aff_ref_share_paid,
                total_occams_share_pendin,
                total_aff_ref_share_pend,
                q1_2020_filed_status,
                filing_date_4267,
                amount_filed_4263,
                q1_2020_benefits,
                q1_2020_eligibility_basis,
                q2_2020_filed_status,
                filing_date_4268,
                amount_filed_4269,
                q2_2020_benefits,
                q2_2020_eligibility_basis,
                q3_2020_filed_status,
                filing_date_4270,
                amount_filed_4266,
                q3_2020_benefits,
                q3_2020_eligibility_basis,
                q4_2020_filed_status,
                filing_date_4272,
                amount_filed_4273,
                q4_2020_benefits,
                q4_2020_eligibility_basis,
                q1_2021_filed_status,
                filing_date_4276,
                amount_filed_4277,
                q1_2021_benefits,
                q1_2021_eligibility_basis,
                q2_2021_filed_status,
                filing_date_4279,
                amount_filed_4280,
                q2_2021_benefits,
                q2_2021_eligibility_basis,
                q3_2021_filed_status,
                filing_date_4282,
                amount_filed_4283,
                q3_2021_benefits,
                q3_2021_eligibility_basis,
                q4_2021_filed_status,
                filing_date_4285,
                amount_filed_4286,
                q4_2021_benefits,
                q4_2021_eligibility_basis,
                q1_2020_loOP,
                q1_2020_letter,
                q1_2020_check,
                q1_2020_chq_amt,
                q2_2020_loOP,
                q2_2020_letter,
                q2_2020_check,
                q2_2020_chq_amt,
                q3_2020_loOP,
                q3_2020_letter,
                q3_2020_check,
                q3_2020_chq_amt,
                q4_2020_loOP,
                q4_2020_letter,
                q4_2020_check,
                q4_2020_chq_amt,
                q1_2021_loOP,
                q1_2021_letter,
                q1_2021_check,
                q1_2021_chq_amt,
                q2_2021_loOP,
                q2_2021_letter,
                q2_2021_check,
                q2_2021_chq_amt,
                q3_2021_loOP,
                q3_2021_letter,
                q3_2021_check,
                q3_2021_chq_amt,
                q4_2021_loOP,
                q4_2021_letter,
                q4_2021_check,
                q4_2021_chq_amt,
                i_invoice_no,
                i_invoice_amount,
                i_invoiced_qtrs,
                i_invoice_sent_date,
                i_invoice_payment_type,
                i_invoice_payment_date,
                i_invoice_pay_cleared,
                i_invoice_pay_returned,
                i_invoice_return_reason,
                i_invoice_occams_share,
                i_invoice_aff_ref_share,
                ii_invoice_no,
                ii_invoice_amount,
                ii_invoiced_qtrs,
                ii_invoice_sent_date,
                ii_invoice_payment_type,
                ii_invoice_payment_date,
                ii_invoice_pay_cleared,
                ii_invoice_pay_returned,
                ii_invoice_return_reason,
                ii_invoice_occams_share,
                ii_invoice_aff_ref_share,
                iii_invoice_no,
                iii_invoice_amount,
                iii_invoiced_qtrs,
                iii_invoice_sent_date,
                iii_invoice_payment_type,
                iii_invoice_payment_date,
                iii_invoice_pay_cleared,
                iii_invoice_pay_returned,
                iii_invoice_return_reason,
                iii_invoice_occams_share,
                iii_invoice_aff_ref_share,
                iv_invoice_no,
                iv_invoice_amount,
                iv_invoiced_qtrs,
                iv_invoice_sent_date,
                iv_invoice_payment_type,
                iv_invoice_payment_date,
                iv_invoice_pay_cleared,
                iv_invoice_pay_returned,
                iv_invoice_return_reason,
                iv_invoice_occams_share,
                iv_invoice_aff_ref_share,
                q1_2021_max_erc_amount,
                q2_2021_max_erc_amount,
                q3_2021_max_erc_amount,
                q4_2021_max_erc_amount,
                q1_2020_max_erc_amount,
                q2_2020_max_erc_amount,
                q3_2020_max_erc_amount,
                q4_2020_max_erc_amount 
                ) VALUES (
                ".$lead_id.", '".$data['error_discovered_date']."', '".$data['q2_2020_941_wages']."', '".$data['q3_2020_941_wages']."',
                '".$data['q4_2020_941_wages']."', '".$data['q1_2021_941_wages']."', '".$data['q2_2021_941_wages']."', '".$data['q3_2021_941_wages']."',
                '".$data['q4_2021_941_wages']."', '".$data['affiliate_name']."', '".$data['affiliate_percentage']."', '".$data['erc_claim_filed']."', '".$data['erc_amount_received']."',
                '".$data['total_erc_fees']."', '".$data['legal_fees']."', '".$data['total_erc_fees_paid']."','".$data['total_erc_fees_pending']."','".$data['total_occams_share']."','".$data['total_aff_ref_share']."','".$data['retain_occams_share']."','".$data['retain_aff_ref_share']."','".$data['bal_retain_occams_share']."','".$data['bal_retain_aff_ref_share']."','".$data['total_occams_share_paid']."','".$data['total_aff_ref_share_paid']."','".$data['total_occams_share_pendin']."','".$data['total_aff_ref_share_pend']."','".$data['q1_2020_filed_status']."','".$data['q1_2020_filing_date']."','".$data['q1_2020_amount_filed']."','".$data['q1_2020_benefits']."','".$data['q1_2020_eligibility_basis']."','".$data['q2_2020_filed_status']."','".$data['q2_2020_filing_date']."','".$data['q2_2020_amount_filed']."','".$data['q2_2020_benefits']."','".$data['q2_2020_eligibility_basis']."','".$data['q3_2020_filed_status']."','".$data['q3_2020_filing_date']."','".$data['q3_2020_amount_filed']."','".$data['q3_2020_benefits']."','".$data['q3_2020_eligibility_basis']."','".$data['q4_2020_filed_status']."','".$data['q4_2020_filing_date']."','".$data['q4_2020_amount_filed']."','".$data['q4_2020_benefits']."','".$data['q4_2020_eligibility_basis']."','".$data['q1_2021_filed_status']."','".$data['q1_2021_filing_date']."','".$data['q1_2021_amount_filed']."','".$data['q1_2021_benefits']."','".$data['q1_2021_eligibility_basis']."','".$data['q2_2021_filed_status']."','".$data['q2_2021_filing_date']."','".$data['q2_2021_amount_filed']."','".$data['q2_2021_benefits']."','".$data['q2_2021_eligibility_basis']."','".$data['q3_2021_filed_status']."','".$data['q3_2021_filing_date']."','".$data['q3_2021_amount_filed']."','".$data['q3_2021_benefits']."','".$data['q3_2021_eligibility_basis']."','".$data['q4_2021_filed_status']."','".$data['q4_2021_filing_date']."','".$data['q4_2021_amount_filed']."','".$data['q4_2021_benefits']."','".$data['q4_2021_eligibility_basis']."','".$data['q1_2020_loop']."','".$data['q1_2020_letter']."','".$data['q1_2020_check']."','".$data['q1_2020_chq_amt']."','".$data['q2_2020_loop']."','".$data['q2_2020_letter']."','".$data['q2_2020_check']."','".$data['q2_2020_chq_amt']."','".$data['q3_2020_loop']."','".$data['q3_2020_letter']."','".$data['q3_2020_check']."','".$data['q3_2020_chq_amt']."','".$data['q4_2020_loop']."','".$data['q4_2020_letter']."','".$data['q4_2020_check']."','".$data['q4_2020_chq_amt']."','".$data['q1_2021_loop']."','".$data['q1_2021_letter']."','".$data['q1_2021_check']."','".$data['q1_2021_chq_amt']."','".$data['q2_2021_loop']."','".$data['q2_2021_letter']."','".$data['q2_2021_check']."','".$data['q2_2021_chq_amt']."','".$data['q3_2021_loop']."','".$data['q3_2021_letter']."','".$data['q3_2021_check']."','".$data['q3_2021_chq_amt']."','".$data['q4_2021_loop']."','".$data['q4_2021_letter']."','".$data['q4_2021_check']."','".$data['q4_2021_chq_amt']."','".$data['i_invoice_no']."','".$data['i_invoice_amount']."','".$data['i_invoiced_qtrs']."','".$data['i_invoice_sent_date']."','".$data['i_invoice_payment_type']."','".$data['i_invoice_payment_date']."','".$data['i_invoice_pay_cleared']."','".$data['i_invoice_pay_returned']."','".$data['i_invoice_return_reason']."','".$data['i_invoice_occams_share']."','".$data['i_invoice_aff_ref_share']."','".$data['ii_invoice_no']."','".$data['ii_invoice_amount']."','".$data['ii_invoiced_qtrs']."','".$data['ii_invoice_sent_date']."','".$data['ii_invoice_payment_type']."','".$data['ii_invoice_payment_date']."','".$data['ii_invoice_pay_cleared']."','".$data['ii_invoice_pay_returned']."','".$data['ii_invoice_return_reason']."','".$data['ii_invoice_occams_share']."','".$data['ii_invoice_aff_ref_share']."','".$data['iii_invoice_no']."','".$data['iii_invoice_amount']."','".$data['iii_invoiced_qtrs']."','".$data['iii_invoice_sent_date']."','".$data['iii_invoice_payment_type']."','".$data['iii_invoice_payment_date']."','".$data['iii_invoice_pay_cleared']."','".$data['iii_invoice_pay_returned']."','".$data['iii_invoice_return_reason']."','".$data['iii_invoice_occams_share']."','".$data['iii_invoice_aff_ref_share']."','".$data['iv_invoice_no']."','".$data['iv_invoice_amount']."','".$data['iv_invoiced_qtrs']."','".$data['iv_invoice_sent_date']."','".$data['iv_invoice_payment_type']."','".$data['iv_invoice_payment_date']."','".$data['iv_invoice_pay_cleared']."','".$data['iv_invoice_pay_returned']."','".$data['iv_invoice_return_reason']."','".$data['iv_invoice_occams_share']."','".$data['iv_invoice_aff_ref_share']."','".$data['q1_2021_max_erc_amount']."','".$data['q2_2021_max_erc_amount']."','".$data['q3_2021_max_erc_amount']."','".$data['q4_2021_max_erc_amount']."','".$data['q1_2020_max_erc_amount']."','".$data['q2_2020_max_erc_amount']."','".$data['q3_2020_max_erc_amount']."','".$data['q4_2020_max_erc_amount']."')"
            );
        }
    }
    //Update Fulfilment Tab
    if(isset($data['income_2019'])){
      $new_fulfilment_data = array();
      $new_fulfilment_data['income_2019'] = $data['income_2019'];
      $new_fulfilment_data['income_2020'] = $data['income_2020'];
      $new_fulfilment_data['income_2021'] = $data['income_2021'];
      $new_fulfilment_data['stc_amount_2020'] = $data['stc_amount_2020'];
      $new_fulfilment_data['stc_amount_2021'] = $data['stc_amount_2021'];
      $new_fulfilment_data['maximum_credit'] = $data['maximum_credit'];
      $new_fulfilment_data['actual_credit'] = $data['actual_credit'];
      $new_fulfilment_data['actual_fee'] = $data['actual_fee'];
      $new_fulfilment_data['estimated_fee'] = $data['estimated_fee'];
      $new_fulfilment_data['years'] = $data['years'];
    
      $this->project_log_audit_entry($user_id,$project_id, $project_info, $new_fulfilment_data,$project_table);   
      $wpdb->query("UPDATE {$wpdb->prefix}projects SET 
                     income_2019 = '".$data['income_2019']."',
                     income_2020 = '".$data['income_2020']."',
                     income_2021 = '".$data['income_2021']."',
                     stc_amount_2020 = '".$data['stc_amount_2020']."',
                     stc_amount_2021 = '".$data['stc_amount_2021']."',
                     maximum_credit = '".$data['maximum_credit']."',
                     actual_credit = '".$data['actual_credit']."',
                     actual_fee = '".$data['actual_fee']."',
                     estimated_fee = '".$data['estimated_fee']."',
                     years = '".$data['years']."'
                     WHERE project_id = ".$project_id."
                     ");
    }

    $results['status'] = 1;
    $results['message'] = 'Project updated successfully';
}else{
    $results['status'] = 0;
    $results['message'] = 'No lead found.';
}
echo json_encode($results);die;
?>