<?php
/**
 * CRM ERP Audit Logs REST API
 *
 * Provides REST API endpoints for audit logs functionality
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class CRM_ERP_Audit_Logs_API {
    
    public function __construct() {
        add_action('rest_api_init', array($this, 'register_api_routes'));
    }

    public function register_api_routes() {
        header("Access-Control-Allow-Origin: *");
        header("Access-Control-Allow-Methods: GET, POST, OPTIONS, PUT, DELETE");
        header("Access-Control-Allow-Headers: Content-Type, Authorization");

        // Project Fields Audit Logs
        register_rest_route('productsplugin/v1', '/project-fields-audit-logs', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_project_fields_audit_logs'),
            'permission_callback' => '__return_true'
        ));

        // Milestone & Stages Audit Logs
        register_rest_route('productsplugin/v1', '/milestone-stages-audit-logs', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_milestone_stages_audit_logs'),
            'permission_callback' => '__return_true'
        ));

        // Invoices Audit Logs
        register_rest_route('productsplugin/v1', '/invoices-audit-logs', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_invoices_audit_logs'),
            'permission_callback' => '__return_true'
        ));

        // Documents Audit Logs
        register_rest_route('productsplugin/v1', '/documents-audit-logs', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_documents_audit_logs'),
            'permission_callback' => '__return_true'
        ));

        // Business Audit Logs
        register_rest_route('productsplugin/v1', '/business-audit-logs', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_business_audit_logs'),
            'permission_callback' => '__return_true'
        ));

        // Milestones
        register_rest_route('productsplugin/v1', '/milestones', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_custom_milestones'),
            'permission_callback' => '__return_true'
        ));

        // Stages
        register_rest_route('productsplugin/v1', '/milestone-stages', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_milestone_stages'),
            'permission_callback' => '__return_true'
        ));

        // collaborators
        register_rest_route('productsplugin/v1', '/collaborators', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_project_collaborators'),
            'permission_callback' => '__return_true'
        ));
    }

    function get_project_collaborators(WP_REST_Request $request) {
        global $wpdb;

        /*$project_id = $request->get_param('project_id');

        if (!$project_id) {
            return new WP_Error('missing_parameter', 'Project ID is required', array('status' => 400));
        }

        $project_id = intval($project_id);
        $collaborators_table = $wpdb->prefix . 'collaborators';

        $query = $wpdb->prepare("SELECT * FROM {$collaborators_table} WHERE project_id = %d", $project_id);
        $results = $wpdb->get_results($query);*/

        $collaborator_users = array(
            'role__in' => array(
                'iris_sales_agent',
                'iris_sales_agent_rep',
                'master_sales',
                'echeck_staff',
                'echeck_admin',
                'master_ops'
            ),
            'orderby' => 'display_name',
            'order'   => 'ASC',
            'fields'  => array('ID', 'display_name')
        );

        $listed_users = get_users($collaborator_users);

        $user_list = array_map(function($user) {
            return array(
                'ID'           => $user->ID,
                'display_name' => $user->display_name
            );
        }, $listed_users);

        return rest_ensure_response($user_list);
    }

    function get_custom_milestones(WP_REST_Request $request) {
        global $wpdb;
        $milestone_table = $wpdb->prefix . 'milestones';

        $product_id = $request->get_param('product_id');
        if (!$product_id) {
            return new WP_Error('missing_parameter', 'Product ID is required', array('status' => 400));
        }
        $where_milestone = '1=1';
        if (!empty($product_id)) {
            // Sanitize and safely inject into SQL
            $product_id = intval($product_id);
            $where_milestone .= $wpdb->prepare(" AND FIND_IN_SET(%d, {$milestone_table}.product_id)", $product_id);
        }

        $query = "
        SELECT 
            {$milestone_table}.milestone_id,
            {$milestone_table}.milestone_name,
            {$milestone_table}.map,
            {$milestone_table}.status,
            {$milestone_table}.deleted_at
        FROM {$milestone_table}
        WHERE {$where_milestone}
          AND {$milestone_table}.status = 'active'
          AND {$milestone_table}.deleted_at IS NULL
          AND {$milestone_table}.map LIKE %s
    ";

        $like = '%"project"%';
        $results = $wpdb->get_results($wpdb->prepare($query, $like));

        return rest_ensure_response($results);
    }

    function get_milestone_stages(WP_REST_Request $request) {
        global $wpdb;
        $milestone_status_table = $wpdb->prefix . 'milestone_stages';

        $milestone_id = $request->get_param('milestone_id');
        if (!$milestone_id) {
            return new WP_Error('missing_parameter', 'Milestone ID is required', array('status' => 400));
        }
        $where_stage = '1=1';
        if (!empty($milestone_id)) {
            $milestone_id = intval($milestone_id);
            $where_stage .= $wpdb->prepare(" AND {$milestone_status_table}.milestone_id = %d", $milestone_id);
        }

        $query = "
        SELECT 
            {$milestone_status_table}.milestone_stage_id,
            {$milestone_status_table}.stage_name,
            {$milestone_status_table}.status,
            {$milestone_status_table}.deleted_at
        FROM {$milestone_status_table}
        WHERE {$where_stage}
          AND {$milestone_status_table}.status = 'active'
          AND {$milestone_status_table}.deleted_at IS NULL
    ";

        $results = $wpdb->get_results($query);

        return rest_ensure_response($results);
    }

    /**
     * Get project fields audit logs
     */
    public function get_project_fields_audit_logs($request) {
        global $wpdb;
        //ini_set('display_errors', 1);
        //ini_set('display_startup_errors', 1);
        //error_reporting(E_ALL);
        $table_name = $wpdb->prefix . 'audit_logs';

        $project_id = $request->get_param('project_id');
        $lead_id = $request->get_param('lead_id');

        if (!$project_id || !$lead_id) {
            return new WP_Error('missing_parameter', 'Project ID and Lead ID are required', array('status' => 400));
        }

        $query = "
        SELECT * FROM $table_name
        WHERE
            (FieldID = $project_id AND TableName = 'eccom_projects' AND FieldName NOT IN ('milestone_id', 'milestone_stage_id', 'modified_at', 'modified_by'))
            OR (FieldID = $lead_id AND TableName IN ('eccom_erc_bank_info', 'eccom_erc_erc_intake', 'eccom_erc_erc_fees', 'eccom_erc_business_info'))
            OR (FieldID = $project_id AND TableName = 'eccom_collaborators')
        ORDER BY LogID DESC
    ";

        $audit_logs = $wpdb->get_results($query);
        $output = [];
        $no = 1;

        foreach ($audit_logs as $value) {
            $fieldname = $value->FieldName;
            $from = $this->get_field_meaning($fieldname, $value->BeforeValueString);
            $to = $this->get_field_meaning($fieldname, $value->AfterValueString);

            $change_date = $value->DateCreated ? date('m/d/Y H:i:s', strtotime($value->DateCreated)) : '';
            $user_name = '';

            if ($value->CreatedBy) {
                $user_details = get_user_by('id', $value->CreatedBy);
                if ($user_details) {
                    $user_name = $user_details->display_name;
                }
            }

            $fieldname = ucfirst(str_replace('_', ' ', $this->get_field_name($fieldname)));

            // Normalize NO/N/A edge cases
            if (
                ($from == 'NO' && $to == '') ||
                ($from == '' && $to == 'NO') ||
                ($from == '' && $to == 'N/A') ||
                ($from == 'N/A' && $to == '')
            ) {
                $to = 'NO';
                $from = 'NO';
            }

            if (trim($from) != trim($to)) {
                $output[] = [
                    'no' => $no,
                    'field_name' => $fieldname,
                    'from' => $from,
                    'to' => $to,
                    'change_date' => $change_date,
                    'user_name' => $user_name
                ];
                $no++;
            }
        }

        return $output;
        /*$table_name = $wpdb->prefix . 'audit_logs';
        
        $audit_logs = $wpdb->get_results("SELECT $table_name.* FROM $table_name WHERE (FieldID=$project_id AND TableName='eccom_projects' AND FieldName!='milestone_id' AND FieldName!='milestone_stage_id' AND FieldName!='modified_at' AND FieldName!='modified_by') OR (FieldID=$lead_id AND TableName='eccom_erc_bank_info') OR (FieldID=$lead_id AND TableName='eccom_erc_erc_intake') OR (FieldID=$lead_id AND TableName='eccom_erc_erc_fees') OR (FieldID=$lead_id AND TableName='eccom_erc_business_info') OR (FieldID=$project_id AND TableName='eccom_collaborators') ORDER BY LogID DESC");
        
        $formatted_logs = array();
        foreach ($audit_logs as $key => $value) {
            $fieldname = $value->FieldName;
            $from = $value->BeforeValueString;
            $to = $value->AfterValueString;
            $change_date = $value->DateCreated;
            $changed_by = $value->CreatedBy;
            $user_name = '';
            if ($changed_by) {
                $user_details = get_user_by('id', $changed_by);
                $user_name = $user_details ? $user_details->display_name : '';
            }
            
            $formatted_logs[] = array(
                'field_name' => $fieldname,
                'from' => $from,
                'to' => $to,
                'change_date' => $change_date,
                'user_name' => $user_name
            );
        }
        
        return $formatted_logs;*/
    }

    /**
     * Get milestone & stages audit logs
     */
    public function get_milestone_stages_audit_logs($request) {
        global $wpdb;

        $project_id = $request->get_param('project_id');
        if (!$project_id) {
            return new WP_Error('missing_parameter', 'Project ID is required', array('status' => 400));
        }

        $milestone_stage_log = $wpdb->prefix . 'milestone_stage_log';
        $milestones_table    = $wpdb->prefix . 'milestones';
        $stage_table         = $wpdb->prefix . 'milestone_stages';

        $results = $wpdb->get_results("SELECT * FROM $milestone_stage_log WHERE project_id = $project_id ORDER BY id DESC");

        $log_history = [];
        $no = 1;

        foreach ($results as $row) {
            $updated_milestone_id = $row->updated_milestone_id;
            $previous_milestone_id = $row->previous_milestone_id;
            $updated_stage_id = $row->updated_stage_id;
            $previous_stage_id = $row->previous_stage_id;
            $change_date = $row->changed_date;
            $changed_by = $row->changed_by;

            $user_name = '';
            if ($changed_by) {
                $user = get_user_by('id', $changed_by);
                $user_name = $user ? $user->display_name : '';
            }

            $milestone_name = $wpdb->get_var("SELECT milestone_name FROM $milestones_table WHERE milestone_id = $updated_milestone_id");
            $pre_milestone_name = $wpdb->get_var("SELECT milestone_name FROM $milestones_table WHERE milestone_id = $previous_milestone_id");

            $stage_name = $wpdb->get_var("SELECT stage_name FROM $stage_table WHERE milestone_stage_id = $updated_stage_id");
            $pre_stage_name = $wpdb->get_var("SELECT stage_name FROM $stage_table WHERE milestone_stage_id = $previous_stage_id");

            $formatted_date = $change_date ? date('m/d/Y H:i:s', strtotime($change_date)) : '';

            $log_history[] = [
                'no' => $no,
                'previous_milestone' => $pre_milestone_name,
                'updated_milestone' => $milestone_name,
                'previous_stage' => $pre_stage_name,
                'updated_stage' => $stage_name,
                'changed_date' => $formatted_date,
                'changed_by' => $user_name
            ];

            $no++;
        }
        return $log_history;
    }

    /**
     * Get invoices audit logs
     */
    public function get_invoices_audit_logs($request) {
        global $wpdb;

        $lead_id = $request->get_param('lead_id');
        $product_id = $request->get_param('product_id');
        
        if (!$lead_id || !$product_id) {
            return new WP_Error('missing_parameter', 'Lead ID and Product ID are required', array('status' => 400));
        }

        $invoice_table = $wpdb->prefix . 'invoices';
        $audit_log_table = $wpdb->prefix . 'invoice_audit_logs';
        $users_table = $wpdb->prefix . 'users';

        $query = "
        SELECT 
            inv.id,
            inv.invoice_no,
            inv.customer_id,
            inv.customer_invoice_no,
            inv_al.*,
            inv_user.display_name
        FROM $invoice_table inv
        LEFT JOIN $audit_log_table inv_al ON inv.id = inv_al.action_id
        LEFT JOIN $users_table inv_user ON inv_al.CreatedBy = inv_user.ID
        WHERE inv.lead_id = $lead_id
          AND inv.parent_product = $product_id
          AND (
            inv_al.FieldName IN ('customer_invoice_no', 'invoice_date', 'due_date', 'total_amount')
            OR inv_al.FieldName LIKE 'productdata.%'
        )";

        $results = $wpdb->get_results($query);
        $log_history = [];
        $no = 1;

        $service_head_table = $wpdb->prefix . "product_service_head";
        $service_head_query = "SELECT * FROM $service_head_table";
        $service_head_results = $wpdb->get_results($service_head_query, ARRAY_A);

        foreach ($results as $row) {
            $field_name = $this->clear_text_to_show($row->FieldName); // custom function

            // Custom logic
            $before_value = $row->BeforeValueString;
            $after_value = $row->AfterValueString;

            if (preg_match('/\d+\.product_name+/', $row->FieldName)) {
                $before_value = $before_value ? $this->find_product_head_by_qb_product_id($before_value, $service_head_results) : '';
                $after_value = $after_value ? $this->find_product_head_by_qb_product_id($after_value, $service_head_results) : '';
            } elseif (preg_match('/\d+\.discount_type+/', $row->FieldName)) {
                $before_value = ($before_value !== '') ? (($before_value == 1) ? '%' : '$') : '';
                $after_value = ($after_value !== '') ? (($after_value == 1) ? '%' : '$') : '';
            }

            $log_history[] = [
                'no' => $no,
                'customer_invoice_no' => $row->customer_invoice_no,
                'field_name' => $field_name,
                'before_value' => $before_value,
                'after_value' => $after_value,
                'changed_date' => $this->date_formate_as_asked($row->DateCreated), // custom function
                'changed_by' => $row->display_name
            ];
            $no++;
        }

        return $log_history;
    }

    /**
     * Get documents audit logs
     */
    public function get_documents_audit_logs($request) {
        global $wpdb;

        $lead_id = $request->get_param('lead_id');
        $product_id = $request->get_param('product_id');

        if (!$lead_id || !$product_id) {
            return new WP_Error('missing_parameter', 'Lead ID and Product ID are required', array('status' => 400));
        }

        // Document-enabled product configuration
        $is_document_enabled = in_array($product_id, [935, 937, 936]);

        if (!$is_document_enabled) {
            return rest_ensure_response([]); // Return empty if product doesn't support it
        }

        $products = [
            "935" => [
                "ERC" => [
                    1 => "Company Documents",
                    2 => "Payroll Documents",
                    3 => "ERC Documents",
                    4 => "Other Documents"
                ]
            ],
            "937" => [
                "STC" => [
                    5 => "Impacted Days",
                    6 => "Required Documents"
                ]
            ],
            "936" => [
                "Tax Amendment" => [
                    7 => "Required Documents",
                    8 => "Additional Documents"
                ]
            ]
        ];

        $form_id = $this->get_form_id($product_id, $products); // Assume defined elsewhere
        if ($form_id == "") {
            $form_id = 0;
        }

        $query = "
        SELECT 
            ldn.comments,
            ldn_mapping.doc_label,
            ldn.update_datetime,
            inv_user.display_name,
            ldn_mapping.form_id
        FROM {$wpdb->prefix}leads_document_notes ldn
        LEFT JOIN {$wpdb->prefix}users inv_user ON ldn.user_id = inv_user.ID
        LEFT JOIN {$wpdb->prefix}leads_document_upload ldn_upload ON ldn.lead_id = ldn_upload.lead_id
        LEFT JOIN {$wpdb->prefix}leads_document_mapping ldn_mapping ON ldn_mapping.doc_type_id = ldn.doc_type_id
        WHERE ldn.lead_id = $lead_id AND ldn_mapping.form_id IN ($form_id)
        GROUP BY ldn.update_datetime
        ORDER BY ldn.update_datetime DESC
    ";

        $results = $wpdb->get_results($query);
        $output = [];
        $no = 1;

        foreach ($results as $row) {
            $output[] = [
                'no' => $no,
                'form_name' => $this->get_form_name($row->form_id, $products), // Assume defined elsewhere
                'comments' => $row->comments,
                'doc_label' => $row->doc_label,
                'update_datetime' => $this->date_formate_as_asked($row->update_datetime),
                'user_name' => $row->display_name
            ];
            $no++;
        }
        return $output;

        /*$lead_id = $request->get_param('lead_id');
        $product_id = $request->get_param('product_id');
        
        if (!$lead_id || !$product_id) {
            return new WP_Error('missing_parameter', 'Lead ID and Product ID are required', array('status' => 400));
        }
        
        $is_document_enabled = false;
        if (in_array($product_id, array(935, 937, 936))) {
            $is_document_enabled = true;
        }
        
        if ($is_document_enabled == false) {
            return array();
        }
        
        $products = array(
            "935" => array(
                "ERC" => array(
                    1 => "Company Documents",
                    2 => "Payroll Documents",
                    3 => "ERC Documents",
                    4 => "Other Documents"
                )
            ),
            "937" => array(
                "STC" => array(
                    5 => "Impacted Days",
                    6 => "Required Documents"
                )
            ),
            "936" => array(
                "Tax Amendment" => array(
                    7 => "Required Documents",
                    8 => "Additional Documents"
                )
            ),
        );
        
        $form_id = $this->get_form_id($product_id, $products);
        
        if ($form_id == "") {
            $form_id = 0;
        }
        
        $document_audit_log_query = "
            SELECT 
                ldn.comments,
                ldn_mapping.doc_label,
                ldn.update_datetime,
                inv_user.display_name,
                ldn_mapping.form_id
            FROM {$wpdb->prefix}leads_document_notes ldn
            LEFT JOIN
                {$wpdb->prefix}users inv_user
            ON 
                ldn.user_id = inv_user.ID
            LEFT JOIN 
                {$wpdb->prefix}leads_document_upload ldn_upload
            ON 
                ldn.lead_id = ldn_upload.lead_id 
            LEFT JOIN 
                {$wpdb->prefix}leads_document_mapping ldn_mapping
            ON
                ldn_mapping.doc_type_id = ldn.doc_type_id
            WHERE 
                ldn.lead_id = %d AND ldn_mapping.form_id IN ($form_id)
            GROUP BY
                ldn.update_datetime
            ORDER BY 
                ldn.update_datetime DESC";
        
        $document_audit_logs = $wpdb->get_results($wpdb->prepare($document_audit_log_query, $lead_id));
        
        $formatted_logs = array();
        foreach ($document_audit_logs as $key => $value) {
            $formatted_logs[] = array(
                'form_name' => $this->get_form_name($value->form_id, $products),
                'comments' => $value->comments,
                'doc_label' => $value->doc_label,
                'update_datetime' => $value->update_datetime,
                'user_name' => $value->display_name
            );
        }*/
        
        return $formatted_logs;
    }

    /**
     * Get business audit logs
     */
    public function get_business_audit_logs($request) {
        global $wpdb;

        $lead_id = $request->get_param('lead_id');

        if (!$lead_id) {
            return new WP_Error('missing_parameter', 'Lead ID is required', array('status' => 400));
        }

        $audit_log_table = $wpdb->prefix . 'erc_audit_log';
        $mapping_table = $wpdb->prefix . 'erc_iris_mapping_table';

        $query = "
        SELECT $audit_log_table.*, $mapping_table.iris_label
        FROM $audit_log_table
        LEFT JOIN $mapping_table
        ON $audit_log_table.field_name = $mapping_table.db_field_name
        WHERE $audit_log_table.lead_id = $lead_id
        ORDER BY $audit_log_table.change_date DESC
    ";

        $results = $wpdb->get_results($query);
        $data = [];

        foreach ($results as $row) {
            if (
                ($row->from === '' && $row->to === '') ||
                ($row->from === '' && $row->to === 'N/A') ||
                ($row->field_name === '')
            ) {
                continue;
            }

            $data[$row->id] = $row;
        }

        $output = [];
        $c = 1;

        foreach ($data as $row) {
            $user_data = get_user_by('id', $row->change_by);
            $changed_by = $user_data ? $user_data->display_name : '';

            // Determine readable field name
            if ($row->field_name === 'assign_user') {
                $field_name = 'Assign user';
            } elseif ($row->field_name === 'unassign_user') {
                $field_name = 'Unassign user';
            } elseif (in_array($row->field_name, ['category', 'Category'])) {
                $field_name = 'Category';
            } elseif (in_array($row->field_name, ['status', 'lead_status'])) {
                $field_name = 'Status';
            } elseif (!empty($row->note)) {
                $field_name = 'Note';
            } else {
                $field_name = $row->iris_label ?: $row->field_name;
            }

            $output[] = [
                'no' => $c,
                'field_name' => $field_name,
                'from' => $row->from,
                'to' => $row->to,
                'note' => $row->note,
                'change_date' => $this->date_formate_as_asked($row->change_date), // custom function
                'changed_by' => $changed_by
            ];

            $c++;
        }

        return $output;
        /*$lead_id = $request->get_param('lead_id');
        
        if (!$lead_id) {
            return new WP_Error('missing_parameter', 'Lead ID is required', array('status' => 400));
        }
        
        $audit_log_table = $wpdb->prefix . 'erc_audit_log';
        $mapping_table = $wpdb->prefix . 'erc_iris_mapping_table';
        
        $lead_audit_logs = $wpdb->get_results($wpdb->prepare(
            "SELECT $audit_log_table.*, $mapping_table.iris_label 
            FROM $audit_log_table 
            LEFT JOIN $mapping_table ON $audit_log_table.field_name = $mapping_table.db_field_name 
            WHERE $audit_log_table.lead_id = %d 
            ORDER BY $audit_log_table.change_date DESC",
            $lead_id
        ));
        
        $data = array();
        foreach ($lead_audit_logs as $key => $value) {
            if(($value->from =='' && $value->to =='') || ($value->from =='' && $value->to =='N/A') || ($value->field_name =='')){
                // Skip empty entries
            } else {
                $id = $value->id;
                $user = get_user_by('id', $value->user_id);
                $data[$id] = array(
                    'id' => $value->id,
                    'field_name' => $value->field_name,
                    'iris_label' => $value->iris_label,
                    'from' => $value->from,
                    'to' => $value->to,
                    'change_date' => $value->change_date,
                    'user_id' => $value->user_id,
                    'user_name' => $user ? $user->display_name : ''
                );
            }
        }
        
        return array_values($data);*/
    }

    function get_field_name($fieldname){

        if($fieldname=='2020_q1_4144'){
            $fieldname = '2020_q1_941';
        }else if($fieldname =='2020_q2_4145'){
            $fieldname = '2020_q2_941';
        }else if($fieldname =='2020_q3_4146'){
            $fieldname = '2020_q3_941';
        }else if($fieldname =='2020_q4_4147'){
            $fieldname = '2020_q4_941';
        }else if($fieldname =='2021_q1_4149'){
            $fieldname = '2021_q1_941';
        }else if($fieldname =='2021_q2_4151'){
            $fieldname = '2021_q2_941';
        }else if($fieldname =='2021_q3_4152'){
            $fieldname = '2021_q3_941';
        }else if($fieldname =='2020_q1_4155'){
            $fieldname = '2020_q1_payroll';
        }else if($fieldname =='2020_q2_4156'){
            $fieldname = '2020_q2_payroll';
        }else if($fieldname =='2020_q3_4157'){
            $fieldname = '2020_q3_payroll';
        }else if($fieldname =='2020_q4_4158'){
            $fieldname = '2020_q4_payroll';
        }else if($fieldname =='2021_q1_4160'){
            $fieldname = '2021_q1_payroll';
        }else if($fieldname =='2021_q2_4161'){
            $fieldname = '2021_q2_payroll';
        }else if($fieldname =='2021_q3_4162'){
            $fieldname = '2021_q3_payroll';
        }else if($fieldname =='filing_date_4267'){
            $fieldname = 'q1_2020_filed_date';
        }else if($fieldname =='amount_filed_4263'){
            $fieldname = 'q1_2020_amount_filed';
        }else if($fieldname =='filing_date_4268'){
            $fieldname = 'q2_2020_filed_date';
        }else if($fieldname =='amount_filed_4269'){
            $fieldname = 'q2_2020_amount_filed';
        }else if($fieldname =='filing_date_4270'){
            $fieldname = 'q3_2020_filed_date';
        }else if($fieldname =='amount_filed_4266'){
            $fieldname = 'q3_2020_amount_filed';
        }else if($fieldname =='filing_date_4272'){
            $fieldname = 'q4_2020_filed_date';
        }else if($fieldname =='amount_filed_4273'){
            $fieldname = 'q4_2020_amount_filed';
        }else if($fieldname =='filing_date_4276'){
            $fieldname = 'q1_2021_filed_date';
        }else if($fieldname =='amount_filed_4277'){
            $fieldname = 'q1_2021_amount_filed';
        }else if($fieldname =='filing_date_4279'){
            $fieldname = 'q2_2021_filed_date';
        }else if($fieldname =='amount_filed_4280'){
            $fieldname = 'q2_2021_amount_filed';
        }else if($fieldname =='filing_date_4282'){
            $fieldname = 'q3_2021_filed_date';
        }else if($fieldname =='amount_filed_4283'){
            $fieldname = 'q3_2021_amount_filed';
        }else if($fieldname =='filing_date_4285'){
            $fieldname = 'q4_2021_filed_date';
        }else if($fieldname =='amount_filed_4286'){
            $fieldname = 'q4_2021_amount_filed';
        }else if($fieldname =='avg_emp_count_2019'){
            $fieldname = 'average_employee_count_2019';
        }else if($fieldname =='bal_retainer_return_reaso'){
            $fieldname = 'balance_retainer_return_reason';
        }

        return $fieldname;

    }

    function get_field_meaning($fieldname,$value){
        $field_meaning = $value;
        if($fieldname=='business_entity_type'){
            if($value==1){
                $field_meaning = 'N/A';
            }else if($value==2){
                $field_meaning = 'Limited Liability (LLC)';
            }else if($value==3){
                $field_meaning = 'Partnership';
            }else if($value==4){
                $field_meaning = 'Sole Proprietorship';
            }else if($value==5){
                $field_meaning = 'Other';
            }else if($value==6){
                $field_meaning = 'Corporation (S,C,B,etc)';
            }else if($value==7){
                $field_meaning = 'Trust';
            }
        }else if($fieldname=='account_type'){
            if($value==1){
                $field_meaning = 'N/A';
            }else if($value==2){
                $field_meaning = 'Savings';
            }else if($value==3){
                $field_meaning = 'Checking';
            }else if($value==4){
                $field_meaning = 'Other';
            }
        }else if($fieldname=='avg_emp_count_2019'){
            if($value==0){
                $field_meaning = 'N/A';
            }else if($value==1){
                $field_meaning = 'Less Than 100';
            }else if($value==2){
                $field_meaning = 'Between 100-500';
            }else if($value==3){
                $field_meaning = 'More Than 500';
            }
        }else if($fieldname=='retainer_payment_type'){
            if($value==1){
                $field_meaning = 'ACH';
            }else if($value==2){
                $field_meaning = 'CC/DB Card';
            }
        }else if($fieldname=='coi_aoi' || $fieldname=='voided_check' || $fieldname=='2019_tax_return' || $fieldname=='2020_tax_return' || $fieldname=='2021_financials'){
            if($value==1){
                $field_meaning = 'YES';
            }else if($value==2){
                $field_meaning = 'NO';
            }else if($value==3){
                $field_meaning = 'N/A';
            }
        }else if($fieldname=='2020_q1_941' || $fieldname=='2020_q2_941' || $fieldname=='2020_q3_941' || $fieldname=='2020_q4_941' || $fieldname=='2021_q1_941' || $fieldname=='2021_q2_941' ||$fieldname=='2021_q3_941' || $fieldname=='2020_q1_payroll' || $fieldname=='2020_q2_payroll' || $fieldname=='2020_q3_payroll' || $fieldname=='2020_q4_payroll' || $fieldname=='2021_q1_payroll' || $fieldname=='2021_q2_payroll' || $fieldname=='2021_q3_payroll' || $fieldname=='ppp_1_applied' || $fieldname=='ppp_1_forgiveness_applied' || $fieldname=='ppp_2_applied' || $fieldname=='ppp_2_forgiveness_applied' || $fieldname=='2020_q1_4144' || $fieldname =='2020_q2_4145' || $fieldname =='2020_q3_4146' || $fieldname =='2020_q4_4147' || $fieldname =='2021_q1_4149' || $fieldname =='2021_q2_4151' || $fieldname =='2021_q3_4152' || $fieldname =='2020_q1_4155' || $fieldname =='2020_q2_4156' || $fieldname =='2020_q3_4157' || $fieldname =='2020_q4_4158' || $fieldname =='2021_q1_4160' || $fieldname =='2021_q2_4161' || $fieldname =='2021_q3_4162'){
            if($value==1){
                $field_meaning = 'N/A';
            }else if($value==2){
                $field_meaning = 'YES';
            }else if($value==3){
                $field_meaning = 'NO';
            }
        }else if($fieldname=='created_by'){
            $user_details = get_user_by('id', $value);
            $field_meaning = $user_details->display_name;
        }

        return $field_meaning;
    }

    function clear_text_to_show($key)
    {
        $key = str_replace('_', ' ', $key);
        if (preg_match('/productdata\.(\d+)/', $key, $matches)) {
            $key = preg_replace('/productdata\.\d+/', 'Service  ' . $matches[1] + 1 . " - ", $key);
        } elseif (strpos($key, 'productdata') !== false) {
            $key = str_replace('productdata', 'Product', $key);
        }
        $key = str_replace('.', ' ', $key);
        $key = str_replace('product', '', $key);
        $key = ucwords($key);

        return $key;
    }

    function date_formate_as_asked($requested_date)
    {
        $formatted_date = date('m/d/Y H:i:s', strtotime($requested_date));
        return $formatted_date;
    }

    function get_form_id($product_id, $products)
    {

        if (isset($products[$product_id])) {
            $category = current($products[$product_id]);
            $form_ids = array_keys($category);
            return implode(', ', $form_ids);
        }

        return '';
    }

    function get_form_name($form_id, $products)
    {

        foreach ($products as $product) {
            foreach ($product as $category) {
                if (isset($category[$form_id])) {
                    return $category[$form_id];
                }
            }
        }

        return 'Form ID not found';
    }

    function find_product_head_by_qb_product_id($product_id, $products)
    {
        foreach ($products as $product) {
            if ($product['qb_product_id'] == $product_id) {
                return $product['product_head'];
            }
        }
        return "Product head missing.";
    }

}

// Initialize the API
new CRM_ERP_Audit_Logs_API();

