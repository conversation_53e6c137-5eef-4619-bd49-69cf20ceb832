<?php
/**
 * The admin-specific functionality of the plugin.
 */

class CRM_ERP_Admin_Interface {

    /**
     * Initialize the class and set its properties.
     */
    public function __construct() {
		require_once plugin_dir_path(__FILE__) . 'partials/oc-crm-settings-class.php';
        add_action('wp_ajax_oc_save_crm_settings', array($this,'oc_save_crm_settings'));
		add_action('wp_ajax_oc_save_product_roles', array($this,'oc_save_product_roles'));
		add_action('wp_ajax_fetch_users_by_role_crm', array($this,'fetch_users_by_role_crm'));		
        
        add_action('admin_menu', array($this, 'add_admin_menus'));
        
        add_action('wp_ajax_opportunity_submit', array($this,'opportunity_submit_handler'));
        add_action('wp_ajax_nopriv_opportunity_submit', array($this,'opportunity_submit_handler'));

        add_action('wp_ajax_opportunity_listing_ajax', array($this,'opportunity_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_opportunity_listing_ajax', array($this,'opportunity_listing_ajax_handler'));

        add_action('wp_ajax_stc_opportunity_listing_ajax', array($this,'stc_opportunity_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_stc_opportunity_listing_ajax', array($this,'stc_opportunity_listing_ajax_handler'));

        add_action('wp_ajax_ertc_opportunity_listing_ajax', array($this,'ertc_opportunity_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_ertc_opportunity_listing_ajax', array($this,'ertc_opportunity_listing_ajax_handler'));

        add_action('wp_ajax_ertc_opportunity_search_ajax', array($this,'ertc_opportunity_search_ajax_handler'));
        add_action('wp_ajax_nopriv_ertc_opportunity_search_ajax', array($this,'ertc_opportunity_search_ajax_handler'));

        add_action('wp_ajax_partnership_opportunity_listing_ajax', array($this,'partnership_opportunity_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_partnership_opportunity_listing_ajax', array($this,'partnership_opportunity_listing_ajax_handler'));

        add_action('wp_ajax_partnership_opportunity_search_ajax', array($this,'partnership_opportunity_search_ajax_handler'));
        add_action('wp_ajax_nopriv_partnership_opportunity_search_ajax', array($this,'partnership_opportunity_search_ajax_handler'));

        // ----- get opportunity filter data by ajax
        add_action('wp_ajax_fetch_opportunity_filter',array($this,'fetch_opportunity_filter_callback'));
        add_action('wp_ajax_fetch_opportunity_filter',array($this,'fetch_opportunity_filter_callback'));

        // ----- get stc opportunity filter data by ajax
        add_action('wp_ajax_fetch_stc_opportunity_filter',array($this,'fetch_stc_opportunity_filter_callback'));
        add_action('wp_ajax_fetch_stc_opportunity_filter',array($this,'fetch_stc_opportunity_filter_callback'));

        // ----- get ta opportunity filter data by ajax
        add_action('wp_ajax_fetch_ta_opportunity_filter',array($this,'fetch_ta_opportunity_filter_callback'));
        add_action('wp_ajax_fetch_ta_opportunity_filter',array($this,'fetch_ta_opportunity_filter_callback'));

        add_action('wp_ajax_fetch_audit_advisory_opportunity_filter',array($this,'fetch_audit_advisory_opportunity_filter_callback'));
        add_action('wp_ajax_fetch_rdc_opportunity_filter',array($this,'fetch_rdc_opportunity_filter_callback'));
        // ----- get ertc opportunity filter data by ajax
        add_action('wp_ajax_fetch_ertc_opportunity_filter',array($this,'fetch_ertc_opportunity_filter_callback'));
        add_action('wp_ajax_fetch_ertc_opportunity_filter',array($this,'fetch_ertc_opportunity_filter_callback'));

        add_action('wp_ajax_fetch_partnership_opportunity_filter',array($this,'fetch_partnership_opportunity_filter_callback'));
        add_action('wp_ajax_fetch_partnership_opportunity_filter',array($this,'fetch_partnership_opportunity_filter_callback'));
		
		        // ----- get tax amendment opportunity filter data by ajax
				
        add_action('wp_ajax_ta_opportunity_listing_ajax', array($this,'ta_opportunity_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_ta_opportunity_listing_ajax', array($this,'ta_opportunity_listing_ajax_handler'));

        add_action('wp_ajax_audit_advisory_opportunity_listing_ajax', array($this,'audit_advisory_opportunity_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_audit_advisory_opportunity_listing_ajax', array($this,'audit_advisory_opportunity_listing_ajax_handler'));

        add_action('wp_ajax_rdc_opportunity_listing_ajax', array($this,'rdc_opportunity_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_rdc_opportunity_listing_ajax', array($this,'rdc_opportunity_listing_ajax_handler'));
		
        add_action('wp_ajax_ta_fetch_opportunity_filter',array($this,'ta_fetch_opportunity_filter_callback'));
        add_action('wp_ajax_ta_fetch_opportunity_filter',array($this,'ta_fetch_opportunity_filter_callback'));

        add_action('wp_ajax_submit_opportunity_project_milestone',array($this,'submit_opportunity_project_milestone_callback'));
        add_action('wp_ajax_submit_opportunity_project_milestone',array($this,'submit_opportunity_project_milestone_callback'));

        add_action('wp_ajax_delete_opportunity', array($this,'delete_opportunity_callback')); // For logged in users
        add_action('wp_ajax_nopriv_delete_opportunity', array($this,'delete_opportunity_callback')); // For non-logged in users
        add_action('wp_ajax_product_qb_service', array($this,'product_qb_service'));
        add_action('wp_ajax_nopriv_product_qb_service', array($this,'product_qb_service'));
        add_action('wp_ajax_product_submit', array($this,'product_submit_handler')); // For logged in users
        add_action('wp_ajax_nopriv_product_submit', array($this,'product_submit_handler')); // For non-logged in users
        add_action('wp_ajax_unit_type_submit', array($this,'unit_type_submit_handler')); // For logged in users
        add_action('wp_ajax_nopriv_unit_type_submit', array($this,'unit_type_submit_handler')); // For non-logged in users
        add_action('wp_ajax_product_type_submit', array($this,'product_type_submit_handler')); // For logged in users
        add_action('wp_ajax_nopriv_product_type_submit', array($this,'product_type_submit_handler')); // For non-logged in users
        add_action('wp_ajax_product_category_submit', array($this,'product_category_submit_handler')); // For logged in users
        add_action('wp_ajax_nopriv_product_category_submit', array($this,'product_category_submit_handler')); // For non-logged in users
        add_action('wp_ajax_delete_product', array($this,'delete_product')); // For logged in users
        add_action('wp_ajax_nopriv_delete_product', array($this,'delete_product')); // For non-logged in users
        add_action('wp_ajax_delete_category', array($this,'delete_category')); // For logged in users
        add_action('wp_ajax_nopriv_delete_category', array($this,'delete_category')); // For non-logged in users
        add_action('wp_ajax_delete_product_type', array($this,'delete_product_type')); // For logged in users
        add_action('wp_ajax_nopriv_delete_product_type', array($this,'delete_product_type')); // For non-logged in users
        add_action('wp_ajax_delete_unit_type', array($this,'delete_unit_type')); // For logged in users
        add_action('wp_ajax_nopriv_delete_unit_type', array($this,'delete_unit_type')); // For non-logged in users
        add_action('wp_ajax_export_product_categories', array($this,'export_product_categories')); // For logged in users
        add_action('wp_ajax_nopriv_export_product_categories', array($this,'export_product_categories')); // For non-logged in users
        add_action('wp_ajax_export_product_types', array($this,'export_product_types')); // For logged in users
        add_action('wp_ajax_nopriv_export_product_types', array($this,'export_product_types')); // For non-logged in users
        add_action('wp_ajax_export_unit_types', array($this,'export_unit_types')); // For logged in users
        add_action('wp_ajax_nopriv_export_unit_types', array($this,'export_unit_types')); // For non-logged in users
        add_action('wp_ajax_export_products', array($this,'export_products')); // For logged in users
        add_action('wp_ajax_nopriv_export_products', array($this,'export_products')); // For non-logged in users
        add_action('wp_ajax_currency_submit', array($this,'currency_submit_handler')); // For logged in users
        add_action('wp_ajax_nopriv_currency_submit', array($this,'currency_submit_handler')); // For non-logged in users
        add_action('wp_ajax_delete_currency', array($this,'delete_currency')); // For logged in users
        add_action('wp_ajax_nopriv_delete_currency', array($this,'delete_currency')); // For non-logged in users
        add_action('wp_ajax_export_currency', array($this,'export_currency')); // For logged in users
        add_action('wp_ajax_nopriv_export_currency', array($this,'export_currency')); // For non-logged in users
        add_action('wp_ajax_product_status_submit', array($this,'product_status_submit_handler')); // For logged in users
        add_action('wp_ajax_nopriv_product_status_submit', array($this,'product_status_submit_handler')); // For non-logged in users
        add_action('wp_ajax_delete_product_status', array($this,'delete_product_status')); // For logged in users
        add_action('wp_ajax_nopriv_delete_product_status', array($this,'delete_product_status')); // For non-logged in users
        add_action('wp_ajax_export_product_status', array($this,'export_product_status')); // For logged in users
        add_action('wp_ajax_nopriv_export_product_status', array($this,'export_product_status')); // For non-logged in users
        add_action('plugins_loaded', array($this,'check_and_create_columns')); // To create columns in table

        add_action('wp_ajax_next_step_submit', array($this,'next_step_submit_handler')); // For logged in users
        add_action('wp_ajax_nopriv_next_step_submit', array($this,'next_step_submit_handler')); // For non-logged in users
        add_action('wp_ajax_delete_next_step', array($this,'delete_next_step')); // For logged in users
        add_action('wp_ajax_nopriv_delete_next_step', array($this,'delete_next_step')); // For non-logged in users
        add_action('wp_ajax_export_next_step', array($this,'export_next_step')); // For logged in users
        add_action('wp_ajax_nopriv_export_next_step', array($this,'export_next_step')); // For non-logged in users

        add_action('wp_ajax_add_milestone_action', array($this,'add_milestone_action')); // For logged in users
        add_action('wp_ajax_nopriv_add_milestone_action', array($this,'add_milestone_action')); // For non-logged in user

        add_action('wp_ajax_add_milestone_stage_action', array($this,'add_milestone_stage_action')); // For logged in users
        add_action('wp_ajax_nopriv_add_milestone_stage_action', array($this,'add_milestone_stage_action')); // For non-logged in user

        add_action('wp_ajax_export_milestone', array($this,'export_milestone')); // For logged in users
        add_action('wp_ajax_nopriv_export_milestone', array($this,'export_milestone')); // For non-logged in users

        add_action('wp_ajax_export_milestone_stage', array($this,'export_milestone_stage')); // For logged in users
        add_action('wp_ajax_nopriv_export_milestone_stage', array($this,'export_milestone_stage')); // For non-logged in users

        add_action('wp_ajax_delete_milestone', array($this,'delete_milestone')); // For logged in users
        add_action('wp_ajax_nopriv_delete_milestone', array($this,'delete_milestone')); // For non-logged in users

        add_action('wp_ajax_delete_milestone_stage', array($this,'delete_milestone_stage_fun')); // For logged in users
        add_action('wp_ajax_nopriv_delete_milestone_stage', array($this,'delete_milestone_stage_fun')); // For non-logged in users

        add_action('wp_ajax_change_milestone_status', array($this,'change_milestone_status'));
        add_action('wp_ajax_nopriv_change_milestone_status', array($this,'change_milestone_status'));

        add_action('wp_ajax_fee_type_submit', array($this,'fee_type_submit_handler')); // For logged in users
        add_action('wp_ajax_nopriv_fee_type_submit', array($this,'fee_type_submit_handler')); // For non-logged in users

        add_action('wp_ajax_delete_fee_type', array($this,'delete_fee_type')); // For logged in users
        add_action('wp_ajax_nopriv_delete_fee_type', array($this,'delete_fee_type')); // For non-logged in users

        add_action('wp_ajax_export_fee_type', array($this,'export_fee_type')); // For logged in users
        add_action('wp_ajax_nopriv_export_fee_type', array($this,'export_fee_type')); // For non-logged in users

        add_action('wp_ajax_edit_fee_type', array($this,'edit_fee_type')); // For logged in users
        add_action('wp_ajax_nopriv_edit_fee_type', array($this,'edit_fee_type')); // For non-logged in users

        add_action('wp_ajax_project_space_submit', array($this,'project_space_submit_handler')); // For logged in users
        add_action('wp_ajax_nopriv_project_space_submit', array($this,'project_space_submit_handler')); // For non-logged in users

        add_action('wp_ajax_project_listing_ajax', array($this,'project_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_project_listing_ajax', array($this,'project_listing_ajax_handler'));

        add_action('wp_ajax_stc_project_listing_ajax', array($this,'stc_project_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_stc_project_listing_ajax', array($this,'stc_project_listing_ajax_handler'));

        add_action('wp_ajax_ertc_project_listing_ajax', array($this,'ertc_project_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_ertc_project_listing_ajax', array($this,'ertc_project_listing_ajax_handler'));

        add_action('wp_ajax_audit_advisory_project_listing_ajax', array($this,'audit_advisory_project_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_audit_advisory_project_listing_ajax', array($this,'audit_advisory_project_listing_ajax_handler'));

        add_action('wp_ajax_tax_amendmen_project_listing_ajax', array($this,'tax_amendmen_project_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_tax_amendmen_project_listing_ajax', array($this,'tax_amendmen_project_listing_ajax_handler'));

        add_action('wp_ajax_fetch_project_filter',array($this,'fetch_project_filter_callback'));
        add_action('wp_ajax_nopriv_fetch_project_filterr',array($this,'fetch_project_filter_callback'));


        add_action('wp_ajax_ertc_fetch_project_filter',array($this,'ertc_fetch_project_filter_callback'));
        add_action('wp_ajax_nopriv_ertc_fetch_project_filter',array($this,'ertc_fetch_project_filter_callback'));

        add_action('wp_ajax_audit_advisory_fetch_project_filter',array($this,'audit_advisory_fetch_project_filter_callback'));
        add_action('wp_ajax_nopriv_audit_advisory_fetch_project_filter',array($this,'audit_advisory_fetch_project_filter_callback'));

        add_action('wp_ajax_stc_fetch_project_filter',array($this,'stc_fetch_project_filter_callback'));
        add_action('wp_ajax_nopriv_stc_fetch_project_filter',array($this,'stc_fetch_project_filter_callback'));

        add_action('wp_ajax_tax_amendmen_fetch_project_filter',array($this,'tax_amendmen_fetch_project_filter_callback'));
        add_action('wp_ajax_nopriv_tax_amendmen_fetch_project_filter',array($this,'tax_amendmen_fetch_project_filter_callback'));

        //----- CUSTOM NOTES
        add_action('wp_ajax_add_custom_note_data',array($this,'add_custom_note_data_callback'));

        add_action('wp_ajax_add_project_note_data',array($this,'add_project_note_data_callback'));

        add_action('wp_ajax_update_sales_agent_of_project', array($this,'update_sales_agent_of_project'));
        add_action('wp_ajax_nopriv_update_sales_agent_of_project', array($this,'update_sales_agent_of_project'));

        add_action( 'wp_ajax_update_sales_support_of_project', array($this, 'update_sales_support_of_project' ));
        add_action( 'wp_ajax_nopriv_update_sales_support_of_project', array($this, 'update_sales_support_of_project' ));

        // --- Get Date for second tab lead detail page functionality ----------
        add_action( 'wp_ajax_erc_project_bank_info', array($this, 'erc_project_bank_info_function'));
        add_action( 'wp_ajax_nopriv_erc_project_bank_info', array($this, 'erc_project_bank_info_function'));

        add_action( 'wp_ajax_erc_project_intake_data', array($this, 'erc_project_intake_data_function'));
        add_action( 'wp_ajax_nopriv_erc_project_intake_data', array($this, 'erc_project_intake_data_function'));

        add_action( 'wp_ajax_erc_project_fee_data', array($this, 'erc_project_fee_data_function'));
        add_action( 'wp_ajax_nopriv_erc_project_fee_data', array($this, 'erc_project_fee_data_function'));
        
        // documents tab 
        add_action( 'wp_ajax_erc_company_document_data', array($this, 'erc_company_document_data_function'));
        add_action( 'wp_ajax_nopriv_erc_company_document_data', array($this, 'erc_company_document_data_function'));

        // Project audit log tab 
        add_action( 'wp_ajax_projects_audit_log', array($this, 'projects_audit_log_function'));
        add_action( 'wp_ajax_nopriv_projects_audit_log', array($this, 'projects_audit_log_function'));

        // opportunity audit log tab 
        add_action( 'wp_ajax_opportunity_audit_logs', array($this, 'opportunity_audit_logs_function'));
        add_action( 'wp_ajax_nopriv_opportunity_audit_logs', array($this, 'opportunity_audit_logs_function'));

        add_action( 'wp_ajax_erc_payroll_document_data', array($this, 'erc_payroll_document_data_function'));
        add_action( 'wp_ajax_nopriv_erc_payroll_document_data', array($this, 'erc_payroll_document_data_function'));

        add_action( 'wp_ajax_erc_project_document_data', array($this, 'erc_project_document_data_function'));
        add_action( 'wp_ajax_nopriv_erc_project_document_data', array($this, 'erc_project_document_data_function'));

        add_action( 'wp_ajax_erc_other_document_data', array($this, 'erc_other_document_data_function'));
        add_action( 'wp_ajax_nopriv_erc_other_document_data', array($this, 'erc_other_document_data_function'));
        //-- documents tab ------

        //update project info
        add_action( 'wp_ajax_update_erc_project_info', array($this, 'update_erc_project_info_function'));
        add_action( 'wp_ajax_nopriv_update_erc_project_info', array($this, 'update_erc_project_info_function'));

        //assign collborators
        add_action( 'wp_ajax_assign_collaborators', array($this, 'assign_collaborators'));
        add_action( 'wp_ajax_nopriv_assign_collaborators', array($this, 'assign_collaborators'));

        //Delete Project
        add_action('wp_ajax_delete_project', array($this,'delete_project_callback')); // For logged in users
        add_action('wp_ajax_nopriv_delete_project', array($this,'delete_project_callback')); // For non-logged in users

        add_action('wp_ajax_handle_stc__file_upload', array($this,'handle_stc__file_upload'));
        add_action('wp_ajax_nopriv_handle_stc__file_upload', array($this,'handle_stc__file_upload'));

        add_action('wp_ajax_handle_erc__file_upload', array($this,'handle_erc__file_upload'));
        add_action('wp_ajax_nopriv_handle_erc__file_upload', array($this,'handle_erc__file_upload'));


        add_action('wp_ajax_update_stc_document_status', array($this,'update_stc_document_status'));
        add_action('wp_ajax_nopriv_update_stc_document_status', array($this,'update_stc_document_status'));

        add_action('wp_ajax_check_rejected_reason', array($this,'check_rejected_reason'));
        add_action('wp_ajax_nopriv_check_rejected_reason', array($this,'check_rejected_reason'));

        add_action('wp_ajax_view_stc_comments', array($this,'view_stc_comments'));
        add_action('wp_ajax_nopriv_view_stc_comments', array($this,'view_stc_comments'));

        add_action('wp_ajax_project_doc_comment_data', array($this,'project_doc_comment_data'));
        add_action('wp_ajax_nopriv_project_doc_comment_data', array($this,'project_doc_comment_data'));

        add_action('wp_ajax_approved_rejected_comment_data', array($this,'approved_rejected_comment_data'));
        add_action('wp_ajax_nopriv_approved_rejected_comment_data', array($this,'approved_rejected_comment_data'));
		
		add_action('wp_ajax_send_stc_ssf', array($this,'send_stc_ssf'));
        add_action('wp_ajax_nopriv_send_stc_ssf', array($this,'send_stc_ssf'));


        add_action('wp_ajax_send_stc_agreement_link', array($this,'send_stc_agreement_link'));
        add_action('wp_ajax_nopriv_send_stc_agreement_link', array($this,'send_stc_agreement_link'));

        add_action('wp_ajax_update_milestone_when_opportunity_won', array($this,'update_milestone_when_opportunity_won'));
        add_action('wp_ajax_nopriv_update_milestone_when_opportunity_won', array($this,'update_milestone_when_opportunity_won'));

        add_action('wp_ajax_send_ta_agreement', array($this,'send_ta_agreement_action'));
        add_action('wp_ajax_nopriv_send_ta_agreement', array($this,'send_ta_agreement_action'));

        add_action('wp_ajax_rdc_project_listing_ajax', array($this,'rdc_project_listing_ajax_handler'));
        add_action('wp_ajax_nopriv_rdc_project_listing_ajax', array($this,'rdc_project_listing_ajax_handler'));

        add_action('wp_ajax_rdc_fetch_project_filter',array($this,'rdc_fetch_project_filter_callback'));
        add_action('wp_ajax_nopriv_rdc_fetch_project_filter',array($this,'rdc_fetch_project_filter_callback'));

        add_action('wp_ajax_delete_service', array($this,'delete_service_callback')); // For logged in users
        add_action('wp_ajax_nopriv_delete_service', array($this,'delete_service_callback')); // For non-logged in users
    
        //process impacted days data
        //https://play.occamsadvisory.com/portal/wp-admin/admin.php?page=manage-stc-project&id=1690
        add_action('wp_ajax_process_impacted_data_aff', array($this,'process_impacted_data_aff'));
        add_action('wp_ajax_nopriv_process_impacted_data_aff', array($this,'process_impacted_data_aff'));
        
        
        
        // Register the AJAX action
        add_action('wp_ajax_submit_selfdocumentform_aff', array($this,'handle_selfdocumentform_submission_aff'));
        add_action('wp_ajax_nopriv_submit_selfdocumentform_aff', array($this,'handle_selfdocumentform_submission_aff'));


    }

    /**
     * Register the administration menu for this plugin into the WordPress Dashboard menu.
     */
    public function add_admin_menus() {
        global $wpdb;
        $userdata = get_user_by( 'id', get_current_user_id() );
        $user_roles=$userdata->roles;
		$crmSettings = new OC_Crm_Settings();
		// Fetch options with default empty strings if not set
		$saved_roles = $crmSettings->get_crm_option('opt_select_crm_roles', '');
		$saved_roles_project = $crmSettings->get_crm_option('opt_project_sb_roles', ''); // For project submenu
		$saved_roles_setting = $crmSettings->get_crm_option('opt_project_setting_roles', ''); // For settings submenu

		// Convert the comma-separated roles to arrays, if not empty
		$saved_roles = !empty($saved_roles) ? explode(',', $saved_roles) : [];
		$saved_roles_project = !empty($saved_roles_project) ? explode(',', $saved_roles_project) : [];
		$saved_roles_setting = !empty($saved_roles_setting) ? explode(',', $saved_roles_setting) : [];

		// Get current user's roles
		$current_user = wp_get_current_user();
		$current_user_roles = (array) $current_user->roles;

		// Centralized check for default access
		$default_access = in_array('administrator', $current_user_roles) || in_array('echeck_client', $current_user_roles) || in_array('master_ops', $current_user_roles) || $current_user->ID == 111462;

		// Determine access for main menu, project submenu, and settings submenu
		$has_access = $default_access || !empty(array_intersect($current_user_roles, $saved_roles));
		$has_access_project = $default_access || !empty(array_intersect($current_user_roles, $saved_roles_project));
		$has_access_setting = $default_access || !empty(array_intersect($current_user_roles, $saved_roles_setting));

		if ($has_access) {
			// Fetch and set default values for menu names if not set
			$mainMenuName = $crmSettings->get_crm_option('opt_crm_main_menu', 'Projects');
			$projectsMenuName = $crmSettings->get_crm_option('opt_crm_project_submenu', 'Projects');
			$settingName = $crmSettings->get_crm_option('opt_crm_settings_menu', 'Settings');

			// Main menu
			add_menu_page($mainMenuName, $mainMenuName, 'manage_options', 'projects', '', 'dashicons-admin-generic', 25);

			// Project submenu, if access is allowed
			//if ($has_access_project) {
                add_submenu_page('projects', $projectsMenuName, $projectsMenuName, 'manage_options', 'projects', array($this, 'display_projects'));
                add_submenu_page('projects', 'ERC Projects', 'ERC Projects', 'manage_options', 'erc-projects', array($this, 'erc_display_projects'));
                add_submenu_page('projects', 'STC Projects', 'STC Projects', 'manage_options', 'stc-projects', array($this, 'stc_display_projects'));
                add_submenu_page('projects', 'Tax Amendment Projects', 'Tax Amendment Projects', 'manage_options', 'tax-amendment-projects', array($this, 'tax_amendment_display_projects'));
                add_submenu_page('projects', 'Audit Advisory Projects', 'Audit Advisory Projects', 'manage_options', 'audit-advisory-projects', array($this, 'audit_advisory_display_projects'));
                add_submenu_page('projects', 'RDC Projects', 'RDC Projects', 'manage_options', 'rdc-projects', array($this, 'rdc_display_projects'));
			//}

			// Settings submenu, if access is allowed
			if ($has_access_setting) {
				add_submenu_page('projects', $settingName, $settingName, 'manage_options', 'crm-setting', array($this, 'oc_crm_settings'));
			}
		}
		
		// Main menu
        if(in_array("administrator", $user_roles) || in_array('master_ops', $user_roles)){
    		add_menu_page('product_settings', 'Products', 'manage_options', 'product_settings', array($this, 'product_settings'), 'dashicons-products', 25);
    			
    		add_submenu_page('product_settings', 'Products', 'Products', 'manage_options', 'crm_products', array($this, 'display_products_page')); 
    		// Submenu for Unit Types
    		add_submenu_page(
    			'product_settings',                          // Parent slug
    			__('Unit Types', 'productsplugin-crm-erp'),  // Page title
    			__('Unit Types', 'productsplugin-crm-erp'),  // Menu title
    			'manage_options',                            // Capability
    			'unit_types',                                // Menu slug
    			array($this, 'display_unit_types')           // Callback function
    		);

    		// Submenu for Product Types
    		add_submenu_page(
    			'product_settings',                          // Parent slug
    			__('Product Types', 'productsplugin-crm-erp'),            // Page title
    			__('Product Types', 'productsplugin-crm-erp'),            // Menu title
    			'manage_options',                                  // Capability
    			'product_types',                           // Menu slug
    			array($this, 'display_product_types')              // Callback function
    		);

    		// Submenu for Product Categories
    		add_submenu_page(
    			'product_settings',                          // Parent slug
    			__('Product Categories', 'productsplugin-crm-erp'),            // Page title
    			__('Product Categories', 'productsplugin-crm-erp'),            // Menu title
    			'manage_options',                                  // Capability
    			'product_categories',                           // Menu slug
    			array($this, 'display_product_categories')              // Callback function
    		);

    		// Submenu for Fee Structured
    		/*add_submenu_page(
    			'product_settings',                          // Parent slug
    			__('Fee Structure', 'productsplugin-crm-erp'),            // Page title
    			__('Fee Structure', 'productsplugin-crm-erp'),            // Menu title
    			'manage_options',                                  // Capability
    			'fee_structure',                           // Menu slug
    			array($this, 'display_fee_structure')              // Callback function
    		);*/
    		// Submenu for Currency
    		add_submenu_page('product_settings', 'Currency', 'Currency', 'manage_options', 'currency-settings', array($this, 'currency_settings_page'));

    		// Submenu for Unit Types
    		add_submenu_page(
    			'product_settings',                          // Parent slug
    			__('Project Config', 'productsplugin-crm-erp'),            // Page title
    			__('Project Config', 'productsplugin-crm-erp'),            // Menu title
    			'manage_options',                                  // Capability
    			'project_spaces',                           // Menu slug
    			array($this, 'display_project_spaces')              // Callback function
    		);

            // Submenu for Product Categories
            add_submenu_page(
                'product_settings',                          // Parent slug
                __('Product Settings', 'productsplugin-crm-erp'),            // Page title
                __('Product Settings', 'productsplugin-crm-erp'),            // Menu title
                'manage_options',                                  // Capability
                'role_products',                           // Menu slug
                array($this, 'show_role_products')              // Callback function
            );
        }
        if(in_array("administrator", $user_roles)|| in_array('master_ops', $user_roles) || in_array('echeck_staff', $user_roles) || in_array('echeck_admin', $user_roles) || in_array('iris_sales_agent', $user_roles) || in_array('master_sales', $user_roles)  || in_array('fprs_account_executive', $user_roles) || in_array('fprs_sales_agent', $user_roles)){
            add_menu_page('opportunities', 'Opportunities', 'manage_options', 'opportunities', array($this, 'display_opportunities'), 'dashicons-admin-generic', 25);
            if(!in_array("iris_affiliate_users", $user_roles)){
        		// Submenu for Opportunity
        		add_submenu_page(
        			'opportunities',                          // Parent slug
        			__('Opportunities Report', 'productsplugin-crm-erp'),            // Page title
        			__('Opportunities Report', 'productsplugin-crm-erp'),            // Menu title
        			'manage_options',                                  // Capability
        			'opportunities',                           // Menu slug
        			array($this, 'display_opportunities')              // Callback function
        		);
            }
            add_submenu_page(
                'opportunities',                          // Parent slug
                __('ERC Opportunities', 'productsplugin-crm-erp'),            // Page title
                __('ERC Opportunities', 'productsplugin-crm-erp'),            // Menu title
                'manage_options',                                  // Capability
                'erc-report',                           // Menu slug
                array($this, 'display_erc_report')              // Callback function
            );
            add_submenu_page(
                'opportunities',                          // Parent slug
                __('STC Opportunities', 'productsplugin-crm-erp'),            // Page title
                __('STC Opportunities', 'productsplugin-crm-erp'),            // Menu title
                'manage_options',                                  // Capability
                'stc-report',                           // Menu slug
                array($this, 'display_stc_report')              // Callback function
            );
            add_submenu_page(
                'opportunities',                          // Parent slug
                __('Tax Amendment Opportunities', 'productsplugin-crm-erp'),            // Page title
                __('Tax Amendment Opportunities', 'productsplugin-crm-erp'),            // Menu title
                'manage_options',                                  // Capability
                'tax-amendment-report',                           // Menu slug
                array($this, 'display_tax_report')              // Callback function
            );
            add_submenu_page(
                'opportunities',                          // Parent slug
                __('Audit Advisory Opportunities', 'productsplugin-crm-erp'),            // Page title
                __('Audit Advisory Opportunities', 'productsplugin-crm-erp'),            // Menu title
                'manage_options',                                  // Capability
                'audit-advisory-report',                           // Menu slug
                array($this, 'display_audit_advisory_report')              // Callback function
            );
            add_submenu_page(
                'opportunities',                          // Parent slug
                __('RDC Opportunities', 'productsplugin-crm-erp'),            // Page title
                __('RDC Opportunities', 'productsplugin-crm-erp'),            // Menu title
                'manage_options',                                  // Capability
                'rdc-report',                           // Menu slug
                array($this, 'display_rdc_report')              // Callback function
            );
            add_submenu_page(
                'opportunities',                          // Parent slug
                __('Partnership Opportunities', 'productsplugin-crm-erp'),            // Page title
                __('Partnership Opportunities', 'productsplugin-crm-erp'),            // Menu title
                'manage_options',                                  // Capability
                'partnership-report',                           // Menu slug
                array($this, 'display_partnership_report')              // Callback function
            );
            // Submenu for Product Milestones
            if(in_array("administrator", $user_roles) || in_array('master_ops', $user_roles)){
                add_submenu_page(
                    'opportunities',                          // Parent slug
                    __('Milestone & Stages', 'productsplugin-crm-erp'),            // Page title
                    __('Milestone & Stages', 'productsplugin-crm-erp'),            // Menu title
                    'manage_options',                                  // Capability
                    'crm_erp_milestones',                           // Menu slug
                    array($this, 'display_milestone')              // Callback function
                );
            }
        }
        add_submenu_page(
            '',                                                // Parent slug
            __('Stages', 'productsplugin-crm-erp'),            // Page title
            '',                                                // Menu title
            'manage_options',                                  // Capability
            'crm_erp_milestone_stages',                   // Menu slug
            array($this, 'display_milestone_stages')           // Callback function
        );
		// Submenu for Opportunity
		// add_submenu_page(
		//     'opportunities',                          // Parent slug
		//     __('Next Step', 'productsplugin-crm-erp'),            // Page title
		//     __('Next Step', 'productsplugin-crm-erp'),            // Menu title
		//     'manage_options',                                  // Capability
		//     'crm_erp_next_step',                           // Menu slug
		//     array($this, 'display_next_step')              // Callback function
		// );

		// Submenu for Product Add Edit
		add_submenu_page(
			'',                          // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'add_edit_product',                           // Menu slug
			array($this, 'add_edit_product')              // Callback function
		);

		// Submenu for Product View
		add_submenu_page(
			'',                          // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'view_product',                           // Menu slug
			array($this, 'view_product')              // Callback function
		);

		// Submenu for Unit Type Add Edit
		add_submenu_page(
			'',                          // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'add_edit_unit_type',                           // Menu slug
			array($this, 'add_edit_unit_type')              // Callback function
		);

		// Submenu for Product Type Add Edit
		add_submenu_page(
			'',                          // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'add_edit_product_type',                           // Menu slug
			array($this, 'add_edit_product_type')              // Callback function
		);

		// Submenu for Product Category Add Edit
		add_submenu_page(
			'',                          // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'add_edit_product_category',                           // Menu slug
			array($this, 'add_edit_product_category')              // Callback function
		);

		// Submenu for Currency Add Edit
		add_submenu_page(
			'',                          // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'add_edit_currency',                           // Menu slug
			array($this, 'add_edit_currency')              // Callback function
		);

		// Submenu for Product Status Add Edit
		add_submenu_page(
			'',                          // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'add_edit_milestone',                           // Menu slug
			array($this, 'add_edit_milestone')              // Callback function
		);


		// Submenu for Opportunity Add Edit
		add_submenu_page(
			'',            // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'add_edit_opportunity',                           // Menu slug
			array($this, 'add_edit_opportunity')              // Callback function
		);

		// Submenu for Opportunity View
		add_submenu_page(
			'',            // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'manage-opportunity',                           // Menu slug
			array($this, 'view_opportunity')              // Callback function
		);

		// Submenu for Next Step Add Edit
		add_submenu_page(
			'',                          // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'add_edit_next_step',                           // Menu slug
			array($this, 'add_edit_next_step')              // Callback function
		);

		// Submenu for Fee Type Add Edit
		add_submenu_page(
			'',                          // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'add_edit_fee_type',                           // Menu slug
			array($this, 'add_edit_fee_type')              // Callback function
		);
		// Add more submenus here
		remove_submenu_page('product_settings','product_settings');

		// Submenu for ERC Product Project View
		add_submenu_page(
			'',            // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'manage-erc-project',                           // Menu slug
			array($this, 'manage_erc_project')              // Callback function
		);

		// Submenu for RDC Product Project View
		add_submenu_page(
			'',            // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'manage-rdc-project',                           // Menu slug
			array($this, 'manage_rdc_project')              // Callback function
		);

		// Submenu for Audit Product Project View
		add_submenu_page(
			'',            // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'manage-audit-project',                           // Menu slug
			array($this, 'manage_audit_project')              // Callback function
		);	


		// Submenu for STC Product Project View
		add_submenu_page(
			'',            // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'manage-stc-project',                           // Menu slug
			array($this, 'manage_stc_project')              // Callback function
		);	

		// Submenu for Project Add Edit
		add_submenu_page(
			'',                          // Parent slug
			'',            // Page title
			'',            // Menu title
			'manage_options',                                  // Capability
			'add_edit_project',                           // Menu slug
			array($this, 'add_edit_project')              // Callback function
		);	

        // Submenu for TA Product Project View
        add_submenu_page(
            '',            // Parent slug
            '',            // Page title
            '',            // Menu title
            'manage_options',                                  // Capability
            'manage-ta-project',                           // Menu slug
            array($this, 'manage_ta_project')              // Callback function
        );  
    }

    /**
     * Display the dashboard page.
     */
    public function display_dashboard() {
        echo '<div class="wrap"><h1>' . esc_html(get_admin_page_title()) . '</h1>';
        echo '<p>' . __(' CRM ERP Dashboard', 'productsplugin-crm-erp') . '</p>';
        echo '</div>';
    }
    /**
     * Display Setting Page.
     */
    public function oc_crm_settings() {
		include_once('partials/oc-crm-settings-section.php');
    }
     /**
     * Action callback for setting saved.
     */	
	public function oc_save_crm_settings(){
		$ocCrmSettings = new OC_Crm_Settings();
		$response = $ocCrmSettings->save_crm_settings($_POST);

		// Send back a response
		wp_send_json($response);	
	}

    /**
     * Action callback for product roles saved.
     */ 
    public function oc_save_product_roles(){
        $ocCrmProductManager = new CRM_ERP_Product_Manager();
        $response = $ocCrmProductManager->save_product_roles(json_encode($_POST));
        // Send back a response
        wp_send_json($response);    
    }
	
	public function fetch_users_by_role_crm() {
		// Check for the presence of required parameters, in this case, 'roles'
		if (isset($_POST['roles']) && !empty($_POST['roles'])) {
			$roles = $_POST['roles'];
			$args = array(
				'role__in' => $roles,
				'fields'   => array('ID', 'display_name'),
			);

			$users_query = new WP_User_Query($args);
			$users = $users_query->get_results();

			// Prepare the users data
			$users_data = array();
			foreach ($users as $user) {
				$users_data[] = array(
					'id'   => $user->ID,
					'name' => $user->display_name,
				);
			}
			wp_send_json_success(array('users' => $users_data));
		} else {
			// No roles provided, or an error occurred
			wp_send_json_error(array('message' => 'No roles provided.'));
		}

		wp_die(); 
	}	
	
	
    /**
     * Display the products page.
     */
    public function display_products_page() {
        global $wpdb;
        //$wpdb->query("ALTER TABLE eccom_audit_logs RENAME COLUMN AfterValueText TO Action");die;
        require plugin_dir_path(__FILE__) . 'crm-erp-product-list.php';
        $productListTable = new CRM_ERP_Product_List();
        $productListTable->prepare_items();
    }

    /**
     * Create/Edit a product.
     */
    public function add_edit_product() {
        global $wpdb;
        $args = array(
          'orderby' => 'display_name',
          'order'   => 'ASC',
          'role__in' => array('administator','master_sales','master_ops','echeck_client'),
        );

        $users = get_users( $args );
        $currencies = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}currencies WHERE {$wpdb->prefix}currencies.deleted_at IS NULL ORDER BY {$wpdb->prefix}currencies.currency_id DESC");
        $unit_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}unit_types WHERE {$wpdb->prefix}unit_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}unit_types.UnitTypeID DESC");
        $product_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_types WHERE {$wpdb->prefix}product_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_types.ProductTypeID DESC");
        $product_categories = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_categories WHERE {$wpdb->prefix}product_categories.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_categories.CategoryID DESC");
        require plugin_dir_path(__FILE__) . 'crm-erp-product-add-edit.php';
    }

    /**
     * Create/Edit a product.
     */
    public function view_product() {
        global $wpdb;
        $args = array(
          'orderby' => 'display_name',
          'order'   => 'ASC',
          'role__in' => array('administator','master_sales','master_ops','echeck_client'),
        );

        $users = get_users( $args );
        $currencies = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}currencies WHERE {$wpdb->prefix}currencies.deleted_at IS NULL ORDER BY {$wpdb->prefix}currencies.currency_id DESC");
        $unit_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}unit_types WHERE {$wpdb->prefix}unit_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}unit_types.UnitTypeID DESC");
        $product_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_types WHERE {$wpdb->prefix}product_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_types.ProductTypeID DESC");
        $product_categories = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_categories WHERE {$wpdb->prefix}product_categories.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_categories.CategoryID DESC");
        require plugin_dir_path(__FILE__) . 'crm-erp-product-view.php';
    }

    /**
     * Create/Edit a opportunity.
     */
    public function add_edit_opportunity() {
        global $wpdb;
        $args = array(
          'orderby' => 'display_name',
          'order'   => 'ASC'
        );
        $users = get_users( $args );
        $currencies = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}currencies WHERE {$wpdb->prefix}currencies.deleted_at IS NULL ORDER BY {$wpdb->prefix}currencies.currency_id DESC");
        $unit_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}unit_types WHERE {$wpdb->prefix}unit_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}unit_types.UnitTypeID DESC");
        $product_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_types WHERE {$wpdb->prefix}product_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_types.ProductTypeID DESC");
        $product_categories = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_categories WHERE {$wpdb->prefix}product_categories.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_categories.CategoryID DESC");
        $next_step_datas = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}next_step WHERE {$wpdb->prefix}next_step.deleted_at IS NULL ORDER BY {$wpdb->prefix}next_step.next_step_id DESC");
        require plugin_dir_path(__FILE__) . 'crm-erp-opportunity-add-edit.php';
    }

    /**
     * View a opportunity.
     */
    public function view_opportunity() {
        global $wpdb;
        $args = array(
          'orderby' => 'display_name',
          'order'   => 'ASC'
        );
        $users = get_users( $args );
        $currencies = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}currencies WHERE {$wpdb->prefix}currencies.deleted_at IS NULL ORDER BY {$wpdb->prefix}currencies.currency_id DESC");
        $unit_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}unit_types WHERE {$wpdb->prefix}unit_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}unit_types.UnitTypeID DESC");
        $product_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_types WHERE {$wpdb->prefix}product_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_types.ProductTypeID DESC");
        $product_categories = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_categories WHERE {$wpdb->prefix}product_categories.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_categories.CategoryID DESC");
        $next_step_datas = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}next_step WHERE {$wpdb->prefix}next_step.deleted_at IS NULL ORDER BY {$wpdb->prefix}next_step.next_step_id DESC");
        require plugin_dir_path(__FILE__) . 'crm-erp-opportunity-view.php';
    }


    function product_submit_handler() {   
        if (isset($_POST['logo_data'])) {
            $logo_data = $_POST['logo_data'];
        
            // Extracting mime type from base64 data
            preg_match('/^data:image\/(\w+);base64,/', $logo_data, $type);
            $extension = strtolower($type[1]); // Get the image extension
        
            // Remove the data type header to get only the base64-encoded image data
            $logo_data = str_replace('data:image/' . $extension . ';base64,', '', $logo_data);
            $logo_data = str_replace(' ', '+', $logo_data); // Replace space character with '+'
        
            // Decode the base64-encoded image data
            $decoded_image = base64_decode($logo_data);
        
            if ($decoded_image !== false) {
                // Save the image to a file
                $image_name = uniqid() . '.' . $extension;
                $image_file = plugin_dir_path(dirname(__FILE__)) . 'uploads/' . $image_name;
                $file_saved = file_put_contents($image_file, $decoded_image);
        
                if ($file_saved !== false) {
                    // Now, $image_file contains the path to the saved image
                    $_POST['ProductLogo'] = $image_name;
                } else {
                    echo 'Failed to save the image.';
                }
            } else {
                echo 'Failed to decode the image data.';
            }
        }

        if (isset($_POST['image_data'])) {
            $image_data = $_POST['image_data'];
        
            // Extracting mime type from base64 data
            preg_match('/^data:image\/(\w+);base64,/', $image_data, $type);
            $extension = strtolower($type[1]); // Get the image extension
        
            // Remove the data type header to get only the base64-encoded image data
            $image_data = str_replace('data:image/' . $extension . ';base64,', '', $image_data);
            $image_data = str_replace(' ', '+', $image_data); // Replace space character with '+'
        
            // Decode the base64-encoded image data
            $decoded_image = base64_decode($image_data);
        
            if ($decoded_image !== false) {
                // Save the image to a file
                $image_name = uniqid() . '.' . $extension;
                $image_file = plugin_dir_path(dirname(__FILE__)) . 'uploads/' . $image_name;
                $file_saved = file_put_contents($image_file, $decoded_image);
        
                if ($file_saved !== false) {
                    // Now, $image_file contains the path to the saved image
                    $_POST['ProductImages'] = $image_name;
                } else {
                    echo 'Failed to save the image.';
                }
            } else {
                echo 'Failed to decode the image data.';
            }
        }
        
        

        // Check the nonce - security first!
        $product_manager = new CRM_ERP_Product_Manager();
        $data = $_POST;
        
        unset($data['action']);
        unset($data['logo_data']); // No need to send large image data
        unset($data['image_data']); // No need to send large image data
        $current_user_id = get_current_user_id();
        if($data['UnitTypeID'] == 0){
            unset($data['UnitTypeID']);
        }
        if($data['CategoryID'] == 0){
            unset($data['CategoryID']);
        }
        if($data['product_id'] == ''){
            unset($data['product_id']);
            $data['CreatedAt'] = date('Y-m-d H:i:s');
            $data['CreatedBy'] = $current_user_id;
            $products_data = $product_manager->create_product(json_encode($data));
        }else{
            $data['ModifiedAt'] = date('Y-m-d H:i:s');
            $data['ModifiedBy'] = $current_user_id;
            $products_data = $product_manager->edit_product(json_encode($data),$data['product_id']);
        }
        if($products_data){
            wp_send_json_success($products_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }

    function opportunity_submit_handler() {       
        
        // Check the nonce - security first!
        $opportunity_manager = new CRM_ERP_Opportunity_Manager();
        $data = $_POST;
        unset($data['action']);
        $current_user_id = get_current_user_id();
        if($data['opportunity_id'] == ''){
            unset($data['opportunity_id']);
            $data['CreatedAt'] = date('Y-m-d H:i:s');
            $data['CreatedBy'] = $current_user_id;
            $products_data = $opportunity_manager->create_opportunity(json_encode($data));
        }else{
            $data['CreatedAt'] = date('Y-m-d H:i:s');
            $data['CreatedBy'] = $current_user_id;
            $data['ModifiedAt'] = date('Y-m-d H:i:s');
            $data['ModifiedBy'] = $current_user_id;
            $products_data = $opportunity_manager->edit_opportunity(json_encode($data),$data['opportunity_id']);
        }
        if($products_data){
            wp_send_json_success($products_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }
    function opportunity_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities.php');
        $wp_list_table = new Opportunity_Report_Table();
        $wp_list_table->prepare_items();

        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();

        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }

    function stc_opportunity_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-stc.php');
        $wp_list_table = new STC_Opportunity_Report_Table();
        $wp_list_table->prepare_items();

        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();

        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }

    function ertc_opportunity_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-erc.php');
        $wp_list_table = new ERTC_Opportunity_Report_Table();
        $wp_list_table->prepare_items();
        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();

        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }

    function ertc_opportunity_search_ajax_handler(){
        include_once(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-erc.php');
        $wp_list_table = new ERTC_Opportunity_Report_Table();
        //$wp_list_table->prepare_items();

        ob_start();
        $wp_list_table->search_form_fields();
        $display = ob_get_clean();

        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }
	
    function ta_opportunity_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-ta.php');
        $wp_list_table = new TA_Opportunity_Report_Table();
        $wp_list_table->prepare_items();

        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();

        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }
    function audit_advisory_opportunity_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-audit-advisory.php');
        $wp_list_table = new Audit_Advisory_Opportunity_Report_Table();
        $wp_list_table->prepare_items();

        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();

        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }
    function rdc_opportunity_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-rdc.php');
        $wp_list_table = new RDC_Opportunity_Report_Table();
        $wp_list_table->prepare_items();

        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();

        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }
    function partnership_opportunity_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-partnership.php');
        $wp_list_table = new PARTNERSHIP_Opportunity_Report_Table();
        $wp_list_table->prepare_items();
        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();
        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }
    function fetch_opportunity_filter_callback() {
            include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities.php');
            $wp_list_table = new Opportunity_Report_Table();
            $wp_list_table->ajax_response();
            exit;
    }

    function fetch_stc_opportunity_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-stc.php');
        $wp_list_table = new STC_Opportunity_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }
    function fetch_audit_advisory_opportunity_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-audit-advisory.php');
        $wp_list_table = new Audit_Advisory_Opportunity_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }

    function fetch_rdc_opportunity_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-rdc.php');
        $wp_list_table = new RDC_Opportunity_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }
    function fetch_ertc_opportunity_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-erc.php');
        $wp_list_table = new ERTC_Opportunity_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }

    function fetch_ta_opportunity_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-ta.php');
        $wp_list_table = new TA_Opportunity_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }
	
    function fetch_partnership_opportunity_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-partnership.php');
        $wp_list_table = new PARTNERSHIP_Opportunity_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }

	function ta_fetch_opportunity_filter_callback() {
            include(plugin_dir_path(__FILE__) . 'crm-erp-opportunities-ta.php');
            $wp_list_table = new TA_Opportunity_Report_Table();
            $wp_list_table->ajax_response();
            exit;
    }

    function submit_opportunity_project_milestone_callback() {
            include(plugin_dir_path(__FILE__) . 'submit-opportunity-project-milestone.php');
    }

    /**
     * Delete Opportunity.
    */
    public function delete_opportunity_callback() {
        $opportunity_id = $_REQUEST['opportunity_id'];
        $OpportunityManager = new CRM_ERP_Opportunity_Manager();
        $opportunity_data = $OpportunityManager->delete_opportunity($opportunity_id);
        if($opportunity_data){
            wp_send_json_success($opportunity_data); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }

    /**
     * Delete Product.
     */
    public function delete_product() {
        $product_id = $_REQUEST['product_id'];
        $productManager = new CRM_ERP_Product_Manager();
        $products_data = $productManager->delete_product($product_id);
        if($products_data){
            wp_send_json_success($products_data); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }

    /**
     * Display the unit types.
     */
    public function display_unit_types() {
        require plugin_dir_path(__FILE__) . 'crm-erp-unit-types.php';
        $unitTypesTable = new unit_types();
        $unitTypesTable->prepare_items();
    }

    /**
     * Create/Edit a unit type.
     */
    public function add_edit_unit_type() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-unit-type-add-edit.php';
    }

    /**
     * Unit Type Submit Handler.
     */
    function unit_type_submit_handler() {
        // Check the nonce - security first!
        $unit_type_manager = new CRM_ERP_Unit_Type_Manager();
        $data = $_POST;
        unset($data['action']);
        $current_user_id = get_current_user_id();
        if($data['unit_type_id'] == ''){
            unset($data['unit_type_id']);
            $data['CreatedAt'] = date('Y-m-d H:i:s');
            $data['CreatedBy'] = $current_user_id;
            $unit_type_data = $unit_type_manager->create_unit_type(json_encode($data));
        }else{
            $data['ModifiedAt'] = date('Y-m-d H:i:s');
            $data['ModifiedBy'] = $current_user_id;
            $unit_type_data = $unit_type_manager->edit_unit_type(json_encode($data),$data['unit_type_id']);
        }

        if($unit_type_data){
            wp_send_json_success($unit_type_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }

    /**
     * Display the product types.
     */
    public function display_product_types() {
        require plugin_dir_path(__FILE__) . 'crm-erp-product-types.php';
        $productTypesTable = new product_types();
        $productTypesTable->prepare_items();
    }

    /**
     * Create/Edit a product type.
     */
    public function add_edit_product_type() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-product-type-add-edit.php';
    }

    /**
     * Product Type Submit Handler.
     */
    function product_type_submit_handler() {
        // Check the nonce - security first!
        $product_manager = new CRM_ERP_Product_Manager();
        $data = $_POST;

        $current_user_id = get_current_user_id();
        unset($data['action']);
        if($data['product_type_id'] == ''){
            unset($data['product_type_id']);
            $data['CreatedAt'] = date('Y-m-d H:i:s');
            $data['CreatedBy'] = $current_user_id;
            $product_type_data = $product_manager->create_product_type(json_encode($data));
        }else{
            $data['ModifiedAt'] = date('Y-m-d H:i:s');
            $data['ModifiedBy'] = $current_user_id;
            $product_type_data = $product_manager->edit_product_type(json_encode($data),$data['product_type_id']);
        }
        if($product_type_data){
            wp_send_json_success($product_type_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }

    /**
     * show products.
     */
    public function show_role_products() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-show-role-products.php';
        $roleProductsTable = new role_products();
        $roleProductsTable->prepare_items();
    }

    /**
     * Display the product categories.
     */
    public function display_product_categories() {
        global $wpdb;
        //$wpdb->query("ALTER TABLE eccom_product_categories ADD COLUMN status varchar(10) NOT NULL DEFAULT 'active' AFTER `CustomField6`");
        require plugin_dir_path(__FILE__) . 'crm-erp-product-categories.php';
        $productCategoriesTable = new product_categories();
        $productCategoriesTable->prepare_items();
    }

    /**
     * Create/Edit a product category.
     */
    public function add_edit_product_category() {
        global $wpdb;
        $products_data = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}crm_products WHERE {$wpdb->prefix}crm_products.DeletedAt IS NULL AND {$wpdb->prefix}crm_products.status = 'active' ORDER BY {$wpdb->prefix}crm_products.ProductID DESC");
        require plugin_dir_path(__FILE__) . 'crm-erp-product-category-add-edit.php';
    }

    /**
     * Product Category Submit Handler.
     */
    function product_category_submit_handler() {
        // Check the nonce - security first!
        $category_manager = new CRM_ERP_Category_Manager();
        $data = $_POST;
        unset($data['action']);
        $current_user_id = get_current_user_id();
        if($data['ParentCategoryID'] == 0){
            unset($data['ParentCategoryID']);
        }
        if($data['product_category_id'] == ''){
            unset($data['product_category_id']);
            $data['CreatedAt'] = date('Y-m-d H:i:s');
            $data['CreatedBy'] = $current_user_id;
            $product_category_data = $category_manager->create_product_category(json_encode($data));
        }else{
            $data['ModifiedAt'] = date('Y-m-d H:i:s');
            $data['ModifiedBy'] = $current_user_id;
            $product_category_data = $category_manager->edit_product_category(json_encode($data),$data['product_category_id']);
        }
        if($product_category_data){
            wp_send_json_success($product_category_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }
    /**
     * Delete Product Category.
     */
    public function delete_category() {
        $category_id = $_REQUEST['category_id'];
        $category_manager = new CRM_ERP_Category_Manager();
        $category_data = $category_manager->delete_category($category_id);
        if($category_data){
            wp_send_json_success('Product Category deleted'); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }

    /**
     * Delete Product Type.
     */
    public function delete_product_type() {
        $product_type_id = $_REQUEST['product_type_id'];
        $productManager = new CRM_ERP_Product_Manager();
        $products_data = $productManager->delete_product_type($product_type_id);
        if($products_data){
            wp_send_json_success('Product Type deleted'); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }

    /**
     * Delete Unit Type.
     */
    public function delete_unit_type() {
        $unit_type_id = $_REQUEST['unit_type_id'];
        $unitTypeManager = new CRM_ERP_Unit_Type_Manager();
        $unit_type_data = $unitTypeManager->delete_unit_type($unit_type_id);
        if($unit_type_data){
            wp_send_json_success('Unit Type deleted'); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }

    /**
     * Export Product Categories.
     */
    public function export_product_categories() {
        global $wpdb;
        $data = $_POST;
        $productCategoryManager = new CRM_ERP_Category_Manager();
        $product_categories_data = $productCategoryManager->export_product_categories(json_encode($data));
    }

    /**
     * Export Product Types.
     */
    public function export_product_types() {
        global $wpdb;
        $data = $_POST;
        $productManager = new CRM_ERP_Product_Manager();
        $product_types_data = $productManager->export_product_types(json_encode($data));
    }

    /**
     * Export Unit Types.
     */
    public function export_unit_types() {
        global $wpdb;
        $data = $_POST;
        $UnitTypeManager = new CRM_ERP_Unit_Type_Manager();
        $unit_types_data = $UnitTypeManager->export_unit_types(json_encode($data));
    }

    /**
     * Export Products.
     */
    public function export_products() {
        global $wpdb;
        $data = $_POST;
        $ProductManager = new CRM_ERP_Product_Manager();
        $product_data = $ProductManager->export_products(json_encode($data));
    }

    /**
     * Display the opportunities.
     */
    public function display_opportunities() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-opportunities.php';
            $opportunityListTable = new Opportunity_Report_Table();
            if (isset($_REQUEST['filter_invoice_status'])) {
                $filter_invoice_status = $_REQUEST['filter_invoice_status'];
            } else {
                $filter_invoice_status = "All";
            }
            if (isset($_REQUEST['download_excel_custom_report'])) {
                //ob_start();
                ob_end_clean();
                $opportunityListTable->export_leads_to_excel($filter_invoice_status);
                
                exit;
            }
            wp_list_page();
    }

    /**
     * Display STC opportunities.
     */
    public function display_stc_report() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-opportunities-stc.php';
            $STCopportunityListTable = new STC_Opportunity_Report_Table();
            if (isset($_REQUEST['filter_invoice_status'])) {
                $filter_invoice_status = $_REQUEST['filter_invoice_status'];
            } else {
                $filter_invoice_status = "All";
            }
            if (isset($_REQUEST['download_excel_custom_report'])) {
                //ob_start();
                ob_end_clean();
                $STCopportunityListTable->export_leads_to_excel($filter_invoice_status);
                
                exit;
            }
            wp_list_page();
    }

    /**
     * Display ERTC opportunities.
     */
    public function display_erc_report() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-opportunities-erc.php';
            $opportunityListTable = new ERTC_Opportunity_Report_Table();
            if (isset($_REQUEST['filter_invoice_status'])) {
                $filter_invoice_status = $_REQUEST['filter_invoice_status'];
            } else {
                $filter_invoice_status = "All";
            }
            if (isset($_REQUEST['download_excel_custom_report'])) {
                //ob_start();
                ob_end_clean();
                $opportunityListTable->export_leads_to_excel($filter_invoice_status);
                
                exit;
            }
            wp_list_page();
    }

    /**
     * Display RDC opportunities.
     */
    public function display_rdc_report() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-opportunities-rdc.php';
            $opportunityListTable = new RDC_Opportunity_Report_Table();
            if (isset($_REQUEST['filter_invoice_status'])) {
                $filter_invoice_status = $_REQUEST['filter_invoice_status'];
            } else {
                $filter_invoice_status = "All";
            }
            if (isset($_REQUEST['download_excel_custom_report'])) {
                //ob_start();
                ob_end_clean();
                $opportunityListTable->export_leads_to_excel($filter_invoice_status);
                
                exit;
            }
            wp_list_page();
    }

    /**
     * Display Partnership opportunities.
     */
    public function display_partnership_report() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-opportunities-partnership.php';
            $opportunityListTable = new PARTNERSHIP_Opportunity_Report_Table();
            if (isset($_REQUEST['filter_invoice_status'])) {
                $filter_invoice_status = $_REQUEST['filter_invoice_status'];
            } else {
                $filter_invoice_status = "All";
            }
            if (isset($_REQUEST['download_excel_custom_report'])) {
                //ob_start();
                ob_end_clean();
                $opportunityListTable->export_leads_to_excel($filter_invoice_status);
                
                exit;
            }
            wp_list_page();
    }

    /**
     * Display Audit Advisory opportunities.
     */
    public function display_audit_advisory_report() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-opportunities-audit-advisory.php';
            $opportunityListTable = new Audit_Advisory_Opportunity_Report_Table();
            if (isset($_REQUEST['filter_invoice_status'])) {
                $filter_invoice_status = $_REQUEST['filter_invoice_status'];
            } else {
                $filter_invoice_status = "All";
            }
            if (isset($_REQUEST['download_excel_custom_report'])) {
                //ob_start();
                ob_end_clean();
                $opportunityListTable->export_leads_to_excel($filter_invoice_status);
                
                exit;
            }
            wp_list_page();
    }
    /**
     * Display Tax Amendment opportunities.
     */
    public function display_tax_report() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-opportunities-ta.php';
            $opportunityListTable = new TA_Opportunity_Report_Table();
            if (isset($_REQUEST['filter_invoice_status'])) {
                $filter_invoice_status = $_REQUEST['filter_invoice_status'];
            } else {
                $filter_invoice_status = "All";
            }
            if (isset($_REQUEST['download_excel_custom_report'])) {
                //ob_start();
                ob_end_clean();
                $opportunityListTable->export_leads_to_excel($filter_invoice_status);
                
                exit;
            }
            wp_list_page();
    }


    // Callback function to display the currency settings page content
    function currency_settings_page() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-currency-list.php';
        $currencyListTable = new CRM_ERP_Currency_List();
        $currencyListTable->prepare_items();
    }

    /**
     * Create/Edit a currency setting.
     */
    public function add_edit_currency() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-add-edit-currency.php';
    }

    /**
     * Currency Submit Handler.
     */
    function currency_submit_handler() {
        // Check the nonce - security first!
        $currency_manager = new CRM_ERP_Currency_Manager();
        $data = $_POST;
        unset($data['action']);
        $current_user_id = get_current_user_id();
        if($data['currency_id'] == ''){
            unset($data['currency_id']);
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['created_by'] = $current_user_id;
            $currency_data = $currency_manager->create_currency(json_encode($data));
        }else{
            $data['modified_at'] = date('Y-m-d H:i:s');
            $data['modified_by'] = $current_user_id;
            $currency_data = $currency_manager->edit_currency(json_encode($data),$data['currency_id']);
        }
        if($currency_data){
            wp_send_json_success($currency_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }

    /**
     * Delete Currency.
     */
    public function delete_currency() {
        $currency_id = $_REQUEST['currency_id'];
        $currencyManager = new CRM_ERP_Currency_Manager();
        $currency_data = $currencyManager->delete_currency($currency_id);
        if($currency_data){
            wp_send_json_success('Currency deleted'); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }
    /**
     * Export Currency.
     */
    public function export_currency() {
        global $wpdb;
        $data = $_POST;
        $CurrencyManager = new CRM_ERP_Currency_Manager();
        $currency_data = $CurrencyManager->export_currency(json_encode($data));
    }
    /**
     * Function to check and create columns in the table
     */
    function check_and_create_columns() {
        /*global $wpdb;
        $table_name = $wpdb->prefix . 'crm_products'; // Get the table name with the proper WordPress prefix

        // Check if the table exists
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
            $columns = $wpdb->get_results("DESCRIBE $table_name");
            $column_names = array_column($columns, 'Field');

            if (!in_array('ProductLogo', $column_names)) {
                // Add ProductLogo column if not present
                $wpdb->query("ALTER TABLE $table_name ADD ProductLogo VARCHAR(255) DEFAULT ''");
            }

            if (!in_array('ProductImages', $column_names)) {
                // Add ProductImages column if not present
                $wpdb->query("ALTER TABLE $table_name ADD ProductImages TEXT");
            }
        }

        // Get the user's IP address
        $user_ip = $_SERVER['REMOTE_ADDR'];

        // Check if the IP address matches the desired value
        if ($user_ip === '**************') {
            // Set the table name
            $table_name = $wpdb->prefix . 'opportunities';

            // Check if the columns already exist
            $columns = $wpdb->get_col("DESC $table_name", 0);

            $custom_columns = array(
                'currency_deletedat',
                'currency_deletedby',
                'nextStep_deletedat',
                'nextStep_deletedby',
                'lead_deletedat',
                'lead_deletedby',
                'contact_deletedat',
                'contact_deletedby',
            );

            $missing_columns = array_diff($custom_columns, $columns);

            // If any columns are missing, add them
            if (!empty($missing_columns)) {
                foreach ($missing_columns as $column) {
                    $data_type = (strpos($column, 'deletedat') !== false) ? 'DATETIME' : 'INT';
                    $query = "ALTER TABLE $table_name ADD COLUMN $column $data_type DEFAULT NULL";
                    $result = $wpdb->query($query);
                }

                // Check if the query was successful
                if ($result !== false) {
                    echo 'Custom columns added successfully!';
                } else {
                    echo 'Error adding custom columns: ' . $wpdb->last_error;
                }
            } else {
                echo 'Custom columns already exist.';
            }
        }*/

    }
    /**
     * Display products milestones
     */
    public function display_milestone() {
        global $wpdb;
        // require plugin_dir_path(__FILE__) . 'crm-erp-product-status.php';
        require plugin_dir_path(__FILE__) . 'crm-erp-milestones.php';
        $milestonesTable = new CRM_ERP_Milestones();
        $milestonesTable->prepare_items();
    }

    /**
     * Display Milestone Stages
     */
    public function display_milestone_stages() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-milestone-stages.php';
        $milestoneStatusTable = new CRM_ERP_Milestone_Stages();
        $milestoneStatusTable->prepare_items();
    }

    /**
     * Create/Edit a product status.
     */
    public function add_edit_milestone() {
        global $wpdb;
        $products = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}crm_products WHERE {$wpdb->prefix}crm_products.DeletedAt IS NULL AND {$wpdb->prefix}crm_products.status = 'active' ORDER BY {$wpdb->prefix}crm_products.ProductID DESC");
        require plugin_dir_path(__FILE__) . 'crm-erp-milestone-add-edit.php';
    }
    /**
     * Product Status Submit Handler.
     */
    function product_status_submit_handler() {
        // Check the nonce - security first!
        $product_status_manager = new CRM_ERP_Product_Status_Manager();
        $data = $_POST;
        unset($data['action']);
        $current_user_id = get_current_user_id();
        if($data['product_status_id'] == ''){
            unset($data['product_status_id']);
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['created_by'] = $current_user_id;
            $product_status_data = $product_status_manager->create_product_status(json_encode($data));
        }else{
            $data['modified_at'] = date('Y-m-d H:i:s');
            $data['modified_by'] = $current_user_id;
            $product_status_data = $product_status_manager->edit_product_status(json_encode($data),$data['product_status_id']);
        }
        if($product_status_data){
            if($data['product_status_id'] == ''){
                wp_send_json_success('Product status submitted'); // Send back a JSON response
            }else{
                wp_send_json_success('Product status updated'); // Send back a JSON response
            }
            wp_die(); // All AJAX handlers should die when finished
        }
    }
    /**
     * Delete Product Status.
     */
    public function delete_product_status() {
        $product_status_id = $_REQUEST['product_status_id'];
        $productStatusManager = new CRM_ERP_Product_Status_Manager();
        $product_status_data = $productStatusManager->delete_product_status($product_status_id);
        if($product_status_data){
            wp_send_json_success('Product status deleted'); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }
    /**
     * Export Product Status.
     */
    public function export_product_status() {
        global $wpdb;
        $data = $_POST;
        $productStatusManager = new CRM_ERP_Product_Status_Manager();
        $product_status_data = $productStatusManager->export_product_status(json_encode($data));
    }

    /**
     * Export Milestones.
     */
    public function export_milestone() {
        global $wpdb;
        $data = $_POST;
        $productStatusManager = new CRM_ERP_Milestone_Manager();
        $product_status_data = $productStatusManager->export_milestone(json_encode($data));
    }

    /**
     * Export Milestone Status.
     */
    public function export_milestone_stage() {
        global $wpdb;
        $data = $_POST;
        $productStatusManager = new CRM_ERP_Milestone_Stage_Manager();
        $product_status_data = $productStatusManager->export_milestone_stage(json_encode($data));
    }

    public function delete_milestone() {
        $milestone_id = $_REQUEST['milestone_id'];
        $MilestoneManager = new CRM_ERP_Milestone_Manager();
        $mile_data = $MilestoneManager->delete_milestone($milestone_id);
        if($mile_data){
            wp_send_json_success('milestone deleted'); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }

    public function delete_milestone_stage_fun() {
        $milestone_stage_id = $_REQUEST['milestone_stage_id'];
        $MilestoneStatusManager = new CRM_ERP_Milestone_Stage_Manager();
        $milestatus_data = $MilestoneStatusManager->delete_milestone_stage($milestone_stage_id);
        if($milestatus_data){
            wp_send_json_success('Milestone Status deleted'); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }

        public function change_milestone_status() {
            $milestone_id = $_POST['milestone_id'];
            $val = $_POST['value'];
            
            if($val=='active'){
                $status = 'inactive';
            }else if($val=='inactive'){
                $status = 'active';
            }else{
                $status = 'active';
            }

        $MilestoneStatusManager = new CRM_ERP_Milestone_Manager();
        $milestatus_data = $MilestoneStatusManager->update_milestones_status($milestone_id,$status);
        if($milestatus_data){
            wp_send_json_success('Milestone Status change'); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }

    /**
     * Display Next Step.
     */
    public function display_next_step() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-next-step.php';
        $nextStepTable = new Next_Step_Table();
        $nextStepTable->prepare_items();
        //wp_list_page();
    }
     /**
     * Create/Edit a next step.
     */
    public function add_edit_next_step() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-add-edit-next-step.php';
    }
    /**
     * Next Step Submit Handler.
     */
    function next_step_submit_handler() {
        $next_step_manager = new CRM_ERP_Next_Step_Manager();
        $data = $_POST;
        unset($data['action']);
        $current_user_id = get_current_user_id();
        if($data['next_step_id'] == ''){
            unset($data['next_step_id']);
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['created_by'] = $current_user_id;
            $next_step_data = $next_step_manager->create_next_step(json_encode($data));
        }else{
            $data['modified_at'] = date('Y-m-d H:i:s');
            $data['modified_by'] = $current_user_id;
            $next_step_data = $next_step_manager->edit_next_step(json_encode($data),$data['next_step_id']);
        }
        if($next_step_data){
            wp_send_json_success($next_step_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }
    /**
     * Delete Next Step.
     */
    public function delete_next_step() {
        $next_step_id = $_REQUEST['next_step_id'];
        $nextStepManager = new CRM_ERP_Next_Step_Manager();
        $next_step_data = $nextStepManager->delete_next_step($next_step_id);
        if($next_step_data){
            wp_send_json_success('Next Step deleted'); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }
    /**
     * Export Next Step.
     */
    public function export_next_step() {
        global $wpdb;
        $data = $_POST;
        $nextStepManager = new CRM_ERP_Next_Step_Manager();
        $next_step_data = $nextStepManager->export_next_step(json_encode($data));
    }

    public function add_milestone_action() {
        global $wpdb;
        $data = $_POST;
        $MilesManager = new CRM_ERP_Milestone_Manager();
        $mile_data = $MilesManager->createMilestone($data);
        if($mile_data){
            wp_send_json_success($mile_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }

    public function add_milestone_stage_action() {
        global $wpdb;
        $data = $_POST;
        $MileStatusManager = new CRM_ERP_Milestone_Stage_Manager();
        $mile_status_data = $MileStatusManager->createMilestoneStage($data);
        if($mile_status_data){
            wp_send_json_success($mile_status_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }

    /**
     * Display the fee structured.
     */
    public function display_fee_structure() {
        require plugin_dir_path(__FILE__) . 'crm-erp-fee-structure.php';
        $feeStructureTable = new fee_structure();
        $feeStructureTable->prepare_items();
    }
    /**
     * Create/Edit a fee type.
     */
    public function add_edit_fee_type() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-fee-type-add-edit.php';
    }
    /**
     * Fee Type Submit Handler.
     */
    function fee_type_submit_handler() {
        // Check the nonce - security first!
        $fee_type_manager = new CRM_ERP_Fee_Structure_Manager();
        $data = $_POST;
        unset($data['action']);
        $current_user_id = get_current_user_id();
        if($data['fee_type_id'] == ''){
            unset($data['fee_type_id']);
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['created_by'] = $current_user_id;
            $fee_type_data = $fee_type_manager->create_fee_type(json_encode($data));
        }else{
            $data['modified_at'] = date('Y-m-d H:i:s');
            $data['modified_by'] = $current_user_id;
            $fee_type_data = $fee_type_manager->edit_fee_type(json_encode($data),$data['fee_type_id']);
        }

        if($fee_type_data){
            wp_send_json_success($fee_type_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }
    /**
     * Edit Fee Type.
     */
    public function edit_fee_type() {
        $fee_type_id = $_REQUEST['fee_type_id'];
        $feeTypeManager = new CRM_ERP_Fee_Structure_Manager();
        $fees_data = $feeTypeManager->get_fee_type($fee_type_id);
        wp_send_json_success($fees_data); // Send back a JSON response
        wp_die();
    }
    /**
     * Delete Fee Type.
     */
    public function delete_fee_type() {
        $fee_type_id = $_REQUEST['fee_type_id'];
        $feeTypeManager = new CRM_ERP_Fee_Structure_Manager();
        $fees_data = $feeTypeManager->delete_fee_type($fee_type_id);
        if($fees_data){
            wp_send_json_success('Fee Type deleted'); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }
    /**
     * Display the project config.
     */
    public function display_project_spaces() {
        require plugin_dir_path(__FILE__) . 'crm-erp-project-config.php';
        $projectConfigTable = new CRM_ERP_Project_Config();
        $projectConfigTable->prepare_items();
    }
    /**
     * Project Space Submit Handler.
     */
    function project_space_submit_handler() {
        $project_space_manager = new CRM_ERP_Project_Space_Manager();
        $data = $_POST;
        $tabs = '';
        foreach($data['tabs_name'] as $key => $value){
            $tabs .= $value.',';
        }
        $tabs = rtrim($tabs,",");
        unset($data['tabs_name']);
        $data['subsections_name'] = $tabs;
        unset($data['action']);
        $current_user_id = get_current_user_id();
        if($data['project_space_id'] == 0){
            unset($data['project_space_id']);
            $data['created_at'] = date('Y-m-d H:i:s');
            $data['created_by'] = $current_user_id;
            $project_space_data = $project_space_manager->create_project_space(json_encode($data));
        }else{
            $data['modified_at'] = date('Y-m-d H:i:s');
            $data['modified_by'] = $current_user_id;
            $project_space_data = $project_space_manager->edit_project_space(json_encode($data),$data['project_space_id']);
        }
        if($project_space_data){
            wp_send_json_success($project_space_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }
    /**
     * Display the projects.
     */
    public function display_projects() {
        global $wpdb;
        //$wpdb->query("DELETE FROM eccom_projects WHERE project_id = 2");die;
        require plugin_dir_path(__FILE__) . 'crm-erp-projects.php';
        $projectListTable = new Project_Report_Table();
        if (isset($_REQUEST['download_excel_custom_report'])) {
            ob_end_clean();
            $projectListTable->export_leads_to_excel();
            exit;
        }
        wp_list_page();
    }

    public function erc_display_projects() {
        global $wpdb;
        //$wpdb->query("DELETE FROM eccom_projects WHERE project_id = 2");die;
        require plugin_dir_path(__FILE__) . 'crm-erp-erc-projects.php';
        $projectListTable = new ERTC_Project_Report_Table();
        if (isset($_REQUEST['download_excel_custom_report'])) {
            ob_end_clean();
            $projectListTable->export_leads_to_excel();
            exit;
        }
        wp_list_page();
    }

    public function audit_advisory_display_projects() {
        global $wpdb;
        //$wpdb->query("DELETE FROM eccom_projects WHERE project_id = 2");die;
        require plugin_dir_path(__FILE__) . 'crm-erp-audit-advisory-projects.php';
        $projectListTable = new Audit_Advisory_Project_Report_Table();
        if (isset($_REQUEST['download_excel_custom_report'])) {
            ob_end_clean();
            $projectListTable->export_leads_to_excel();
            exit;
        }
        wp_list_page();
    }
    public function rdc_display_projects() {
        global $wpdb;
        //$wpdb->query("DELETE FROM eccom_projects WHERE project_id = 2");die;
        require plugin_dir_path(__FILE__) . 'crm-erp-rdc-projects.php';
        $projectListTable = new RDC_Project_Report_Table();
        if (isset($_REQUEST['download_excel_custom_report'])) {
            ob_end_clean();
            $projectListTable->export_leads_to_excel();
            exit;
        }
        wp_list_page();
    }

    public function tax_amendment_display_projects() {
        global $wpdb;
        //$wpdb->query("DELETE FROM eccom_projects WHERE project_id = 2");die;
        require plugin_dir_path(__FILE__) . 'crm-erp-tax-amendment-projects.php';
        $projectListTable = new Tax_Amendment_Project_Report_Table();
        if (isset($_REQUEST['download_excel_custom_report'])) {
            ob_end_clean();
            $projectListTable->export_leads_to_excel();
            exit;
        }
        wp_list_page();
    }


    public function stc_display_projects() {
        global $wpdb;
        //$wpdb->query("DELETE FROM eccom_projects WHERE project_id = 2");die;
        require plugin_dir_path(__FILE__) . 'crm-erp-stc-projects.php';
        $projectListTable = new STC_Project_Report_Table();
        if (isset($_REQUEST['download_excel_custom_report'])) {
            ob_end_clean();
            $projectListTable->export_leads_to_excel();
            exit;
        }
        wp_list_page();
    }

    /**
     * Display the projects by ajax.
     */
    function project_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-projects.php');
        $wp_list_table = new Project_Report_Table();
        $wp_list_table->prepare_items();
        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();
        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }

    function tax_amendmen_project_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-tax-amendment-projects.php');
        $wp_list_table = new Tax_Amendment_Project_Report_Table();
        $wp_list_table->prepare_items();
        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();
        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }

    function stc_project_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-stc-projects.php');
        $wp_list_table = new STC_Project_Report_Table();
        $wp_list_table->prepare_items();
        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();
        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }

    function ertc_project_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-erc-projects.php');
        $wp_list_table = new ERTC_Project_Report_Table();
        $wp_list_table->prepare_items();
        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();
        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }

    function audit_advisory_project_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-audit-advisory-projects.php');
        $wp_list_table = new Audit_Advisory_Project_Report_Table();
        $wp_list_table->prepare_items();
        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();
        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }

    function rdc_project_listing_ajax_handler(){
        include(plugin_dir_path(__FILE__) . 'crm-erp-rdc-projects.php');
        $wp_list_table = new RDC_Project_Report_Table();
        $wp_list_table->prepare_items();
        ob_start();
        $wp_list_table->display();
        $display = ob_get_clean();
        die(
            json_encode(array(
                "display" => $display
            ))
        );
        exit;
    }

    /**
     * Display the project filter by ajax.
     */
    function fetch_project_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-projects.php');
        $wp_list_table = new Project_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }


    function tax_amendmen_fetch_project_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-tax-amendment-projects.php');
        $wp_list_table = new Tax_Amendment_Project_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }

    function stc_fetch_project_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-stc-projects.php');
        $wp_list_table = new STC_Project_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }

    function ertc_fetch_project_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-erc-projects.php');
        $wp_list_table = new ERTC_Project_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }

    function audit_advisory_fetch_project_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-audit-advisory-projects.php');
        $wp_list_table = new Audit_Advisory_Project_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }

    function rdc_fetch_project_filter_callback() {
        include(plugin_dir_path(__FILE__) . 'crm-erp-rdc-projects.php');
        $wp_list_table = new RDC_Project_Report_Table();
        $wp_list_table->ajax_response();
        exit;
    }

    /**
     * Manage erc project.
     */
    public function manage_erc_project() {
        global $wpdb;
        $args = array(
          'orderby' => 'display_name',
          'order'   => 'ASC'
        );
        $users = get_users( $args );
        $currencies = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}currencies WHERE {$wpdb->prefix}currencies.deleted_at IS NULL ORDER BY {$wpdb->prefix}currencies.currency_id DESC");
        $unit_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}unit_types WHERE {$wpdb->prefix}unit_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}unit_types.UnitTypeID DESC");
        $product_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_types WHERE {$wpdb->prefix}product_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_types.ProductTypeID DESC");
        $product_categories = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_categories WHERE {$wpdb->prefix}product_categories.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_categories.CategoryID DESC");
        $next_step_datas = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}next_step WHERE {$wpdb->prefix}next_step.deleted_at IS NULL ORDER BY {$wpdb->prefix}next_step.next_step_id DESC");
        require plugin_dir_path(__FILE__) . 'crm-erp-erc-project-view.php';
    }

    /**
     * Manage rdc project.
     */
    public function manage_rdc_project() {
        global $wpdb;
        $args = array(
          'orderby' => 'display_name',
          'order'   => 'ASC'
        );
        $users = get_users( $args );
        $currencies = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}currencies WHERE {$wpdb->prefix}currencies.deleted_at IS NULL ORDER BY {$wpdb->prefix}currencies.currency_id DESC");
        $unit_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}unit_types WHERE {$wpdb->prefix}unit_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}unit_types.UnitTypeID DESC");
        $product_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_types WHERE {$wpdb->prefix}product_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_types.ProductTypeID DESC");
        $product_categories = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_categories WHERE {$wpdb->prefix}product_categories.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_categories.CategoryID DESC");
        $next_step_datas = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}next_step WHERE {$wpdb->prefix}next_step.deleted_at IS NULL ORDER BY {$wpdb->prefix}next_step.next_step_id DESC");
        require plugin_dir_path(__FILE__) . 'crm-erp-rdc-project-view.php';
    }

    /**
     * Manage audit project.
     */
    public function manage_audit_project() {
        global $wpdb;
        $args = array(
          'orderby' => 'display_name',
          'order'   => 'ASC'
        );
        $users = get_users( $args );
        $currencies = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}currencies WHERE {$wpdb->prefix}currencies.deleted_at IS NULL ORDER BY {$wpdb->prefix}currencies.currency_id DESC");
        $unit_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}unit_types WHERE {$wpdb->prefix}unit_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}unit_types.UnitTypeID DESC");
        $product_types = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_types WHERE {$wpdb->prefix}product_types.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_types.ProductTypeID DESC");
        $product_categories = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}product_categories WHERE {$wpdb->prefix}product_categories.DeletedAt IS NULL ORDER BY {$wpdb->prefix}product_categories.CategoryID DESC");
        $next_step_datas = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}next_step WHERE {$wpdb->prefix}next_step.deleted_at IS NULL ORDER BY {$wpdb->prefix}next_step.next_step_id DESC");
        require plugin_dir_path(__FILE__) . 'crm-erp-audit-project-view.php';
    }

    /**
     * Manage stc project.
     */
    public function manage_stc_project() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-stc-project-view.php';
    }

    /**
     * Manage TA project.
     */
    public function manage_ta_project() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-ta-project-view.php';
    }

    //For custom notes
    public function add_custom_note_data_callback(){
        global $wpdb;

        $opportunity_id = $_POST['opportunity_id'];
        $note = $_POST['notes'];
        $user_id = $_POST['created_by'];
        $time = date("Y-m-d h:i:sa");

        $current_user_id = 0;
        if(isset($user_id) && !empty($user_id)){
            $current_user_id = $user_id;
            $userdata = get_user_by( 'id', $current_user_id );
            $user_name='';
            if(isset($userdata->data)){
                $user_name = $userdata->data->display_name;
            }
            $comment_pre_tag = strpos($note,"added a comment");
            if(!empty($user_name) && empty($comment_pre_tag)){
                $note = $user_name .' added a comment: '.$note;
            }
        }

        if(!empty($opportunity_id)){
            try {
                $result = $wpdb->query("INSERT INTO `eccom_erc_opportunity_notes` (`opportunity_id`, `created_by`, `note`, `created`) VALUES ('".$opportunity_id."', '".$user_id."', '".$note."', '".$time."');");

                $result_array['status'] = 200;
                $result_array['message'] ='The note has been added successfully';
                $result_array['code'] = 'success';
            } catch (Exception $e) {
                $result_array['status'] =400;
                $result_array['message'] ='The note has been added failed';
                $result_array['error_message'] = $e->getMessage();
                $result_array['code'] ='failure';
            }

            $body = json_encode($result_array);
            echo $body;
            wp_die();
        }
    }

    //For custom project notes
    public function add_project_note_data_callback(){
        global $wpdb;
        $project_id = $_POST['project_id'];
        $note = $_POST['notes'];
        $user_id = $_POST['created_by'];
        $time = date("Y-m-d h:i:sa");
        $current_user_id = 0;
        if(isset($user_id) && !empty($user_id)){
            $current_user_id = $user_id;
            $userdata = get_user_by( 'id', $current_user_id );
            $user_name='';
            if(isset($userdata->data)){
                $user_name = $userdata->data->display_name;
            }
            $comment_pre_tag = strpos($note,"added a comment");
            if(!empty($user_name) && empty($comment_pre_tag)){
                $note = $user_name .' added a comment: '.$note;
            }
        }
        if(!empty($project_id)){
            try {
                $result = $wpdb->query("INSERT INTO {$wpdb->prefix}erc_project_notes (`project_id`, `created_by`, `note`, `created`) VALUES ('".$project_id."', '".$user_id."', '".$note."', '".$time."');");

                $result_array['status'] = 200;
                $result_array['message'] ='The note has been added successfully';
                $result_array['code'] = 'success';
            } catch (Exception $e) {
                $result_array['status'] =400;
                $result_array['message'] ='The note has been added failed';
                $result_array['error_message'] = $e->getMessage();
                $result_array['code'] ='failure';
            }
            $body = json_encode($result_array);
            echo $body;
            wp_die();
        }
    }

    //Update Sales Agent On Project
    public function update_sales_agent_of_project(){
        global $wpdb;
        $project_id = $_POST['project_id'];
        $sales_user_id = $_POST['sales_agent_id'];
        $wpdb->query("UPDATE {$wpdb->prefix}projects SET sales_user_id = ".$sales_user_id." WHERE project_id = ".$project_id.""); 
    }
    //Update Sales Support On Project
    public function update_sales_support_of_project(){
        global $wpdb;
        $project_id = $_POST['project_id'];
        $sales_support_id = $_POST['sales_support_id'];
        $wpdb->query("UPDATE {$wpdb->prefix}projects SET sales_support_id = ".$sales_support_id." WHERE project_id = ".$project_id.""); 
    }
    /**
     * Create/Edit a project.
     */
    public function add_edit_project() {
        global $wpdb;
        require plugin_dir_path(__FILE__) . 'crm-erp-project-add-edit.php';
    }

    /**
     * Get bank data.
     */
    public function erc_project_bank_info_function(){
        include(plugin_dir_path(__FILE__) . 'erc_project_bank_info.php');
    }
    
    /**
     * Get intake data.
     */
    public function erc_project_intake_data_function(){
        include(plugin_dir_path(__FILE__) . 'erc_project_intake_data.php');
  	}
    /**
     * Get fee data.
     */
    public function erc_project_fee_data_function(){
        include(plugin_dir_path(__FILE__) . 'erc_project_fee_data.php');
    }
    public function erc_company_document_data_function(){
        include(plugin_dir_path(__FILE__) . 'erc_company_documents.php');
    }
    public function projects_audit_log_function(){
        include(plugin_dir_path(__FILE__) . 'projects_audit_log.php');
    }
    public function opportunity_audit_logs_function(){
        include(plugin_dir_path(__FILE__) . 'opportunity_audit_logs.php');
    }
    public function erc_payroll_document_data_function(){
        include(plugin_dir_path(__FILE__) . 'erc_payroll_documents.php');
    }
    public function erc_project_document_data_function(){
        include(plugin_dir_path(__FILE__) . 'erc_projects_documents.php');
    }
    public function erc_other_document_data_function(){
        include(plugin_dir_path(__FILE__) . 'erc_other_documents.php');
    }


  	/**
     * Update project information.
     */
    public function update_erc_project_info_function(){
        global $wpdb;
        include(plugin_dir_path(__FILE__) . 'update_erc_project_info.php');
  	}
    /**
     * Assign Collaborators.
     */
    public function assign_collaborators(){
        global $wpdb;
        include(plugin_dir_path(__FILE__) . 'assign_collaborators.php');
    }
    /**
     * Delete Project.
    */
    public function delete_project_callback() {
        $project_id = $_REQUEST['project_id'];
        $ProjectManager = new CRM_ERP_Project_Manager();
        $project_data = $ProjectManager->delete_project($project_id);
        if($project_data){
            wp_send_json_success($project_data); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }
    /**
     * Delete Service.
    */
    public function delete_service_callback() {
        $service_id = $_REQUEST['service_id'];
        $ProductManager = new CRM_ERP_Product_Manager();
        $service_data = $ProductManager->delete_service($service_id);
        if($service_data){
            wp_send_json_success($service_data); 
        }
        wp_die(); // All AJAX handlers should die when finished
    }
    //Upload STC Documents
    function handle_stc__file_upload() {
        $parent_folder = $_POST['parent_folder'];
        $lead_id = $_POST['lead_id'];
        $content_directory = WP_CONTENT_DIR . '/uploads/';
        if (!file_exists($content_directory.'documents_uploads/'.$lead_id)) {
            mkdir( $content_directory . 'documents_uploads/'.$lead_id);
        }
        if (!file_exists($content_directory.'documents_uploads/'.$lead_id . '/'.$parent_folder.'/')) {
            mkdir( $content_directory . 'documents_uploads/'.$lead_id . '/'.$parent_folder.'/');
        }

        $target_dir_location = $content_directory . 'documents_uploads/'.$lead_id .  '/'.$parent_folder.'/';
        $doc_key = $_POST['doc_key'];
        $doc_type_id = $_POST['doc_type_id'];
        $product_id = $_POST['product_id'];
        $document_folder_link = $this->get_document_folder_link($lead_id, $product_id);
        if($document_folder_link == ''){
            $return_array = array('uploadok' => 'false', 'error' => 'We are experiencing technical difficulties. Please try again later.');
            echo json_encode($return_array);
            die();
        }
        if (!empty($_FILES)) {
            $filez = $_FILES;
            $return_array = $this->upload_single_docs_array($filez, $target_dir_location, $lead_id, $parent_folder,$doc_key);
        }

        if ($return_array['uploadok'] > 0) {
            $pdf_dir = $target_dir_location;
            $foldername = $parent_folder;
            $document_folder = $document_folder_link;
            $file_path = site_url().'/wp-content/uploads/documents_uploads/'.$lead_id.'/'.$foldername.'/'.$return_array['uploaded_path']['file'] ;

            $pdf_filename = $return_array['uploaded_path']['file'];
            if (!empty($pdf_filename)) {
                
                $onedrive_file_id= do_shortcode('[upload_file_onedrive pdf_dir="'.$pdf_dir.'" foldername="'.$foldername.'"  pdf_filename="'.$pdf_filename.'" document_folder="'.$document_folder.'"]');

                $current_user_id = get_current_user_id();
                global $wpdb;
                $table_name_uploads = $wpdb->prefix . 'leads_document_upload';
                $table_name_status = $wpdb->prefix . 'leads_document_status';
                // Insert data into leads_document_upload table
                 $wpdb->insert(
                     $table_name_uploads,
                     array(
                         'lead_id' => $lead_id,
                         'parent_folder' => $parent_folder,
                         'uploded_documents' => $file_path,
                         'doc_key' => $doc_key,
                         'doc_type_id' => $doc_type_id,
                         'onedrive_itemid' => $onedrive_file_id,
                         'local_path_filename' => $pdf_filename,
                         'deleted' => 0,
                         'updated_by' => $current_user_id,
                         'ip_address' => $_SERVER['REMOTE_ADDR'],
                     )
                );
                $inserted_id = $wpdb->insert_id;
                // Insert data into leads_document_status table
                 $wpdb->insert(
                     $table_name_status,
                     array(
                         'doc_id' => $inserted_id,
                         'doc_status' => 'In review',
                         'doc_type_id' => $doc_type_id,
                         'user_id' => $current_user_id,
                         'ip_address' => $_SERVER['REMOTE_ADDR']
                     )
                 );
                $table_name_comments = $wpdb->prefix . 'leads_document_notes';
                $comment = $pdf_filename.' uploaded';
                $wpdb->insert(
                    $table_name_comments,
                    array(
                        'doc_id' => $inserted_id,
                        'lead_id' => $lead_id,
                        'comments' => $comment,
                        'doc_type_id' => $doc_type_id,
                        'user_id' => $current_user_id,
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    )
                );
                $return_array = array(
                    'doc_id' => $inserted_id,
                     'uploadok' => 'true',
                     "message" => "File uploaded successfully",
                     "file_url" => $file_path,
                     "pdf_filename" => $pdf_filename,
                     "parent_folder" => $parent_folder,
                     "lead_id" => $lead_id,
                     "doc_key" => $doc_key,
                     "doc_type_id" => $doc_type_id,
                     "onedrive_file_id" => $onedrive_file_id
                ); 
                echo json_encode($return_array);
                die();
            }
        }
    }

    //Upload ERC Documents
    function handle_erc__file_upload() {
        global $wpdb;
        $parent_folder = $_POST['parent_folder'];
        $lead_id = $_POST['lead_id'];
        $content_directory = WP_CONTENT_DIR . '/uploads/';
        if (!file_exists($content_directory.'documents_uploads/'.$lead_id)) {
            mkdir( $content_directory . 'documents_uploads/'.$lead_id);
        }
        if (!file_exists($content_directory.'documents_uploads/'.$lead_id . '/'.$parent_folder.'/')) {
            mkdir( $content_directory . 'documents_uploads/'.$lead_id . '/'.$parent_folder.'/');
        }

        $target_dir_location = $content_directory . 'documents_uploads/'.$lead_id .  '/'.$parent_folder.'/';
        $doc_key = $_POST['doc_key'];
        $doc_type_id = $_POST['doc_type_id'];
        
        $intake_table = $wpdb->prefix . 'erc_erc_intake';

        $document_folder_link = $wpdb->get_var("SELECT document_folder_link FROM $intake_table WHERE lead_id='".$lead_id."'");
        if($document_folder_link == ''){
            $return_array = array('uploadok' => 'false', 'error' => 'We are experiencing technical difficulties. Please try again later.');
            echo json_encode($return_array);
            die();
        }
        if (!empty($_FILES)) {
            $filez = $_FILES;
            $return_array = $this->upload_single_docs_array($filez, $target_dir_location, $lead_id, $parent_folder,$doc_key);
        }

        if ($return_array['uploadok'] > 0) {
            $pdf_dir = $target_dir_location;
            $foldername = $parent_folder;
            $document_folder = $document_folder_link;
            $file_path = site_url().'/wp-content/uploads/documents_uploads/'.$lead_id.'/'.$foldername.'/'.$return_array['uploaded_path']['file'] ;

            $pdf_filename = $return_array['uploaded_path']['file'];
            if (!empty($pdf_filename)) {
                
                $onedrive_file_id= do_shortcode('[upload_file_onedrive pdf_dir="'.$pdf_dir.'" foldername="'.$foldername.'"  pdf_filename="'.$pdf_filename.'" document_folder="'.$document_folder.'"]');

                $current_user_id = get_current_user_id();
                global $wpdb;
                $table_name_uploads = $wpdb->prefix . 'leads_document_upload';
                $table_name_status = $wpdb->prefix . 'leads_document_status';
                // Insert data into leads_document_upload table
                 $wpdb->insert(
                     $table_name_uploads,
                     array(
                         'lead_id' => $lead_id,
                         'parent_folder' => $parent_folder,
                         'uploded_documents' => $file_path,
                         'doc_key' => $doc_key,
                         'doc_type_id' => $doc_type_id,
                         'onedrive_itemid' => $onedrive_file_id,
                         'local_path_filename' => $pdf_filename,
                         'deleted' => 0,
                         'updated_by' => $current_user_id,
                         'ip_address' => $_SERVER['REMOTE_ADDR'],
                     )
                );
                $inserted_id = $wpdb->insert_id;
                // Insert data into leads_document_status table
                 $wpdb->insert(
                     $table_name_status,
                     array(
                         'doc_id' => $inserted_id,
                         'doc_status' => 'In review',
                         'doc_type_id' => $doc_type_id,
                         'user_id' => $current_user_id,
                         'ip_address' => $_SERVER['REMOTE_ADDR']
                     )
                 );
                $table_name_comments = $wpdb->prefix . 'leads_document_notes';
                $comment = $pdf_filename.' uploaded';
                $wpdb->insert(
                    $table_name_comments,
                    array(
                        'doc_id' => $inserted_id,
                        'lead_id' => $lead_id,
                        'comments' => $comment,
                        'doc_type_id' => $doc_type_id,
                        'user_id' => $current_user_id,
                        'ip_address' => $_SERVER['REMOTE_ADDR']
                    )
                );
                $return_array = array(
                    'doc_id' => $inserted_id,
                     'uploadok' => 'true',
                     "message" => "File uploaded successfully",
                     "file_url" => $file_path,
                     "pdf_filename" => $pdf_filename,
                     "parent_folder" => $parent_folder,
                     "lead_id" => $lead_id,
                     "doc_key" => $doc_key,
                     "doc_type_id" => $doc_type_id,
                     "onedrive_file_id" => $onedrive_file_id
                ); 
                echo json_encode($return_array);
                die();
            }
        }
    }

    function upload_single_docs_array($filez,$target_dir_location,$user_id,$parent_folder,$doc_key=''){
        if (!empty($filez)) {
           $count = 0;
           $uploadok = 0;
           $uploaded_path = array();
           foreach ($filez as $key => $files) {
               if (!empty($files['name'])) {
                    $uploaded_path[$key] = array();
                    $name_file = $files['name'];
                    $name_file = preg_replace('/[\[\]\(\)\{\}]+/', '_', $name_file);
                    $name_file = str_replace('_.', '.', $name_file);

                   $name_file = pathinfo($name_file, PATHINFO_FILENAME) . '_' . $doc_key . '.' . pathinfo($name_file, PATHINFO_EXTENSION);

                    $tmp_name = $files['tmp_name'];
                    $field_name = $key;
                    // get file type
                    $file_type = wp_check_filetype(basename($name_file), null);
                    $file_type = $file_type['ext'];
                    $file_path = $target_dir_location . $name_file;
                   if (move_uploaded_file($tmp_name, $target_dir_location . $name_file)) {
                       $uploadok = $uploadok + 1;
                       $uploaded_path[$key]= $name_file;
                   }
               }
               $count++;
           }
           $return_array = array(
               'uploadok' => $uploadok,
               'uploaded_path' => $uploaded_path
           );
           return $return_array;
        }
    }
    /*
        Function to get the product id from current logged in user
    */    
    public function get_product_id_by_user($user_stc_email)
    {
        global $wpdb;
        $user_email= $user_stc_email;
        // Get the WordPress database table prefix
        $table_prefix = $wpdb->prefix;
        // Define the custom table names
        $table_name_emails = $table_prefix . 'op_emails'; // Replace 'op_emails' with your actual table name for emails
        $table_name_email_contact = $table_prefix . 'op_email_contact'; // Replace 'op_email_contact' with your actual table name for email contacts
        $table_name_contact_lead = $table_prefix . 'op_contact_lead'; // Replace 'op_contact_lead' with your actual table name for contact leads
        $table_name_opportunities = $table_prefix . 'opportunities'; // Replace 'opportunities' with your actual table name for opportunities
        $table_name_opportunity_products = $table_prefix . 'opportunity_products'; // Replace 'opportunity_products' with your actual table name for opportunity products

        // Prepare the SQL query to get the email_id
        $query_emails = $wpdb->prepare("
            SELECT id
            FROM $table_name_emails
            WHERE custom1 !='import_from_lead' AND email = %s
        ", $user_email);

        // Execute the query to get the email_id
        $email_id = $wpdb->get_var($query_emails);
        if ($email_id) {
            // Prepare the SQL query to get the contact_id
            $query_contact_id = $wpdb->prepare("
                SELECT contact_id
                FROM $table_name_email_contact
                WHERE email_id = %d
            ", $email_id);

            // Execute the query to get the contact_id
            $contact_id = $wpdb->get_var($query_contact_id);

            if ($contact_id) {
                // Prepare the SQL query to get the lead_id
                $query_lead_id = $wpdb->prepare("
                    SELECT lead_id
                    FROM $table_name_contact_lead
                    WHERE contact_id = %d
                ", $contact_id);

                // Execute the query to get the lead_id
                $lead_id = $wpdb->get_var($query_lead_id);

                if ($lead_id) {
                    // Prepare the SQL query to get the OpportunityID
                    $query_opportunity_id = $wpdb->prepare("
                        SELECT OpportunityID
                        FROM $table_name_opportunities
                        WHERE LeadID = %d
                    ", $lead_id);

                    // Execute the query to get the OpportunityID
                    $opportunity_id = $wpdb->get_var($query_opportunity_id);

                    if ($opportunity_id) {
                        // Prepare the SQL query to get the product_id
                        $query_product_id = $wpdb->prepare("
                            SELECT product_id
                            FROM $table_name_opportunity_products
                            WHERE opportunity_id = %d
                        ", $opportunity_id);

                        // Execute the query to get the product_id
                        $product_id = $wpdb->get_var($query_product_id);
                        
                        if ($product_id) {
                            //echo "Product ID corresponding to the email address {$user_email}: " . $product_id;
                            return $product_id;
                        } else {
                            //echo "No Product ID found for the email address {$user_email}.";
                        }
                    } else {
                        //echo "No OpportunityID found for the email address {$user_email}.";
                    }
                } else {
                    //echo "No Lead ID found for the email address {$user_email}.";
                }
            } else {
                //echo "No Contact ID found for the email address {$user_email}.";
            }
        } else {
            //echo "No email ID found for the email address {$user_email}.";
        }
    }
    function get_document_folder_link($lead_id, $product_id)
    {

        global $wpdb;
        $table_lead_folder_mapping = $wpdb->prefix . 'lead_folder_mapping';
        $result = $wpdb->get_results("SELECT * FROM " . $table_lead_folder_mapping . " WHERE lead_id = '$lead_id' and product_id = '$product_id' order by id desc ");
        $document_folder_link = "";
        $prefix_url = "https://occams-my.sharepoint.com/personal/admin_occamsparadigm_com/Documents/Occams/USA/Business%20Segments/Financial%20%26%20Tax%20Advisory%20(FTA)/Tax%20Credits/ERC/ERC%20Client%27s/AutoX/";
        if($result[0]->document_folder){
            $document_folder_link = $prefix_url.$result[0]->document_folder;
        }

        return $document_folder_link;
    }
    function update_stc_document_status(){
        global $wpdb;
        $user_id = get_current_user_id();
        $doc_status = $_POST['value'];
        $doc_id = $_POST['doc_id'];
        $label = $_POST['label'];
        $doc_type_id = $_POST['doc_type_id'];
        $reason = $_POST['reason'];

        $table_name_status = $wpdb->prefix . 'leads_document_status';
        $table_name_comments = $wpdb->prefix . 'leads_document_notes';

        $table_name_uploads = $wpdb->prefix . 'leads_document_upload';
        $result = $wpdb->get_results("SELECT * FROM " . $table_name_uploads . " WHERE id = '$doc_id'  order by id desc ");
        
        $local_path_filename = $result[0]->local_path_filename;
        $updated_by = $result[0]->updated_by;
        $lead_id = $result[0]->lead_id;
        $user_data = get_userdata($updated_by);
        $user_email = $user_data->user_email;
        $user_name = $user_data->user_login;
        $user_first_name = get_user_meta($updated_by, 'first_name', true);
        $user_last_name = get_user_meta($updated_by, 'last_name', true);
        
        $user_name = $user_first_name;
        if ($doc_status =='Approved') {
            $comment = $local_path_filename.' approved';
        } elseif ($doc_status =='Rejected'){
            $comment = $local_path_filename.' rejected';          
            $subject = 'ERC Document Rejection -'.$local_path_filename;
            $body = '<p>Dear '.$user_name.',</p>';
            $body .= '<p>We regret to inform you that the document you uploaded for the Employee Retention Credit filling
            process has been reviewed and rejected by our back office team. We appreciate your effort in providing
            the necessary documentation, but unfortunately, it did not meet the required criteria.</p>';           
            $body .= '<p>Document Title: '.$label.'</p>';
            $body .= '<p>Document File Name: '.$local_path_filename.'</p>';
            $body .= '<p>Rejection Date: '.date("m/d/Y").'</p>'; 
            $body .= '<p>Reason for Rejection: '. $reason.'</p>';
            $body .= '<p>Please review the reason provided for the rejection and make the necessary adjustments. You can
            upload the corrected document through our portal, ensuring that it complies with the specified
            requirements.</p>';
            $body .= '<p>If you have any questions or need further clarification, please do not hesitate to contact us.</p>';
            $body .= '<p>Regards,<br>Occams ERC Support Team<br> 212-531-11111<br> <EMAIL></p>';
            $to =  $user_email;
            $headers = array('Content-Type: text/html; charset=UTF-8'); 
        } elseif($doc_status =='In review') {
            $comment = $local_path_filename.' in review';
        }
        $wpdb->insert(
            $table_name_status,
            array(
                'doc_id' => $doc_id,
                'doc_status' => $doc_status,
                'user_id' => $user_id,
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'doc_type_id' => $doc_type_id
            )
        );
        $wpdb->insert(
            $table_name_comments,
            array(
                'doc_id' => $doc_id,
                'comments' => $comment,
                'lead_id' => $lead_id,
                'user_id' => $user_id,
                'ip_address' => $_SERVER['REMOTE_ADDR'],
                'doc_type_id' => $doc_type_id
            )
        );
        $return_array = array(
            "message" => "Document status updated successfully",
        );
        echo json_encode($return_array);
        die();
    }
    function check_rejected_reason(){
        $doc_id = $_POST['doc_id'];
        $doc_type_id = $_POST['doc_type_id'];
        $value = $_POST['value'];
        global $wpdb;
        $table_name_comments = $wpdb->prefix . 'leads_document_notes';
        $table_name_uploads = $wpdb->prefix . 'leads_document_upload';
        $result = $wpdb->get_results("SELECT * FROM " . $table_name_uploads . " WHERE id = '$doc_id'  order by id desc ");
        $lead_id = $result[0]->lead_id;
        $comment_data = $wpdb->get_results($wpdb->prepare('SELECT * FROM '.$table_name_comments.' WHERE doc_type_id = '.$doc_type_id.' AND  doc_id = '.$doc_id.' AND lead_id = '.$lead_id.' ORDER BY id DESC LIMIT 1'));
        if($value ==  'Rejected'){
            $error_message = 'Please add comment before rejecting document';
        }else if($value == 'Approved'){
            $error_message = 'Please add comment before approving document';
        }else if($value == 'In review'){
            $error_message = 'Please add comment before sending document for review';
        }
        if (!empty($comment_data)) {
            // get the user id added by lead_user id
            $comment_added_by_User_id = $comment_data[0]->user_id;
            //get role of $comment_added_by_User_id user_id
            $userdata = get_user_by( 'id', $comment_added_by_User_id );
            $user_roles=$userdata->roles;
            if (in_array("echeck_admin", $user_roles )) {
                $comment = $comment_data[0]->comments;
                $expectedValues = array("uploaded", "approved", "In review","deleted","rejected");
                $words = explode(" ", $comment);
                $lastWord = end($words);
                if (in_array($lastWord, $expectedValues)) {
                    $return_array = array('uploadok' => 'false', 'error' => $error_message);
                    echo json_encode($return_array);
                    die();
            
                } else {
                    $return_array = array('uploadok' => 'true','reason' => $comment);
                    echo json_encode($return_array);
                    die();
            
                }
            }
            else
            {
                $return_array = array('uploadok' => 'false','reason' => 'Ops User comment is missing, Please try after some time');
                echo json_encode($return_array);
                die();
            }
        }else{
            $return_array = array('uploadok' => 'false', 'error' => $error_message);
            echo json_encode($return_array);
            die();
        }
    }
    public function view_stc_comments(){
        global $wpdb;
        $user_id = get_current_user_id();
        $doc_type_id = $_POST['doc_type_id'];
        $doc_id = $_POST['doc_id'];
        if(isset($doc_type_id) && !empty($doc_type_id )){
            $table_name_uploads = $wpdb->prefix . 'leads_document_upload';
            $result = $wpdb->get_results("SELECT * FROM " . $table_name_uploads . " WHERE id = '$doc_id'  order by id desc ");
            $lead_id = (isset($_POST['lead_id']) && !empty($_POST['lead_id'])) ? $_POST['lead_id'] : $result[0]->lead_id;
            $table_name_comments = $wpdb->prefix . 'leads_document_notes';
            $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $table_name_comments WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ORDER BY id desc ;"));

            $tmpcommentarray = array();
            $html = '';
            $comment_count = count($comment_data);
            foreach ($comment_data as $key => $comment) {
                $user = get_user_by( 'id', $comment->user_id );
                $user->display_name;
                $prepare_html = '';
                if($key > 0)
                {
                    $prepare_html = "<hr style='border-top: 1px solid #000; '>";
                }
                $prepare_html .= '<span class="comment_username" style="font-size:14px;color: grey;" >Username - ' . $user->display_name . '</span>
            <p class="mt-2" style="color:black;word-wrap: break-word;">' . $comment->comments . '</p>
            <span class="comment_date" style="float: left; font-size:12px;color: grey;"> Date & Time - ' . date("m/d/Y H:i", strtotime($comment->update_datetime)) . '</span>
            <br>';
            $tmpcommentarray[] = array($prepare_html);
            }
        }
        $return_array = array(
        "message" => "Document status updated successfully",
        );
        echo json_encode($tmpcommentarray);
        die();
    }
    function approved_rejected_comment_data(){
        global $wpdb;
        $user_id = get_current_user_id();
        $comment = $_POST['comment'];
        $doc_id = $_POST['doc_id'];
        $doc_type_id = $_POST['doc_type_id'];
        $comment_status = $_POST['comment_status'];
        $table_name_document_mapping = $wpdb->prefix . 'leads_document_mapping';
        $result1 = $wpdb->get_results("SELECT * FROM " . $table_name_document_mapping . " WHERE doc_type_id = '$doc_type_id'  order by doc_type_id desc ");
        $label = $result1[0]->doc_label;
        $table_name_uploads = $wpdb->prefix . 'leads_document_upload';
        $result = $wpdb->get_results("SELECT * FROM " . $table_name_uploads . " WHERE id = '$doc_id'  order by id desc ");
        $local_path_filename = $result[0]->local_path_filename;
        $updated_by = $result[0]->updated_by;
        $lead_id = $result[0]->lead_id;
        $user_data = get_userdata($updated_by);
        $user_email = $user_data->user_email;
        $user_name = $user_data->user_login;
        $user_first_name = get_user_meta($updated_by, 'first_name', true);
        $user_last_name = get_user_meta($updated_by, 'last_name', true);
        $user_name = $user_first_name;
        $table_name_comments = $wpdb->prefix . 'leads_document_notes';
        $sql = $wpdb->prepare( "INSERT INTO ".$table_name_comments." (user_id,lead_id,doc_id,doc_type_id,comments) VALUES ( %d,%d, %d,%d, %s)", $user_id, $lead_id ,$doc_id,$doc_type_id,$comment);              $result = $wpdb->query($sql);   
        $subject = 'New comment added for your file submission for '.$label;    
        $body = '<p>Dear '.$user_name.',</p>';
        $body .= '<p>We would like to inform you that a comment has been added to the document you uploaded for the
         Employee Retention Credit filling process. The back office team has provided their feedback and
         comments regarding the document.</p>';    
        $body .= '<p>Document Title: '.$label.'</p>';
        $body .= '<p>Document File Name: '.$local_path_filename.'</p>';
        $body .= '<p>Comment: '.$comment.'</p>';
        $body .= '<p>Comment Date: '.date("m/d/Y").'</p>';
        $body .= '<p>You can also log in to our portal and view the comment section for this document. The comment will
         provide valuable insights and guidance for any necessary revisions or actions required on your part.</p>';
         $body .= '<p>If you have any queries or need assistance in understanding the comment or making the requested
         changes, please feel free to reach out to us.</p>';
        $body .= '<p>Thank you for your attention to this matter.</p>';
        $body .= '<p>Regards,<br>Occams ERC Support Team<br> 212-531-11111<br> <EMAIL></p>';
        $to =  $user_email;
        $headers = array('Content-Type: text/html; charset=UTF-8');
        //  $sent = wp_mail( $to, $subject, $body, $headers );
        $comment_data = $wpdb->get_results($wpdb->prepare('SELECT * FROM '.$table_name_comments.' WHERE doc_type_id = '.$doc_type_id.' AND  doc_id = '.$doc_id.' AND lead_id = '.$lead_id.' ORDER BY id DESC LIMIT 1'));
        if (!empty($comment_data)) {
            $reason = $comment_data[0]->comments;
            $expectedValues = array("uploaded", "approved", "In review","deleted","rejected");
            $words = explode(" ", $comment);
            $lastWord = end($words);
            if (in_array($lastWord, $expectedValues)) {
                $return_array = array('uploadok' => 'false');
                echo json_encode($return_array);
                die();
            } else {
                $return_array = array('uploadok' => 'true','reason' => $comment,'label' => $label);
                echo json_encode($return_array);
                die();
            }
        }
    }
	
	
	
    /**
     * Sending Payment Link 
	 * Applicable only for Product ID 937
	 * Getting email from ooprtunity_id
	 * Using lead_id to pull email.
    */
	 function send_stc_ssf(){
		global $wpdb;
		$opportunity_id= $_POST['opportunity_id'];
		$lead_id= $_POST['lead_id'];
		
		
		$table_contacts = $wpdb->prefix . 'op_contacts';
		$table_email_contact = $wpdb->prefix . 'op_email_contact';
		$table_emails = $wpdb->prefix . 'op_emails';
		$table_opportunities = $wpdb->prefix . 'opportunities';
        $opportunity_product_table = $wpdb->prefix.'opportunity_products';
        $add_table = $wpdb->prefix.'erc_iris_leads_additional_info';
		// Query to retrieve the contact details
		$query = $wpdb->prepare("
			SELECT 
				c.first_name, 
				c.last_name, 
				c.trash, 
				e.email 
			FROM 
				$table_contacts AS c 
			INNER JOIN 
				$table_email_contact AS ec 
			ON 
				c.id = ec.contact_id 
			INNER JOIN 
				$table_emails AS e 
			ON 
				ec.email_id = e.id 
			WHERE 
				c.id = (
					SELECT ContactID 
					FROM $table_opportunities 
					WHERE OpportunityID = %d
				)
		", $opportunity_id);

		
		$contact_details = $wpdb->get_row($query);

		
		if ($contact_details) {
			$first_name= $contact_details->first_name;
			$last_name= $contact_details->last_name;
			$email= $contact_details->email;
			$trash= $contact_details->trash;
			if($trash !=1)
			{
				// $dataArray1 = array(
				// 	'id'  => 97,
				// 	'dynamic' => true,
				// 	'to' => $email,
				// 	'from'=>'<EMAIL>',
				// 	'password'=>'Tal40369',
				// 	'client_name'=> $first_name." ".$last_name,
				// 	'payment_link'=>'https://portal.occamsadvisory.com/payment/?data='.base64_encode($lead_id)
			                         
				// );
                
                $add_data = $wpdb->get_row("SELECT affiliate_user_id,employee_id,erc_reffrer_id FROM $add_table WHERE lead_id='$lead_id'");
                $refer_email='';
                $refer_name='';
                if($add_data){
                    $erc_reffrer_id = $add_data->erc_reffrer_id;
                    $affiliate_user_id = $add_data->affiliate_user_id;
                    $employee_id = $add_data->employee_id;
                        $refrer_id = 0;
                    if(!empty($erc_reffrer_id)){
                        $refrer_id = $erc_reffrer_id;
                    }else if(!empty($affiliate_user_id)){
                        $refrer_id = $affiliate_user_id;
                    }else if(!empty($employee_id)){
                        $refrer_id = $employee_id;
                    }
                    
                    if(!empty($refrer_id)){
                        $userdata = get_user_by('id', $refrer_id);
                            if (isset($userdata->data)) {
                             $refer_name = $userdata->data->display_name;
                             $refer_email = $userdata->data->user_email;
                            }
                    }
                }

                $curl = curl_init();
    
                curl_setopt_array($curl, array(
                    CURLOPT_URL => get_site_url() . '/wp-json/v1/generate-auth-key-by-lead-id',
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_ENCODING => '',
                    CURLOPT_MAXREDIRS => 10,
                    CURLOPT_TIMEOUT => 0,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                    CURLOPT_CUSTOMREQUEST => 'POST',
                    CURLOPT_POSTFIELDS => array('lead_id' => $lead_id),
                    CURLOPT_HTTPHEADER => array(
                        'Cookie: PHPSESSID=taamorg590sbpoh6l7833p34ql'
                    ),
                ));
    
                $response = curl_exec($curl);
                curl_close($curl);
                $response = json_decode($response);
                $auth_id = $response->data->payment_auth;
			         $dataArray1 = array(
                        'id'  => 128, 
                        'dynamic' => true,
                        'to' => $email,
                        'cc' => $refer_email,
                        'from'=>'<EMAIL>',
                        'password'=>'Tal40369',
                        'customer_first_name'=> $first_name." ".$last_name,
                        'referring_user_name'=>$refer_name,
                        'form_link'=>'https://play.occamsadvisory.com/eligibility-questions/?data='.$auth_id,
                    );

				send_email_template($dataArray1);
                
				$return_data= array('response'=>'success','lead_id'=>$lead_id,'message'=>'SSF Link Sent Successfully.');
                $update_payment_time = $wpdb->query("UPDATE $table_opportunities SET last_payment_sent='".date('Y-m-d h:i:s',time())."' WHERE OpportunityID=$opportunity_id");
/*
                $milestone_data['milestone_id'] = 122;
                $milestone_data['milestone_stage_id'] = 244;
                $milestone_data['milestone_deletedat'] = NULL;
                $milestone_data['milestone_deletedby'] = NULL;

                $result = $wpdb->update($opportunity_product_table, $milestone_data, array('opportunity_id' => $opportunity_id));
  */

                        $curl = curl_init();
                        $edit_url =  site_url().'/wp-json/productsplugin/v1/edit-opportunity-optional-field';

                        $edit_data = array();
                        $edit_data['OpportunityID'] = $opportunity_id;
                        $edit_data['Milestone'] = 122;
                        $edit_data['MilestoneStage'] = 244;

                        curl_setopt_array($curl, array(
                          CURLOPT_URL => $edit_url,
                          CURLOPT_RETURNTRANSFER => true,
                          CURLOPT_ENCODING => '',
                          CURLOPT_MAXREDIRS => 10,
                          CURLOPT_TIMEOUT => 0,
                          CURLOPT_FOLLOWLOCATION => true,
                          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                          CURLOPT_CUSTOMREQUEST => 'POST',
                          CURLOPT_POSTFIELDS => $edit_data,
                          CURLOPT_HTTPHEADER => array(
                            'Cookie: PHPSESSID=17camucl5vr5heetusfckoh1fd'
                          ),
                        ));

                        $response = curl_exec($curl);
                        curl_close($curl);

				
                echo json_encode($return_data);
				die();

				
			}
			else
			{
				$return_data= array('response'=>'failed','lead_id'=>$lead_id,'message'=>'Contact marked as Deleted');
				echo json_encode($return_data);
				die();
			}
		} else {
				$return_data= array('response'=>'failed','lead_id'=>$lead_id,'message'=>'Contact not found.');
				echo json_encode($return_data);
				die();
		}
		 
	 }

     

     /**
     * Resending Afreement Link 
     * Applicable only for Product ID 937
     * Getting email from ooprtunity_id
     * Using lead_id to pull email.
    */
     function send_stc_agreement_link(){
        global $wpdb;
        $opportunity_id= $_POST['opportunity_id'];
        $lead_id= $_POST['lead_id'];

        $curl = curl_init();

        curl_setopt_array($curl, array(
          CURLOPT_URL => get_site_url().'/wp-json/v1/generate-auth-key-by-lead-id',
          CURLOPT_RETURNTRANSFER => true,
          CURLOPT_ENCODING => '',
          CURLOPT_MAXREDIRS => 10,
          CURLOPT_TIMEOUT => 0,
          CURLOPT_FOLLOWLOCATION => true,
          CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
          CURLOPT_CUSTOMREQUEST => 'POST',
          CURLOPT_POSTFIELDS => array('lead_id' => $lead_id),
          CURLOPT_HTTPHEADER => array(
            'Cookie: PHPSESSID=taamorg590sbpoh6l7833p34ql'
          ),
        ));

        $response = curl_exec($curl);

        curl_close($curl);
        $response = json_decode($response);

        $auth_id = $response->data->payment_auth;
        
        
        $table_contacts = $wpdb->prefix . 'op_contacts';
        $table_email_contact = $wpdb->prefix . 'op_email_contact';
        $table_emails = $wpdb->prefix . 'op_emails';
        $table_opportunities = $wpdb->prefix . 'opportunities';

        // Query to retrieve the contact details
        $query = $wpdb->prepare("
            SELECT 
                c.first_name, 
                c.last_name, 
                c.trash, 
                e.email 
            FROM 
                $table_contacts AS c 
            INNER JOIN 
                $table_email_contact AS ec 
            ON 
                c.id = ec.contact_id 
            INNER JOIN 
                $table_emails AS e 
            ON 
                ec.email_id = e.id 
            WHERE 
                c.id = (
                    SELECT ContactID 
                    FROM $table_opportunities 
                    WHERE OpportunityID = %d
                )
        ", $opportunity_id);

        
        $contact_details = $wpdb->get_row($query);

        
        if ($contact_details) {
            $first_name= $contact_details->first_name;
            $last_name= $contact_details->last_name;
            $email= $contact_details->email;
            //$email= '<EMAIL>';
            $trash= $contact_details->trash;
            if($trash !=1)
            {
                $dataArray1 = array(
                    'id'  => 101,
                    'dynamic' => true,
                    'to' => $email,
                    'from'=>'<EMAIL>',
                    'password'=>'Tal40369',
                    'client_name'=> $first_name." ".$last_name,
                    'business_name'=> $first_name." ".$last_name,
                    'payment_link'=>'https://portal.occamsadvisory.com/agreement/?data='.$auth_id
                                     
                );
            
                send_email_template($dataArray1);
                $return_data= array('response'=>'success','lead_id'=>$lead_id,'message'=>'Agreement Link Sent');
                $update_agreement_time = $wpdb->query("UPDATE $table_opportunities SET last_agreement_sent='".date('Y-m-d h:i:s',time())."' WHERE OpportunityID=$opportunity_id");
                echo json_encode($return_data);
                die();

                
            }
            else
            {
                $return_data= array('response'=>'failed','lead_id'=>$lead_id,'message'=>'Contact marked as Deleted');
                echo json_encode($return_data);
                die();
            }
        } else {
                $return_data= array('response'=>'failed','lead_id'=>$lead_id,'message'=>'Contact not found.');
                echo json_encode($return_data);
                die();
        }
         
     }

     function update_milestone_when_opportunity_won(){
        global $wpdb;
        $opportunity_id = $_POST['opportunity_id'];
        $product_id = $_POST['product_id'];

        if($product_id==936){ //for TA
            $milestone_id = 123;
            $milestone_stage_id = 258;
            // a. If Agreement is signed and payment received - Documents pending  -- 259
            // b. If agreement is signed and payment not received - Agreement signed -- 258
        
        }else if($product_id==937){ // for stc
            $milestone_id = 115;
            $milestone_stage_id = 247;
            //a. If Agreement is signed and payment received - Documents pending  -- 247
            // b. If agreement is signed and payment not received - Agreement signed -- 216
        }    
        $opportunity_pro_table = $wpdb->prefix.'opportunity_products';

        $update = $wpdb->query("UPDATE $opportunity_pro_table SET milestone_id=$milestone_id,milestone_stage_id=$milestone_stage_id WHERE opportunity_id=$opportunity_id");
        echo $update;    
        die();
     }



     function project_doc_comment_data()
    {
        global $wpdb;

        $user_id = get_current_user_id();
        $comment = $_POST['comment'];
        $doc_id = $_POST['doc_id'];
        $doc_type_id = $_POST['doc_type_id'];
        $label = $_POST['label'];

        $table_name_uploads = $wpdb->prefix . 'leads_document_upload';

        $result = $wpdb->get_results("SELECT * FROM " . $table_name_uploads . " WHERE id = '$doc_id'  order by id desc ");

        $local_path_filename = $result[0]->local_path_filename;
        $updated_by = $result[0]->updated_by;
        $lead_id = $result[0]->lead_id;

        if (empty($local_path_filename)) {
            {
                echo 'NA';
            }
        }


        $user_data = get_userdata($updated_by);
        $user_email = $user_data->user_email;
        $user_name = $user_data->user_login;
        $user_first_name = get_user_meta($updated_by, 'first_name', true);
        $user_last_name = get_user_meta($updated_by, 'last_name', true);
        $user_name = $user_first_name;

        $table_name_comments = $wpdb->prefix . 'leads_document_notes';

        $sql = $wpdb->prepare("INSERT INTO " . $table_name_comments . " (user_id,lead_id,doc_id,doc_type_id,comments) VALUES ( %d,%d, %d,%d, %s)", $user_id, $lead_id, $doc_id, $doc_type_id, $comment);

        $result = $wpdb->query($sql);


        $userdata = get_user_by('id', get_current_user_id());
        $user_roles = $userdata->roles;

        if (in_array("lead", $user_roles) || in_array("lead_associate", $user_roles)) {

            $doc_business_info = 'eccom_erc_business_info';

            $business_legal_name = $wpdb->get_var("SELECT business_legal_name FROM " . $doc_business_info . " WHERE lead_id = '$lead_id'  order by id desc ");


            $subject = 'New comment added by ' . $business_legal_name . ' your file submission for ' . $label;

            $body = '<p>Hello,</p>';

            $body .= '<p>A comment has been added by ' . $business_legal_name . ' for ' . $label . ' as below, kindly take the required action.</p>';

            $body .= '<p>Document Name: ' . $business_legal_name . '</p>';

            $body .= '<p>Document Title: ' . $label . '</p>';

            $body .= '<p>Document File Name: ' . $local_path_filename . '</p>';

            $body .= '<p>Comment: ' . $user_name . '</p>';

            $body .= '<p>Comment Date: ' . date("m/d/Y") . '</p>';

            $to = '<EMAIL>';


        } else if (in_array("echeck_admin", $user_roles)) {


            $subject = 'New comment added for your file submission for ' . $label;

            $body = '<p>Dear ' . $user_name . ',</p>';
            $body .= '<p>We would like to inform you that a comment has been added to the document you uploaded for the
     Employee Retention Credit filling process. The back office team has provided their feedback and
     comments regarding the document.</p>';

            $body .= '<p>Document Title: ' . $label . '</p>';

            $body .= '<p>Document File Name: ' . $local_path_filename . '</p>';

            $body .= '<p>Comment: ' . $comment . '</p>';

            $body .= '<p>Comment Date: ' . date("m/d/Y") . '</p>';

            $body .= '<p>You can also log in to our portal and view the comment section for this document. The comment will
     provide valuable insights and guidance for any necessary revisions or actions required on your part.</p>';

            $body .= '<p>If you have any queries or need assistance in understanding the comment or making the requested
     changes, please feel free to reach out to us.</p>';

            $body .= '<p>Thank you for your attention to this matter.</p>';

            $body .= '<p>Regards,<br>Occams ERC Support Team<br> 212-531-11111<br> <EMAIL></p>';


            $to = $user_email;

        }

        $headers = array('Content-Type: text/html; charset=UTF-8');
        //commented this for some time
        //  $sent = wp_mail( $to, $subject, $body, $headers );


        echo 1;
        wp_die();


    }

    function send_ta_agreement_action(){
        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);    

        global $wpdb;
        $lead_id = $_POST['lead_id'];
        $opportunity_id = $_POST['opportunity_id'];
        
        $opportunity_product_table = $wpdb->prefix.'opportunity_products';
        $opportunity_table = $wpdb->prefix.'opportunities';
        $bus_table = $wpdb->prefix . 'erc_business_info';
        
        //  ----------- Get lead all opportinity func start ----------
            $opportunities = $wpdb->get_results("SELECT opportunityID FROM $opportunity_table WHERE leadID=$lead_id AND $opportunity_table.DeletedAt IS NULL"); 

            if($opportunities){
               $opportunity_agreement_signed_date = array(); 
                $i = 0;
               foreach ($opportunities as $key => $value) {
                $opp_id = $value->opportunityID;
                if($opportunity_id != $opp_id){ // check current opportunity id
                    $notes = $wpdb->get_row("SELECT id,opportunity_id, created, note FROM eccom_erc_opportunity_notes WHERE 
                        opportunity_id = $opp_id AND note LIKE '%agreement signed%' ORDER BY id DESC LIMIT 1 ");
                    
                    if($notes){
                        $opportunity_agreement_signed_date[$i]['signed_date'] = $notes->created;
                        $i++;
                    }
                } 
                } 
                
                if(empty($opportunity_agreement_signed_date)){
                       $send_agreement = 1; 
                }else{
                    // $arr = usort($opportunity_agreement_signed_date, 'sortByIDDesc');
                    usort($opportunity_agreement_signed_date, function($a, $b) {
                        return strtotime($b['signed_date']) - strtotime($a['signed_date']);
                    });
                    $send_agreement = 0;
                    if($opportunity_agreement_signed_date[0]){
                        $signed_date = $opportunity_agreement_signed_date[0]['signed_date'];
                        $return_date = array('send_agreement'=>0,'signed_date'=>$signed_date);
                        echo json_encode($return_date);
                        die();
                        // echo "Agreement is not in the system; can't send it at the moment.";
                    }
                }
            }else{
                    // echo "No any opportunities signed of this lead";
                    $send_agreement = 1;
            }
        
        // -----------  check lead opportunity -------------

    if($send_agreement == 1){

        $bus_data = $wpdb->get_results("SELECT business_legal_name,authorized_signatory_name,business_email FROM $bus_table WHERE lead_id='".$lead_id."'");
            if(count($bus_data)!=0){
                $business_legal_name = $bus_data[0]->business_legal_name;
                $authorized_signatory_name = $bus_data[0]->authorized_signatory_name;
                $business_email = $bus_data[0]->business_email;
                if(empty($authorized_signatory_name)){
                    $authorized_signatory_name = $business_legal_name;
                }
                $authorized_signatory_name = ucfirst($authorized_signatory_name);
            }

            $lead_ids = base64_encode($lead_id);    
            $link = "https://portal.occamsadvisory.com/tax-amendment-agreement/?data=".$lead_ids;
            
            // $url = $_SERVER['HTTP_HOST'];
            
            // if($url == 'play.occamsadvisory.com'){
            //     $template_id = 10;
            //     $milestone_id = 134;
            //     $stage_id = 310;
            // }else if($url == 'portal.occamsadvisory.com'){
                $template_id = 98;
                $milestone_id = 126;
                $stage_id = 311;
            // }
            try{
                $ir_dynamic_var = array(
                'id'=>$template_id,
                'to'=>$business_email,
                'customer_name'=>$authorized_signatory_name,
                'click_here_link'=>$link,
                'dynamic'=>true,
                'from'=> '<EMAIL>',
                'password'=> 'Jah09143'
                );
                $email_sent_response = send_email_template($ir_dynamic_var);
            }catch(EXCEPTION $e){

            }

           $last_agreement_sent = date("Y-m-d H:i:s");
           $post_data['opportunityClosure'] = 'send_agreement';
           $post_data['last_agreement_sent'] = $last_agreement_sent;
           $post_data['ModifiedAt'] = date('Y-m-d H:i:s',time());
           $post_data['ModifiedBy'] = get_current_user_id();
           $result = $wpdb->update($opportunity_table, $post_data, array('OpportunityID' => $opportunity_id));
            
            $user_data = get_userdata(get_current_user_id());
            $user_name = $user_data->user_login;
            /*
            $milestone_data['milestone_id'] = $milestone_id;
            $milestone_data['milestone_stage_id'] = $stage_id;
            $milestone_data['milestone_deletedat'] = NULL;
            $milestone_data['milestone_deletedby'] = NULL;

            $result = $wpdb->update($opportunity_product_table, $milestone_data, array('opportunity_id' => $opportunity_id));
            */

                    $curl = curl_init();
                    $edit_url =  site_url().'/wp-json/productsplugin/v1/edit-opportunity-optional-field';

                    $edit_data = array();
                    $edit_data['OpportunityID'] = $opportunity_id;
                    $edit_data['Milestone'] = $milestone_id;
                    $edit_data['MilestoneStage'] = $stage_id;

                    curl_setopt_array($curl, array(
                      CURLOPT_URL => $edit_url,
                      CURLOPT_RETURNTRANSFER => true,
                      CURLOPT_ENCODING => '',
                      CURLOPT_MAXREDIRS => 10,
                      CURLOPT_TIMEOUT => 0,
                      CURLOPT_FOLLOWLOCATION => true,
                      CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                      CURLOPT_CUSTOMREQUEST => 'POST',
                      CURLOPT_POSTFIELDS => $edit_data,
                      CURLOPT_HTTPHEADER => array(
                        'Cookie: PHPSESSID=17camucl5vr5heetusfckoh1fd'
                      ),
                    ));

                    $response = curl_exec($curl);
                    curl_close($curl);

            return true;
        } // send agreement            
    }

    function sortByIDDesc($a, $b) {
        return strtotime($b['signed_date']) - strtotime($a['signed_date']);
    }

    /**
     * Product Qb Service Submit Handler.
     */
    function product_qb_service() {
        $product_manager = new CRM_ERP_Product_Manager();
        $data = $_POST;
        unset($data['action']);
        $current_user_id = get_current_user_id();
        if($data['product_service_head_id'] == 0){
            unset($data['product_service_head_id']);
            $data['CreatedAt'] = date('Y-m-d H:i:s');
            $data['CreatedBy'] = $current_user_id;
            $product_data = $product_manager->create_product_service_head(json_encode($data));
        }else{
            $data['ModifiedAt'] = date('Y-m-d H:i:s');
            $data['ModifiedBy'] = $current_user_id;
            $product_data = $product_manager->edit_product_service_head(json_encode($data),$data['product_service_head_id']);
        }
        if($product_data){
            wp_send_json_success($product_data); // Send back a JSON response
            wp_die(); // All AJAX handlers should die when finished
        }
    }
    
    
    
    
    function process_data($type, $title, $time_of_days_array, $no_of_days_array, $lead_id, $table_name, $wpdb, $data, $no_of_availed_days_array) {

        
        
        $table_name = $wpdb->prefix . 'leads_impacted_days';
        $table_name_dates = $wpdb->prefix . 'leads_impacted_dates';
        $impacted_days_table = $wpdb->prefix . 'impacted_date_ranges';
    //    echo "process_data";
    //
        //print_r($data);

        foreach ($time_of_days_array as $key => $time_of_days) {
            if (!empty($no_of_days_array[$key])) {
                $no_of_days = $no_of_days_array[$key];
            } else {
                $no_of_days = "";
            }

            if (!empty($no_of_availed_days_array[$key])) {
                $no_of_availed_days = $no_of_availed_days_array[$key];
            } else {
                $no_of_availed_days = "";
            }
            //$no_of_availed_days = $no_of_availed_days_array[$key];
            // $unemp_benefit_claimed='';
            if (isset($data[$time_of_days . '_unemployment_claimed'])) {
                $unemp_benefit_claimed = $data[$time_of_days . '_unemployment_claimed'];
            }

            // Check and set undertaking
             $undertaking_key = 'undertaking_' . strtolower($time_of_days);
             $undertaking = isset($data[$undertaking_key]) ? 1 : 0;


            // Check if the record already exists
            $existing_row = $wpdb->get_row(
                    $wpdb->prepare("SELECT * FROM $table_name WHERE type = %s AND time_of_days = %s AND lead_id = %s", $type, $time_of_days, $lead_id)
            );

            // Prepare data for insertion or update
            $idata = array(
                'type' => $type,
                'lead_id' => $lead_id,
                'title' => $title[$key],
                'time_of_days' => $time_of_days,
                'no_of_days' => $no_of_days,
                'no_of_availed_days' => $no_of_availed_days,
                'unemp_benefit_claimed' => $unemp_benefit_claimed,
                'updated_datetime' => date('Y-m-d H:i:s'), // Add current timestamp
                'undertaking' => $undertaking
            );

    //                echo "process_data <pre>";
                   //print_r($idata);
    //                echo '</pre>';
            // Update or insert the record
            if ($existing_row) {
                $wpdb->update($table_name, array('no_of_days' => $no_of_days, 'unemp_benefit_claimed' => $unemp_benefit_claimed, 'no_of_availed_days' => $no_of_availed_days, 'undertaking' => $undertaking), array('id' => $existing_row->id));
            } else {
                $wpdb->insert($table_name, $idata);
            }
        }
    }

    function getDatesInRange($start_date, $end_date) {
    $start = new DateTime($start_date);
    $end = new DateTime($end_date);
    $interval = new DateInterval('P1D');
    $dateRange = new DatePeriod($start, $interval, $end->modify('+1 day'));

    $dates = [];
    foreach ($dateRange as $date) {
        $dates[] = $date->format('m/d/Y');
    }
    return $dates;
    }


    function get_dates_from_range($start, $end) {
        $start_date = DateTime::createFromFormat('m/d/Y', $start);
        $end_date = DateTime::createFromFormat('m/d/Y', $end);

        // Check if the date conversion was successful
        if (!$start_date || !$end_date) {
            return [];
        }

        $dates = [];

        while ($start_date <= $end_date) {
            $dates[] = $start_date->format('m/d/Y');
            $start_date->modify('+1 day');
        }

        return $dates;
    }


    function process_date_ranges($lead_id, $table_name_dates, $posted_data, $wpdb) {
        $mappings = [
            'selectedDateRangeInput_0_' => '2020_Q2_Q3_Q4_',
            'selectedDateRangeInput_1_' => '2021_Q1_',
            'selectedDateRangeInput_2_' => '2021_Q2_Q3_',
        ];

        foreach ($mappings as $input_prefix => $field_prefix) {
            $date_index = 1;

            // Loop to handle up to 10 date range inputs
            for ($i = 0; $i < 10; $i++) {
                $input_key = $input_prefix . $i;

                // Check if the input key exists, if not, set value to empty
                $value = isset($posted_data[$input_key]) ? $posted_data[$input_key] : '';

                // Split the value into start and end dates if not empty
                if (!empty($value)) {
                    list($start_date, $end_date) = explode(' - ', $value);
                    $dates = $this->get_dates_from_range($start_date, $end_date);
                } else {
                    $dates = []; // Handle empty input
                }

                // If dates are empty, still need to handle updating blank values
                if (empty($dates)) {
                    $field_key = $field_prefix . $date_index;
                    $un_benefit = isset($posted_data[$input_key . '_un_benefit']) ? $posted_data[$input_key . '_un_benefit'] : '';

                    $data = array(
                        'type' => 'date_tab',
                        'lead_id' => $lead_id,
                        'field_title' => $field_key,
                        'field_value' => '', // Insert/Update with blank value
                        'unemp_benefit_claimed' => $un_benefit,
                        'updated_datetime' => date('Y-m-d H:i:s') // Add current timestamp
                    );

    //                echo 'process date range <pre>';
    //                print_r($data);
    //                echo '</pre>';
                    // Check if an existing record exists
                    $existing_row = $wpdb->get_row(
                        $wpdb->prepare("SELECT * FROM $table_name_dates WHERE lead_id = %s AND field_title = %s", $lead_id, $field_key)
                    );

                    if ($existing_row) {
                        // Update the existing record
                        $wpdb->update($table_name_dates, $data, array('id' => $existing_row->id));
                    } else {
                        // Insert a new record
                        $wpdb->insert($table_name_dates, $data);
                    }

                    $date_index++;
                } else {
                    // Process non-empty dates
                    foreach ($dates as $date) {
                        $field_key = $field_prefix . $date_index;
                        $un_benefit = isset($posted_data[$input_key . '_un_benefit']) ? $posted_data[$input_key . '_un_benefit'] : '';

                        $data = array(
                            'type' => 'date_tab',
                            'lead_id' => $lead_id,
                            'field_title' => $field_key,
                            'field_value' => $date,
                            'unemp_benefit_claimed' => $un_benefit,
                            'updated_datetime' => date('Y-m-d H:i:s') // Add current timestamp
                        );

                        // Check if an existing record exists
                        $existing_row = $wpdb->get_row(
                            $wpdb->prepare("SELECT * FROM $table_name_dates WHERE lead_id = %s AND field_title = %s", $lead_id, $field_key)
                        );

                        if ($existing_row) {
                            // Update the existing record
                            $wpdb->update($table_name_dates, $data, array('id' => $existing_row->id));
                        } else {
                            // Insert a new record
                            $wpdb->insert($table_name_dates, $data);
                        }

                        $date_index++;
                    }
                }
            }
        }
    }

    function process_impacted_data_aff() {
        global $wpdb;
        
        parse_str($_POST['formData'], $posted_data); // Parse the serialized form data
        
        $lead_id = isset($posted_data['lead_id']) ? intval($posted_data['lead_id']) : 0;
        
        $table_name = $wpdb->prefix . 'leads_impacted_days';
        $table_name_dates = $wpdb->prefix . 'leads_impacted_dates';

        if ($lead_id > 0) {
            $this->process_data('self', $posted_data['self_title'], $posted_data['self_time_of_days'], $posted_data['self_no_of_days'], $lead_id, $table_name, $wpdb, $posted_data, $posted_data['self_no_of_availed_days']);
           $this->process_data('others', $posted_data['other_title'], $posted_data['other_time_of_days'], $posted_data['other_no_of_days'], $lead_id, $table_name, $wpdb, $posted_data, $posted_data['other_no_of_availed_days']);

            for ($i = 1; $i <= 10; $i++) {
                $key = "2020_Q2_Q3_Q4_$i";
                if (!isset($posted_data[$key])) {
                    $posted_data[$key] = '';
                }
            }

            for ($i = 1; $i <= 10; $i++) {
                $key = "2021_Q1_$i";
                if (!isset($posted_data[$key])) {
                    $posted_data[$key] = '';
                }
            }

            for ($i = 1; $i <= 10; $i++) {
                $key = "2021_Q2_Q3_$i";
                if (!isset($posted_data[$key])) {
                    $posted_data[$key] = '';
                }
            }

            $this->process_date_ranges($lead_id, $table_name_dates, $posted_data, $wpdb);

            $wpdb->delete("{$wpdb->prefix}impacted_date_ranges", array('lead_id' => $lead_id));

            foreach ($posted_data as $key => $value) {
                if (strpos($key, 'selectedDateRangeInput') === 0 && !empty($value)) {
                    preg_match('/selectedDateRangeInput_(\d+)_(\d+)/', $key, $matches);
                    $doc_key = $matches[1];
                    $date_range_index = $matches[2];
                    list($start_date, $end_date) = explode(' - ', $value);

                    $existing_entry = $wpdb->get_row(
                        $wpdb->prepare(
                            "SELECT * FROM eccom_impacted_date_ranges WHERE lead_id = %d AND doc_key = %s AND date_range_index = %d",
                            $lead_id, $doc_key, $date_range_index
                        )
                    );

                    if ($existing_entry) {
                        $wpdb->update(
                            'eccom_impacted_date_ranges',
                            array(
                                'start_date' => $start_date,
                                'end_date' => $end_date,
                                'updated_date' => current_time('mysql')
                            ),
                            array(
                                'lead_id' => $lead_id,
                                'doc_key' => $doc_key,
                                'date_range_index' => $date_range_index
                            )
                        );
                    } else {
                        $wpdb->insert(
                            'eccom_impacted_date_ranges',
                            array(
                                'lead_id' => $lead_id,
                                'doc_key' => $doc_key,
                                'date_range_index' => $date_range_index,
                                'start_date' => $start_date,
                                'end_date' => $end_date,
                                'created_date' => current_time('mysql'),
                                'updated_date' => current_time('mysql')
                            )
                        );
                    }
                }
            }
            

            wp_send_json_success('Data saved successfully.');
        } else {
            wp_send_json_error('Invalid lead ID');
        }

        wp_die();
    }
    
    
    
    
    // Register the AJAX action
  
    function handle_selfdocumentform_submission_aff() {
        global $wpdb;
        $table_name = $wpdb->prefix . 'leads_stc_documents_additional_info';

        // Parse the form data
        parse_str($_POST['data'], $requestArr);

  
            $lead_id = intval($requestArr['lead_id']); // Assuming lead_id is in the form data

            if ($lead_id > 0) {
                unset($requestArr['selfdocumentsubmit']);
                unset($requestArr['taxnow_signup_stc_section_required']);

                foreach ($requestArr as $key => $isapplicable) {
                    if (!empty($isapplicable)) {
                        // Check if the record already exists
                        $existing_row = $wpdb->get_row(
                            $wpdb->prepare("SELECT * FROM $table_name WHERE doc_key = %s AND lead_id = %s", $key, $lead_id)
                        );

                        // Update or insert the record
                        if ($existing_row) {
                            $wpdb->update($table_name, array('is_applicable' => $isapplicable), array('id' => $existing_row->id));
                        } else {
                            // Prepare data for insertion or update
                            $data = array(
                                'type' => 'is_appicable',
                                'lead_id' => $lead_id,
                                'doc_key' => $key,
                                'is_applicable' => $isapplicable,
                                'created_at' => current_time('mysql'),
                            );
                            $wpdb->insert($table_name, $data);
                        }
                    }
                }
                
                wp_send_json_success('Files Uploaded Successfully.');
            }else{
                wp_send_json_error('Invalid lead ID.');
            }
      

        
        wp_die();
    }
	
	/**
	 * Fetch invoices based on lead_id and product_id.
	 *
	 * @param string $lead_id_project The lead ID.
	 * @param string $product_id_project The product ID.
	 * @return array An array of invoices.
	*/
/*  	public function get_invoices_by_lead_and_product($lead_id_project, $product_id_project) {
		global $wpdb;

		if ($lead_id_project !== "" && $product_id_project !== "") {
			$invoices = $wpdb->get_results(
				$wpdb->prepare(
					"SELECT * FROM {$wpdb->prefix}invoices 
					WHERE lead_id = %s AND parent_product = %s",
					$lead_id_project, 
					$product_id_project
				),
				ARRAY_A
			);

			return $invoices;
		}

		return [];
	}  */
	
	public function get_invoices_by_lead_and_product($lead_id_project, $product_id_project) {

         ini_set('display_errors', 1);
         ini_set('display_startup_errors', 1);
         error_reporting(E_ALL);



		global $wpdb;

		if ($lead_id_project !== "" && $product_id_project !== "") {
			// $query = "
			// 	SELECT 
            //         inv.*, 
            //         GROUP_CONCAT(prod.product_name SEPARATOR ', ') as product_names,
            //         COUNT(payments.payment_id) as payment_count,
            //         payments.payment_mode as payments_mode,
            //         payments.payment_cleared_date as payments_cleared_date,
            //         payments.payment_date as payments_date,
            //         payments.received_amt as received_amts
			// 	FROM {$wpdb->prefix}invoices inv
			// 	LEFT JOIN {$wpdb->prefix}invoices_products prod
			// 	    ON inv.customer_invoice_no = prod.invoice_number
            //     LEFT JOIN 
            //         {$wpdb->prefix}invoice_payments payments
            //         ON inv.id = payments.invoice_id
			// 	WHERE inv.lead_id = %s AND inv.parent_product = %s AND inv.status != 13
			// 	GROUP BY inv.id ORDER BY inv.id DESC "; // Changed to inv.id or unique identifier

            /* $query = "
				SELECT 
                    inv.*, 
                    GROUP_CONCAT(prod.product_name SEPARATOR ', ') as product_names,
                    COUNT(latest_payment.payment_id) as payment_count,
                    latest_payment.payment_mode as payments_mode,
                    latest_payment.payment_cleared_date as payments_cleared_date,
                    latest_payment.payment_date as payments_date,
                    latest_payment.received_amt as received_amts,
                    inv_token.token
				FROM {$wpdb->prefix}invoices inv
				LEFT JOIN {$wpdb->prefix}invoices_products prod
				    ON inv.customer_invoice_no = prod.invoice_number
                LEFT JOIN 
                    (
                        SELECT 
                            p1.*
                        FROM 
                            {$wpdb->prefix}invoice_payments p1
                        INNER JOIN 
                            (
                                SELECT 
                                    invoice_id, 
                                    MAX(payment_date) as latest_payment_date
                                FROM 
                                    {$wpdb->prefix}invoice_payments
                                GROUP BY 
                                    invoice_id
                            ) p2 
                            ON p1.invoice_id = p2.invoice_id 
                            AND p1.payment_date = p2.latest_payment_date
                    ) latest_payment
                ON 
                    inv.id = latest_payment.invoice_id
                LEFT JOIN {$wpdb->prefix}invoice_token inv_token 
                ON inv.customer_invoice_no = inv_token.invoice_id
				WHERE inv.lead_id = %s AND inv.parent_product = %s AND inv.status != 13
				GROUP BY inv.id ORDER BY inv.id DESC "; // Changed to inv.id or unique identifier

			$invoices = $wpdb->get_results(
				$wpdb->prepare($query, $lead_id_project, $product_id_project),
				ARRAY_A
			); */
			
			$query = "
			SELECT 
				inv.*, 
				
				CASE 
					WHEN prod.product_name <> '' THEN 
						GROUP_CONCAT(DISTINCT prod.product_name ORDER BY prod.product_name SEPARATOR ', ') 
					ELSE ''
				END as product_names,
				
				COUNT(latest_payment.payment_id) as payment_count,
				latest_payment.payment_mode as payments_mode,
				latest_payment.payment_cleared_date as payments_cleared_date,
				latest_payment.payment_date as payments_date,
				latest_payment.received_amt as received_amts,
				inv_token.token
			FROM {$wpdb->prefix}invoices inv
			LEFT JOIN {$wpdb->prefix}invoices_products prod
				ON inv.customer_invoice_no = prod.invoice_number
			LEFT JOIN 
				(
					SELECT 
						p1.*
					FROM 
						{$wpdb->prefix}invoice_payments p1
					INNER JOIN 
						(
							SELECT 
								invoice_id, 
								MAX(payment_date) as latest_payment_date
							FROM 
								{$wpdb->prefix}invoice_payments
							GROUP BY 
								invoice_id
						) p2 
						ON p1.invoice_id = p2.invoice_id 
						AND p1.payment_date = p2.latest_payment_date
				) latest_payment
			ON inv.id = latest_payment.invoice_id
			LEFT JOIN {$wpdb->prefix}invoice_token inv_token 
			ON inv.customer_invoice_no = inv_token.invoice_id
			WHERE inv.lead_id = %s 
			AND inv.parent_product = %s 
			AND inv.status != 13
			GROUP BY inv.id 
			ORDER BY inv.id DESC
		";

		$invoices = $wpdb->get_results(
			$wpdb->prepare($query, $lead_id_project, $product_id_project),
			ARRAY_A
		);
		
		// echo '<pre>';
		// print_r($invoices);
		// echo '</pre>';


            // echo "<pre>";
            // print_r($invoices);
            // echo "</pre>";

			return $invoices;
		}

		return [];
	}	

	public function display_invoices_data($invoices) {
		global $wpdb;
		// Enqueue SweetAlert2 assets
		wp_enqueue_style('sweetalert2-css', 'https://cdn.jsdelivr.net/npm/sweetalert2@11.0.19/dist/sweetalert2.min.css');
		wp_enqueue_script('sweetalert2-js', 'https://cdn.jsdelivr.net/npm/sweetalert2@11.0.19/dist/sweetalert2.min.js', [], null, true);

		//echo '<pre>'; print_r($invoices); echo '</pre>';
		if (!empty($invoices)) {
			foreach ($invoices as $invoice) {
				$invoice_id = $invoice['id'];
				
				$payment_result = $wpdb->get_results("SELECT * from {$wpdb->prefix}invoice_payments where invoice_id =".$invoice_id."");
				if(isset($payment_result)){
					$payment_count_new = count($payment_result);
				}
				
				$updatedBy_result = $wpdb->get_row("SELECT createdBy from {$wpdb->prefix}invoice_audit_logs where action='update' AND action_type='invoice' AND action_id =".$invoice_id."");
				if(isset($updatedBy_result) && !empty($updatedBy_result)){
					$updatedBy = $updatedBy_result->createdBy;
				}else{
					$updatedBy = '';
				}
				include plugin_dir_path(__FILE__) . 'invoice-info-card.php';
			}
		} else {
			echo '<div class="row custom_opp_tab" style="padding: 10px;margin: 0px 30px;">';
            echo '<div class="col-sm-12">';
			echo 'No invoices found.';
			echo '</div>';
            echo '</div>';
		}
	}

}
// Instantiate the class
//$crm_erp_admin_interface = new CRM_ERP_Admin_Interface();
