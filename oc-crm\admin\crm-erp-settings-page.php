<?php
/**
 * The settings page functionality of the plugin.
 */

class CRM_ERP_Settings_Page {

    /**
     * Holds the values to be used in the fields callbacks
     */
    private $options;

    /**
     * Start up
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_plugin_page'));
        add_action('admin_init', array($this, 'page_init'));
    }

    /**
     * Add options page
     */
    public function add_plugin_page() {
        // This page will be under "Settings"
        add_options_page(
            ' Settings', 
            ' CRM ERP', 
            'manage_options', 
            'productsplugin-setting-admin', 
            array($this, 'create_admin_page')
        );
    }

    /**
     * Options page callback
     */
    public function create_admin_page() {
        // Set class property
        $this->options = get_option('crm_erp_options');
        ?>
        <div class="wrap">
            <h1> Settings</h1>
            <form method="post" action="options.php">
            <?php
                // This prints out all hidden setting fields
                settings_fields('crm_erp_option_group');
                do_settings_sections('productsplugin-setting-admin');
                submit_button();
            ?>
            </form>
        </div>
        <?php
    }

    /**
     * Register and add settings
     */
    public function page_init() {        
        register_setting(
            'crm_erp_option_group', // Option group
            'crm_erp_options', // Option name
            array($this, 'sanitize') // Sanitize
        );

        add_settings_section(
            'setting_section_id', // ID
            'My Custom Settings', // Title
            array($this, 'print_section_info'), // Callback
            'productsplugin-setting-admin' // Page
        );  

        add_settings_field(
            'some_id', // ID
            'Some Title', // Title 
            array($this, 'some_id_callback'), // Callback
            'productsplugin-setting-admin', // Page
            'setting_section_id' // Section           
        );      
    }

    /**
     * Sanitize each setting field as needed
     *
     * @param array $input Contains all settings fields as array keys
     */
    public function sanitize($input) {
        $new_input = array();
        if(isset($input['some_id']))
            $new_input['some_id'] = sanitize_text_field($input['some_id']);

        // Add other settings fields as needed

        return $new_input;
    }

    /** 
     * Print the Section text
     */
    public function print_section_info() {
        print 'Enter your settings below:';
    }

    /** 
     * Get the settings option array and print one of its values
     */
    public function some_id_callback() {
        printf(
            '<input type="text" id="some_id" name="crm_erp_options[some_id]" value="%s" />',
            isset($this->options['some_id']) ? esc_attr($this->options['some_id']) : ''
        );
    }

    // Add other callback methods for your settings fields here
}

// Instantiate the class
if (is_admin())
    $crm_erp_settings_page = new CRM_ERP_Settings_Page();



/*

Explanation:
Class Structure: The CRM_ERP_Settings_Page class manages the settings page, including rendering the page and handling form submissions.

Constructor: Sets up actions to add the settings page to the WordPress admin and initialize settings.

Settings Page: The add_plugin_page method adds a new page under the "Settings" menu in WordPress.

Page Content: The create_admin_page method outputs the HTML for the settings page. It uses WordPress settings API functions like settings_fields and do_settings_sections.

Settings Initialization: The page_init method registers a setting, a section, and fields. This is where you define the options your settings page will handle.

Sanitization Callback: The sanitize method is used to sanitize input from the settings fields.

Section and Field Callbacks: Methods like print_section_info and some_id_callback are used to output the settings section and fields.

Additional Notes:
The sanitize_text_field function is used for sanitizing text input. You should use appropriate sanitization functions for different types of data.
This example provides a basic structure. Depending on your plugin's requirements, you might need to handle more complex settings, validation, and output.
Always ensure that any data output is properly escaped to prevent security issues, such as XSS attacks. In this example, esc_attr is used to escape the setting value before outputting it in the HTML.

*/