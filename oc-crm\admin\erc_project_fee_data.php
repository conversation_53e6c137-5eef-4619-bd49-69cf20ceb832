<?php
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);
// get affilate user id for current lead of page
global $wpdb;
$lead_id = $_GET['lead_id'];
//sales agent and sales support
$sales_agent_user_id = 0;
$sales_support_user_id = 0;

$sales_data = $wpdb->get_row("SELECT sales_user_id,sales_support_id FROM {$wpdb->prefix}erc_iris_leads_additional_info WHERE lead_id = ".$lead_id."");
if(!empty($sales_data)){
    $sales_agent_user_id = $sales_data->sales_user_id;
    $sales_support_user_id = $sales_data->sales_support_id;
}

$sales_agent_name = '';
if($sales_agent_user_id > 0){
    $sales_user_data = get_user_by('id',$sales_agent_user_id);
    $sales_agent_name = $sales_user_data->data->display_name;
}

$sales_support_name = '';
if($sales_support_user_id > 0){
    $sales_users_data = get_user_by('id',$sales_support_user_id);
    $sales_support_name = $sales_users_data->data->display_name;
}

$current_user_id = get_current_user_id();
$current_user_data = get_user_by('id', $current_user_id);
$current_user_name = $current_user_data->data->display_name;
$userdata = get_user_by("id", get_current_user_id());
$user_roles = $userdata->roles;
$role_css = "";
$readonly = "";
$disabled = "";
if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles) || in_array("iris_employee", $user_roles) || $user_roles[0] == "lead") {
    $readonly = "readonly";
    $disabled = "disabled";
}
$ercfee_error_discovered_date = "";
$ercfee_q2_2020_941_wages = "";
$ercfee_q3_2020_941_wages = "";
$ercfee_q4_2020_941_wages = "";
$ercfee_q1_2021_941_wages = "";
$ercfee_q2_2021_941_wages = "";
$ercfee_q3_2021_941_wages = "";
$ercfee_q4_2021_941_wages = "";
$ercfee_affiliate_name = "";
$ercfee_affiliate_percentage = "";
$ercfee_erc_claim_filed = "";
$ercfee_erc_amount_received = "";
$ercfee_total_erc_fees = "";
$ercfee_legal_fees = "";
$ercfee_total_erc_fees_paid = "";
$ercfee_total_erc_fees_pending = "";
$ercfee_total_occams_share = "";
$ercfee_total_aff_ref_share = "";
$ercfee_retain_occams_share = "";
$ercfee_retain_aff_ref_share = "";
$ercfee_bal_retain_occams_share = "";
$ercfee_bal_retain_aff_ref_share = "";
$ercfee_total_occams_share_paid = "";
$ercfee_total_aff_ref_share_paid = "";
$ercfee_total_occams_share_pendin = "";
$ercfee_total_aff_ref_share_pend = "";
$ercfee_q1_2020_filed_status = "";
$ercfee_q1_2020_filed_date = "";
$ercfee_q1_2020_amount_filed = "";
$ercfee_q1_2020_benefits = "";
$ercfee_q2_2020_filed_status = "";
$ercfee_q2_2020_filed_date = "";
$ercfee_q2_2020_amount_filed = "";
$ercfee_q2_2020_benefits = "";
$ercfee_q3_2020_filed_status = "";
$ercfee_q3_2020_filed_date = "";
$ercfee_q3_2020_amount_filed = "";
$ercfee_q3_2020_benefits = "";
$ercfee_q4_2020_filed_status = "";
$ercfee_q4_2020_filed_date = "";
$ercfee_q4_2020_amount_filed = "";
$ercfee_q4_2020_benefits = "";
$ercfee_q1_2021_filed_status = "";
$ercfee_q1_2021_filed_date = "";
$ercfee_q1_2021_amount_filed = "";
$ercfee_q1_2021_benefits = "";
$ercfee_q2_2021_filed_status = "";
$ercfee_q2_2021_filed_date = "";
$ercfee_q2_2021_amount_filed = "";
$ercfee_q2_2021_benefits = "";
$ercfee_q3_2021_filed_status = "";
$ercfee_q3_2021_filed_date = "";
$ercfee_q3_2021_amount_filed = "";
$ercfee_q3_2021_benefits = "";
$ercfee_q4_2021_filed_status = "";
$ercfee_q4_2021_filed_date = "";
$ercfee_q4_2021_amount_filed = "";
$ercfee_q4_2021_benefits = "";
$ercfee_q1_2020_loop = "";
$ercfee_q1_2020_letter = "";
$ercfee_q1_2020_check = "";
$ercfee_q1_2020_chq_amt = "";
$ercfee_q2_2020_loop = "";
$ercfee_q2_2020_letter = "";
$ercfee_q2_2020_check = "";
$ercfee_q2_2020_chq_amt = "";
$ercfee_q3_2020_loop = "";
$ercfee_q3_2020_letter = "";
$ercfee_q3_2020_check = "";
$ercfee_q3_2020_chq_amt = "";
$ercfee_q4_2020_loop = "";
$ercfee_q4_2020_letter = "";
$ercfee_q4_2020_check = "";
$ercfee_q4_2020_chq_amt = "";
$ercfee_q1_2021_loop = "";
$ercfee_q1_2021_letter = "";
$ercfee_q1_2021_check = "";
$ercfee_q1_2021_chq_amt = "";
$ercfee_q2_2021_loop = "";
$ercfee_q2_2021_letter = "";
$ercfee_q2_2021_check = "";
$ercfee_q2_2021_chq_amt = "";
$ercfee_q3_2021_loop = "";
$ercfee_q3_2021_letter = "";
$ercfee_q3_2021_check = "";
$ercfee_q3_2021_chq_amt = "";
$ercfee_q4_2021_loop = "";
$ercfee_q4_2021_letter = "";
$ercfee_q4_2021_check = "";
$ercfee_q4_2021_chq_amt = "";
$ercfee_i_invoice_number = "";
$ercfee_i_invoice_amount = "";
$ercfee_i_invoiced_qtrs = "";
$ercfee_i_invoice_sent_date = "";
$ercfee_i_invoice_payment_type = "";
$ercfee_i_invoice_payment_date = "";
$ercfee_i_invoice_pay_cleared = "";
$ercfee_i_invoice_pay_returned = "";
$ercfee_i_invoice_return_reason = "";
$ercfee_i_invoice_occams_share = "";
$ercfee_i_invoice_aff_ref_share = "";
$ercfee_ii__invoice_number = "";
$ercfee_ii_invoice_amount = "";
$ercfee_ii_invoiced_qtrs = "";
$ercfee_ii_invoice_sent_date = "";
$ercfee_ii_invoice_payment_type = "";
$ercfee_ii_invoice_payment_date = "";
$ercfee_ii_invoice_pay_cleared = "";
$ercfee_ii_invoice_pay_returned = "";
$ercfee_ii_invoice_return_reason = "";
$ercfee_ii_invoice_occams_share = "";
$ercfee_ii_invoice_aff_ref_share = "";
$ercfee_iii_invoice_number = "";
$ercfee_iii_invoice_amount = "";
$ercfee_iii_invoiced_qtrs = "";
$ercfee_iii_invoice_sent_date = "";
$ercfee_iii_invoice_payment_type = "";
$ercfee_iii_invoice_payment_date = "";
$ercfee_iii_invoice_pay_cleared = "";
$ercfee_iii_invoice_pay_returned = "";
$ercfee_iii_invoice_return_reason = "";
$ercfee_iii_invoice_occams_share = "";
$ercfee_iii_invoice_aff_ref_share = "";
$ercfee_iv_invoice_number = "";
$ercfee_iv_invoice_amount = "";
$ercfee_iv_invoiced_qtrs = "";
$ercfee_iv_invoice_sent_date = "";
$ercfee_iv_invoice_payment_type = "";
$ercfee_iv_invoice_payment_date = "";
$ercfee_iv_invoice_pay_cleared = "";
$ercfee_iv_invoice_pay_returned = "";
$ercfee_iv_invoice_return_reason = "";
$ercfee_iv_invoice_occams_share = "";
$ercfee_iv_invoice_aff_ref_share = "";

$ercfee_v_invoice_number = "";
$ercfee_v_invoice_amount = "";
$ercfee_v_invoiced_qtrs = "";
$ercfee_v_invoice_sent_date = "";
$ercfee_v_invoice_payment_type = "";
$ercfee_v_invoice_payment_date = "";
$ercfee_v_invoice_pay_cleared = "";
$ercfee_v_invoice_pay_returned = "";
$ercfee_v_invoice_return_reason = "";
$ercfee_v_invoice_occams_share = "";
$ercfee_v_invoice_aff_ref_share = "";

$ercfee_vi_invoice_number = "";
$ercfee_vi_invoice_amount = "";
$ercfee_vi_invoiced_qtrs = "";
$ercfee_vi_invoice_sent_date = "";
$ercfee_vi_invoice_payment_type = "";
$ercfee_vi_invoice_payment_date = "";
$ercfee_vi_invoice_pay_cleared = "";
$ercfee_vi_invoice_pay_returned = "";
$ercfee_vi_invoice_return_reason = "";
$ercfee_vi_invoice_occams_share = "";
$ercfee_vi_invoice_aff_ref_share = "";

$ercfee_vii_invoice_number = "";
$ercfee_vii_invoice_amount = "";
$ercfee_vii_invoiced_qtrs = "";
$ercfee_vii_invoice_sent_date = "";
$ercfee_vii_invoice_payment_type = "";
$ercfee_vii_invoice_payment_date = "";
$ercfee_vii_invoice_pay_cleared = "";
$ercfee_vii_invoice_pay_returned = "";
$ercfee_vii_invoice_return_reason = "";
$ercfee_vii_invoice_occams_share = "";
$ercfee_vii_invoice_aff_ref_share = "";

$ercfee_q1_2020_max_erc_amount = '';
$ercfee_q2_2020_max_erc_amount = '';
$ercfee_q3_2020_max_erc_amount = '';
$ercfee_q4_2020_max_erc_amount = '';
$ercfee_q1_2021_max_erc_amount = '';
$ercfee_q2_2021_max_erc_amount = '';
$ercfee_q3_2021_max_erc_amount = '';
$ercfee_q4_2021_max_erc_amount = '';
$ercfee_q1_2020_eligibility_basis = '';
$ercfee_q2_2020_eligibility_basis = '';
$ercfee_q3_2020_eligibility_basis = '';
$ercfee_q4_2020_eligibility_basis = '';
$ercfee_q1_2021_eligibility_basis = '';
$ercfee_q2_2021_eligibility_basis = '';
$ercfee_q3_2021_eligibility_basis = '';
$ercfee_q4_2021_eligibility_basis = '';
// ---------- ERC FEES ---------
$erc_fees_data = [
    "ercfee_error_discovered_date" => "error_discovered_date",
    "ercfee_q2_2020_941_wages" => "q2_2020_941_wages",
    "ercfee_q3_2020_941_wages" => "q3_2020_941_wages",
    "ercfee_q4_2020_941_wages" => "q4_2020_941_wages",
    "ercfee_q1_2021_941_wages" => "q1_2021_941_wages",
    "ercfee_q2_2021_941_wages" => "q2_2021_941_wages",
    "ercfee_q3_2021_941_wages" => "q3_2021_941_wages",
    "ercfee_q4_2021_941_wages" => "q4_2021_941_wages",
    "ercfee_affiliate_name" => "affiliate_name",
    "ercfee_affiliate_percentage" => "affiliate_percentage",
    "ercfee_erc_claim_filed" => "erc_claim_filed",
    "ercfee_erc_amount_received" => "erc_amount_received",
    "ercfee_total_erc_fees" => "total_erc_fees",
    "ercfee_legal_fees" => "legal_fees",
    "ercfee_total_erc_fees_paid" => "total_erc_fees_paid",
    "ercfee_total_erc_fees_pending" => "total_erc_fees_pending",
    "ercfee_total_occams_share" => "total_occams_share",
    "ercfee_total_aff_ref_share" => "total_aff_ref_share",
    "ercfee_retain_occams_share" => "retain_occams_share",
    "ercfee_retain_aff_ref_share" => "retain_aff_ref_share",
    "ercfee_bal_retain_occams_share" => "bal_retain_occams_share",
    "ercfee_bal_retain_aff_ref_share" => "bal_retain_aff_ref_share",
    "ercfee_total_occams_share_paid" => "total_occams_share_paid",
    "ercfee_total_aff_ref_share_paid" => "total_aff_ref_share_paid",
    "ercfee_total_occams_share_pendin" => "total_occams_share_pendin",
    "ercfee_total_aff_ref_share_pend" => "total_aff_ref_share_pend",
    "ercfee_q1_2020_filed_status" => "q1_2020_filed_status",
    "ercfee_q1_2020_filed_date" => "filing_date_4267",
    "ercfee_q1_2020_amount_filed" => "amount_filed_4263",
    "ercfee_q1_2020_benefits" => "q1_2020_benefits",
    "ercfee_q2_2020_filed_status" => "q2_2020_filed_status",
    "ercfee_q2_2020_filed_date" => "filing_date_4268",
    "ercfee_q2_2020_amount_filed" => "amount_filed_4269",
    "ercfee_q2_2020_benefits" => "q2_2020_benefits",
    "ercfee_q3_2020_filed_status" => "q3_2020_filed_status",
    "ercfee_q3_2020_filed_date" => "filing_date_4270",
    "ercfee_q3_2020_amount_filed" => "amount_filed_4266",
    "ercfee_q3_2020_benefits" => "q3_2020_benefits",
    "ercfee_q4_2020_filed_status" => "q4_2020_filed_status",
    "ercfee_q4_2020_filed_date" => "filing_date_4272",
    "ercfee_q4_2020_amount_filed" => "amount_filed_4273",
    "ercfee_q4_2020_benefits" => "q4_2020_benefits",
    "ercfee_q1_2021_filed_status" => "q1_2021_filed_status",
    "ercfee_q1_2021_filed_date" => "filing_date_4276",
    "ercfee_q1_2021_amount_filed" => "amount_filed_4277",
    "ercfee_q1_2021_benefits" => "q1_2021_benefits",
    "ercfee_q2_2021_filed_status" => "q2_2021_filed_status",
    "ercfee_q2_2021_filed_date" => "filing_date_4279",
    "ercfee_q2_2021_amount_filed" => "amount_filed_4280",
    "ercfee_q2_2021_benefits" => "q2_2021_benefits",
    "ercfee_q3_2021_filed_status" => "q3_2021_filed_status",
    "ercfee_q3_2021_filed_date" => "filing_date_4282",
    "ercfee_q3_2021_amount_filed" => "amount_filed_4283",
    "ercfee_q3_2021_benefits" => "q3_2021_benefits",
    "ercfee_q4_2021_filed_status" => "q4_2021_filed_status",
    "ercfee_q4_2021_filed_date" => "filing_date_4285",
    "ercfee_q4_2021_amount_filed" => "amount_filed_4286",
    "ercfee_q4_2021_benefits" => "q4_2021_benefits",
    "ercfee_q1_2020_loop" => "q1_2020_loOP",
    "ercfee_q1_2020_letter" => "q1_2020_letter",
    "ercfee_q1_2020_check" => "q1_2020_check",
    "ercfee_q1_2020_chq_amt" => "q1_2020_chq_amt",
    "ercfee_q2_2020_loop" => "q2_2020_loOP",
    "ercfee_q2_2020_letter" => "q2_2020_letter",
    "ercfee_q2_2020_check" => "q2_2020_check",
    "ercfee_q2_2020_chq_amt" => "q2_2020_chq_amt",
    "ercfee_q3_2020_loop" => "q3_2020_loOP",
    "ercfee_q3_2020_letter" => "q3_2020_letter",
    "ercfee_q3_2020_check" => "q3_2020_check",
    "ercfee_q3_2020_chq_amt" => "q3_2020_chq_amt",
    "ercfee_q4_2020_loop" => "q4_2020_loOP",
    "ercfee_q4_2020_letter" => "q4_2020_letter",
    "ercfee_q4_2020_check" => "q4_2020_check",
    "ercfee_q4_2020_chq_amt" => "q4_2020_chq_amt",
    "ercfee_q1_2021_loop" => "q1_2021_loOP",
    "ercfee_q1_2021_letter" => "q1_2021_letter",
    "ercfee_q1_2021_check" => "q1_2021_check",
    "ercfee_q1_2021_chq_amt" => "q1_2021_chq_amt",
    "ercfee_q2_2021_loop" => "q2_2021_loOP",
    "ercfee_q2_2021_letter" => "q2_2021_letter",
    "ercfee_q2_2021_check" => "q2_2021_check",
    "ercfee_q2_2021_chq_amt" => "q2_2021_chq_amt",
    "ercfee_q3_2021_loop" => "q3_2021_loOP",
    "ercfee_q3_2021_letter" => "q3_2021_letter",
    "ercfee_q3_2021_check" => "q3_2021_check",
    "ercfee_q3_2021_chq_amt" => "q3_2021_chq_amt",
    "ercfee_q4_2021_loop" => "q4_2021_loOP",
    "ercfee_q4_2021_letter" => "q4_2021_letter",
    "ercfee_q4_2021_check" => "q4_2021_check",
    "ercfee_q4_2021_chq_amt" => "q4_2021_chq_amt",
    "ercfee_q1_2020_max_erc_amount" => "q1_2020_max_erc_amount",
    "ercfee_q2_2020_max_erc_amount" => "q2_2020_max_erc_amount",
    "ercfee_q3_2020_max_erc_amount" => "q3_2020_max_erc_amount",
    "ercfee_q4_2020_max_erc_amount" => "q4_2020_max_erc_amount",
    "ercfee_q1_2021_max_erc_amount" => "q1_2021_max_erc_amount",
    "ercfee_q2_2021_max_erc_amount" => "q2_2021_max_erc_amount",
    "ercfee_q3_2021_max_erc_amount" => "q3_2021_max_erc_amount",
    "ercfee_q4_2021_max_erc_amount" => "q4_2021_max_erc_amount",
    "ercfee_q1_2020_eligibility_basis" => "q1_2020_eligibility_basis",
    "ercfee_q2_2020_eligibility_basis" => "q2_2020_eligibility_basis",
    "ercfee_q3_2020_eligibility_basis" => "q3_2020_eligibility_basis",
    "ercfee_q4_2020_eligibility_basis" => "q4_2020_eligibility_basis",
    "ercfee_q1_2021_eligibility_basis" => "q1_2021_eligibility_basis",
    "ercfee_q2_2021_eligibility_basis" => "q2_2021_eligibility_basis",
    "ercfee_q3_2021_eligibility_basis" => "q3_2021_eligibility_basis",
    "ercfee_q4_2021_eligibility_basis" => "q4_2021_eligibility_basis"
];

// ---------- INVOICE SUCCESS DATA ---------
$invoice_success_data = [
    "ercfee_i_invoice_number" => "i_invoice_no",
    "ercfee_i_invoice_amount" => "i_invoice_amount",
    "ercfee_i_invoiced_qtrs" => "i_invoiced_qtrs",
    "ercfee_i_invoice_sent_date" => "i_invoice_sent_date",
    "ercfee_i_invoice_payment_type" => "i_invoice_payment_type",
    "ercfee_i_invoice_payment_date" => "i_invoice_payment_date",
    "ercfee_i_invoice_pay_cleared" => "i_invoice_pay_cleared",
    "ercfee_i_invoice_pay_returned" => "i_invoice_pay_returned",
    "ercfee_i_invoice_return_reason" => "i_invoice_return_reason",
    "ercfee_i_invoice_occams_share" => "i_invoice_occams_share",
    "ercfee_i_invoice_aff_ref_share" => "i_invoice_aff_ref_share",
    "ercfee_ii__invoice_number" => "ii_invoice_no",
    "ercfee_ii_invoice_amount" => "ii_invoice_amount",
    "ercfee_ii_invoiced_qtrs" => "ii_invoiced_qtrs",
    "ercfee_ii_invoice_sent_date" => "ii_invoice_sent_date",
    "ercfee_ii_invoice_payment_type" => "ii_invoice_payment_type",
    "ercfee_ii_invoice_payment_date" => "ii_invoice_payment_date",
    "ercfee_ii_invoice_pay_cleared" => "ii_invoice_pay_cleared",
    "ercfee_ii_invoice_pay_returned" => "ii_invoice_pay_returned",
    "ercfee_ii_invoice_return_reason" => "ii_invoice_return_reason",
    "ercfee_ii_invoice_occams_share" => "ii_invoice_occams_share",
    "ercfee_ii_invoice_aff_ref_share" => "ii_invoice_aff_ref_share",
    "ercfee_iii_invoice_number" => "iii_invoice_no",
    "ercfee_iii_invoice_amount" => "iii_invoice_amount",
    "ercfee_iii_invoiced_qtrs" => "iii_invoiced_qtrs",
    "ercfee_iii_invoice_sent_date" => "iii_invoice_sent_date",
    "ercfee_iii_invoice_payment_type" => "iii_invoice_payment_type",
    "ercfee_iii_invoice_payment_date" => "iii_invoice_payment_date",
    "ercfee_iii_invoice_pay_cleared" => "iii_invoice_pay_cleared",
    "ercfee_iii_invoice_pay_returned" => "iii_invoice_pay_returned",
    "ercfee_iii_invoice_return_reason" => "iii_invoice_return_reason",
    "ercfee_iii_invoice_occams_share" => "iii_invoice_occams_share",
    "ercfee_iii_invoice_aff_ref_share" => "iii_invoice_aff_ref_share",
    "ercfee_iv_invoice_number" => "iv_invoice_no",
    "ercfee_iv_invoice_amount" => "iv_invoice_amount",
    "ercfee_iv_invoiced_qtrs" => "iv_invoiced_qtrs",
    "ercfee_iv_invoice_sent_date" => "iv_invoice_sent_date",
    "ercfee_iv_invoice_payment_type" => "iv_invoice_payment_type",
    "ercfee_iv_invoice_payment_date" => "iv_invoice_payment_date",
    "ercfee_iv_invoice_pay_cleared" => "iv_invoice_pay_cleared",
    "ercfee_iv_invoice_pay_returned" => "iv_invoice_pay_returned",
    "ercfee_iv_invoice_return_reason" => "iv_invoice_return_reason",
    "ercfee_iv_invoice_occams_share" => "iv_invoice_occams_share",
    "ercfee_iv_invoice_aff_ref_share" => "iv_invoice_aff_ref_share",

    "ercfee_v_invoice_number" => "v_invoice_no",
    "ercfee_v_invoice_amount" => "v_invoice_amount",
    "ercfee_v_invoiced_qtrs" => "v_invoiced_qtrs",
    "ercfee_v_invoice_sent_date" => "v_invoice_sent_date",
    "ercfee_v_invoice_payment_type" => "v_invoice_payment_type",
    "ercfee_v_invoice_payment_date" => "v_invoice_payment_date",
    "ercfee_v_invoice_pay_cleared" => "v_invoice_pay_cleared",
    "ercfee_v_invoice_pay_returned" => "v_invoice_pay_returned",
    "ercfee_v_invoice_return_reason" => "v_invoice_return_reason",
    "ercfee_v_invoice_occams_share" => "v_invoice_occams_share",
    "ercfee_v_invoice_aff_ref_share" => "v_invoice_aff_ref_share",
	
    "ercfee_vi_invoice_number" => "vi_invoice_no",
    "ercfee_vi_invoice_amount" => "vi_invoice_amount",
    "ercfee_vi_invoiced_qtrs" => "vi_invoiced_qtrs",
    "ercfee_vi_invoice_sent_date" => "vi_invoice_sent_date",
    "ercfee_vi_invoice_payment_type" => "vi_invoice_payment_type",
    "ercfee_vi_invoice_payment_date" => "vi_invoice_payment_date",
    "ercfee_vi_invoice_pay_cleared" => "vi_invoice_pay_cleared",
    "ercfee_vi_invoice_pay_returned" => "vi_invoice_pay_returned",
    "ercfee_vi_invoice_return_reason" => "vi_invoice_return_reason",
    "ercfee_vi_invoice_occams_share" => "vi_invoice_occams_share",
    "ercfee_vi_invoice_aff_ref_share" => "vi_invoice_aff_ref_share",

    "ercfee_vii_invoice_number" => "vii_invoice_no",
    "ercfee_vii_invoice_amount" => "vii_invoice_amount",
    "ercfee_vii_invoiced_qtrs" => "vii_invoiced_qtrs",
    "ercfee_vii_invoice_sent_date" => "vii_invoice_sent_date",
    "ercfee_vii_invoice_payment_type" => "vii_invoice_payment_type",
    "ercfee_vii_invoice_payment_date" => "vii_invoice_payment_date",
    "ercfee_vii_invoice_pay_cleared" => "vii_invoice_pay_cleared",
    "ercfee_vii_invoice_pay_returned" => "vii_invoice_pay_returned",
    "ercfee_vii_invoice_return_reason" => "vii_invoice_return_reason",
    "ercfee_vii_invoice_occams_share" => "vii_invoice_occams_share",
    "ercfee_vii_invoice_aff_ref_share" => "vii_invoice_aff_ref_share",	
];

// Fetch ERC fees data from erc_erc_fees table
foreach ($erc_fees_data as $var_name => $column_name) {
    if ($column_name != "") {
        $leadData = $wpdb->get_row(
            "SELECT $column_name FROM {$wpdb->prefix}erc_erc_fees WHERE lead_id = $lead_id"
        );
        if (!empty($leadData)) {
            $column_val = $leadData->$column_name;
        } else {
            $column_val = "";
        }
        $$var_name = $column_val;
    } else {
        $$var_name = "__N/A";
    }
} // erc fees loop

// Fetch invoice success data from invoice_success table
/* echo '<pre>';
print_r($invoice_success_data);
echo '</pre>'; */
foreach ($invoice_success_data as $var_name => $column_name) {
    if ($column_name != "") {
		
		
        $leadData = $wpdb->get_row(
            "SELECT $column_name FROM {$wpdb->prefix}invoice_success WHERE lead_id = $lead_id"
        );
		
        if (!empty($leadData)) {
            $column_val = $leadData->$column_name;
			//echo 'col name:'.$column_name.', col val:'.$column_val.'<br>';
        } else {
            $column_val = "";
        }
        $$var_name = $column_val;
    } else {
        $$var_name = "__N/A";
    }
} // invoice success loop
?>


<div class="erc-project-view">
    <fieldset>
        <legend>941 Details</legend>
        <div class="row mb-3">
            <div class="floating col-sm-4 mb-3">
                <label>Error Discovered Date</label>
                <input name="error_discovered_date" data-name='ercfee_error_discovered_date' value='<?php
                    if (
                        !empty($ercfee_error_discovered_date)
                    ) {
                        $ercfee_error_discovered_date = date(
                            "m/d/Y",
                            strtotime(
                                $ercfee_error_discovered_date
                            )
                        );
                    }
                    echo $ercfee_error_discovered_date;
                    ?>' class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q2 2020 941 Wages</label>
                <input name="q2_2020_941_wages" data-name='ercfee_q2_2020_941_wages' value='<?php echo $ercfee_q2_2020_941_wages; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q3 2020 941 Wages</label>
                <input name="q3_2020_941_wages" data-name='ercfee_q3_2020_941_wages' value='<?php echo $ercfee_q3_2020_941_wages; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q4 2020 941 Wages</label>
                <input name="q4_2020_941_wages" data-name='ercfee_q4_2020_941_wages' value='<?php echo $ercfee_q4_2020_941_wages; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q1 2021 941 Wages</label>
                <input name="q1_2021_941_wages" data-name='ercfee_q1_2021_941_wages' value='<?php echo $ercfee_q1_2021_941_wages; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q2 2021 941 Wages</label>
                <input name="q2_2021_941_wages" data-name='ercfee_q2_2021_941_wages' value='<?php echo $ercfee_q2_2021_941_wages; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q3 2021 941 Wages</label>
                <input name="q3_2021_941_wages" data-name='ercfee_q3_2021_941_wages' value='<?php echo $ercfee_q3_2021_941_wages; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q4 2021 941 Wages</label>
                <input name="q4_2021_941_wages" data-name='ercfee_q4_2021_941_wages' value='<?php echo $ercfee_q4_2021_941_wages; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
            </div>
        </div>
    </fieldset>
</div>
<div class="erc-project-view">
    <fieldset>
        <legend>TOTAL ERC AMOUNT AND FEES</legend>
        <div class="row mb-3">
            <div class="floating col-sm-4 mb-3">
                <input type="text" class="floating__input form-control" name="" value="<?= $sales_agent_name; ?>" readonly>  
                <label class="floating__label" data-content="Internal Sales Agent"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input type="text" class="floating__input form-control" name="" value="<?= $sales_support_name; ?>" readonly>  
                <label class="floating__label" data-content="Internal Sales Support"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="affiliate_name" data-name='ercfee_affiliate_name' value='<?php echo $ercfee_affiliate_name; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Affiliate Name">
                <label class="floating__label" data-content="Affiliate Name"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="affiliate_percentage" data-name='ercfee_affiliate_percentage' value='<?php echo $ercfee_affiliate_percentage; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Affiliate Percentage">
                <label class="floating__label" data-content="Affiliate Percentage"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="erc_claim_filed" data-name='ercfee_erc_claim_filed' value='<?php echo $ercfee_erc_claim_filed; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="ERC Claim Filed">
                <label class="floating__label" data-content="ERC Claim Filed"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="erc_amount_received" data-name='ercfee_erc_amount_received' value='<?php echo $ercfee_erc_amount_received; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="ERC Amount Received">
                <label class="floating__label" data-content="ERC Amount Received"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="total_erc_fees" data-name='ercfee_total_erc_fees' value='<?php echo $ercfee_total_erc_fees; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total ERC Fees">
                <label class="floating__label" data-content="Total ERC Fees"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="legal_fees" data-name='ercfee_legal_fees' value='<?php echo $ercfee_legal_fees; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Legal Fees">
                <label class="floating__label" data-content="Legal Fees"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="total_erc_fees_paid" data-name='ercfee_total_erc_fees_paid' value='<?php echo $ercfee_total_erc_fees_paid; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total ERC Fees Paid">
                <label class="floating__label" data-content="Total ERC Fees Paid"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="total_erc_fees_pending" data-name='ercfee_total_erc_fees_pending' value='<?php echo $ercfee_total_erc_fees_pending; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total ERC Fees Pending">
                <label class="floating__label" data-content="Total ERC Fees Pending"></label>
                <span class="error" id="last_nameErr"></span>
            </div>


            <div class="floating col-sm-4 mb-3">
                <input name="total_occams_share" data-name='ercfee_total_occams_share' value='<?php echo $ercfee_total_occams_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Occams Share">
                <label class="floating__label" data-content="Total Occams Share"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="total_aff_ref_share" data-name='ercfee_total_aff_ref_share' value='<?php echo $ercfee_total_aff_ref_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Aff_Ref Share">
                <label class="floating__label" data-content="Total Aff_Ref Share"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="retain_occams_share" data-name='ercfee_retain_occams_share' value='<?php echo $ercfee_retain_occams_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Retain Occams Share">
                <label class="floating__label" data-content="Retain Occams Share"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="retain_aff_ref_share" data-name='ercfee_retain_aff_ref_share' value='<?php echo $ercfee_retain_aff_ref_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Retain Aff_Ref Share">
                <label class="floating__label" data-content="Retain Aff_Ref Share"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="bal_retain_occams_share" data-name='ercfee_bal_retain_occams_share' value='<?php echo $ercfee_bal_retain_occams_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Bal Retain Occams Share">
                <label class="floating__label" data-content="Bal Retain Occams Share"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="bal_retain_aff_ref_share" data-name='ercfee_bal_retain_aff_ref_share' value='<?php echo $ercfee_bal_retain_aff_ref_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Bal Retain Aff_Ref Share">
                <label class="floating__label" data-content="Bal Retain Aff_Ref Share"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="total_occams_share_paid" data-name='ercfee_total_occams_share_paid' value='<?php echo $ercfee_total_occams_share_paid; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Occams Share Paid">
                <label class="floating__label" data-content="Total Occams Share Paid"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="total_aff_ref_share_paid" data-name='ercfee_total_aff_ref_share_paid' value='<?php echo $ercfee_total_aff_ref_share_paid; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Aff_Ref Share Paid">
                <label class="floating__label" data-content="Total Aff_Ref Share Paid"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="total_occams_share_pendin" data-name='ercfee_total_occams_share_pendin' value='<?php echo $ercfee_total_occams_share_pendin; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Occams Share Pendin">
                <label class="floating__label" data-content="Total Occams Share Pendin"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
            <div class="floating col-sm-4 mb-3">
                <input name="total_aff_ref_share_pend" data-name='ercfee_total_aff_ref_share_pend' value='<?php echo $ercfee_total_aff_ref_share_pend; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Aff_Ref Share Pend">
                <label class="floating__label" data-content="Total Aff_Ref Share Pend"></label>
                <span class="error" id="last_nameErr"></span>
            </div>
        </div>
    </fieldset>
</div>
<div class="erc-project-view">
    <fieldset>
        <legend>Total Max ERC Amount 2020</legend>
        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>2020</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q1 2020 Max ERC Amount</label>
                <input type='text' class='crm-erp-field form-control' id="q1_2020_max_erc_amount" name="q1_2020_max_erc_amount" data-name='q1_2020_max_erc_amount' value='<?php echo $ercfee_q1_2020_max_erc_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q2 2020 Max ERC Amount</label>
                <input type='text' class='crm-erp-field form-control' id="q2_2020_max_erc_amount" name="q2_2020_max_erc_amount" data-name='q2_2020_max_erc_amount' value='<?php echo $ercfee_q2_2020_max_erc_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q3 2020 Max ERC Amount</label>
                <input type='text' class='crm-erp-field form-control' id="q3_2020_max_erc_amount" name="q3_2020_max_erc_amount" data-name='q3_2020_max_erc_amount' value='<?php echo $ercfee_q3_2020_max_erc_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q4 2020 Max ERC Amount</label>
                <input type='text' class='crm-erp-field form-control' id="q4_2020_max_erc_amount" name="q4_2020_max_erc_amount" data-name='q4_2020_max_erc_amount' value='<?php echo $ercfee_q4_2020_max_erc_amount; ?>'>
            </div>
        </div>
    </fieldset>
</div>

<div class="erc-project-view">
    <fieldset>
        <legend>Total Max ERC Amount 2021</legend>
        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>2021</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q1 2021 Max ERC Amount</label>
                <input type='text' class='crm-erp-field form-control' id="q1_2021_max_erc_amount" name="q1_2021_max_erc_amount" data-name='q1_2021_max_erc_amount' value='<?php echo $ercfee_q1_2021_max_erc_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q2 2021 Max ERC Amount</label>
                <input type='text' class='crm-erp-field form-control' id="q2_2021_max_erc_amount" name="q2_2021_max_erc_amount" data-name='q2_2021_max_erc_amount' value='<?php echo $ercfee_q2_2021_max_erc_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q3 2021 Max ERC Amount</label>
                <input type='text' class='crm-erp-field form-control' id="q3_2021_max_erc_amount" name="q3_2021_max_erc_amount" data-name='q3_2021_max_erc_amount' value='<?php echo $ercfee_q3_2021_max_erc_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>Q4 2021 Max ERC Amount</label>
                <input type='text' class='crm-erp-field form-control' id="q4_2021_max_erc_amount" name="q4_2021_max_erc_amount" data-name='q4_2021_max_erc_amount' value='<?php echo $ercfee_q4_2021_max_erc_amount; ?>'>
            </div>
        </div>
    </fieldset>
</div>

<div class="erc-project-view">
    <fieldset>
        <legend>ERC Filed Quarter wise 2020</legend>
        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>2020</h2>
            </div>
            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-4 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type='checkbox' class='custom-control-input' id="q1200" name="q1_2020_filed_status" data-name='ercfee_q1_2020_filed_status' size='10' maxlength='-1' value='Yes' <?php if (
                            $ercfee_q1_2020_filed_status == "Yes"
                        ) {
                            echo "checked";
                        } ?>>


                        <label class="custom-control-label" for="q1200">Q1 2020 Filed Status</label>
                    </div>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q1 2020 Filing Date</label>
                    <input name="q1_2020_filed_date" data-name='ercfee_q1_2020_filed_date' value='<?php
                    if (
                        !empty($ercfee_q1_2020_filed_date)
                    ) {
                        $ercfee_q1_2020_filed_date = date(
                            "m/d/Y",
                            strtotime(
                                $ercfee_q1_2020_filed_date
                            )
                        );
                    }
                    echo $ercfee_q1_2020_filed_date;
                    ?>' class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q1 2020 Amount Filed</label>
                    <input name="q1_2020_amount_filed" data-name='ercfee_q1_2020_amount_filed' value='<?php echo $ercfee_q1_2020_amount_filed; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q1 2020 Benefits</label>
                    <input name="q1_2020_benefits" data-name='ercfee_q1_2020_benefits' value='<?php echo $ercfee_q1_2020_benefits; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q1 2020 Eligibility Basis</label>
                    <select <?= $disabled ?> class="q1_2020_eligibility_basis crm-erp-field form-control" name="q1_2020_eligibility_basis" data-name='ercfee_q1_2020_eligibility_basis'>
                        <option value="N/A" <?php if($ercfee_q1_2020_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                        <option value="FPSO" <?php if($ercfee_q1_2020_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                        <option value="SDGR" <?php if($ercfee_q1_2020_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
                    </select>
                </div>
            </div>

            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-4 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input name="q2_2020_filed_status" type='checkbox' class="custom-control-input" id="q2200" data-name='ercfee_q2_2020_filed_status' size='10' maxlength='-1' value='Yes' <?php if (
                            $ercfee_q2_2020_filed_status == "Yes"
                        ) {
                            echo "checked";
                        } ?>>


                        <label class="custom-control-label" for="q2200">Q2 2020 Filed Status</label>
                    </div>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q2 2020 Filing Date</label>
                    <input name="q2_2020_filed_date" data-name='ercfee_q2_2020_filed_date' value='<?php
                    if (
                        !empty($ercfee_q2_2020_filed_date)
                    ) {
                        $ercfee_q2_2020_filed_date = date(
                            "m/d/Y",
                            strtotime(
                                $ercfee_q2_2020_filed_date
                            )
                        );
                    }
                    echo $ercfee_q2_2020_filed_date;
                    ?>' class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q2 2020 Amount Filed</label>
                    <input name="q2_2020_amount_filed" data-name='ercfee_q2_2020_amount_filed' value='<?php echo $ercfee_q2_2020_amount_filed; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q2 2020 Benefits</label>
                    <input name="q2_2020_benefits" data-name='ercfee_q2_2020_benefits' value='<?php echo $ercfee_q2_2020_benefits; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q2 2020 Eligibility Basis</label>
                    <select <?= $disabled ?> class="q2_2020_eligibility_basis crm-erp-field form-control" name="q2_2020_eligibility_basis" data-name='ercfee_q2_2020_eligibility_basis'>
                        <option value="N/A" <?php if($ercfee_q2_2020_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                        <option value="FPSO" <?php if($ercfee_q2_2020_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                        <option value="SDGR" <?php if($ercfee_q2_2020_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
                    </select>
                </div>
            </div>

            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-4 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type='checkbox' class="custom-control-input" id="q3200" name="q3_2020_filed_status" data-name='ercfee_q3_2020_filed_status' size='10' maxlength='-1' value='Yes' <?php if (
                            $ercfee_q3_2020_filed_status == "Yes"
                        ) {
                            echo "checked";
                        } ?>>

                        <label class="custom-control-label" for="q3200">Q3 2020 Filed Status</label>
                    </div>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q3 2020 Filing Date</label>
                    <input name="q3_2020_filed_date" data-name='ercfee_q3_2020_filed_date' value='<?php
                    if (
                        !empty($ercfee_q3_2020_filed_date)
                    ) {
                        $ercfee_q3_2020_filed_date = date(
                            "m/d/Y",
                            strtotime(
                                $ercfee_q3_2020_filed_date
                            )
                        );
                    }
                    echo $ercfee_q3_2020_filed_date;
                    ?>' class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q3 2020 Amount Filed</label>
                    <input name="q3_2020_amount_filed" data-name='ercfee_q3_2020_amount_filed' value='<?php echo $ercfee_q3_2020_amount_filed; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q3 2020 Benefits</label>
                    <input name="q3_2020_benefits" data-name='ercfee_q3_2020_benefits' value='<?php echo $ercfee_q3_2020_benefits; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q3 2020 Eligibility Basis</label>
                    <select <?= $disabled ?> class="q3_2020_eligibility_basis crm-erp-field form-control" name="q3_2020_eligibility_basis" data-name='ercfee_q3_2020_eligibility_basis'>
                        <option value="N/A" <?php if($ercfee_q3_2020_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                        <option value="FPSO" <?php if($ercfee_q3_2020_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                        <option value="SDGR" <?php if($ercfee_q3_2020_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
                    </select>
                </div>
            </div>

            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-4 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type='checkbox' class="custom-control-input" id="q4200" name="q4_2020_filed_status" data-name='ercfee_q4_2020_filed_status' size='10' maxlength='-1' value='Yes' <?php if (
                            $ercfee_q4_2020_filed_status == "Yes"
                        ) {
                            echo "checked";
                        } ?>>


                        <label class="custom-control-label" for="q4200">Q4 2020 Filed Status</label>
                    </div>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q4 2020 Filing Date</label>
                    <input name="q4_2020_filed_date" data-name='ercfee_q4_2020_filed_date' value='<?php
                    if (
                        !empty($ercfee_q4_2020_filed_date)
                    ) {
                        $ercfee_q4_2020_filed_date = date(
                            "m/d/Y",
                            strtotime(
                                $ercfee_q4_2020_filed_date
                            )
                        );
                    }
                    echo $ercfee_q4_2020_filed_date;
                    ?>' class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q4 2020 Amount Filed</label>
                    <input name="q4_2020_amount_filed" data-name='ercfee_q4_2020_amount_filed' value='<?php echo $ercfee_q4_2020_amount_filed; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q4 2020 Benefits</label>
                    <input name="q4_2020_benefits" data-name='ercfee_q4_2020_benefits' value='<?php echo $ercfee_q4_2020_benefits; ?>' class="crm-erp-field form-control" type="text" <?= $readonly ?>>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q4 2020 Eligibility Basis</label>
                    <select <?= $disabled ?> class="q4_2020_eligibility_basis crm-erp-field form-control" name="q4_2020_eligibility_basis" data-name='ercfee_q4_2020_eligibility_basis'>
                        <option value="N/A" <?php if($ercfee_q4_2020_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                        <option value="FPSO" <?php if($ercfee_q4_2020_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                        <option value="SDGR" <?php if($ercfee_q4_2020_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
                    </select>
                </div>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>2021</h2>
            </div>
            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-4 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q12021" name="q1_2021_filed_status" data-name='ercfee_q1_2021_filed_status' value='Yes' <?php if (
                            $ercfee_q1_2021_filed_status == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q12021">Q1 2021 Filed Status</label>
                    </div>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q1 2021 Filing Date</label>
                    <input id="full_namenn" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="q1_2021_filed_date" data-name='ercfee_q1_2021_filed_date' value='<?php
                    if (!empty($ercfee_q1_2021_filed_date)) {
                        $ercfee_q1_2021_filed_date = date(
                            "m/d/Y",
                            strtotime($ercfee_q1_2021_filed_date)
                        );
                    }
                    echo $ercfee_q1_2021_filed_date;
                    ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q1 2021 Amount Filed</label>
                    <input id="full_name2" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q1_2021_amount_filed" data-name='ercfee_q1_2021_amount_filed' value='<?php echo $ercfee_q1_2021_amount_filed; ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q1 2021 Benefits</label>
                    <input id="full_name3" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q1_2021_benefits" data-name='ercfee_q1_2021_benefits' value='<?php echo $ercfee_q1_2021_benefits; ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q1 2021 Eligibility Basis</label>
                    <select <?= $disabled ?> class="q1_2021_eligibility_basis crm-erp-field form-control" name="q1_2021_eligibility_basis" data-name='ercfee_q1_2021_eligibility_basis'>
                        <option value="N/A" <?php if($ercfee_q1_2021_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                        <option value="FPSO" <?php if($ercfee_q1_2021_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                        <option value="SDGR" <?php if($ercfee_q1_2021_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
                    </select>
                </div>
            </div>
            
            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-4 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q22021" name="q2_2021_filed_status" data-name='ercfee_q2_2021_filed_status' value='Yes' <?php if (
                            $ercfee_q2_2021_filed_status == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q22021">Q2 2021 Filed Status</label>
                    </div>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q2 2021 Filing Date</label>
                    <input id="full_name4" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="q2_2021_filed_date" data-name='ercfee_q2_2021_filed_date' value='<?php
                    if (!empty($ercfee_q2_2021_filed_date)) {
                        $ercfee_q2_2021_filed_date = date(
                            "m/d/Y",
                            strtotime($ercfee_q2_2021_filed_date)
                        );
                    }
                    echo $ercfee_q2_2021_filed_date;
                    ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q2 2021 Amount Filed</label>
                    <input id="full_name5" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q2_2021_amount_filed" data-name='ercfee_q2_2021_amount_filed' value='<?php echo $ercfee_q2_2021_amount_filed; ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q2 2021 Benefits</label>
                    <input id="full_name6" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q2_2021_benefits" data-name='ercfee_q2_2021_benefits' value='<?php echo $ercfee_q2_2021_benefits; ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q2 2021 Eligibility Basis</label>
                    <select <?= $disabled ?> class="q2_2021_eligibility_basis crm-erp-field form-control" name="q2_2021_eligibility_basis" data-name='ercfee_q2_2021_eligibility_basis'>
                        <option value="N/A" <?php if($ercfee_q2_2021_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                        <option value="FPSO" <?php if($ercfee_q2_2021_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                        <option value="SDGR" <?php if($ercfee_q2_2021_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
                    </select>
                </div>
            </div>

            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-4 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q32021" name="q3_2021_filed_status" data-name='ercfee_q3_2021_filed_status' value='Yes' <?php if (
                            $ercfee_q3_2021_filed_status == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q32021">Q3 2021 Filed Status</label>
                    </div>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q3 2021 Filing Date</label>
                    <input id="full_name7" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>  name="q3_2021_filed_date" data-name='ercfee_q3_2021_filed_date' value='<?php
                    if (!empty($ercfee_q3_2021_filed_date)) {
                        $ercfee_q3_2021_filed_date = date(
                            "m/d/Y",
                            strtotime($ercfee_q3_2021_filed_date)
                        );
                    }
                    echo $ercfee_q3_2021_filed_date;
                    ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q3 2021 Amount Filed</label>
                    <input id="full_name8" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q3_2021_amount_filed" data-name='ercfee_q3_2021_amount_filed' value='<?php echo $ercfee_q3_2021_amount_filed; ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q3 2021 Benefits</label>
                    <input id="full_name9" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q3_2021_benefits" data-name='ercfee_q3_2021_benefits' value='<?php echo $ercfee_q3_2021_benefits; ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q3 2021 Eligibility Basis</label>
                    <select <?= $disabled ?> class="q3_2021_eligibility_basis crm-erp-field form-control" name="q3_2021_eligibility_basis" data-name='ercfee_q3_2021_eligibility_basis'>
                        <option value="N/A" <?php if($ercfee_q3_2021_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                        <option value="FPSO" <?php if($ercfee_q3_2021_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                        <option value="SDGR" <?php if($ercfee_q3_2021_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
                    </select>
                </div>
            </div>

            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-4 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q42021" name="q4_2021_filed_status" data-name='ercfee_q4_2021_filed_status' value='Yes' <?php if (
                            $ercfee_q4_2021_filed_status == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q42021">Q4 2021 Filed Status</label>
                    </div>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q4 2021 Filing Date</label>
                    <input id="full_name11" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="q4_2021_filed_date" data-name='ercfee_q4_2021_filed_date' value='<?php
                    if (!empty($ercfee_q4_2021_filed_date)) {
                        $ercfee_q4_2021_filed_date = date(
                            "m/d/Y",
                            strtotime($ercfee_q4_2021_filed_date)
                        );
                    }
                    echo $ercfee_q4_2021_filed_date;
                    ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q4 2021 Amount Filed</label>
                    <input id="full_name12" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q4_2021_amount_filed" data-name='ercfee_q4_2021_amount_filed' value='<?php echo $ercfee_q4_2021_amount_filed; ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q4 2021 Benefits</label>
                    <input id="full_name13" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q4_2021_benefits" data-name='ercfee_q4_2021_benefits' value='<?php echo $ercfee_q4_2021_benefits; ?>'>
                </div>
                <div class="floating col-sm-4 mb-3">
                    <label>Q4 2021 Eligibility Basis</label>
                    <select <?= $disabled ?> class="q4_2021_eligibility_basis crm-erp-field form-control" name="q4_2021_eligibility_basis" data-name='ercfee_q4_2021_eligibility_basis'>
                        <option value="N/A" <?php if($ercfee_q4_2021_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                        <option value="FPSO" <?php if($ercfee_q4_2021_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                        <option value="SDGR" <?php if($ercfee_q4_2021_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
                    </select>
                </div>
            </div>
        </div>
        
    </fieldset>
</div>

<div class="erc-project-view">
    <fieldset>
        <legend>ERC Letter, Check & Amount</legend>
        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>2020</h2>
            </div>
            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-3 mb-3">
                <label>Q1 2020 Loop</label>
                <input id="full_name14" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="q1_2020_loop" data-name='ercfee_q1_2020_loop' value='<?php
                if (!empty($ercfee_q1_2020_loop)) {
                    $ercfee_q1_2020_loop = date(
                        "m/d/Y",
                        strtotime($ercfee_q1_2020_loop)
                    );
                }
                echo $ercfee_q1_2020_loop;
                ?>'>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q1200letter" name="q1_2020_letter" data-name='ercfee_q1_2020_letter' value='Yes' <?php if (
                            $ercfee_q1_2020_letter == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q1200letter">Q1 2020 Letter</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q12020check" name="q1_2020_check" data-name='ercfee_q1_2020_check' value='Yes' <?php if (
                            $ercfee_q1_2020_check == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q12020check">Q1 2020 Check</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <label>Q1 2020 Chq Amt</label>
                    <input id="full_name15" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q1_2020_chq_amt" data-name='ercfee_q1_2020_chq_amt' value='<?php echo $ercfee_q1_2020_chq_amt; ?>'>
                </div>
            </div>

            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-3 mb-3">
                <label>Q2 2020 Loop</label>
                <input id="full_name16" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>  name="q2_2020_loop" data-name='ercfee_q2_2020_loop' value='<?php
                if (!empty($ercfee_q2_2020_loop)) {
                    $ercfee_q2_2020_loop = date(
                        "m/d/Y",
                        strtotime($ercfee_q2_2020_loop)
                    );
                }
                echo $ercfee_q2_2020_loop;
                ?>'>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q2200letter" name="q2_2020_letter" data-name='ercfee_q2_2020_letter' value='Yes' <?php if (
                            $ercfee_q2_2020_letter == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q2200letter">Q2 2020 Letter</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q22020check" name="q2_2020_check" data-name='ercfee_q2_2020_check' value='Yes' <?php if (
                            $ercfee_q2_2020_check == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q22020check">Q2 2020 Check</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <label>Q2 2020 Chq Amt</label>
                    <input id="full_name17" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q2_2020_chq_amt" data-name='ercfee_q2_2020_chq_amt' value='<?php echo $ercfee_q2_2020_chq_amt; ?>'>
                </div>
            </div>

            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-3 mb-3">
                <label>Q3 2020 Loop</label>
                <input id="full_name18" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?>  name="q3_2020_loop" data-name='ercfee_q3_2020_loop' value='<?php
                if (!empty($ercfee_q3_2020_loop)) {
                    $ercfee_q3_2020_loop = date(
                        "m/d/Y",
                        strtotime($ercfee_q3_2020_loop)
                    );
                }
                echo $ercfee_q3_2020_loop;
                ?>'>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q3200letter" name="q3_2020_letter" data-name='ercfee_q3_2020_letter' value='Yes' <?php if (
                            $ercfee_q3_2020_letter == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q3200letter">Q3 2020 Letter</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q32020check" name="q3_2020_check" data-name='ercfee_q3_2020_check' value='Yes' <?php if (
                            $ercfee_q3_2020_check == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q32020check">Q3 2020 Check</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <label>Q3 2020 Chq Amt</label>
                    <input id="full_name19" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q3_2020_chq_amt" data-name='ercfee_q3_2020_chq_amt' value='<?php echo $ercfee_q3_2020_chq_amt; ?>'>
                </div>
            </div>

            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-3 mb-3">
                <label>Q4 2020 Loop</label>
                <input id="full_name20" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="q4_2020_loop" data-name='ercfee_q4_2020_loop' value='<?php
                if (!empty($ercfee_q4_2020_loop)) {
                    $ercfee_q4_2020_loop = date(
                        "m/d/Y",
                        strtotime($ercfee_q4_2020_loop)
                    );
                }
                echo $ercfee_q4_2020_loop;
                ?>'>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q4200letter" name="q4_2020_letter" data-name='ercfee_q4_2020_letter' value='Yes' <?php if (
                            $ercfee_q4_2020_letter == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q4200letter">Q4 2020 Letter</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q42020check" name="q4_2020_check" data-name='ercfee_q4_2020_check' value='Yes' <?php if (
                            $ercfee_q4_2020_check == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q42020check">Q4 2020 Check</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <label>Q4 2020 Chq Amt</label>
                    <input id="full_name21" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q4_2020_chq_amt" data-name='ercfee_q4_2020_chq_amt' value='<?php echo $ercfee_q4_2020_chq_amt; ?>'>
                </div>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>2021</h2>
            </div>
            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-3 mb-3">
                <label>Q1 2021 Loop</label>
                <input id="full_name22" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="q1_2021_loop" data-name='ercfee_q1_2021_loop' value='<?php
                if (!empty($ercfee_q1_2021_loop)) {
                    $ercfee_q1_2021_loop = date(
                        "m/d/Y",
                        strtotime($ercfee_q1_2021_loop)
                    );
                }
                echo $ercfee_q1_2021_loop;
                ?>'>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q12001letter" name="q1_2021_letter" data-name='ercfee_q1_2021_letter' value='Yes' <?php if (
                            $ercfee_q1_2021_letter == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q12001letter">Q1 2021 Letter</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q12021check" name="q1_2021_check" data-name='ercfee_q1_2021_check' value='Yes' <?php if (
                            $ercfee_q1_2021_check == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q12021check">Q1 2021 Check</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <label>Q1 2021 Chq Amt</label>
                    <input id="full_name23" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q1_2021_chq_amt" data-name='ercfee_q1_2021_chq_amt' value='<?php echo $ercfee_q1_2021_chq_amt; ?>'>
                </div>
            </div>

            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-3 mb-3">
                <label>Q2 2021 Loop</label>
                <input id="full_name24" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="q2_2021_loop" data-name='ercfee_q2_2021_loop' value='<?php
                if (!empty($ercfee_q2_2021_loop)) {
                    $ercfee_q2_2021_loop = date(
                        "m/d/Y",
                        strtotime($ercfee_q2_2021_loop)
                    );
                }
                echo $ercfee_q2_2021_loop;
                ?>'>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q22001letter" name="q2_2021_letter" data-name='ercfee_q2_2021_letter' value='Yes' <?php if (
                            $ercfee_q2_2021_letter == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q22001letter">Q2 2021 Letter</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q22021check" name="q2_2021_check" data-name='ercfee_q2_2021_check' value='Yes' <?php if (
                            $ercfee_q2_2021_check == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q22021check">Q2 2021 Check</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <label>Q2 2021 Chq Amt</label>
                    <input id="full_name25" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q2_2021_chq_amt" data-name='ercfee_q2_2021_chq_amt' value='<?php echo $ercfee_q2_2021_chq_amt; ?>'>
                </div>
            </div>

            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-3 mb-3">
                <label>Q3 2021 Loop</label>
                <input id="full_name26" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="q3_2021_loop" data-name='ercfee_q3_2021_loop' value='<?php
                if (!empty($ercfee_q3_2021_loop)) {
                    $ercfee_q3_2021_loop = date(
                        "m/d/Y",
                        strtotime($ercfee_q3_2021_loop)
                    );
                }
                echo $ercfee_q3_2021_loop;
                ?>'>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q32001letter" name="q3_2021_letter" data-name='ercfee_q3_2021_letter' value='Yes' <?php if (
                            $ercfee_q3_2021_letter == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q32001letter">Q3 2021 Letter</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q32021check" name="q3_2021_check" data-name='ercfee_q3_2021_check' value='Yes' <?php if (
                            $ercfee_q3_2021_check == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q32021check">Q3 2021 Check</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <label>Q3 2021 Chq Amt</label>
                    <input id="full_name27" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="q3_2021_chq_amt" data-name='ercfee_q3_2021_chq_amt' value='<?php echo $ercfee_q3_2021_chq_amt; ?>'>
                </div>
            </div>

            <div class="col-sm-12 m-0 p-0">
                <div class="floating col-sm-3 mb-3">
                <label>Q4 2021 Loop</label>
                <input id="full_name28" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="q4_2021_loop" data-name='ercfee_q4_2021_loop' value='<?php
                if (!empty($ercfee_q4_2021_loop)) {
                    $ercfee_q4_2021_loop = date(
                        "m/d/Y",
                        strtotime($ercfee_q4_2021_loop)
                    );
                }
                echo $ercfee_q4_2021_loop;
                ?>'>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q42001letter" name="q4_2021_letter" data-name='ercfee_q4_2021_letter' value='Yes' <?php if (
                            $ercfee_q4_2021_letter == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q42001letter">Q4 2021 Letter</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" id="q42021check" name="q4_2021_check" data-name='ercfee_q4_2021_check' value='Yes' <?php if (
                            $ercfee_q4_2021_check == "Yes"
                        ) {
                            echo "checked";
                        } ?>>
                        <label class="custom-control-label" for="q42021check">Q4 2021 Check</label>
                    </div>
                </div>
                <div class="floating col-sm-3 mb-3">
                    <label>Q4 2021 Chq Amt</label>
                    <input id="full_name29" class="crm-erp-field form-control" type="text" <?= $readonly ?>  name="q4_2021_chq_amt" data-name='ercfee_q4_2021_chq_amt' value='<?php echo $ercfee_q4_2021_chq_amt; ?>'>
                </div>
            </div>
        </div>
    </fieldset>
</div>

<div class="erc-project-view">
    <fieldset>
        <legend>Success Fee Invoice Details</legend>
        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>I - Invoice Details</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>I Invoice number</label>
                <input id="full_name31" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="i_invoice_no" data-name='ercfee_i_invoice_number' value='<?php echo $ercfee_i_invoice_number; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>I Invoice Amount</label>
                <input id="full_name32" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="i_invoice_amount" data-name='ercfee_i_invoice_amount' value='<?php echo $ercfee_i_invoice_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>I Invoiced Qtrs</label>
                <input id="full_name33" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="i_invoiced_qtrs" data-name='ercfee_i_invoiced_qtrs' value='<?php echo $ercfee_i_invoiced_qtrs; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>I Invoice Sent Date</label>
                <input id="full_name34" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="i_invoice_sent_date" data-name='ercfee_i_invoice_sent_date' value='<?php
                if (!empty($ercfee_i_invoice_sent_date)) {
                    $ercfee_i_invoice_sent_date = date(
                        "m/d/Y",
                        strtotime($ercfee_i_invoice_sent_date)
                    );
                }
                echo $ercfee_i_invoice_sent_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>I Invoice Payment Type</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name="i_invoice_payment_type" data-name='ercfee_i_invoice_payment_type' id="2019 Tax Return">
                    <option value="">Select payment type</option>
                    <?php
                    $fees_section = [
                        "occams_initiated_eCheck" =>
                            "Occams Initiated - eCheck",
                        "occams_initiated_ach" =>
                            "Occams Initiated - ACH",
                        "occams_initiated_wire" =>
                            "Client Initiated - Wire",
                        "client_initiated_ach" =>
                            "Client Initiated - ACH",
                        "client_initiated_check_mailed" =>
                            "Client Initiated - Check Mailed",
                        "credit_card_or_debit_card" =>
                            "Credit Card or Debit Card",
                    ];

                    foreach ($fees_section as $inkey => $invalue) {
                        if (
                            $ercfee_i_invoice_payment_type ==
                            $inkey
                        ) {
                            $selected =
                                "selected";
                        } else {
                            $selected = "";
                        } ?>
                                        <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                                    <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>I Invoice Payment Date</label>
                <input id="full_name35" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="i_invoice_payment_date" data-name='ercfee_i_invoice_payment_date' value='<?php
                if (!empty($ercfee_i_invoice_payment_date)) {
                    $ercfee_i_invoice_payment_date = date(
                        "m/d/Y",
                        strtotime($ercfee_i_invoice_payment_date)
                    );
                }
                echo $ercfee_i_invoice_payment_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>I Invoice Pay Cleared</label>
                <input id="full_name36" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="i_invoice_pay_cleared" data-name='ercfee_i_invoice_pay_cleared' value='<?php
                if (!empty($ercfee_i_invoice_pay_cleared)) {
                    $ercfee_i_invoice_pay_cleared = date(
                        "m/d/Y",
                        strtotime($ercfee_i_invoice_pay_cleared)
                    );
                }
                echo $ercfee_i_invoice_pay_cleared;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>I Invoice Pay Returned</label>
                <input id="full_name37" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="i_invoice_pay_returned" data-name='ercfee_i_invoice_pay_returned' value='<?php
                if (!empty($ercfee_i_invoice_pay_returned)) {
                    $ercfee_i_invoice_pay_returned = date(
                        "m/d/Y",
                        strtotime($ercfee_i_invoice_pay_returned)
                    );
                }
                echo $ercfee_i_invoice_pay_returned;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>I Invoice Return Reason</label>
                <input id="full_name38" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="i_invoice_return_reason" data-name='ercfee_i_invoice_return_reason' value='<?php echo $ercfee_i_invoice_return_reason; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>I Invoice Occams Share</label>
                <input id="full_name39" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="i_invoice_occams_share" data-name='ercfee_i_invoice_occams_share' value='<?php echo $ercfee_i_invoice_occams_share; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>I Invoice Aff/Ref Share</label>
                <input id="full_name41" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="i_invoice_aff_ref_share" data-name='ercfee_i_invoice_aff/ref_share' value='<?php echo $ercfee_i_invoice_aff_ref_share; ?>'>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>II - Invoice Details</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>II Invoice number</label>
                <input id="full_name42" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="ii_invoice_no" data-name='ercfee_ii__invoice_number' value='<?php echo $ercfee_ii__invoice_number; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>II Invoice Amount</label>
                <input id="full_name43" class="crm-erp-field form-control" type="text" <?= $readonly ?>  name="ii_invoice_amount" data-name='ercfee_ii_invoice_amount' value='<?php echo $ercfee_ii_invoice_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>II Invoiced Qtrs</label>
                <input id="full_name44" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="ii_invoiced_qtrs" data-name='ercfee_ii_invoiced_qtrs' value='<?php echo $ercfee_ii_invoiced_qtrs; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>II Invoice Sent Date</label>
                <input id="full_name45" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="ii_invoice_sent_date" data-name='ercfee_ii_invoice_sent_date' value='<?php
                if (!empty($ercfee_ii_invoice_sent_date)) {
                    $ercfee_ii_invoice_sent_date = date(
                        "m/d/Y",
                        strtotime($ercfee_ii_invoice_sent_date)
                    );
                }
                echo $ercfee_ii_invoice_sent_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>II Invoice Payment Type</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" id="2019 Tax Return" name="ii_invoice_payment_type" data-name='ercfee_ii_invoice_payment_type'>
                    <option value="">Select payment type</option>
                    <?php
                    $fees_section = [
                        "occams_initiated_eCheck" =>
                            "Occams Initiated - eCheck",
                        "occams_initiated_ach" =>
                            "Occams Initiated - ACH",
                        "occams_initiated_wire" =>
                            "Client Initiated - Wire",
                        "client_initiated_ach" =>
                            "Client Initiated - ACH",
                        "client_initiated_check_mailed" =>
                            "Client Initiated - Check Mailed",
                        "credit_card_or_debit_card" =>
                            "Credit Card or Debit Card",
                    ];

                    foreach ($fees_section as $inkey => $invalue) {
                        if (
                            $ercfee_ii_invoice_payment_type ==
                            $inkey
                        ) {
                            $selected =
                                "selected";
                        } else {
                            $selected = "";
                        } ?>
                                        <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                                    <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>II Invoice Payment Date</label>
                <input id="full_name46" class="crm-erp-field form-control  date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="ii_invoice_payment_date" data-name='ercfee_ii_invoice_payment_date' value='<?php
                if (!empty($ercfee_ii_invoice_payment_date)) {
                    $ercfee_ii_invoice_payment_date = date(
                        "m/d/Y",
                        strtotime($ercfee_ii_invoice_payment_date)
                    );
                }
                echo $ercfee_ii_invoice_payment_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>II Invoice Pay Cleared</label>
                <input id="full_name47" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="ii_invoice_pay_cleared" data-name='ercfee_ii_invoice_pay_cleared' value='<?php
                if (!empty($ercfee_ii_invoice_pay_cleared)) {
                    $ercfee_ii_invoice_pay_cleared = date(
                        "m/d/Y",
                        strtotime($ercfee_ii_invoice_pay_cleared)
                    );
                }
                echo $ercfee_ii_invoice_pay_cleared;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>II Invoice Pay Returned</label>
                <input id="full_name48" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="ii_invoice_pay_returned" data-name='ercfee_ii_invoice_pay_returned' value='<?php
                if (!empty($ercfee_ii_invoice_pay_returned)) {
                    $ercfee_ii_invoice_pay_returned = date(
                        "m/d/Y",
                        strtotime($ercfee_ii_invoice_pay_returned)
                    );
                }
                echo $ercfee_ii_invoice_pay_returned;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>II Invoice Return Reason</label>
                <input id="full_name49" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="ii_invoice_return_reason" data-name='ercfee_ii_invoice_return_reason' value='<?php echo $ercfee_ii_invoice_return_reason; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>II Invoice Occams Share</label>
                <input id="full_name51" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="ii_invoice_occams_share" data-name='ercfee_ii_invoice_occams_share' value='<?php echo $ercfee_ii_invoice_occams_share; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>II Invoice Aff/Ref Share</label>
                <input id="full_name52" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="ii_invoice_aff_ref_share" data-name='ercfee_ii_invoice_aff/ref_share' value='<?php echo $ercfee_ii_invoice_aff_ref_share; ?>'>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>III - Invoice Details</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>III Invoice number</label>
                <input id="full_name53" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="iii_invoice_no" data-name='ercfee_iii_invoice_number' value='<?php echo $ercfee_iii_invoice_number; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>III Invoice Amount</label>
                <input id="full_name54" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="iii_invoice_amount" data-name='ercfee_iii_invoice_amount' value='<?php echo $ercfee_iii_invoice_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>III Invoiced Qtrs</label>
                <input id="full_name55" class="crm-erp-field form-control" type="text" <?= $readonly ?>  name="iii_invoiced_qtrs" data-name='ercfee_iii_invoiced_qtrs' value='<?php echo $ercfee_iii_invoiced_qtrs; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>III Invoice Sent Date</label>
                <input id="full_name56" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="iii_invoice_sent_date" data-name='ercfee_iii_invoice_sent_date' value='<?php
                if (!empty($ercfee_iii_invoice_sent_date)) {
                    $ercfee_iii_invoice_sent_date = date(
                        "m/d/Y",
                        strtotime($ercfee_iii_invoice_sent_date)
                    );
                }
                echo $ercfee_iii_invoice_sent_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>III Invoice Payment Type</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name="iii_invoice_payment_type" id="2019 Tax Return" data-name='ercfee_iii_invoice_payment_type'>
                    <option value="">Select payment type</option>
                    <?php
                    $fees_section = [
                        "occams_initiated_eCheck" =>
                            "Occams Initiated - eCheck",
                        "occams_initiated_ach" =>
                            "Occams Initiated - ACH",
                        "occams_initiated_wire" =>
                            "Client Initiated - Wire",
                        "client_initiated_ach" =>
                            "Client Initiated - ACH",
                        "client_initiated_check_mailed" =>
                            "Client Initiated - Check Mailed",
                        "credit_card_or_debit_card" =>
                            "Credit Card or Debit Card",
                    ];

                    foreach ($fees_section as $inkey => $invalue) {
                        if (
                            $ercfee_iii_invoice_payment_type ==
                            $inkey
                        ) {
                            $selected =
                                "selected";
                        } else {
                            $selected = "";
                        } ?>
                                        <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                                    <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>III Invoice Payment Date</label>
                <input id="full_name57" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="iii_invoice_payment_date" data-name='ercfee_iii_invoice_payment_date' value='<?php
                if (!empty($ercfee_iii_invoice_payment_date)) {
                    $ercfee_iii_invoice_payment_date = date(
                        "m/d/Y",
                        strtotime($ercfee_iii_invoice_payment_date)
                    );
                }
                echo $ercfee_iii_invoice_payment_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>III Invoice Pay Cleared</label>
                <input id="full_name58" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="iii_invoice_pay_cleared" data-name='ercfee_iii_invoice_pay_cleared' value='<?php
                if (!empty($ercfee_iii_invoice_pay_cleared)) {
                    $ercfee_iii_invoice_pay_cleared = date(
                        "m/d/Y",
                        strtotime($ercfee_iii_invoice_pay_cleared)
                    );
                }
                echo $ercfee_iii_invoice_pay_cleared;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>III Invoice Pay Returned</label>
                <input id="full_name59" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="iii_invoice_pay_returned" data-name='ercfee_iii_invoice_pay_returned' value='<?php
                if (!empty($ercfee_iii_invoice_pay_returned)) {
                    $ercfee_iii_invoice_pay_returned = date(
                        "m/d/Y",
                        strtotime($ercfee_iii_invoice_pay_returned)
                    );
                }
                echo $ercfee_iii_invoice_pay_returned;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>III Invoice Return Reason</label>
                <input id="full_name61" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="iii_invoice_return_reason" data-name='ercfee_iii_invoice_return_reason' value='<?php echo $ercfee_iii_invoice_return_reason; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>III Invoice Occams Share</label>
                <input id="full_name62" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="iii_invoice_occams_share" data-name='ercfee_iii_invoice_occams_share' value='<?php echo $ercfee_iii_invoice_occams_share; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>III Invoice Aff/Ref Share</label>
                <input id="full_name63" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="iii_invoice_aff_ref_share" data-name='ercfee_iii_invoice_aff_ref_share' value='<?php echo $ercfee_iii_invoice_aff_ref_share; ?>'>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                    <h2>IV - Invoice Details</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>IV Invoice number</label>
                <input id="full_name64" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="iv_invoice_no" data-name='ercfee_iv_invoice_number' value='<?php echo $ercfee_iv_invoice_number; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>IV Invoice Amount</label>
                <input id="full_name65" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="iv_invoice_amount" data-name='ercfee_iv_invoice_amount' value='<?php echo $ercfee_iv_invoice_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>IV Invoiced Qtrs</label>
                <input id="full_name66" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="iv_invoiced_qtrs" data-name='ercfee_iv_invoiced_qtrs' value='<?php echo $ercfee_iv_invoiced_qtrs; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>IV Invoice Sent Date</label>
                <input id="full_name67" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="iv_invoice_sent_date" data-name='ercfee_iv_invoice_sent_date' value='<?php
                if (!empty($ercfee_iv_invoice_sent_date)) {
                    $ercfee_iv_invoice_sent_date = date(
                        "m/d/Y",
                        strtotime($ercfee_iv_invoice_sent_date)
                    );
                }
                echo $ercfee_iv_invoice_sent_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>IV Invoice Payment Type</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" id="2019 Tax Return" name="iv_invoice_payment_type" data-name='ercfee_iv_invoice_payment_type'>
                    <option value="">Select payment type</option>
                    <?php
                    $fees_section = [
                        "occams_initiated_eCheck" =>
                            "Occams Initiated - eCheck",
                        "occams_initiated_ach" =>
                            "Occams Initiated - ACH",
                        "occams_initiated_wire" =>
                            "Client Initiated - Wire",
                        "client_initiated_ach" =>
                            "Client Initiated - ACH",
                        "client_initiated_check_mailed" =>
                            "Client Initiated - Check Mailed",
                        "credit_card_or_debit_card" =>
                            "Credit Card or Debit Card",
                    ];

                    foreach ($fees_section as $inkey => $invalue) {
                        if (
                            $ercfee_iv_invoice_payment_type ==
                            $inkey
                        ) {
                            $selected =
                                "selected";
                        } else {
                            $selected = "";
                        } ?>
                                        <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                                    <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>IV Invoice Payment Date</label>
                <input id="full_name68" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="iv_invoice_payment_date" data-name='ercfee_iv_invoice_payment_date' value='<?php
                if (!empty($ercfee_iv_invoice_payment_date)) {
                    $ercfee_iv_invoice_payment_date = date(
                        "m/d/Y",
                        strtotime($ercfee_iv_invoice_payment_date)
                    );
                }
                echo $ercfee_iv_invoice_payment_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>IV Invoice Pay Cleared</label>
                <input id="full_name69" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="iv_invoice_pay_cleared" data-name='ercfee_iv_invoice_pay_cleared' value='<?php
                if (!empty($ercfee_iv_invoice_pay_cleared)) {
                    $ercfee_iv_invoice_pay_cleared = date(
                        "m/d/Y",
                        strtotime($ercfee_iv_invoice_pay_cleared)
                    );
                }
                echo $ercfee_iv_invoice_pay_cleared;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>IV Invoice Pay Returned</label>
                <input id="full_name71" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="iv_invoice_pay_returned" data-name='ercfee_iv_invoice_pay_returned' value='<?php
                if (!empty($ercfee_iv_invoice_pay_returned)) {
                    $ercfee_iv_invoice_pay_returned = date(
                        "m/d/Y",
                        strtotime($ercfee_iv_invoice_pay_returned)
                    );
                }
                echo $ercfee_iv_invoice_pay_returned;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>IV Invoice Return Reason</label>
                <input id="full_name72" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="iv_invoice_return_reason" data-name='ercfee_iv_invoice_return_reason' value='<?php echo $ercfee_iv_invoice_return_reason; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>IV Invoice Occams Share</label>
                <input id="full_name73" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="iv_invoice_occams_share" data-name='ercfee_iv_invoice_occams_share' value='<?php echo $ercfee_iv_invoice_occams_share; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>IV Invoice Aff/Ref Share</label>
                <input id="full_name74" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="iv_invoice_aff_ref_share" data-name='ercfee_iv_invoice_aff_ref_share' value='<?php echo $ercfee_iv_invoice_aff_ref_share; ?>'>
            </div>
        </div>
		
        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>V - Invoice Details</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>V Invoice number</label>
                <input id="full_name75" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="v_invoice_no" data-name='ercfee_v_invoice_number' value='<?php echo $ercfee_v_invoice_number; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>V Invoice Amount</label>
                <input id="full_name76" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="v_invoice_amount" data-name='ercfee_v_invoice_amount' value='<?php echo $ercfee_v_invoice_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>V Invoiced Qtrs</label>
                <input id="full_name77" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="v_invoiced_qtrs" data-name='ercfee_v_invoiced_qtrs' value='<?php echo $ercfee_v_invoiced_qtrs; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>V Invoice Sent Date</label>
                <input id="full_name78" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="v_invoice_sent_date" data-name='ercfee_v_invoice_sent_date' value='<?php
                if (!empty($ercfee_v_invoice_sent_date)) {
                    $ercfee_v_invoice_sent_date = date(
                        "m/d/Y",
                        strtotime($ercfee_v_invoice_sent_date)
                    );
                }
                echo $ercfee_v_invoice_sent_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>V Invoice Payment Type</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" id="2019 Tax Return" name="v_invoice_payment_type" data-name='ercfee_v_invoice_payment_type'>
                    <option value="">Select payment type</option>
                    <?php
                    $fees_section = [
                        "occams_initiated_eCheck" =>
                            "Occams Initiated - eCheck",
                        "occams_initiated_ach" =>
                            "Occams Initiated - ACH",
                        "occams_initiated_wire" =>
                            "Client Initiated - Wire",
                        "client_initiated_ach" =>
                            "Client Initiated - ACH",
                        "client_initiated_check_mailed" =>
                            "Client Initiated - Check Mailed",
                        "credit_card_or_debit_card" =>
                            "Credit Card or Debit Card",
                    ];

                    foreach ($fees_section as $inkey => $invalue) {
                        if (
                            $ercfee_v_invoice_payment_type ==
                            $inkey
                        ) {
                            $selected =
                                "selected";
                        } else {
                            $selected = "";
                        } ?>
                                        <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                                    <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>V Invoice Payment Date</label>
                <input id="full_name79" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="v_invoice_payment_date" data-name='ercfee_v_invoice_payment_date' value='<?php
                if (!empty($ercfee_v_invoice_payment_date)) {
                    $ercfee_v_invoice_payment_date = date(
                        "m/d/Y",
                        strtotime($ercfee_v_invoice_payment_date)
                    );
                }
                echo $ercfee_v_invoice_payment_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>V Invoice Pay Cleared</label>
                <input id="full_name80" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="v_invoice_pay_cleared" data-name='ercfee_v_invoice_pay_cleared' value='<?php
                if (!empty($ercfee_v_invoice_pay_cleared)) {
                    $ercfee_v_invoice_pay_cleared = date(
                        "m/d/Y",
                        strtotime($ercfee_v_invoice_pay_cleared)
                    );
                }
                echo $ercfee_v_invoice_pay_cleared;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>V Invoice Pay Returned</label>
                <input id="full_name81" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="v_invoice_pay_returned" data-name='ercfee_v_invoice_pay_returned' value='<?php
                if (!empty($ercfee_v_invoice_pay_returned)) {
                    $ercfee_v_invoice_pay_returned = date(
                        "m/d/Y",
                        strtotime($ercfee_v_invoice_pay_returned)
                    );
                }
                echo $ercfee_v_invoice_pay_returned;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>V Invoice Return Reason</label>
                <input id="full_name82" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="v_invoice_return_reason" data-name='ercfee_v_invoice_return_reason' value='<?php echo $ercfee_v_invoice_return_reason; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>V Invoice Occams Share</label>
                <input id="full_name83" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="v_invoice_occams_share" data-name='ercfee_v_invoice_occams_share' value='<?php echo $ercfee_v_invoice_occams_share; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>V Invoice Aff/Ref Share</label>
                <input id="full_name84" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="v_invoice_aff_ref_share" data-name='ercfee_v_invoice_aff_ref_share' value='<?php echo $ercfee_v_invoice_aff_ref_share; ?>'>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>VI - Invoice Details</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VI Invoice number</label>
                <input id="full_name85" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vi_invoice_no" data-name='ercfee_vi_invoice_number' value='<?php echo $ercfee_vi_invoice_number; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VI Invoice Amount</label>
                <input id="full_name86" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vi_invoice_amount" data-name='ercfee_vi_invoice_amount' value='<?php echo $ercfee_vi_invoice_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VI Invoiced Qtrs</label>
                <input id="full_name87" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vi_invoiced_qtrs" data-name='ercfee_vi_invoiced_qtrs' value='<?php echo $ercfee_vi_invoiced_qtrs; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VI Invoice Sent Date</label>
                <input id="full_name88" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="vi_invoice_sent_date" data-name='ercfee_vi_invoice_sent_date' value='<?php
                if (!empty($ercfee_vi_invoice_sent_date)) {
                    $ercfee_vi_invoice_sent_date = date(
                        "m/d/Y",
                        strtotime($ercfee_vi_invoice_sent_date)
                    );
                }
                echo $ercfee_vi_invoice_sent_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VI Invoice Payment Type</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" id="2019 Tax Return" name="vi_invoice_payment_type" data-name='ercfee_vi_invoice_payment_type'>
                    <option value="">Select payment type</option>
                    <?php
                    $fees_section = [
                        "occams_initiated_eCheck" =>
                            "Occams Initiated - eCheck",
                        "occams_initiated_ach" =>
                            "Occams Initiated - ACH",
                        "occams_initiated_wire" =>
                            "Client Initiated - Wire",
                        "client_initiated_ach" =>
                            "Client Initiated - ACH",
                        "client_initiated_check_mailed" =>
                            "Client Initiated - Check Mailed",
                        "credit_card_or_debit_card" =>
                            "Credit Card or Debit Card",
                    ];

                    foreach ($fees_section as $inkey => $invalue) {
                        if (
                            $ercfee_vi_invoice_payment_type ==
                            $inkey
                        ) {
                            $selected =
                                "selected";
                        } else {
                            $selected = "";
                        } ?>
                                        <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                                    <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VI Invoice Payment Date</label>
                <input id="full_name89" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="vi_invoice_payment_date" data-name='ercfee_vi_invoice_payment_date' value='<?php
                if (!empty($ercfee_vi_invoice_payment_date)) {
                    $ercfee_vi_invoice_payment_date = date(
                        "m/d/Y",
                        strtotime($ercfee_vi_invoice_payment_date)
                    );
                }
                echo $ercfee_vi_invoice_payment_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VI Invoice Pay Cleared</label>
                <input id="full_name90" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="vi_invoice_pay_cleared" data-name='ercfee_vi_invoice_pay_cleared' value='<?php
                if (!empty($ercfee_vi_invoice_pay_cleared)) {
                    $ercfee_vi_invoice_pay_cleared = date(
                        "m/d/Y",
                        strtotime($ercfee_vi_invoice_pay_cleared)
                    );
                }
                echo $ercfee_vi_invoice_pay_cleared;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VI Invoice Pay Returned</label>
                <input id="full_name91" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="vi_invoice_pay_returned" data-name='ercfee_vi_invoice_pay_returned' value='<?php
                if (!empty($ercfee_vi_invoice_pay_returned)) {
                    $ercfee_vi_invoice_pay_returned = date(
                        "m/d/Y",
                        strtotime($ercfee_vi_invoice_pay_returned)
                    );
                }
                echo $ercfee_vi_invoice_pay_returned;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VI Invoice Return Reason</label>
                <input id="full_name92" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vi_invoice_return_reason" data-name='ercfee_vi_invoice_return_reason' value='<?php echo $ercfee_vi_invoice_return_reason; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VI Invoice Occams Share</label>
                <input id="full_name93" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vi_invoice_occams_share" data-name='ercfee_vi_invoice_occams_share' value='<?php echo $ercfee_vi_invoice_occams_share; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VI Invoice Aff/Ref Share</label>
                <input id="full_name94" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vi_invoice_aff_ref_share" data-name='ercfee_vi_invoice_aff_ref_share' value='<?php echo $ercfee_vi_invoice_aff_ref_share; ?>'>
            </div>
        </div>

        <div class="row mb-3">
            <div class="col-sm-12 erc-project-view-title">
                <h2>VII - Invoice Details</h2>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VII Invoice number</label>
                <input id="full_name95" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vii_invoice_no" data-name='ercfee_vii_invoice_number' value='<?php echo $ercfee_vii_invoice_number; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VII Invoice Amount</label>
                <input id="full_name96" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vii_invoice_amount" data-name='ercfee_vii_invoice_amount' value='<?php echo $ercfee_vii_invoice_amount; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VII Invoiced Qtrs</label>
                <input id="full_name97" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vii_invoiced_qtrs" data-name='ercfee_vii_invoiced_qtrs' value='<?php echo $ercfee_vii_invoiced_qtrs; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VII Invoice Sent Date</label>
                <input id="full_name98" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="vii_invoice_sent_date" data-name='ercfee_vii_invoice_sent_date' value='<?php
                if (!empty($ercfee_vii_invoice_sent_date)) {
                    $ercfee_vii_invoice_sent_date = date(
                        "m/d/Y",
                        strtotime($ercfee_vii_invoice_sent_date)
                    );
                }
                echo $ercfee_vii_invoice_sent_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VII Invoice Payment Type</label>
                <select <?= $disabled ?> class="bank_account_type crm-erp-field form-control" name="vii_invoice_payment_type" id="2019 Tax Return" data-name='ercfee_vii_invoice_payment_type'>
                    <option value="">Select payment type</option>
                    <?php
                    $fees_section = [
                        "occams_initiated_eCheck" =>
                            "Occams Initiated - eCheck",
                        "occams_initiated_ach" =>
                            "Occams Initiated - ACH",
                        "occams_initiated_wire" =>
                            "Client Initiated - Wire",
                        "client_initiated_ach" =>
                            "Client Initiated - ACH",
                        "client_initiated_check_mailed" =>
                            "Client Initiated - Check Mailed",
                        "credit_card_or_debit_card" =>
                            "Credit Card or Debit Card",
                    ];

                    foreach ($fees_section as $inkey => $invalue) {
                        if (
                            $ercfee_vii_invoice_payment_type ==
                            $inkey
                        ) {
                            $selected =
                                "selected";
                        } else {
                            $selected = "";
                        } ?>
                                        <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                                    <?php
                    }
                    ?>
                </select>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VII Invoice Payment Date</label>
                <input id="full_name99" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="vii_invoice_payment_date" data-name='ercfee_vii_invoice_payment_date' value='<?php
                if (!empty($ercfee_vii_invoice_payment_date)) {
                    $ercfee_vii_invoice_payment_date = date(
                        "m/d/Y",
                        strtotime($ercfee_vii_invoice_payment_date)
                    );
                }
                echo $ercfee_vii_invoice_payment_date;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VII Invoice Pay Cleared</label>
                <input id="full_name100" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="vii_invoice_pay_cleared" data-name='ercfee_vii_invoice_pay_cleared' value='<?php
                if (!empty($ercfee_vii_invoice_pay_cleared)) {
                    $ercfee_vii_invoice_pay_cleared = date(
                        "m/d/Y",
                        strtotime($ercfee_vii_invoice_pay_cleared)
                    );
                }
                echo $ercfee_vii_invoice_pay_cleared;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VII Invoice Pay Returned</label>
                <input id="full_name101" class="crm-erp-field form-control date_field" placeholder="MM/DD/YYYY" type="text" <?= $readonly ?> name="vii_invoice_pay_returned" data-name='ercfee_vii_invoice_pay_returned' value='<?php
                if (!empty($ercfee_vii_invoice_pay_returned)) {
                    $ercfee_vii_invoice_pay_returned = date(
                        "m/d/Y",
                        strtotime($ercfee_vii_invoice_pay_returned)
                    );
                }
                echo $ercfee_vii_invoice_pay_returned;
                ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VII Invoice Return Reason</label>
                <input id="full_name102" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vii_invoice_return_reason" data-name='ercfee_vii_invoice_return_reason' value='<?php echo $ercfee_vii_invoice_return_reason; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VII Invoice Occams Share</label>
                <input id="full_name103" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vii_invoice_occams_share" data-name='ercfee_vii_invoice_occams_share' value='<?php echo $ercfee_vii_invoice_occams_share; ?>'>
            </div>
            <div class="floating col-sm-4 mb-3">
                <label>VII Invoice Aff/Ref Share</label>
                <input id="full_name104" class="crm-erp-field form-control" type="text" <?= $readonly ?> name="vii_invoice_aff_ref_share" data-name='ercfee_vii_invoice_aff_ref_share' value='<?php echo $ercfee_vii_invoice_aff_ref_share; ?>'>
            </div>
        </div>
		
    </fieldset>
</div>

<script>
    jQuery(document).ready(function() {
		jQuery(".date_field").datepicker({
			dateFormat: "mm/dd/yy",
		});
	});
</script>
<?php

exit;

?>