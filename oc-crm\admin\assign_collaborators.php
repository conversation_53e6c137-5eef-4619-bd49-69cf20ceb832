<?php
$project_assign_users = $wpdb->prefix.'collaborators';
$project_id = $_POST['project_id'];
$user_id = $_POST['user_id'];
$type = $_POST['type'];
if($type == 'assign-user'){
	$wpdb->query("INSERT INTO $project_assign_users (project_id,user_id) VALUES ($project_id,$user_id)");

        $audit_data = array(
         'DateCreated' => current_time('mysql'),
         'CreatedBy' => get_current_user_id(),
         'TableName' => $project_assign_users,
         'FieldName' => 'collaborators',
         'DataType' => gettype($user_id),
         'BeforeValueString' => '',
         'AfterValueString' => $user_id,
         'FieldID' => $project_id,
         'Action' => 'Add',
         );
                            // print_r($audit_data);
        // Insert the audit log entry
        $result = $wpdb->insert($wpdb->prefix . 'audit_logs', $audit_data);

}else{
	$wpdb->query("DELETE FROM $project_assign_users WHERE project_id = ".$project_id." AND user_id = ".$user_id."");

        $audit_data = array(
         'DateCreated' => current_time('mysql'),
         'CreatedBy' => get_current_user_id(),
         'TableName' => $project_assign_users,
         'FieldName' => 'collaborators',
         'DataType' => '',
         'BeforeValueString' => $user_id,
         'AfterValueString' => 0,
         'FieldID' => $project_id,
         'Action' => 'Remove',
         );
                            // print_r($audit_data);
        // Insert the audit log entry
        $result = $wpdb->insert($wpdb->prefix . 'audit_logs', $audit_data);
}
die;