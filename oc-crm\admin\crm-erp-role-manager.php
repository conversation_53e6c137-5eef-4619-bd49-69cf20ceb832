<?php
/**
 * Handles custom role creation and management for the plugin.
 */

class CRM_ERP_Role_Manager {

    /**
     * Add custom roles and capabilities when the plugin is activated.
     */
    public static function activate() {
        // Define custom capabilities for the plugin
        $capabilities = array(
            'manage_crm_erp' => true,
            'view_debug_logs'     => true,
            // ... other capabilities ...
        );

        // Add a new custom role with defined capabilities
        add_role('crm_erp_manager', __(' Manager', 'productsplugin-crm-erp'), $capabilities);

        // Optionally, add capabilities to existing roles
        $roles = array('administrator', 'editor'); // ... other roles as needed ...
        foreach ($roles as $role_name) {
            $role = get_role($role_name);
            if ($role) {
                foreach ($capabilities as $cap => $grant) {
                    $role->add_cap($cap, $grant);
                }
            }
        }
    }

    /**
     * Remove custom roles and capabilities when the plugin is deactivated.
     */
    public static function deactivate() {
        // Remove custom role
        remove_role('crm_erp_manager');

        // Optionally, remove capabilities from existing roles
        $roles = array('administrator', 'editor'); // ... other roles as needed ...
        $capabilities = array('manage_crm_erp', 'view_debug_logs'); // ... other capabilities ...
        foreach ($roles as $role_name) {
            $role = get_role($role_name);
            if ($role) {
                foreach ($capabilities as $cap) {
                    $role->remove_cap($cap);
                }
            }
        }
    }
}

// Activation and deactivation hooks
register_activation_hook(__FILE__, array('CRM_ERP_Role_Manager', 'activate'));
register_deactivation_hook(__FILE__, array('CRM_ERP_Role_Manager', 'deactivate'));







/*

Explanation:
Class Structure: CRM_ERP_Role_Manager handles the creation and removal of custom roles and capabilities.

Activation Method: The activate method is called when the plugin is activated. It adds a new role (crm_erp_manager) with specific capabilities. It can also add these capabilities to existing WordPress roles.

Deactivation Method: The deactivate method is called when the plugin is deactivated. It removes the custom role and its capabilities from all roles.

Capabilities: Define custom capabilities specific to your plugin. In this example, manage_crm_erp and view_debug_logs are custom capabilities. You can modify these based on your plugin's functionality.

Hooks: The register_activation_hook and register_deactivation_hook functions are used to handle the activation and deactivation of these roles. Note that __FILE__ should be replaced with the main plugin file path if this code is included in a separate file.

Additional Notes:
Custom roles and capabilities are a powerful feature in WordPress, allowing fine-grained access control within your plugin.
Be cautious when modifying existing roles, as changes will affect users with those roles across the site.
Always ensure that roles and capabilities are appropriately cleaned up on plugin deactivation to avoid leaving unused roles in the WordPress installation.

*/

