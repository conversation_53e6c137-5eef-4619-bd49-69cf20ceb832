<?php   

$current_user_id = get_current_user_id();
$user_data = get_user_by('id', $current_user_id);
$user_roles = $user_data->roles;

    $stcProductID = '937';
    $rdcProductID = '932';
    $taProductID = '936';

    $stcProspectingMilestoneID = '122';
    $rdcProspectingMilestoneID = '100';
    $taProspectingMilestoneID = '126';

    $stcClosedMilestoneID = '117';
    $taClosedMilestoneID = '117';
    $ercClosedMilestoneID = '117';
    $rdcClosedMilestoneID = '117';
    $stcCloseWonMilestoneID  = '116';

    $stcIdentifiedStageID = '243';
    $rdcIdentifiedStageID = '142';
    $taIdentifiedStageID = '310';

    $stcAgreementSignedbutPaymentPendingStageID = '320';
    $stcRetainerPaidButPendingAgreementStageID = '319';

    $rdcAgreementSentStageID = '326';
    $taAgreementSentStageID = '311';

    $taReopenStageID = '327';

    $rdcdocusignVoidStageID = '313';
    $rdcdocusignLapsedStageID = '314';
    $rdcdocusignsentStageID = '140';
    
    // $rdcClosingMilestoneID  = '108';
    // $rdcCloseWonMilestoneID  = '116';
    // $rdcCloseLostMilestoneID  = '117';
    
    $rdcClose = array(108,116,117);
    $opportunity_closure = $single_opportunity->opportunityClosure;
    
    $ercProductID = 935;

    if($single_opportunity->product_ID == $stcProductID){

        $hide_send_stc_agreement_button = 'hidebutton';
        if($opportunityStageID == $stcRetainerPaidButPendingAgreementStageID && $opportunityMilestoneID == $stcProspectingMilestoneID){
        if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles) || in_array("master_sales", $user_roles) || in_array("iris_sales_agent", $user_roles)|| in_array("iris_sales_agent", $user_roles)|| in_array("master_sales", $user_roles)) { 
            //&& ($single_opportunity->product_ID==935)
                $hide_send_stc_agreement_button = '';
            }
        }

        $last_agreement_time = '';
        if($oppstunityLastAgreementLinkTime){
            $last_agreement_time = date('F jS, Y, \a\\t H:i:s',strtotime($oppstunityLastAgreementLinkTime));
        }
        ?>
        <button class="Opp_Closure Opp_Closures send_stc_agreement <?php echo $hide_send_stc_agreement_button; ?>" data-eventtype="send_stc_agreement" data-last_agreement_sent='<?php  echo $last_agreement_time; ?>' style="display: none;">Send Agreement</button> 

        <?php
        $hidestcpaymetlinkbutton = 'hidebutton';
        // if(($opportunityStageID == $stcIdentifiedStageID || $opportunityStageID == $stcAgreementSignedbutPaymentPendingStageID) && $opportunityMilestoneID == $stcProspectingMilestoneID ){
        if($opportunityMilestoneID != $stcClosedMilestoneID && $opportunityMilestoneID !=$stcCloseWonMilestoneID){
            if (in_array("iris_affiliate_users", $user_roles) || in_array("administrator", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles)|| in_array("iris_sales_agent", $user_roles)|| in_array("master_sales", $user_roles)) { 
                    $hidestcpaymetlinkbutton = '';
            }
        }
        $last_payment_time = '';
        if($oppsLastPaymentLinkTime){
            $last_payment_time = date('F jS, Y, \a\\t H:i:s',strtotime($oppsLastPaymentLinkTime));
        }

        ?>   
        <button class="Opp_Closure Opp_Closures  send_stc_ssf <?php echo $hidestcpaymetlinkbutton;  ?>" data-eventtype="send_stc_ssf" data-last_payment_sent='<?php  echo $last_payment_time; ?>'>Send SSF</button> 



        <?php 
            $hide_stc_reopen_agreement_button = 'hidebutton';
            if($opportunityMilestoneID == $stcClosedMilestoneID){   
                if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles) || in_array("master_sales", $user_roles) || in_array("iris_sales_agent", $user_roles)|| in_array("iris_sales_agent", $user_roles)|| in_array("master_sales", $user_roles)) { 
                
                    $hide_stc_reopen_agreement_button = '';
                } 
            }

        ?>
            <button class="Opp_Closure Opp_Closures  reopen_opportunity <?php  echo $hide_stc_reopen_agreement_button; ?>" data-eventtype="reopen_stc_opportunity" >Reopen Opportunity</button> 

        <?php

        }elseif($single_opportunity->product_ID == $rdcProductID){
                if($opportunity_closure != 'opportunity_won' && $opportunity_closure !='opportunity_lose' && $opportunity_closure !='send_agreement'){
                    $closure_val = 0;
                }else{
                    $closure_val = 1;
                }

                if($opportunity_closure != 'opportunity_won' && $opportunity_closure !='opportunity_lose' && $opportunity_closure !='void_agreement'){
                    $won_closure_val = 0;
                }else{
                    $won_closure_val = 1;
                }

                if (in_array($opportunityMilestoneID, $rdcClose)){
                    $rdc_close = 1;
                }else{
                    $rdc_close = 0;
                }

                
                $hide_send_agreement_button = 'hidebutton';
                // if(($opportunityMilestoneID == $rdcProspectingMilestoneID && $opportunityStageID == $rdcIdentifiedStageID) || ($opportunityStageID == $rdcdocusignVoidStageID) || ($opportunityStageID == $rdcdocusignLapsedStageID) || ($envelop_id=='' && $rdc_close == 0  )){   

                  if($envelop_id=='' && $rdc_close == 0 && $closure_val == 0){  
                    if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles) || in_array("master_sales", $user_roles) || in_array("iris_sales_agent", $user_roles)) { 
                    
                        $hide_send_agreement_button = '';
                    } 
                 }
                ?>
                    <button class="Opp_Closure Opp_Closures  send_rdc_agreement <?php echo $hide_send_agreement_button; ?>" data-eventtype="send_rdc_agreement" >Send Agreement</button>

                <?php 

                $hide_resend_rdc_agreement_button = 'hidebutton';
                // if(($opportunityMilestoneID == $rdcProspectingMilestoneID && $opportunityStageID == $rdcAgreementSentStageID && !empty($oppstunityLastAgreementLinkTime)) || ($opportunityStageID == $rdcdocusignsentStageID) && $envelop_id!=''){   
                 if($envelop_id!='' && $rdc_close == 0 && $won_closure_val == 0){  
                    if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles) || in_array("master_sales", $user_roles) || in_array("iris_sales_agent", $user_roles)) { 
                    
                        $hide_resend_rdc_agreement_button = '';
                    } 
                }

                $last_agreement_time = '';
                if($oppstunityLastAgreementLinkTime){
                    $last_agreement_time = date('F jS, Y, \a\\t H:i:s',strtotime($oppstunityLastAgreementLinkTime));
                }
                ?>
                    <!-- <button class="Opp_Closure Opp_Closures  resend_rdc_agreement <?php //echo $hide_resend_rdc_agreement_button; ?>" data-eventtype="resend_rdc_agreement" data-envelop_id='<?php // echo $envelop_id;?>' data-last_agreement_sent='<?php // echo $last_agreement_time; ?>'>Resend Agreement</button> -->

                    <button class="resend_agreement_button <?php echo $hide_resend_rdc_agreement_button; ?>" >Resend Agreement</button>

                    <button class="Opp_Closure Opp_Closures  void_agreement <?php echo $hide_resend_rdc_agreement_button; ?>" data-eventtype="void_agreement" data-envelop_id='<?php  echo $envelop_id;?>'>Void Agreement</button>

                    <?php 
                        
                        $hide_erc_reopen_agreement_button = 'hidebutton';
                        
                        if($opportunityMilestoneID == $ercClosedMilestoneID || $opportunity_closure =='opportunity_lose'){   
                            if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles) || in_array("master_sales", $user_roles) || in_array("iris_sales_agent", $user_roles)|| in_array("iris_sales_agent", $user_roles)|| in_array("master_sales", $user_roles)) { 
                            
                                $hide_erc_reopen_agreement_button = '';
                            } 
                        }
                    ?>
                    
                    <button class="Opp_Closure Opp_Closures  reopen_opportunity <?php  echo $hide_erc_reopen_agreement_button; ?>" data-eventtype="reopen_rdc_opportunity" >Reopen Opportunity</button> 

            <?php
        }elseif($single_opportunity->product_ID == $taProductID){
            $hide_send_ta_agreement_button = 'hidebutton';
                if($opportunityMilestoneID == $taProspectingMilestoneID && ($opportunityStageID == $taIdentifiedStageID || $opportunityStageID == $taReopenStageID)  ){   
                    if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles) || in_array("master_sales", $user_roles) || in_array("iris_sales_agent", $user_roles)) { 
                    
                        $hide_send_ta_agreement_button = '';
                    } 
                }
                ?>
                    <button class="Opp_Closure Opp_Closures  send_ta_agreement <?php echo $hide_send_ta_agreement_button; ?>" data-eventtype="send_ta_agreement" >Send Agreement</button>

                <?php 

                $hide_resend_ta_agreement_button = 'hidebutton';
                if($opportunityMilestoneID == $taProspectingMilestoneID && $opportunityStageID == $taAgreementSentStageID && !empty($oppstunityLastAgreementLinkTime) ){   
                    if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles) || in_array("master_sales", $user_roles) || in_array("iris_sales_agent", $user_roles)) { 
                    
                        $hide_resend_ta_agreement_button = '';
                    } 
                }

                $last_agreement_time = '';
                if($oppstunityLastAgreementLinkTime){
                    $last_agreement_time = date('F jS, Y, \a\\t H:i:s',strtotime($oppstunityLastAgreementLinkTime));
                }
                ?>
                    <button class="Opp_Closure Opp_Closures  resend_ta_agreement <?php echo $hide_resend_ta_agreement_button; ?>" data-eventtype="resend_ta_agreement" data-last_agreement_sent='<?php  echo $last_agreement_time; ?>'>Resend Agreement</button>


                <?php 
                    $hide_reopen_agreement_button = 'hidebutton';
                    if($opportunityMilestoneID == $taClosedMilestoneID){   
                        if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles) || in_array("master_sales", $user_roles) || in_array("iris_sales_agent", $user_roles)) { 
                        
                            $hide_reopen_agreement_button = '';
                        } 
                    }

                ?>
                    <button class="Opp_Closure Opp_Closures  reopen_opportunity <?php  echo $hide_reopen_agreement_button; ?>" data-eventtype="reopen_opportunity" >Reopen Opportunity</button> 
            <?php

        }elseif($single_opportunity->product_ID == $ercProductID){
                    if($opportunity_closure != 'opportunity_won' && $opportunity_closure !='opportunity_lose' && $opportunity_closure !='send_agreement'){
                    $closure_val = 0;
                }else{
                    $closure_val = 1;
                }

                if($opportunity_closure != 'opportunity_won' && $opportunity_closure !='opportunity_lose' && $opportunity_closure !='void_agreement'){
                    $won_closure_val = 0;
                }else{
                    $won_closure_val = 1;
                }

                if (in_array($opportunityMilestoneID, $rdcClose)){
                    $rdc_close = 1;
                }else{
                    $rdc_close = 0;
                }

                
                $hide_send_agreement_button = 'hidebutton';
                // if(($opportunityMilestoneID == $rdcProspectingMilestoneID && $opportunityStageID == $rdcIdentifiedStageID) || ($opportunityStageID == $rdcdocusignVoidStageID) || ($opportunityStageID == $rdcdocusignLapsedStageID) || ($envelop_id=='' && $rdc_close == 0  )){   

                  if($envelop_id=='' && $rdc_close == 0 && $closure_val == 0){  
                    if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles) || in_array("master_sales", $user_roles) || in_array("iris_sales_agent", $user_roles)) { 
                    
                        $hide_send_agreement_button = '';
                    } 
                 }
                 //temp
                 // $hide_send_agreement_button = 'hidebutton';
                ?>
                    <button class="Opp_Closure Opp_Closures  send_erc_agreement <?php echo $hide_send_agreement_button; ?>" data-eventtype="send_erc_agreement" >Send Agreement</button>

                <?php 

                $hide_resend_rdc_agreement_button = 'hidebutton';
                // if(($opportunityMilestoneID == $rdcProspectingMilestoneID && $opportunityStageID == $rdcAgreementSentStageID && !empty($oppstunityLastAgreementLinkTime)) || ($opportunityStageID == $rdcdocusignsentStageID) && $envelop_id!=''){   
                 if($envelop_id!='' && $rdc_close == 0 && $won_closure_val == 0){  
                    if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles) || in_array("master_sales", $user_roles) || in_array("iris_sales_agent", $user_roles)) { 
                    
                        $hide_resend_rdc_agreement_button = '';
                    } 
                }

                $last_agreement_time = '';
                if($oppstunityLastAgreementLinkTime){
                    $last_agreement_time = date('F jS, Y, \a\\t H:i:s',strtotime($oppstunityLastAgreementLinkTime));
                }
                ?>
                    <!-- <button class="Opp_Closure Opp_Closures  resend_rdc_agreement <?php //echo $hide_resend_rdc_agreement_button; ?>" data-eventtype="resend_rdc_agreement" data-envelop_id='<?php // echo $envelop_id;?>' data-last_agreement_sent='<?php  //echo $last_agreement_time; ?>'>Resend Agreement</button> -->

                    <button class="resend_agreement_button <?php echo $hide_resend_rdc_agreement_button; ?>" >Resend Agreement</button>

                    <button class="Opp_Closure Opp_Closures  void_agreement <?php echo $hide_resend_rdc_agreement_button; ?>" data-eventtype="void_agreement" data-envelop_id='<?php  echo $envelop_id;?>'>Void Agreement</button>

                    <?php 
                        
                        $hide_erc_reopen_agreement_button = 'hidebutton';
                        
                        if($opportunityMilestoneID == $ercClosedMilestoneID || $opportunity_closure =='opportunity_lose'){   
                            if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles)|| in_array("master_ops", $user_roles) || in_array("master_sales", $user_roles) || in_array("iris_sales_agent", $user_roles)|| in_array("iris_sales_agent", $user_roles)|| in_array("master_sales", $user_roles)) { 
                            
                                $hide_erc_reopen_agreement_button = '';
                            } 
                        }
                    ?>
            <button class="Opp_Closure Opp_Closures  reopen_opportunity <?php  echo $hide_erc_reopen_agreement_button; ?>" data-eventtype="reopen_erc_opportunity" >Reopen Opportunity</button> 

        <?php
        }else{

        }

?>
