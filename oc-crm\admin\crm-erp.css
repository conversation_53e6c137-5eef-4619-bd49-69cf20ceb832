/* Start Search Popup */
.search_field img {
  width: 22px !important;
  padding: 0px !important;
  position: absolute !important;
  top: 20px;
  left: 40px;
}

#show_ac_search {
  color: #86898e;
  font-size: 14px !important;
  height: 35px !important;
  border: 0 !important;
  background: #ffffff;
  border-radius: 6px !important;
  border: 0 !important;
  font-weight: 400 !important;
  padding-left: 3%;
}

.popup-overlay.active {
  visibility: visible;
  text-align: center;
  z-index: 1000;
}

.popup-content {
  visibility: hidden;
}

.popup-content.active {
  visibility: visible;
}

.open {
  border: none !important;
  padding-left: 4% !important;
}

.popup-overlay button {
  display: inline-block;
  vertical-align: middle;
  border-radius: 30px;
  margin: 0.2rem;
  font-size: 1rem;
  color: #666666;
  background: #ffffff;
  border: 1px solid #666666;
}

.search_field {
  position: relative;
}

.search_field img {
  width: 22px !important;
  padding: 0px !important;
  position: absolute !important;
  top: 9px;
  right: 20px !important;
  left: inherit !important;
}

.open {
  padding: 4px !important;
  width: 100%;
}

.popup-overlay {
  visibility: hidden;
  position: absolute;
  background: #ffffff;
  border: 0;
  width: 50%;
  height: auto;
  left: 19px;
  top: 15px;
  padding: 10px;
  box-shadow: 5px 5px 5px #00000040 !important;
}

#overlay {
  position: fixed;
  display: none;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  cursor: pointer;
}

.search_header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ccc;
  padding-bottom: 0px;
  margin-bottom: 20px;
}

.search-popup-input-select {
  height: 30px !important;
  font-size: 16px !important;
  transition: border-color 0.2s ease !important;
  caret-color: var(--color__accent) !important;
  width: 100% !important;
}

.close {
  font-size: 20px;
  padding: 0px 10px !important;
  border-radius: 10px !important;
  font-weight: 700;
  height: 32px;
  opacity: 1;
  line-height: 30px;
}

.search-popup-submit-btn {
  border: 1px solid #ff5c00 !important;
  color: #fff !important;
  padding: 1px 15px !important;
  border-radius: 10px !important;
  box-shadow: 0px 0px 5px #0000001a !important;
  font-weight: 500 !important;
  background: #ff5c00 !important;
  width: auto !important;
  height: 32px !important;
}

.search-popup-reset-btn {
  font-size: 13px;
  border: transparent !important;
  padding: 1px 15px !important;
  border-radius: 10px !important;
  box-shadow: 0px 0px 5px #0000001a !important;
  font-weight: 700;
  height: 32px;
  opacity: 1;
  float: inherit !important;
  display: inline-block;
  line-height: 28px;
  margin-left: 15px;
  background: red !important;
  color: #fff !important;
}

.search-popup
  .select2-container--default
  .select2-results
  > .select2-results__options {
  overflow-x: hidden;
}

.search-popup .select2-container {
  width: inherit !important;
}

.search-popup
  .select2-container--default
  .select2-selection--single
  .select2-selection__rendered {
  text-align: left;
}

.search-popup .select2-container .select2-selection--single {
  height: 30px !important;
}

.search-popup
  .select2-container--default
  .select2-selection--single
  .select2-selection__rendered {
  line-height: 30px !important;
}

.search-popup
  .select2-container--default
  .select2-selection--single
  .select2-selection__arrow {
  height: 30px !important;
}

/* End Search Popup */

/* Start Date Filter */
.invoice_date_range {
  text-align: right;
}

.invoice_date_filter span {
  color: #000 !important;
  font-weight: 500;
  margin-left: 10px;
}

.product_date_filter span {
  color: #000 !important;
  font-weight: 500;
  margin-left: 10px;
}

/* End Date Filter */

::-webkit-scrollbar {
  width: 8px;
}

/* Start Scroll Bar */
::-webkit-scrollbar-track {
  width: 8px;
  background: white;
  border-left: 0px solid white;
  border-right: 0px solid white;
}

::-webkit-scrollbar-thumb {
  background: #1658a5;
  width: 0;
  height: 25%;
}

/* End Scroll Bar */

/* Start Common DataTable */
.dataTables_wrapper .dataTables_length {
  margin-bottom: 15px;
}

.dataTables_wrapper .dataTables_length select {
  width: 50px;
}

.dataTables_wrapper .dataTables_filter {
  margin-bottom: 15px;
}

.dataTables_wrapper .dataTable {
  margin-bottom: 15px;
}

.dataTables_wrapper .dataTable {
  border: 1px solid #e3e3e3;
}

.dataTables_wrapper .dataTable tr th,
.dataTables_wrapper .dataTable tr td {
  border: 1px solid #e3e3e3;
  font-size: 12px;
  padding: 8px 10px;
}

.dataTables_wrapper .dataTable .odd {
  background-color: #f6f7f7;
}

.dataTables_wrapper .dataTable .dataTables_info {
  margin-top: 15px;
  margin-bottom: 25px;
}

.dataTables_wrapper .dataTable .dataTables_paginate {
  margin-top: 15px;
  margin-bottom: 25px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.disabled,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover,
.dataTables_wrapper .dataTables_paginate .paginate_button.disabled:active {
  background: transparent !important;
  border: 0 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
  color: #fff !important;
  background: #ff5c00 !important;
  border: 0 !important;
}

.dataTables_wrapper .dataTables_paginate .paginate_button:hover {
  background: #ff5c00 !important;
  border: 0 !important;
}
/* End Common DataTable */

/* Start Loader*/
.loader_box {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading__text {
  font-weight: 500;
  font-size: 15px;
  color: #fff;
}

.loading__bar {
  position: relative;
  height: 10px;
  width: 12rem;
  background-color: #1758a8;
  border-radius: 1em;
  overflow: hidden;
}

.loading__bar::after {
  position: absolute;
  top: 0;
  left: 0;
  content: "";
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, #fff5, rgba(230, 230, 230, 0.891));
  animation: loading-animation 1.3s infinite;
  border-radius: 1em;
}

@keyframes loading-animation {
  0% {
    left: -50%;
  }

  100% {
    left: 150%;
  }
}
/* End Loader*/

.title_img h4 {
  color: #fff !important;
}
.p-15 {
  padding: 7px !important;
}

.add-opp-custom-icon {
  background: #ff5c00 0% 0% no-repeat padding-box !important;
  color: #fff !important;
  padding: 10px !important;
  border-radius: 10px !important;
  opacity: 1 !important;
  font-size: 15px;
  font-weight: 600;
  display: initial;
}

.status_btn_export:before {
  content: "\f56e" !important;
}

.crm_erp_btn {
  padding: 11px 50px;
  color: #ff5f00;
  font-size: 16px;
  font-weight: 600;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 3px 6px #00000029;
  border: 2px solid #ff5f00;
  border-radius: 11px;
}

.crm-erp-field {
  height: 46px !important;
  font-size: 16px !important;
  border-bottom: 0.1rem solid var(--input__border);
  transition: border-color 0.2s ease;
  caret-color: var(--color__accent);
  max-width: 100% !important;
}

.button-container {
  text-align: center;
  margin-top: 30px;
}

#form-response {
  color: red;
  margin-top: 10px;
  font-size: 15px;
}

.fade:not(.show) {
  opacity: 1;
}

.swal-button--confirm {
  pointer-events: none;
}

.swal-button-container:has(.swal-button--confirm) {
  cursor: pointer;
}

.select2-container--default .select2-results > .select2-results__options {
  overflow-x: hidden;
}

.select2-container {
  width: inherit !important;
}

.select2-container--default
  .select2-selection--single
  .select2-selection__rendered {
  text-align: left;
}

.select2-container .select2-selection--single {
  height: 46px !important;
}

.select2-container--default
  .select2-selection--single
  .select2-selection__rendered {
  line-height: 46px !important;
}

.select2-container--default
  .select2-selection--single
  .select2-selection__arrow {
  height: 46px !important;
}

.custom-crm-erp-product-report input#cb-select-all-1 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-product-report input#cb-select-all-2 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-product-report #cb {
  width: 3%;
}

.custom-crm-erp-product-report #ProductID {
  width: 8%;
}

.custom-crm-erp-product-report #Title {
  width: 10%;
}

.custom-crm-erp-product-report #SKU {
  width: 8%;
}

.custom-crm-erp-product-report #UnitPrice {
  width: 6%;
}

.custom-crm-erp-product-report #HourlyPrice {
  width: 7%;
}

.custom-crm-erp-product-report #UnitName {
  width: 7%;
}

.custom-crm-erp-product-report #OwnerName {
  width: 8%;
}

.custom-crm-erp-product-report #ProductType {
  width: 9%;
}

.custom-crm-erp-product-report #ProductCategory {
  width: 10%;
}

.custom-crm-erp-product-report #LaunchDate {
  width: 7%;
}

.custom-crm-erp-product-report #ExpiryDate {
  width: 7%;
}

.custom-crm-erp-product-report #Status {
  width: 5%;
}

.delete_product {
  font-size: 12px;
  border: 0;
  padding: 0.5rem !important;
  background: red;
  text-align: center;
  width: 30px;
  height: 30px;
  cursor: pointer;
  border-radius: 50% !important;
  line-height: 17px;
  color: #fff;
  display: inline-block;
}

input[type="file"]::file-selector-button {
  padding: 12px 16px;
}

.crm-erp-products-delete-swal .swal-content {
  text-align: left;
  margin-top: -62px;
}

.crm-erp-products-delete-swal .swal-text {
  margin-left: 38px;
}

.crm-erp-products-delete-swal .swal-footer {
  margin-top: 60px;
}

.product-preview {
  position: relative;
  display: block;
  margin-top: 10px;
  max-width: 200px;
}

.product-preview img {
  width: 100%;
  height: auto; /* Maintain aspect ratio */
  object-fit: cover; /* Cover the container */
}

.remove-icon {
  position: absolute;
  top: -37px;
  right: -13px;
  cursor: pointer;
  color: red;
  font-size: 50px;
}

.delete_unit_type {
  font-size: 12px;
  border: 0;
  padding: 0.5rem !important;
  background: red;
  text-align: center;
  width: 30px;
  height: 30px;
  cursor: pointer;
  border-radius: 50% !important;
  line-height: 17px;
  color: #fff;
  display: inline-block;
}

.custom-crm-erp-unit-report input#cb-select-all-1 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-unit-report input#cb-select-all-2 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-unit-report #cb {
  width: 3%;
}

.crm-erp-unit-type-delete-swal .swal-content {
  text-align: left;
  margin-top: -45px;
}

.crm-erp-unit-type-delete-swal .swal-text {
  margin-left: 38px;
}

.crm-erp-unit-type-delete-swal .swal-footer {
  margin-top: 50px;
}

.delete_product_type {
  font-size: 12px;
  border: 0;
  padding: 0.5rem !important;
  background: red;
  text-align: center;
  width: 30px;
  height: 30px;
  cursor: pointer;
  border-radius: 50% !important;
  line-height: 17px;
  color: #fff;
  display: inline-block;
}

.custom-crm-erp-prdouct-type-report input#cb-select-all-1 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-prdouct-type-report input#cb-select-all-2 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-prdouct-type-report #cb {
  width: 3%;
}

.crm-erp-products-types-delete-swal .swal-content {
  text-align: left;
  margin-top: -62px;
}

.crm-erp-products-types-delete-swal .swal-text {
  margin-left: 38px;
}

.crm-erp-products-types-delete-swal .swal-footer {
  margin-top: 60px;
}

.delete_category {
  font-size: 12px;
  border: 0;
  padding: 0.5rem !important;
  background: red;
  text-align: center;
  width: 30px;
  height: 30px;
  cursor: pointer;
  border-radius: 50% !important;
  line-height: 17px;
  color: #fff;
  display: inline-block;
}

.custom-crm-erp-prdocuut-categories-report input#cb-select-all-1 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-prdocuut-categories-report input#cb-select-all-2 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-prdocuut-categories-report #cb {
  width: 3%;
}
.crm-erp-products-categories-type-delete-swal .swal-content {
  text-align: left;
  margin-top: -45px;
}

.crm-erp-products-categories-type-delete-swal .swal-text {
  margin-left: 38px;
}

.crm-erp-products-categories-type-delete-swal .swal-footer {
  margin-top: 50px;
}

.delete_currency {
  font-size: 12px;
  border: 0;
  padding: 0.5rem !important;
  background: red;
  text-align: center;
  width: 30px;
  height: 30px;
  cursor: pointer;
  border-radius: 50% !important;
  line-height: 17px;
  color: #fff;
  display: inline-block;
}

.custom-crm-erp-currency-report input#cb-select-all-1 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-currency-report input#cb-select-all-2 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-currency-report #cb {
  width: 3%;
}

.crm-erp-currency-delete-swal .swal-content {
  text-align: left;
  margin-top: -62px;
}

.crm-erp-currency-delete-swal .swal-text {
  margin-left: 38px;
}

.crm-erp-currency-delete-swal .swal-footer {
  margin-top: 60px;
}
.custom-crm-erp-milestone-report input#cb-select-all-1 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-milestone-report input#cb-select-all-2 {
  margin-top: 0px !important;
  vertical-align: super;
}

.custom-crm-erp-milestone-report #cb {
  width: 3%;
}

.custom-crm-erp-milestone-report #milestone_id {
  width: 8%;
  padding-left: 0 !important;
}

.custom-crm-erp-milestone-report .milestone_id {
  padding-left: 0 !important;
}

.custom-crm-erp-milestone-report .column-milestone_id {
  padding-left: 0 !important;
}

.custom-crm-erp-milestone-report #milestone_name {
  width: 12%;
}

.custom-crm-erp-milestone-report #product_id {
  width: 21%;
}

.custom-crm-erp-milestone-report #map {
  width: 14%;
}

.custom-crm-erp-milestone-report #status {
  width: 6%;
}

.custom-crm-erp-milestone-report #created_at {
  width: 14%;
}

.custom-crm-erp-milestone-report .action.column-action {
  display: inline-flex;
}

.custom-active-inactive-btn {
  width: 90px !important;
}

.mile-popup-overlay.active {
  visibility: visible;
  z-index: 1000;
}

.mile-popup-content {
  visibility: hidden;
}

.mile-popup-content.active {
  visibility: visible;
}

.mile-close {
  font-size: 20px;
  padding: 0px 10px !important;
  border-radius: 10px !important;
  font-weight: 700;
  height: 32px;
  opacity: 1;
  line-height: 30px;
  cursor: pointer;
}

.mile-popup-overlay button {
  display: inline-block;
  vertical-align: middle;
  border-radius: 30px;
  margin: 0.2rem;
  font-size: 1rem;
  color: #666666;
  background: #ffffff;
  border: 1px solid #666666;
}

.mile-popup-overlay {
  visibility: hidden;
  position: absolute;
  background: #ffffff;
  border: 0;
  width: 50%;
  height: auto;
  left: 19px;
  top: 15px;
  padding: 10px;
  box-shadow: 5px 5px 5px #00000040 !important;
}

.mile-header {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #ccc;
  padding-bottom: 0px;
  margin-bottom: 20px;
}

#add-milestone {
  cursor: pointer;
}

#popup_product_id_chosen,
#search_product_id_chosen {
  width: 100% !important;
}

.chosen-single {
  height: 30px;
}

.chosen-container .chosen-results li {
    text-align: left !important;
}

.milestone-id-col {
  color: #0056b3;
  cursor: pointer;
}

.column-milestone_id {
  padding-left: 3% !important;
}

.delete_milestone {
  color: #a00;
}

.delete_milestone1,
#cancel_mile_button {
  background: red;
  border: 0;
  padding: 7px 12px;
  color: #fff;
  border-radius: 10px;
  margin-top: 2%;
  margin-left: 15px;
}

.crm-erp-milestones-delete-swal .swal-content {
  text-align: left;
  margin-top: -63px;
}

.crm-erp-milestones-delete-swal .swal-text {
  margin-left: 38px;
}

.crm-erp-milestones-delete-swal .swal-footer {
  margin-top: 60px !important;
}

.action_milestone,
.action_milestone:disabled {
  color: #ffffff !important;
  background: #057fbc !important;
  padding-top: 2% !important;
  width: 83%;
  padding: 0px;
}

.activate-btn,
.activate-btn:disabled {
  color: #ffffff !important;
  background: #057fbc !important;
  margin-top: 2% !important;
  width: 84%;
}

.checkbox-wrapper-46 input[type="checkbox"] {
  display: none;
  visibility: hidden;
}

.checkbox-wrapper-46 .cbx {
  margin: auto;
  -webkit-user-select: none;
  user-select: none;
  cursor: pointer;
}

.checkbox-wrapper-46 .cbx span {
  display: inline-block;
  vertical-align: middle;
  transform: translate3d(0, 0, 0);
}

.checkbox-wrapper-46 .cbx span:first-child {
  position: relative;
  width: 18px;
  height: 18px;
  border-radius: 3px;
  transform: scale(1);
  vertical-align: middle;
  border: 1px solid #9098a9;
  transition: all 0.2s ease;
}

.checkbox-wrapper-46 .cbx span:first-child svg {
  position: absolute;
  top: 3px;
  left: 2px;
  fill: none;
  stroke: #ffffff;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
  stroke-dasharray: 16px;
  stroke-dashoffset: 16px;
  transition: all 0.3s ease;
  transition-delay: 0.1s;
  transform: translate3d(0, 0, 0);
}

.checkbox-wrapper-46 .cbx span:first-child:before {
  content: "";
  width: 100%;
  height: 100%;
  background: #506eec;
  display: block;
  transform: scale(0);
  opacity: 1;
  border-radius: 50%;
}

.checkbox-wrapper-46 .cbx span:last-child {
  padding-left: 8px;
}

.checkbox-wrapper-46 .cbx:hover span:first-child {
  border-color: #506eec;
}

.checkbox-wrapper-46 .inp-cbx:checked + .cbx span:first-child {
  background: #506eec;
  border-color: #506eec;
  animation: wave-46 0.4s ease;
}

.checkbox-wrapper-46 .inp-cbx:checked + .cbx span:first-child svg {
  stroke-dashoffset: 0;
}

.checkbox-wrapper-46 .inp-cbx:checked + .cbx span:first-child:before {
  transform: scale(3.5);
  opacity: 0;
  transition: all 0.6s ease;
}

@keyframes wave-46 {
  50% {
    transform: scale(0.9);
  }
}

.custom-crm-erp-milestone-stage-report input#cb-select-all-1 {
    margin-top: 0px !important;
   vertical-align: super;
}

.custom-crm-erp-milestone-stage-report input#cb-select-all-2 {
   margin-top: 0px !important;
   vertical-align: super;
}

.custom-crm-erp-milestone-stage-report #cb {
   width: 3%;
}

.wp-core-ui select {
    line-height: normal !important;
}

.delete_next_step {
    font-size: 12px;
    border: 0;
    padding: 0.5rem !important;
    background: red;
    text-align: center;
    width: 30px;
    height: 30px;
    cursor: pointer;
    border-radius: 50% !important;
    line-height: 17px;
    color: #fff;
    display: inline-block;
}

.custom-crm-erp-next-step-report input#cb-select-all-1 {
    margin-top: 0px !important;
   vertical-align: super;
}

.custom-crm-erp-next-step-report input#cb-select-all-2 {
   margin-top: 0px !important;
   vertical-align: super;
}

.custom-crm-erp-next-step-report #cb {
   width: 3%;
}

.delete_opportunity {
    font-size: 12px;
    border: 0;
    padding: 0.5rem !important;
    background: red;
    text-align: center;
    width: 30px;
    height: 30px;
    cursor: pointer;
    border-radius: 50% !important;
    line-height: 17px;
    color: #fff;
    display: inline-block;
}

.crm-erp-next-step-delete-swal .swal-content {
    text-align: left;
    margin-top: -45px;
  }
  
  .crm-erp-next-step-delete-swal .swal-text {
    margin-left: 38px;
  }
  
  .crm-erp-next-step-delete-swal .swal-footer {
    margin-top: 50px;
  }

  .form-control:disabled, .form-control[readonly] {
    background-color: #e9ecef !important;
  }