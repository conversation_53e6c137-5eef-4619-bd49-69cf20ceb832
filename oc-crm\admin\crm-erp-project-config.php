<?php
/**
 * Create a new table class that will extend the WP_List_Table
 */
class CRM_ERP_Project_Config extends WP_List_Table {
    private $userRole; 
    private $limitPerpage;
    function __construct() {
        global $status, $page;
        $this->limitPerpage = 15;
        parent::__construct(array(
            'singular' => 'bulk-delete', //singular name of the listed records
            'plural' => 'bulk-deletes', //plural name of the listed records
            'ajax' => false
                //does this table support ajax?
        ));
    }
    public function column_default($item, $column_name) {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        switch ($column_name) {
            case 'cb':
            return '<input type="checkbox" name="deleteItem" value="'.$item['project_space_id'].'"/>';
            case 'project_space_id':
                return $item->$column_name;
            case 'product_name':
                return ucfirst($item->$column_name);
            case 'subsections_count':
                return ucfirst($item->$column_name);
            case 'subsections_name':
                return $item->$column_name;
            case 'Action':
                return $item->$column_name;
            default:
                return print_r($item, true);
        }
    }
    function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" class="chkbox" name="deleteItem[]" value="'.$item->project_space_id.'"/>',

            $this->_args['singular'], //Let's simply repurpose the table's singular label ("plugin")
            /* $2%s */
            $item->project_space_id //The value of the checkbox should be the record's id
        );
    }
    function column_project_space_id($item){
        //Build row actions
        $actions = array(
            'edit'      => sprintf('<a href="?page=add_edit_project_space&action=edit&id='.$item->project_space_id.'">Edit</a>',$_REQUEST['page'],'edit',$item->project_space_id),
        );
        
        //Return the title contents
        return sprintf('%1$s %2$s',$item->project_space_id,$this->row_actions($actions));
    }
    /**
     * Prepare the items for the table to process
     *
     * @return Void
     */
    public function prepare_items() {
        global $wpdb;
        //$wpdb->query("INSERT INTO eccom_projects (lead_id,product_id,project_name,milestone_id,milestone_stage_id,created_by) VALUES (8186,927,'Test Project',109,182,41571)");die;
        $products = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}crm_products WHERE {$wpdb->prefix}crm_products.DeletedAt IS NULL AND {$wpdb->prefix}crm_products.status = 'active' ORDER BY {$wpdb->prefix}crm_products.ProductID DESC");
        //$this->process_bulk_action();  
        ?>
        <form method="get" action="" id="search_all_form">
            <?php 
                $this->search_box_new($_GET); 
            ?>
            <div class="wrap woocommerce">
                <div class="white_card card_height_100 mb_30">
                    <div class="white_card_header">
                        <div class="box_header m-0 new_report_header">
                            <div class="title_img">
                                <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/products-icon.png" class="page-title-img" alt="">
                                <h4>Project Spaces</h4>
                            </div>
                            <div class="invoice_exports">
                                <a id="add-edit-project-space" class="add-opp-custom-icon"><i class="fa-solid fa-plus"></i> New Project Space</a>
                                <a href="javascript:void(0);" class="status_btn_export export_invoice_excel export_project_spaces">Export</a>
                            </div>
                        </div>
                    </div>
                    <div class="project-space-popup-overlay" style="left:17%; top:10%">
		                <div class="project-space-popup-content text-left" id="add-edit-project-space-form">
		                    <div class="project-space-header">
		                        <h4 id="project-space-header-h4">New Project Space</h4>
		                        <span class="project-space-close">
		                            <i class="fa-solid fa-xmark"></i>
		                        </span>
		                    </div>
		                    <div class="row mt-2">
		                        <div class="col-md-12">
		                            <input type="hidden" id="popup-project-space_id" name="project-space_id" value="0">
		                            <label>Products:</label>
		                            <select id="popup-product_id" name="product_id" class="form-control product_id-class">
		                            	<option value="">Select Product</option>
		                                <?php foreach($products as $product): ?>
		                                    <option id="<?php echo "product-".$product->ProductID; ?>" value="<?php echo $product->ProductID; ?>"><?php echo $product->Title; ?></option>
		                                <?php endforeach; ?>
		                            </select>
		                            <p class="add-prod-msg project-space-err" style="color:red;"></p>
		                        </div>
		                    </div>    
		                    <div class="row mt-2">
		                        <div class="col-md-12">
		                            <label>Subsections Count*:</label>
		                            <select id="popup-subsections_count" name="subsections_count" class="search-popup-input-select form-control">
		                                <option value="">Select Subsections Count</option>
		                                <option value="1">1</option>
		                                <option value="2">2</option>
		                                <option value="3">3</option>
		                                <option value="4">4</option>
		                                <option value="5">5</option>
		                                <option value="6">6</option>
		                                <option value="7">7</option>
		                                <option value="8">8</option>
		                            </select>
		                            <p class="add-subsections-count-msg project-space-error" style="color:red;"></p>
		                        </div>
		                    </div>
		                    <div class="project_tabs">

		                    </div>
		                    <div class="row mt-3">
		                        <div class="col-md-12 text-center">
		                        <input type="button" id="btn_project_space_submit" class="search-popup-submit-btn" value="Submit" />
		                        <input type="button" class="reset-project-space" value="Cancel" id="cancel_project_space_button" onclick="cancelProjectSpace()"/>
		                        <p class="add-project-space-msgs" style="color:red;"></p>
		                        </div>
		                    </div>  
		                </div>
		            </div>
                    <div class="loader_box" id="loader_box" style="display: none;">
                        <div class="loading">
                            <p class="loading__text">Please Wait. Deleting Project Space.</p>
                            <div class="loading__bar"></div>
                        </div>
                    </div>
                    <div class="white_card_body custom-crm-erp-product-report p-15" id="echeck_report_table_wrap">
                <?php
                $columns = $this->get_columns();
                $hidden = $this->get_hidden_columns();
                $sortable = $this->get_sortable_columns();
                $this->_column_headers = array(
                    $columns,
                    $hidden,
                    $sortable
                );
                //$this->process_bulk_action();
                $search = $_GET;
                $data = $this->table_data($search);
                usort($data, array($this, 'sort_data'));
                $perPage = $this->limitPerpage;
                $currentPage = $this->get_pagenum();
                $totalItems = count($data);
                $this->set_pagination_args(array(
                    'total_items' => $totalItems,
                    'per_page' => $perPage
                ));
                $data = array_slice($data, (($currentPage - 1) * $perPage), $perPage);
                $this->items = $data;
                $this->display();
                

                ?>
                    </div>
                </div>
            </div>
        </form>
        <script type="text/javascript">
            $(document).ready(function() {
                // Handler for the change event of #agreeCheckbox
                $(document).on('change', '#agreeCheckbox', function() {
                    // Check if #agreeCheckbox is checked
                    if ($(this).prop('checked')) {
                        $(".swal-button--confirm").css("pointer-events", "auto");
                    } else {
                        $(".swal-button--confirm").css("pointer-events", "none");
                    }
                });

                $(document).on('click', '.swal-button-container:has(.swal-button--confirm)', function() {
                    if ($('#agreeCheckbox').prop('checked')) {
                        return true;
                    } else {
                        // Check if error message already exists
                        if (!$('.swal-content + p.error-message').length) {
                            // Append the error message if it doesn't exist
                            $('.swal-content').after('<p class="error-message" style="color: red; margin: 40px;">Please agree to delete the project space!</p>');
                        }
                    }
                });
            });

            jQuery(document).on('click', '.delete_project_space', function () {
                swal({
                    title: "Are you sure?",
                    text: "Yes, I want to delete this project space. Related data will no longer be linked to the Selected project space.",
                    icon: "warning",
                    buttons: {
                        cancel: "Cancel",
                        confirm: "Delete",
                    },
                    content: {
                        element: "input",
                        attributes: {
                            type: "checkbox",
                            id: "agreeCheckbox"
                        },
                    },
                    dangerMode: true,
                })
                .then((willDelete) => {
                    if (willDelete && $('#agreeCheckbox').prop('checked')) {
                        $(this).attr('disabled', true);
                        $("#loader_box").show();
                        var project_space_id = jQuery(this).data('project_space_id');
                        jQuery.ajax({
                            url: '<?php echo admin_url('admin-ajax.php'); ?>',
                            method: 'post',
                            data: { action: 'delete_project_space', project_space_id: project_space_id },
                            success(response) {
                                location.reload(true);
                            }
                        }); // ajax
                    }
                    $('.swal-modal').removeClass('crm-erp-project-space-delete-swal');
                });
                $('.swal-modal').addClass('crm-erp-project-space-delete-swal');
            });

            jQuery(document).on("click", ".export_project_spaces",function(){
                jQuery(this).text('Please wait..');
                jQuery(this).css('pointer-events','none');
                var product_id = jQuery("#product_id").val();
                jQuery.ajax({
                    url:'<?php echo admin_url('admin-ajax.php'); ?>',
                    method:'post',
                    data:{action: 'export_project_spaces',product_id: product_id},
                    success(response){
                        jQuery(".export_project_spaces").text('Export');
                        jQuery(".export_project_spaces").css('pointer-events','');
                        var downloadLink = document.createElement("a");
                        var responseData = jQuery.trim(response);
                        var fileData = ['\ufeff'+responseData];
                        var blobObject = new Blob(fileData,{
                        type: "text/csv;charset=utf-8;"
                        });
                        var url = URL.createObjectURL(blobObject);
                        downloadLink.href = url;
                        var currentDate = new Date();
                        var dateString = currentDate.toISOString().slice(0, 10); // YYYY-MM-DD
                        var timeString = currentDate
                            .toLocaleTimeString("en-US", {
                                hour12: false,
                                hour: "2-digit",
                                minute: "2-digit",
                                second: "2-digit",
                            })
                            .replace(/:/g, "-");
                        var filename = "Project_Spaces_" + dateString + "_" + timeString + ".csv";
                        downloadLink.download = filename;
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                    }
                })
            }) 
            jQuery(document).on("change", "#popup-subsections_count", function(){
            	var subsections_count = jQuery(this).val();
            	jQuery(".project_tabs .mt-2").remove();
            	var tab_html = '';
            	for(i=1; i<=subsections_count; i++){
            		if(i%2 == 0){
            			var row_class = '';
            		}else{
            			var row_class = 'row';
            		}
            		tab_html += '<div class="'+row_class+' col-md-6 mt-2"><label>Tab '+i+' Name*:</label><input class="form-control" name="tab'+i+'" id="tab'+i+'"><p class="tab'+i+'-msg project-space-error" style="color:red;"></p></div></div>';
            	}
            	jQuery(".project_tabs").append(tab_html);
            })
            jQuery(document).on("click", "#btn_project_space_submit",function() {
                var button_txt = jQuery(this).val();
                var product = jQuery('#popup-product_id').val();
                var subsections_count = jQuery('#popup-subsections_count').val();
                var project_space_id = jQuery('#popup-project-space_id').val();
                
                if(product==''){
                    jQuery('.add-prod-msg').html("Please select Product.").show();
                }else if(subsections_count==''){
                    jQuery('.add-prod-msg').html("").hide();
                    jQuery('.add-subsections-count-msg').html("Please select subsections count.").show();
                }else{
	            	var tabs_value = 1;
	            	var tabs_name = [];
	            	for(i=1; i<=subsections_count; i++){
	            		tab_value = jQuery("#tab"+i).val();
	            		if(tab_value == ''){
	            			jQuery(".tab"+i+"-msg").html('Please input Tab '+i+' name');
	            			tabs_value = 0;
	            		}else{
	            			tabs_name.push(tab_value);
	            			//tabs_name += tab_value+',';
	            		}
	            	}
	            	if(tabs_value == 1){
	            		var strLen = tabs_name.length;
	            		//tabs_name = tabs_name.slice(0,strLen-1);
	                    jQuery(this).val('Loading..').prop('disabled',true);
	                    jQuery('.project-space-error').html("").hide();
	                    jQuery.ajax({
                        url:"<?php echo site_url().'/wp-json/productsplugin/v1/check-product-unique'; ?>",
                        method:'post',
                        data:{product_id:product,project_space_id:project_space_id},
                        success(response){
                            if(response.status==400){
                                jQuery('.add-edit-project-space-msgs').html("Project space already created for this product.").show();
                                jQuery('#btn_project_space_submit').val(button_txt).prop('disabled',false);
                            }else{
                                create_update_project_space(product,subsections_count,project_space_id,tabs_name);
                            }// check
                           }//check success 
                        });//check ajax 
	                }
                }// validation
            });// click 
            function create_update_project_space(product,subsections_count,project_space_id,tabs_name){
                if(project_space_id==0){
                    var title = "Project Space Created Successfully.";
                }else{
                    var title = "Project Space Updated Successfully.";
                }
                jQuery.ajax({
                    url:'<?php echo admin_url('admin-ajax.php'); ?>',
                    method:'post',
                    data:{action: 'project_space_submit',product_id:product,subsections_count:subsections_count,tabs_name:tabs_name,project_space_id:project_space_id},
                    success(response){
                        // Hide Add/Edit Product Popup
                        $(".project-space-popup-overlay, .project-space-popup-content").removeClass("active");
                        $('#overlay').hide();
                        swal({
                             title: title,
                            //  text: text,
                             icon: 'success',
                             type: 'success'
                        });
                        location.reload(true);
                    }
                 });// ajax
            }
        </script>
    <?php
    }
    public function get_columns() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        $columns = array(
            'cb' => '<input type="checkbox" />',
            'project_space_id' => 'ID',
            'product_name' => 'Product',
            'subsections_count' => 'Sub Sections',
            'subsections_name' => 'Subsections Name',
            'Action' => 'Action'
        );
        //$columns['action'] = 'Action';
        return $columns;
    }
    public function get_sortable_columns() {
        $sortable = array(
            'project_space_id'     => array('project_space_id',true),
            'product_name'     => array('product_name',true),
            'subsections_count'     => array('subsections_count',true),
            'subsections_name'     => array('subsections_name',true),
        );
        return $sortable;
    }
    function get_bulk_actions() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
          if(in_array('administrator', $user_roles) || in_array('echeck_staff', $user_roles) || in_array('echeck_client', $user_roles) || in_array('master_sales', $user_roles)){ 
            return array(
                'delete' => __( 'Delete', ''),
            );
          }else{
            return array();
          }      
    }
    function process_bulk_action() {
        if ('delete' === $this->current_action()) {
            $nonce = esc_attr($_REQUEST['_wpnonce']);
            if (isset($_REQUEST['action2']) && $_REQUEST['action2']=='delete'){
                if(!empty($_REQUEST['deleteItem'])){
                    $project_space_manager = new CRM_ERP_Project_Space_Manager();
                    foreach($_REQUEST['deleteItem'] as $singleProjectSpace){
                        $project_space_manager->delete_project_space($singleProjectSpace);
                    }
                    wp_redirect('?page=project_spaces');
                    exit;
                }
                
            }
        } 
    }
    /**
     * Define which columns are hidden
     *
     * @return Array
     */

    public function get_hidden_columns() {
        return array();
    }

    /**

     * Get the table data

     *

     * @return Array

     */

    private function table_data($search) {
        global $wpdb;
        if( (isset($search['product_title']) && $search['product_title'] != '')){
            $project_space_manager = new CRM_ERP_Project_Space_Manager();
            $project_space_data = $project_space_manager->search_project_spaces($search);
        }else{
            $project_space_manager = new CRM_ERP_Project_Space_Manager();
            $project_space_data = $project_space_manager->get_project_spaces();
        }
        if(!empty($project_space_data)){
            foreach($project_space_data as $key => $projectspacedata){
                $projectspacedata->Action = '<button data-project_space_id ="'.$projectspacedata->project_space_id.'"  class="delete_project_space" type="button" title="Remove"> <i class="fa-solid fa-trash"></i></button>';
            }
        }
        return $project_space_data;
    }
    /**
     * Allows you to sort the data by the variables set in the $_GET
     *
     * @return Mixed
     */
    private function sort_data($a, $b) {
        // Set defaults
        $orderby = isset($_GET['orderby']) ? sanitize_text_field($_GET['orderby']) : 'project_space_id';
        $order = isset($_GET['order']) ? sanitize_text_field($_GET['order']) : 'desc';
        
        $result = strnatcmp($a->$orderby, $b->$orderby);
    
        return ($order === 'asc') ? $result : -$result;
    }

    protected function pagination( $which ) {
        if ( empty( $this->_pagination_args ) ) {
            return;
        }
        $total_items     = $this->_pagination_args['total_items'];
        $total_pages     = $this->_pagination_args['total_pages'];
        $infinite_scroll = false;
        if ( isset( $this->_pagination_args['infinite_scroll'] ) ) {
            $infinite_scroll = $this->_pagination_args['infinite_scroll'];
        }
        if ( 'top' === $which && $total_pages > 1 ) {
            $this->screen->render_screen_reader_content( 'heading_pagination' );
        }
        $output = '<span class="displaying-num">' . sprintf(
            /* translators: %s: Number of items. */
            _n( '%s item', '%s items', $total_items ),
            number_format_i18n( $total_items )
        ) . '</span>';
        $current              = $this->get_pagenum();
        $removable_query_args = wp_removable_query_args();
        $current_url = set_url_scheme( 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] );
        $current_url = remove_query_arg( $removable_query_args, $current_url );
        $page_links = array();
        $total_pages_before = '<span class="paging-input">';
        $total_pages_after  = '</span></span>';
        $disable_first = false;
        $disable_last  = false;
        $disable_prev  = false;
        $disable_next  = false;
        if ( 1 == $current ) {
            $disable_first = true;
            $disable_prev  = true;
        }
        if ( 2 == $current ) {
            $disable_first = true;
        }
        if ( $total_pages == $current ) {
            $disable_last = true;
            $disable_next = true;
        }
        if ( $total_pages - 1 == $current ) {
            $disable_last = true;
        }
        if ( $disable_first ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&laquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='first-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( remove_query_arg( 'paged', $current_url ) ),
                __( 'First page' ),
                '&laquo;'
            );
        }
        if ( $disable_prev ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&lsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='prev-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( 'paged', max( 1, $current - 1 ), $current_url ) ),
                __( 'Previous page' ),
                '&lsaquo;'
            );
        }
        if ( 'bottom' === $which ) {
            $html_current_page  = $current;
            $total_pages_before = '<span class="screen-reader-text">' . __( 'Current Page' ) . '</span><span id="table-paging" class="paging-input"><span class="tablenav-paging-text">';
        } else {
            $html_current_page = sprintf(
                "%s<input class='current-page' id='current-page-selector' type='text' name='paged' value='%s' size='%d' aria-describedby='table-paging' /><span class='tablenav-paging-text'>",
                '<label for="current-page-selector" class="screen-reader-text">' . __( 'Current Page' ) . '</label>',
                $current,
                strlen( $total_pages )
            );
        }
        $html_total_pages = sprintf( "<span class='total-pages'>%s</span>", number_format_i18n( $total_pages ) );
        $page_links[]     = $total_pages_before . sprintf(
            _x( '%1$s of %2$s', 'paging' ),
            $html_current_page,
            $html_total_pages
        ) . $total_pages_after;
        if ( $disable_next ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&rsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='next-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=> min( $total_pages, $current + 1 )), $current_url ) ),
                __( 'Next page' ),
                '&rsaquo;'
            );
        }
        if ( $disable_last ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&raquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='last-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=>$total_pages), $current_url ) ),
                __( 'Last page' ),
                '&raquo;'
            );
        }
        $pagination_links_class = 'pagination-links';
        if ( ! empty( $infinite_scroll ) ) {
            $pagination_links_class .= ' hide-if-js';
        }
        $output .= "\n<span class='$pagination_links_class'>" . implode( "\n", $page_links ) . '</span>';
        if ( $total_pages ) {
            $page_class = $total_pages < 2 ? ' one-page' : '';
        } else {
            $page_class = ' no-pages';
        }
        $this->_pagination = "<div class='tablenav-pages{$page_class}'>$output</div>";
        echo $this->_pagination;
    }
    public function search_box_new($search_data)
    {
    ?>
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
        <?php
        global $wpdb;
        ?>
        
        <input type="hidden" name="page" value="crm_products">
        <div id="overlay" onclick="overlay_off()"></div>
        <div class="row align-items-center mb-3">
            <div class="col-md-3 search_field">
                <img src="https://design.echeck.money/portal/wp-content/plugins/echeck-theme111/assets/img/icon/Search.svg" alt="" class="icon">
                <input type="text" placeholder="Search.." id="show_lead_search" autocomplete="off" class="open">
                <input type="submit" style="visibility: hidden;position:absolute;">
            </div>
            <div class="col-md-9 invoice_date_range" style="margin-top: 10px;">
                <p class="invoice_date_filter">
                    <span>Date From : <input type="text" id="date_timepicker_start" class="date_from" value="<?php if(isset($_GET['start_date'])){ echo $_GET['start_date']; }  ?>" name="start_date" pattern="\d{2}/\d{2}/\d{4}" placeholder="MM/DD/YYYY" autocomplete="off" onkeydown="return false"></span>
                    <span>Date To : <input type="text" id="date_timepicker_end" value="<?php if(isset($_GET['end_date'])){ echo $_GET['end_date']; }  ?>" name="end_date" class="date_to" pattern="\d{2}/\d{2}/\d{4}" placeholder="MM/DD/YYYY" autocomplete="off" onkeydown="return false"></span>
                    <input type="submit" class="button product_date_submit" value="Submit">
                    <input type="button" class="button product_date_submit" id="date_timepicker_reset" value="Reset">
                </p>
            </div>
        </div>
        <div class="popup-overlay search-popup">
            <div class="popup-content">
                <div class="search_header">
                    <h4>Search</h4>
                    <span class="close">
                        <i class="fa-solid fa-xmark"></i>
                    </span>
                </div>
                <div class="row mt-2">
                    <div class="col-md-6">
                        <input type="text" id="product_title" name="product_title" placeholder="Product Title" value="<?php if (isset($search_data['product_title'])) {echo $search_data['product_title'];} ?>" class="search-popup-input-select" >
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-12 text-center">
                    <input type="submit" id="btn_search_submit" class="search-popup-submit-btn" value="Search" />
                    <input type="button" class="search-popup-reset-btn" value="Clear" id="reset_Form_button" onclick="resetAffForm()"/>
                    </div>
                </div>  
            </div>
        </div>
        <script>
         jQuery(document).ready(function () {
            jQuery('.date_from').datetimepicker({
                format: 'm/d/Y',
                autoclose: true,
                orientation: 'bottom',
                timepicker: false,
                autocomplete: 'off',
                maxDate : 'now',
                onChangeDateTime:function(dp,$input){
                    console.log(new Date($('.date_to').val()));
                    if (new Date($('.date_to').val()) < new Date($input.val()) ) {
                        alert("Date To can not be less than Date from");
                        $('.date_to').val('');
                        $('.date_from').val('');
                    }
                }
            });
            jQuery('.date_to').datetimepicker({
                format: 'm/d/Y',
                autoclose: true,
                orientation: 'bottom',
                timepicker: false,
                autocomplete: 'off',
                maxDate : 'now',
                onChangeDateTime:function(dp,$input){
                    console.log(new Date($('.date_from').val()));
                    if (new Date($input.val() ) < new Date($('.date_from').val()) ) {
                        alert("Date To can not be less than Date from");
                        $('.date_to').val('');
                        $('.date_from').val('');
                    }
                }
            });
         });
        jQuery(".open").on("click", function() {
          jQuery('.status_box').hide(); 
          jQuery(".popup-overlay, .popup-content").addClass("active");
          jQuery(".search_lead_id").focus();
          jQuery('#overlay').show();
        });

        jQuery(".close").on("click", function() {
          jQuery(".popup-overlay, .popup-content").removeClass("active");
          jQuery('#overlay').hide();
        });

        function overlay_off(){
          jQuery(".close").trigger("click");
          jQuery('#overlay').hide();
        }
        function resetAffForm(){
            jQuery("#reset_Form_button").val('Clearing...');
            var site_url = '<?php  echo get_site_url() ?>';
            window.location.href = site_url+'/wp-admin/admin.php?page=project_spaces';
        }
        jQuery(document).on("click",'#doaction2', function(e) {
            e.preventDefault();
            var is_check = 0;
            $(".chkbox").each(function(){
                if($(this).is(":checked")){
                    is_check = 1;
                }
            })
            if($('#bulk-action-selector-bottom').val()=='delete'){
                if(is_check == 1){
                    swal({
                        title: "Are you sure?",
                        text: "Yes, I want to delete Selected Project Spaces.",
                        icon: "warning",
                        buttons: {
                            cancel: "Cancel",
                            confirm: "Delete",
                        },
                        content: {
                            element: "input",
                            attributes: {
                                type: "checkbox",
                                id: "agreeCheckbox"
                            },
                        },
                        dangerMode: true,
                    })
                    .then((willDelete) => {
                        if (willDelete && $('#agreeCheckbox').prop('checked')) {
                            $(this).attr('disabled',true);
                            $("#loader_box").show();
                            $('#search_all_form').submit();
                        }
                        $('.swal-modal').removeClass('crm-erp-products-delete-swal');
                    });
                    $('.swal-modal').addClass('crm-erp-products-delete-swal');
                }else{
                    swal({
                        title: "Please select atleast one checkbox",
                        text: "",
                        icon: "warning",
                        buttons: {
                            cancel: "Cancel",
                        },
                        dangerMode: true,
                    })
                    //alert('Please select some option.');
                }
            }else{ 
                swal({
                        title: "Please select some action.",
                        text: "",
                        icon: "warning",
                        buttons: {
                            cancel: "Cancel",
                        },
                        dangerMode: true,
                    })
            }
        });
        jQuery(document).on("click", "#add-edit-project-space",function() {
            jQuery('#project-space-header-h4').html('New Project Space');
            jQuery("#btn_project_space_submit").val("Submit");
            jQuery('#popup-product_id').val('');
            jQuery('#popup-subsections_count').val('');
            $('.project-space-popup-overlay .inp-cbx').prop('checked', false); // Uncheck all checkboxes
            $('.project-space-err, .project-space-error, .add-edit-project-space-msgs').html("").hide();

	        jQuery('.status_box').hide(); 
	        jQuery(".project-space-popup-overlay, .project-space-popup-content").addClass("active");
	        //jQuery("input[name=milestone_name]").focus();
	        jQuery('#overlay').show();
        });
        jQuery(".project-space-close").on("click", function() {
          jQuery(".project-space-popup-overlay, .project-space-popup-content").removeClass("active");
          jQuery('#overlay').hide();
        });

        function cancelProjectSpace(){ 
            jQuery(".project-space-popup-overlay, .project-space-popup-content").removeClass("active");
            jQuery('#overlay').hide();
        }
        </script>
    <?php
    }
}
?>