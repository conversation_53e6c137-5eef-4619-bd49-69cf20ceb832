<?php
global $wpdb;
$lead_id = $_GET['lead_id'];

$readonly = "";
$disabled = "";

$ercbi_bank_name = "";
$ercbi_bank_mailing_address = "";
$ercbi_city = "";
$ercbi_state = "";
$ercbi_zip = "";
$ercbi_country = "";
$ercbi_bank_phone = "";
$ercbi_account_holder_name = "";
$ercbi_account_type = "";
$ercbi_other = "";
$ercbi_aba_routing_number = "";
$ercbi_account_number = "";
$ercbi_swift = "";
$ercbi_iban = "";

// ----------------- Get data from db ------
global $wpdb;

// Bank Info Code here
$bank_data_arr = [
    "ercbi_bank_name" => "bank_name",
    "ercbi_bank_mailing_address" => "bank_mailing_address",
    "ercbi_city" => "city",
    "ercbi_state" => "state",
    "ercbi_zip" => "zip",
    "ercbi_country" => "country",
    "ercbi_bank_phone" => "bank_phone",
    "ercbi_account_holder_name" => "account_holder_name",
    "ercbi_account_type" => "account_type",
    "ercbi_other" => "other",
    "ercbi_aba_routing_number" => "aba_routing_no",
    "ercbi_account_number" => "account_number",
    "ercbi_swift" => "swift",
    "ercbi_iban" => "iban",
];
foreach ($bank_data_arr as $var_name => $column_name) {
    $leadData = $wpdb->get_row(
        "SELECT $column_name FROM {$wpdb->prefix}erc_bank_info WHERE lead_id = $lead_id"
    );
    if (!empty($leadData)) {
        $column_val = $leadData->$column_name;
    } else {
        $column_val = "";
    }
    $$var_name = $column_val;
} // bank loop

?>

<div class="row"> 
    <div class="col-sm-12">
               <h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;">Bank Info</h5>
           </div>
    <div class="floating col-md-6">
         <input name='bank_name' value='<?php echo $ercbi_bank_name; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Bank Name">
        <label class="floating__label" data-content="Bank Name"></label> 
        <span class="error" id="last_nameErr"></span>
    </div>
    <div class="floating col-md-6">
         <input name='bank_mailing_address' value='<?php echo $ercbi_bank_mailing_address; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Bank Mailing Address">
        <label class="floating__label" data-content="Bank Mailing Address"></label> 
        <span class="error" id="last_nameErr"></span>
    </div>
    <div class="floating col-sm-3">
         <input class="floating__input form-control" type="text" name='bank_city' value='<?php echo $ercbi_city; ?>' <?= $readonly ?> placeholder=" City*">
        <label class="floating__label" data-content=" City*"></label> 
    </div>
    <div class="floating col-sm-3">
         <input class="floating__input form-control" type="text"  name='bank_state' value='<?php echo $ercbi_state; ?>' <?= $readonly ?> placeholder=" State*">
        <label class="floating__label" data-content=" State*"></label> 
    </div>
    <div class="floating col-sm-3">
         <input class="floating__input form-control" type="text" name='bank_zip' value='<?php echo $ercbi_zip; ?>' <?= $readonly ?> placeholder=" Zip*">
        <label class="floating__label" data-content=" Zip*"></label> 
    </div>
    <div class="floating col-sm-3">
         <input class="floating__input form-control" type="text" name='country' value='<?php echo $ercbi_country; ?>' <?= $readonly ?> placeholder=" Country">
        <label class="floating__label" data-content=" Country"></label> 
    </div>
    <div class="floating col-md-3">
         <input name='bank_phone' value='<?php echo str_replace(
             "-",
             "",
             $ercbi_bank_phone
         ); ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Bank Phone">
        <label class="floating__label" data-content="Bank Phone"></label> 
        <span class="error" id="last_nameErr"></span>
    </div>
    <div class="floating col-sm-3">
        <input name='account_holder_name' value='<?php echo $ercbi_account_holder_name; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Account Holder Name">
        <label class="floating__label" data-content="Account Holder Name"></label> 
        <span class="error" id="last_nameErr"></span>
    </div>
    <div class="floating col-md-3">
        <select <?= $disabled ?> class='floating__input form-control' name='account_type' >
            <option value="1">N/A</option>
              <?php
              $account_type = [
                  "2" => "Savings",
                  "3" => "Checking",
                  "4" => "Other",
              ];
              foreach ($account_type as $akey => $avalue) {
                    if (
                      $ercbi_account_type ==
                      $avalue
                    ) {
                      $selected = "selected";
                    } else {
                          $selected = "";
                    } ?>
                    <option value='<?= $akey ?>' <?= $selected ?>><?php echo $avalue; ?></option>
                   <?php } ?>
        </select>
        <label class="floating__label" data-content="Account Type"></label> 
        <span class="error" id="last_nameErr"></span>
    </div>
    <div class="floating col-sm-3">
        <input name='other' value='<?php echo $ercbi_other; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Other">
        <label class="floating__label" data-content="Other"></label> 
        <span class="error" id="last_nameErr"></span>
    </div>
    <div class="floating col-md-3">
         <input name='aba_routing_no' value='<?php echo $ercbi_aba_routing_number; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="ABA Routing number">
        <label class="floating__label" data-content="ABA Routing number"></label> 
        <span class="error" id="last_nameErr"></span>
    </div>
    <div class="floating col-md-3">
         <input  name='account_number' value='<?php echo $ercbi_account_number; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Account Number">
        <label class="floating__label" data-content="Account Number"></label> 
        <span class="error" id="last_nameErr"></span>
    </div>
    <div class="floating col-md-3">
         <input name='swift' value='<?php echo $ercbi_swift; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="SWIFT">
        <label class="floating__label" data-content="SWIFT"></label> 
        <span class="error" id="last_nameErr"></span>
    </div> 
    <div class="floating col-md-3">
         <input name='iban' value='<?php echo $ercbi_iban; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="IBAN">
        <label class="floating__label" data-content="IBAN"></label> 
        <span class="error" id="last_nameErr"></span>
    </div> 
</div>
                                                    

<?php  

exit;

?>