<?php
global $wpdb, $apiInstance;

// $source = $apiInstance->leadsSourcesGet();
// $groups = $apiInstance->leadsGroupsGet();

$source = array();
$groups = array();

if (isset($source['data'])) {
    $source_array = (array)$source['data'];
}

if (isset($groups['data'])) {
    $groups_array = (array)$groups['data'];
}

$current_user_id = get_current_user_id();
$userdata = get_user_by('id', $current_user_id);
$user_roles = $userdata->roles;

// 43341   anupam.satyasheel
// 43613   josh.westfall 
// 43342   david.king
// 43456   master Sales
// 44025   master ops

$userdisable ="readonly";
if($current_user_id == 44025 || $current_user_id == 43341 || $current_user_id == 43613 || $current_user_id == 43342 || $current_user_id ==43456){
    $userdisable = "";
}

$opportunityID = '';

if(isset($_GET['id']) && $_GET['id'] != ''){
    $opportunity_data = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}opportunities WHERE OpportunityID = " . $_GET['id'] . "");

    if (!empty($opportunity_data)) {
        $opportunityID = $opportunity_data->OpportunityID;
        $product_id = $single_opportunity->product_ID;
        $old_business_legal_name = $opportunity_data->business_name;
        $old_business_email = $opportunity_data->business_email;
        $old_ercagr_state = $opportunity_data->state_of_formation;
        $old_ercarg_address = $opportunity_data->street_address;
        $old_ercarg_zip = $opportunity_data->zip;
        $old_ercarg_city = $opportunity_data->city;
        $old_ercarg_state = $opportunity_data->state;
        $old_tax_year = $opportunity_data->tax_years;
        $old_eligible_employee = $opportunity_data->eligible_employees;
        $old_authorized_signatory = $opportunity_data->authorized_signatory;
        $old_auths_mobile_number = $opportunity_data->authorized_signatory_phone;
        $old_business_title = $opportunity_data->business_title;
        $old_hrms_value = $opportunity_data->hrms;
        $old_envelop_id = $opportunity_data->envelop_id;
    }
}
$disabled = 'disabled';
$user_type = 0;
// check cmb user functionality
$cmb_user = 0;
$cmb_disable = '';

$source_map_table = $wpdb->prefix . "affiliate_source_mapping";
$source_data = $wpdb->get_row("SELECT * FROM $source_map_table WHERE affiliate_id=" . $current_user_id);
$group_id = $source_data->group_id;

if (in_array("fprs_account_executive", $user_roles) || in_array("fprs_sales_agent", $user_roles)) {
    $cmb_user = 1;
    $cmb_disable = 'disabled';
} else if (in_array("iris_affiliate_users", $user_roles) || in_array("iris_employee", $user_roles)) {

    if ($group_id == 20 || $group_id == 6) {
        $cmb_user = 1;
        $cmb_disable = 'disabled';
    }
}

$send_erc_agreement = 0;
$SourceUserData = $wpdb->get_row("SELECT send_erc_agreement FROM {$wpdb->prefix}erc_iris_source_users WHERE user_id = " . $current_user_id . "");
if (!empty($SourceUserData)) {
    $send_erc_agreement = $SourceUserData->send_erc_agreement;
}

$addition_table_name = $wpdb->prefix . 'erc_iris_leads_additional_info';
$addition_data = $wpdb->get_row("SELECT * FROM $addition_table_name WHERE lead_id = '$lead_id'");
$envelop_id = '';
$lead_status = '';
$category = '';
$group = '';
$source = '';
if ($addition_data) {
    $envelop_id = $addition_data->envelop_id;
    $lead_status = $addition_data->lead_status;
    $category = $addition_data->category;
    $group = $addition_data->lead_group;
    $source = $addition_data->source;
    $hrms_value = $addition_data->hrms_value;
    $sales_agent_id = $addition_data->sales_user_id;
}

$sales_agent_email = '';
if (!empty($sales_agent_id)) {
    $salesuserdata = get_user_by('id', $sales_agent_id);
    $sales_agent_email = $salesuserdata->user_email;
}

$disabled = '';
$ercagr_business_legal_name = '';
$ercagr_auths_mobile_number = '';
$ercarg_address = '';
$ercarg_city = '';
$ercarg_state = '';
$ercarg_zip = '';
$ercbusi_country = '';
$ercagr_business_name = '';
$ercagr_authorized_signatory = '';
$ercagr_sales_agent_name = '';
$ercagr_sales_agent_email = '';
$ercagr_bank_name = '';
$ercagr_bank_mailing_address = '';
$ercagr_city = '';
$ercagr_state = '';
$ercagr_zip = '';
$ercagr_country = '';
$ercagr_bank_phone = '';
$ercagr_account_holder_name = '';
$ercagr_account_type = '';
$ercagr_aba_routing_number = '';
$ercagr_account_number = '';
$ercagr_w2_employees_count = '';
$business_email = '';
$business_info_data = array(
    'ercagr_business_legal_name' => 'business_legal_name',
    'ercagr_auths_mobile_number' => 'business_phone',
    'ercarg_address' => 'street_address',
    'ercarg_city' => 'city',
    'ercarg_state' => 'state',
    'ercagr_state' => 'state',
    'ercarg_zip' => 'zip',
    'ercbusi_country' => 'country',
    'ercagr_business_name' => 'business_title',
    'ercagr_authorized_signatory' => 'authorized_signatory_name',
    'business_email' => 'business_email'
);
foreach ($business_info_data as $var_name => $column_name) {
    if ($column_name != '') {
        $leadData = $wpdb->get_row("SELECT $column_name FROM {$wpdb->prefix}erc_business_info WHERE lead_id = $lead_id");
        if (!empty($leadData)) {
            $column_val = $leadData->$column_name;
        } else {
            $column_val = '';
        }
        $$var_name = $column_val;
    } else {
        $$var_name = '__N/A';
    }
}

//Business Details
$business_details = array('ercagr_sales_agent_name' => 'sales_agent_name', 'ercagr_sales_agent_email' => 'sales_agent_email');
foreach ($business_details as $var_name => $column_name) {
    if ($column_name != '') {
        $leadData = $wpdb->get_row("SELECT $column_name FROM {$wpdb->prefix}erc_business_details WHERE lead_id = $lead_id");
        if (!empty($leadData)) {
            $column_val = $leadData->$column_name;
        } else {
            $column_val = '';
        }
        $$var_name = $column_val;
    } else {
        $$var_name = '__N/A';
    }
}

// Bank Info Code here 
$bank_data_arr = array(
    'ercagr_bank_name' => 'bank_name',
    'ercagr_bank_mailing_address' => 'bank_mailing_address',
    'ercagr_city' => 'city',
    'ercagr_zip' => 'zip',
    'ercagr_country' => 'country',
    'ercagr_bank_phone' => 'bank_phone',
    'ercagr_account_holder_name' => 'account_holder_name',
    'ercagr_account_type' => 'account_type',
    'ercagr_aba_routing_number' => 'aba_routing_no',
    'ercagr_account_number' => 'account_number'
);

foreach ($bank_data_arr as $var_name => $column_name) {
    $leadData = $wpdb->get_row("SELECT $column_name FROM {$wpdb->prefix}erc_bank_info WHERE lead_id = $lead_id");
    if (!empty($leadData)) {
        $column_val = $leadData->$column_name;
    } else {
        $column_val = '';
    }
    $$var_name = $column_val;
} // bank loop

$erc_intake_data_arr = array(
    'ercagr_w2_employees_count' => 'w2_employees_count'
);
foreach ($erc_intake_data_arr as $var_name => $column_name) {
    $leadData = $wpdb->get_row("SELECT $column_name FROM {$wpdb->prefix}erc_erc_intake WHERE lead_id = $lead_id");
    if (!empty($leadData)) {
        $column_val = $leadData->$column_name;
    } else {
        $column_val = '';
    }
    $$var_name = $column_val;
} // Erc intake loop

$fee_table = $wpdb->prefix . 'erc_erc_fees';
$fee_detail = $wpdb->get_row("SELECT success_fee, retainer_completion_fee,selected_fee_type FROM $fee_table WHERE lead_id = '" . $lead_id . "'");

$suc_fee = '';
$compl_fee = '';
$selected_fee_type = '';

if (!empty($fee_detail)) {
    $suc_fee = $fee_detail->success_fee;
    $compl_fee = $fee_detail->retainer_completion_fee;
    $selected_fee_type = $fee_detail->selected_fee_type;
}

$agreement_type = 1;

$custom_agree_access = 0;
$user_id = get_current_user_id();
$sales_access_data = $wpdb->get_row("SELECT custom_agreement_access FROM {$wpdb->prefix}erc_sales_team WHERE userid=" . $user_id . "");

$custom_agreement_access = 0;
if (isset($sales_access_data)) {
    $custom_agreement_access = $sales_access_data->custom_agreement_access;
}

$readonly = 'readonly';
if ($custom_agreement_access == 1 || in_array("administrator", $user_roles)) {
    $custom_agree_access = 1;
    $readonly = "";
}
$cmb_or_sbam_source = 0;
if ($group == 'ERC - CMB') {
    $agreement_type = 3;
    $cmb_or_sbam_source = 1;
}
if ($source == 'SBAM') {
    $agreement_type = 2;
    $cmb_or_sbam_source = 2;
}

$erc_product_id=935;

$current_product_id = $single_opportunity->product_ID;

$rdc_product_id=932;
if(isset($single_opportunity->product_ID)&&!empty($single_opportunity->product_ID)&&$single_opportunity->product_ID==$rdc_product_id){ // for RDC product
    $agreement_type = 4;
}

$aa_agreement_product_id=934;
if(isset($single_opportunity->product_ID)&&!empty($single_opportunity->product_ID)&&$single_opportunity->product_ID==$aa_agreement_product_id){ // for AA Agreement product
    $agreement_type = 5;
}

$stc_product_id=937;
if(isset($single_opportunity->product_ID)&&!empty($single_opportunity->product_ID)&&$single_opportunity->product_ID==$stc_product_id){ // for STC product
    $agreement_type = 6;
}



$send_agreement_type = '';
$field_disable = "";
if ($cmb_or_sbam_source == 0) {
    $send_agreement_type = 'standard';
} else if ($cmb_or_sbam_source == 1) {
    $send_agreement_type = 'cmb';
} else if ($cmb_or_sbam_source == 2) {
    $send_agreement_type = 'sbam';
    if ($custom_agree_access != 1) {
        $field_disable = "readonly";
    }
}
$master_sales_access = 0;
if ($custom_agreement_access == 1) {
    if ($send_agreement_type == 'sbam') {
        $master_sales_access = 1;
        $field_disable = "";
        $readonly = "";
    }
}
?>

<div class="modal send-aggrements-add-new" id="add-new-opp-send-aggrements" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-lg modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-file-lines"></i> <?php echo ucwords($single_opportunity->OpportunityName); ?> Agreement</h5>
                <button type="button" class="close-popup-send-aggrements close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                    <form name="send_erc_aggrement_form " id="send_erc_aggrement_form" method="post">
                            <div class="row">
                                <div class="floating col-sm-6 mb-3">
                                    <label>Business Name*</label>
                                    <input type="hidden" name="retainer_fee_type" id="retainer_fee_type-val" value="1">
                                    <input type="hidden" name="sales_agent_email" value="<?php echo $sales_agent_email; ?>">
                                    <input type="hidden" name="opportunity_id" value="<?php echo $opportunityID; ?>">
                                    <input type="hidden" name="opportunity_product_id" value="<?php echo $product_id; ?>">
                                    <input type="hidden" name="erc_aggregate_service_fee" id="erc_aggregate_service_fee" value="">
                                    <input type="hidden" name="erc_calculate_fee_per" id="erc_calculate_fee_per" value="0">

                                    <input id="ercagr_retainer_fee" type="hidden" name="ercagr_retainer_fee">
                                    <input class="crm-erp-field form-control" type="text" data-name='ercagr_business_legal_name' name="ercagr_business_legal_name" value='<?php echo $ercagr_business_legal_name; ?>' id="business_name">
                                    <span id="business_nameErr" class="error"></span>
                                </div>

                                <div class="floating col-sm-6 mb-3">
                                    <label>Business Email Address</label>
                                    <input class="crm-erp-field form-control" id="business_email" name="business_email" type="text" value='<?php echo $business_email; ?>' readonly>
                                </div>
                                <div class="floating col-sm-6 mb-3" id="state_of_incorporation_clm">
                                    <label>State of Formation/Incorporation</label>
                                    <input class="crm-erp-field form-control" id="state_of_incorporation" type="text" data-name='ercagr_state' name="ercagr_state" value='<?php echo $ercagr_state; ?>'>
                                    <span id="state_of_incorporationErr" class="error"></span>
                                </div>

                                <div class="floating col-sm-6 mb-3" id="ercarg_address_clm">
                                    <label>Street Address</label>
                                    <input class="crm-erp-field form-control" type="text" data-name='ercarg_address' name="ercarg_address" value='<?php echo $ercarg_address; ?>' id="business_address">
                                    <span class="error" id="business_addressErr"></span>
                                </div>

                                <div class="floating col-sm-6 mb-3" id="ercarg_zip_clm">
                                    <label>Zip</label>
                                    <input class="crm-erp-field form-control" type="number" min="1" data-name='ercarg_zip' name="ercarg_zip" value='<?php echo $ercarg_zip; ?>' id="zip">
                                    <span class="error" id="zipErr"></span>
                                </div>

                                <div class="floating col-sm-6 mb-3" id="ercarg_city_clm">
                                    <label>City</label>
                                    <input class="crm-erp-field form-control" type="text" data-name='ercarg_city' name="ercarg_city" value='<?php echo $ercarg_city; ?>' id="city">
                                    <span class="error" id="cityErr"></span>
                                </div>

                                <div class="floating col-sm-6 mb-3" id="ercarg_state_clm">
                                    <label>State</label>
                                    <input class="crm-erp-field form-control" type="text" data-name='ercarg_state' name="ercarg_state" value='<?php echo $ercarg_state; ?>' id="state">
                                    <span class="error" id="stateErr"></span>
                                </div>
                                <?php
                                if(isset($single_opportunity->product_ID)&&!empty($single_opportunity->product_ID)&&$single_opportunity->product_ID==$rdc_product_id){ // for RDC product
                                ?>
                                <div class="floating col-sm-6 mb-3">
                                    <label>Tax Years Covered</label>
                                    <select class="crm-erp-field form-control tax_year_covered" id="tax_year_covered" data-name='tax_year_covered' name="tax_year_covered[]" multiple>
                                    <?php 
                                        $year = date('Y');
                                        for ($i=2019; $i <= $year; $i++) {  ?>
                                            <option value="<?php echo $i; ?>"><?php echo $i; ?></option>
                                    <?php } ?>
                                    </select>
                                    <span id="tax_yearErr" class="error"></span>
                                </div>
                                <?php }else{ ?>    
                                <div class="floating col-sm-6 mb-3" id="eligi_employee_clm">
                                    <label>Estimated # of Eligible Employees</label>
                                    <input class="crm-erp-field form-control" type="number" min="0" id="eligi_employee" data-name='ercagr_estimated_emp' name="ercagr_estimated_emp" value='<?php echo $ercagr_w2_employees_count; ?>'>
                                    <span id="eligi_employeeErr" class="error"></span>
                                </div>
                                <?php } ?>    
                                <div class="floating col-sm-6 mb-3">
                                    <label>Full Name of Authorized Signatory*</label>
                                    <input class="crm-erp-field form-control" id="authorized_signatory_name" type="text" data-name='ercagr_authorized_signatory' name="ercagr_authorized_signatory" value='<?php echo $ercagr_authorized_signatory; ?>'>
                                    <span id="authorized_signatory_nameErr" class="error"></span>
                                </div>

                                <div class="floating col-sm-6 mb-3">
                                    <label>Authorized Signatory Mobile Phone*</label>
                                    <input class="crm-erp-field form-control" id="auth_sign_phone" min="0" type="number" data-name='ercagr_auths_mobile_number' name="ercagr_auths_mobile_number" value='<?php
                                        $ercagr_auths_mobile_number = str_replace("-", "", $ercagr_auths_mobile_number);
                                        echo $ercagr_auths_mobile_number; ?>'>
                                    <span class="error" id="auth_sign_phoneErr"></span>
                                </div>

                                <div class="floating col-sm-6 mb-3">
                                    <label>Business Title e.g. CEO/CFO/Director/Owner*</label>
                                    <input class="crm-erp-field form-control" id="business_title" type="text" data-name='ercagr_business_name' name="ercagr_business_name" value='<?php echo $ercagr_business_name; ?>'>
                                    <span id="business_titleErr" class="error"></span>
                                </div>

                                <div class="floating col-sm-6 mb-3" id="hrms_value_clm">
                                    <label>HRMS</label>
                                    <select class="crm-erp-field form-control" id="hrms_value" name="hrms_value" placeholder="HRMS">
                                        <option value="38" <?php if ($hrms_value == 38) echo "selected"; ?>>ADP TotalSource</option>
                                        <option value="2" <?php if ($hrms_value == 2) echo "selected"; ?>>ADP Workforce Now</option>
                                        <option value="49" <?php if ($hrms_value == 49) echo "selected"; ?>>ADP Streamline Payroll</option>
                                        <option value="25" <?php if ($hrms_value == 25) echo "selected"; ?>>APS Payroll Solution</option>
                                        <option value="3" <?php if ($hrms_value == 3) echo "selected"; ?>>BambooHR</option>
                                        <option value="4" <?php if ($hrms_value == 4) echo "selected"; ?>>Bob (HiBob)</option>
                                        <option value="26" <?php if ($hrms_value == 26) echo "selected"; ?>>BrightPay</option>
                                        <option value="40" <?php if ($hrms_value == 40) echo "selected"; ?>>CheckMark Payroll</option>
                                        <option value="17" <?php if ($hrms_value == 17) echo "selected"; ?>>Ceridian Dayforce</option>
                                        <option value="46" <?php if ($hrms_value == 46) echo "selected"; ?>>Execupay</option>
                                        <option value="34" <?php if ($hrms_value == 34) echo "selected"; ?>>Evolution Payroll</option>
                                        <option value="35" <?php if ($hrms_value == 35) echo "selected"; ?>>EPAY Systems</option>
                                        <option value="30" <?php if ($hrms_value == 30) echo "selected"; ?>>Fingercheck</option>
                                        <option value="5" <?php if ($hrms_value == 5) echo "selected"; ?>>Gusto</option>
                                        <option value="6" <?php if ($hrms_value == 6) echo "selected"; ?>>Humaans</option>
                                        <option value="31" <?php if ($hrms_value == 31) echo "selected"; ?>>Heartland Payroll</option>
                                        <option value="50" <?php if ($hrms_value == 50) echo "selected"; ?>>Inova Payroll</option>
                                        <option value="33" <?php if ($hrms_value == 33) echo "selected"; ?>>Intuit Online Payroll</option>
                                        <option value="37" <?php if ($hrms_value == 37) echo "selected"; ?>>iSolved</option>
                                        <option value="27" <?php if ($hrms_value == 27) echo "selected"; ?>>Justworks</option>
                                        <option value="42" <?php if ($hrms_value == 42) echo "selected"; ?>>KashFlow</option>
                                        <option value="18" <?php if ($hrms_value == 18) echo "selected"; ?>>Kronos Workforce Ready</option>
                                        <option value="39" <?php if ($hrms_value == 39) echo "selected"; ?>>MyPayrollHR</option>
                                        <option value="15" <?php if ($hrms_value == 15) echo "selected"; ?>>Namely</option>
                                        <option value="23" <?php if ($hrms_value == 23) echo "selected"; ?>>OnPay</option>
                                        <option value="11" <?php if ($hrms_value == 11) echo "selected"; ?>>Patriot Payroll</option>
                                        <option value="1" <?php if ($hrms_value == 1) echo "selected"; ?>>Payroll Providers</option>
                                        <option value="7" <?php if ($hrms_value == 7) echo "selected"; ?>>Paychex</option>
                                        <option value="13" <?php if ($hrms_value == 13) echo "selected"; ?>>Paycor</option>
                                        <option value="24" <?php if ($hrms_value == 24) echo "selected"; ?>>Paylocity</option>
                                        <option value="28" <?php if ($hrms_value == 28) echo "selected"; ?>>PrimePay</option>
                                        <option value="29" <?php if ($hrms_value == 29) echo "selected"; ?>>Payroll4Free</option>
                                        <option value="32" <?php if ($hrms_value == 32) echo "selected"; ?>>PayUSA</option>
                                        <option value="47" <?php if ($hrms_value == 47) echo "selected"; ?>>PenSoft Payroll</option>
                                        <option value="8" <?php if ($hrms_value == 8) echo "selected"; ?>>QuickBooks Payroll</option>
                                        <option value="22" <?php if ($hrms_value == 22) echo "selected"; ?>>Rippling</option>
                                        <option value="9" <?php if ($hrms_value == 9) echo "selected"; ?>>Square Payroll</option>
                                        <option value="12" <?php if ($hrms_value == 12) echo "selected"; ?>>SurePayroll</option>
                                        <option value="19" <?php if ($hrms_value == 19) echo "selected"; ?>>Sage Payroll</option>
                                        <option value="41" <?php if ($hrms_value == 41) echo "selected"; ?>>Sage HRMS</option>
                                        <option value="51" <?php if ($hrms_value == 51) echo "selected"; ?>>Sage 300 Construction and Real Estate</option>
                                        <option value="43" <?php if ($hrms_value == 43) echo "selected"; ?>>StarGarden HR Suite</option>
                                        <option value="45" <?php if ($hrms_value == 45) echo "selected"; ?>>SimpleX Payroll</option>
                                        <option value="16" <?php if ($hrms_value == 16) echo "selected"; ?>>TriNet</option>
                                        <option value="20" <?php if ($hrms_value == 20) echo "selected"; ?>>Ultimate Software UltiPro</option>
                                        <option value="48" <?php if ($hrms_value == 48) echo "selected"; ?>>Workforce PayHub</option>
                                        <option value="36" <?php if ($hrms_value == 36) echo "selected"; ?>>Workday Payroll</option>
                                        <option value="44" <?php if ($hrms_value == 44) echo "selected"; ?>>Wagepoint</option>
                                        <option value="10" <?php if ($hrms_value == 10) echo "selected"; ?>>Wave Payroll</option>
                                        <option value="21" <?php if ($hrms_value == 21) echo "selected"; ?>>Xero</option>
                                        <option value="14" <?php if ($hrms_value == 14) echo "selected"; ?>>Zenefits</option>
                                        <option value="0" <?php if ($hrms_value == 0) echo "selected"; ?>>Not Available</option>
                                    </select>
                                    <span id="hrmsErr" class="error"></span>
                                </div>

                                <?php 

                                if(isset($single_opportunity->product_ID)&&!empty($single_opportunity->product_ID)&&$single_opportunity->product_ID==$erc_product_id){ // for ERC product
                                ?>
                                    <div class="floating col-sm-6 mb-3">
                                        <label>Retainer($)</label>
                                        <input type="number" class="crm-erp-field form-control erc_retainer" id="erc_retainer" data-name='erc_retainer' name="erc_retainer" value="0" min="0" max="90" disabled>
                                        <span id="erc_retainerErr" class="error"></span>
                                        <input type="hidden" class="new_erc_retainer" id="new_erc_retainer" name="new_erc_retainer">
                                    </div>

                                    <div class="floating col-sm-6 mb-3">
                                        <label>Success Fee(%)</label>
                                        <input type="number" class="crm-erp-field form-control erc_success_fee" id="erc_success_fee" data-name='erc_success_fee' name="erc_success_fee" value="20" <?php echo $userdisable;?> min="0" max="99">
                                        <span id="success_feeErr" class="error"></span>
                                    </div>

                                    <div class="floating col-sm-6 mb-3">
                                        <label>Completion Fee(%)</label>
                                        <input type="number" class="crm-erp-field form-control erc_completion_fee" id="erc_completion_fee" data-name='erc_completion_fee' name="erc_completion_fee" value="15" <?php echo $userdisable;?> min="0" max="99">
                                        <span id="completion_feeErr" class="error"></span>
                                    </div>
                                    <div class="floating col-sm-6 mb-3">
                                        <!-- <label>First Installment</label> -->
                                        <input type="hidden" class="crm-erp-field form-control erc_first_installment" id="erc_first_installment" data-name='erc_first_installment' name="erc_first_installment" value="0" min="0">
                                        <span id="first_installmentErr" class="error"></span>
                                    </div>
                                    <div class="floating col-sm-6 mb-3">
                                        <!-- <label>Second Installment</label> -->
                                        <input type="hidden" class="crm-erp-field form-control erc_second_installment" id="erc_second_installment" data-name='erc_second_installment' name="erc_second_installment" min="0" value="0">
                                        <span id="second_installmentErr" class="error"></span>
                                    </div>
                                <?php } ?>

                                <div class="floating col-sm-6 mb-3" style="display:none;">
                                <label>Select Group</label>
                                    <select <?= $disabled ?> class="crm-erp-field form-control" name="assigned_group">
                                        <option value="">Select Group</option>
                                        <?php foreach ($groups_array as $gkey => $gvalue) {
                                            foreach ((array) $gvalue as $grkey => $grvalue) {
                                                $selected = "";
                                                if ($group == $grvalue["name"]) {
                                                    $selected = "selected";
                                                }
                                        ?>
                                                <option value="<?= $grvalue["id"] ?>" <?= $selected ?>><?= $grvalue["name"] ?></option>
                                        <?php
                                            }
                                        }
                                        ?>
                                    </select>
                                    <span id="lead_groupErr" class="error"></span>
                                </div>


                                <?php
                                if(isset($single_opportunity->product_ID)&&!empty($single_opportunity->product_ID)&&$single_opportunity->product_ID==$aa_agreement_product_id){ // for RDC product
                                ?>
                                <div class="floating col-sm-6 mb-3">
                                <label>Quarters</label>
                                    <input class="crm-erp-field form-control" id="ercagr_quarters" type="number" data-name="ercagr_quarters" name="ercagr_quarters" value="">
                                    <span id="ercagr_quartersErr" class="error"></span>
                                </div>

                                <?php } ?>

                                <?php
                                if(isset($single_opportunity->product_ID)&&!empty($single_opportunity->product_ID)&&$single_opportunity->product_ID==$stc_product_id){ // for stc product
                                ?>
                                <div class="floating col-sm-6 mb-3">
                                <label>Retainer Amount</label>
                                    <input class="crm-erp-field form-control" id="ercagr_retainer_amount" type="number" data-name="ercagr_retainer_amount" name="ercagr_retainer_amount" value="<?php echo $single_opportunity->stc_retainer; ?>">
                                    <span id="ercagr_retainer_amountErr" class="error"></span>
                                </div>

                                <?php } ?>


                                <div class="floating col-sm-6 mb-3" style="display:none;">
                                <label>Select Lead Source</label>
                                    <select <?= $disabled ?> class="crm-erp-field form-control" name="lead_source">
                                        <option value="0">Select Lead Source</option>
                                        <?php foreach ($source_array as $skey => $svalue) {
                                            foreach ((array) $svalue as $sokey => $sovalue) {
                                                $selected = "";
                                                if ($sovalue["name"] == $source) {
                                                    $selected = "selected";
                                                } ?>
                                                <option value="<?= $sovalue["id"] ?>" <?= $selected ?>><?= $sovalue["name"] ?></option>
                                        <?php  }
                                        } ?>
                                    </select>
                                    <span id="lead_sourceErr" class="error"></span>
                                </div>

                                <div class="floating col-sm-6 mb-3" style="display: none;">
                                    <label>Client Peo</label>
                                    <input class="floating__input form-control" id="client_peo" type="text" data-name='client_peo' name="client_peo">
                                    <span id="client_peoErr" class="error"></span>
                                </div>
                            </div>
                    
                        <div class="buttion_next_prev">
                                <input type="hidden" name="agreement_type" value="<?php echo $agreement_type;?>" id="agreement_type-id">
                                <input type="hidden" name="send_agreement_type" value="<?=$send_agreement_type;?>" id="send_agreement_type">
                                <input type="hidden" name="action" value="erc_send_new_aggrement_action">
                                <input type="hidden" name="lead_id" value="<?php echo $lead_id; ?>">
                                
                                <button type="button" class="nxt_btn" id="erc_send_aggrement_submit">Send Agreement</button>
                                <button type="button" class="close-popup-document close_btn_sendagreement">Cancel</button>
                        </div>

                        <div class="row mt-3">
                            <div class="col-xs-12">
                                <div id="success_msg"></div>
                            </div>
                        </div>
                    </form>
            </div>
        </div>
    </div>
</div>
     
     <!-- ----------- Resend popup functionality ------->
<div class="modal resend-aggrements-add-new" id="add-new-opp-resend-aggrements" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" style="overflow-x: hidden;overflow-y: auto;">
    <div class="modal-lg modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-file-lines"></i> <?php echo ucwords($single_opportunity->OpportunityName); ?> Agreement</h5>
                <button type="button" class="close-popup-resend-aggrements close">
                    <span aria-hidden="true">×</span>
                </button>
                
            </div>
            <div class="modal-body">
                    <form name="resend_erc_aggrement_form " id="resend_erc_aggrement_form" method="post">
                            <div class="row">
                                <div class="floating col-sm-6 mb-3">
                                    <label>Business Name*</label>
                                    <input class="crm-erp-field form-control" type="text" data-name='ercagr_business_legal_name' name="ercagr_business_legal_name" value='<?php echo $old_business_legal_name; ?>' id="business_name" readonly>
                                </div>

                                <div class="floating col-sm-6 mb-3">
                                    <label>Business Email Address</label>
                                    <input class="crm-erp-field form-control" id="business_email" name="business_email" type="text" value='<?php echo $old_business_email; ?>' readonly>
                                </div>
                                <div class="floating col-sm-6 mb-3" id="state_of_incorporation_clm">
                                    <label>State of Formation/Incorporation</label>
                                    <input class="crm-erp-field form-control" id="state_of_incorporation" type="text" data-name='ercagr_state' name="ercagr_state" value='<?php echo $old_ercagr_state; ?>' readonly>
                                </div>

                                <div class="floating col-sm-6 mb-3" id="ercarg_address_clm">
                                    <label>Street Address</label>
                                    <input class="crm-erp-field form-control" type="text" data-name='ercarg_address' name="ercarg_address" value='<?php echo $old_ercarg_address; ?>' id="business_address" readonly>
                                </div>

                                <div class="floating col-sm-6 mb-3" id="ercarg_zip_clm">
                                    <label>Zip</label>
                                    <input class="crm-erp-field form-control" type="number" min="1" data-name='ercarg_zip' name="ercarg_zip" value='<?php echo $old_ercarg_zip; ?>' id="zip" readonly>
                                </div>

                                <div class="floating col-sm-6 mb-3" id="ercarg_city_clm">
                                    <label>City</label>
                                    <input class="crm-erp-field form-control" type="text" data-name='ercarg_city' name="ercarg_city" value='<?php echo $old_ercarg_city; ?>' id="city" readonly>
                                </div>

                                <div class="floating col-sm-6 mb-3" id="ercarg_state_clm">
                                    <label>State</label>
                                    <input class="crm-erp-field form-control" type="text" value='<?php echo $old_ercarg_state; ?>' id="state" readonly>
                                </div>
                                <?php
                                if(isset($single_opportunity->product_ID)&&!empty($single_opportunity->product_ID)&&$single_opportunity->product_ID==$rdc_product_id){ // for RDC product
                                ?>
                                <div class="floating col-sm-6 mb-3">
                                    <label>Tax Years Covered</label>
                                    <input class="crm-erp-field form-control" type="text" value='<?php echo $old_tax_year; ?>' id="state" readonly>
                                </div>
                                <?php }else{ ?>    
                                <div class="floating col-sm-6 mb-3" id="eligi_employee_clm">
                                    <label>Estimated # of Eligible Employees</label>
                                    <input class="crm-erp-field form-control" type="number" min="0" id="eligi_employee" value='<?php echo $old_eligible_employee; ?>' readonly>
                                </div>
                                <?php } ?>    
                                <div class="floating col-sm-6 mb-3">
                                    <label>Full Name of Authorized Signatory*</label>
                                    <input class="crm-erp-field form-control" id="authorized_signatory_name" type="text" value='<?php echo $old_authorized_signatory; ?>' readonly>
                                </div>

                                <div class="floating col-sm-6 mb-3">
                                    <label>Authorized Signatory Mobile Phone*</label>
                                    <input class="crm-erp-field form-control" id="auth_sign_phone" min="0" type="number" value='<?php
                                        echo $old_auths_mobile_number; ?>' readonly>
                                </div>

                                <div class="floating col-sm-6 mb-3">
                                    <label>Business Title e.g. CEO/CFO/Director/Owner*</label>
                                    <input class="crm-erp-field form-control" id="business_title" type="text" value='<?php echo $old_business_title; ?>' readonly>
                                </div>

                                <div class="floating col-sm-6 mb-3" id="hrms_value_clm">
                                    <label>HRMS</label>
                                    <select class="crm-erp-field form-control" id="hrms_value" name="hrms_value" placeholder="HRMS" disabled>
                                        <option value="38" <?php if ($old_hrms_value == 38) echo "selected"; ?>>ADP TotalSource</option>
                                        <option value="2" <?php if ($old_hrms_value == 2) echo "selected"; ?>>ADP Workforce Now</option>
                                        <option value="49" <?php if ($old_hrms_value == 49) echo "selected"; ?>>ADP Streamline Payroll</option>
                                        <option value="25" <?php if ($old_hrms_value == 25) echo "selected"; ?>>APS Payroll Solution</option>
                                        <option value="3" <?php if ($old_hrms_value == 3) echo "selected"; ?>>BambooHR</option>
                                        <option value="4" <?php if ($old_hrms_value == 4) echo "selected"; ?>>Bob (HiBob)</option>
                                        <option value="26" <?php if ($old_hrms_value == 26) echo "selected"; ?>>BrightPay</option>
                                        <option value="40" <?php if ($old_hrms_value == 40) echo "selected"; ?>>CheckMark Payroll</option>
                                        <option value="17" <?php if ($old_hrms_value == 17) echo "selected"; ?>>Ceridian Dayforce</option>
                                        <option value="46" <?php if ($old_hrms_value == 46) echo "selected"; ?>>Execupay</option>
                                        <option value="34" <?php if ($old_hrms_value == 34) echo "selected"; ?>>Evolution Payroll</option>
                                        <option value="35" <?php if ($old_hrms_value == 35) echo "selected"; ?>>EPAY Systems</option>
                                        <option value="30" <?php if ($old_hrms_value == 30) echo "selected"; ?>>Fingercheck</option>
                                        <option value="5" <?php if ($old_hrms_value == 5) echo "selected"; ?>>Gusto</option>
                                        <option value="6" <?php if ($old_hrms_value == 6) echo "selected"; ?>>Humaans</option>
                                        <option value="31" <?php if ($old_hrms_value == 31) echo "selected"; ?>>Heartland Payroll</option>
                                        <option value="50" <?php if ($old_hrms_value == 50) echo "selected"; ?>>Inova Payroll</option>
                                        <option value="33" <?php if ($old_hrms_value == 33) echo "selected"; ?>>Intuit Online Payroll</option>
                                        <option value="37" <?php if ($old_hrms_value == 37) echo "selected"; ?>>iSolved</option>
                                        <option value="27" <?php if ($old_hrms_value == 27) echo "selected"; ?>>Justworks</option>
                                        <option value="42" <?php if ($old_hrms_value == 42) echo "selected"; ?>>KashFlow</option>
                                        <option value="18" <?php if ($old_hrms_value == 18) echo "selected"; ?>>Kronos Workforce Ready</option>
                                        <option value="39" <?php if ($old_hrms_value == 39) echo "selected"; ?>>MyPayrollHR</option>
                                        <option value="15" <?php if ($old_hrms_value == 15) echo "selected"; ?>>Namely</option>
                                        <option value="23" <?php if ($old_hrms_value == 23) echo "selected"; ?>>OnPay</option>
                                        <option value="11" <?php if ($old_hrms_value == 11) echo "selected"; ?>>Patriot Payroll</option>
                                        <option value="1" <?php if ($old_hrms_value == 1) echo "selected"; ?>>Payroll Providers</option>
                                        <option value="7" <?php if ($old_hrms_value == 7) echo "selected"; ?>>Paychex</option>
                                        <option value="13" <?php if ($old_hrms_value == 13) echo "selected"; ?>>Paycor</option>
                                        <option value="24" <?php if ($old_hrms_value == 24) echo "selected"; ?>>Paylocity</option>
                                        <option value="28" <?php if ($old_hrms_value == 28) echo "selected"; ?>>PrimePay</option>
                                        <option value="29" <?php if ($old_hrms_value == 29) echo "selected"; ?>>Payroll4Free</option>
                                        <option value="32" <?php if ($old_hrms_value == 32) echo "selected"; ?>>PayUSA</option>
                                        <option value="47" <?php if ($old_hrms_value == 47) echo "selected"; ?>>PenSoft Payroll</option>
                                        <option value="8" <?php if ($old_hrms_value == 8) echo "selected"; ?>>QuickBooks Payroll</option>
                                        <option value="22" <?php if ($old_hrms_value == 22) echo "selected"; ?>>Rippling</option>
                                        <option value="9" <?php if ($old_hrms_value == 9) echo "selected"; ?>>Square Payroll</option>
                                        <option value="12" <?php if ($old_hrms_value == 12) echo "selected"; ?>>SurePayroll</option>
                                        <option value="19" <?php if ($old_hrms_value == 19) echo "selected"; ?>>Sage Payroll</option>
                                        <option value="41" <?php if ($old_hrms_value == 41) echo "selected"; ?>>Sage HRMS</option>
                                        <option value="51" <?php if ($old_hrms_value == 51) echo "selected"; ?>>Sage 300 Construction and Real Estate</option>
                                        <option value="43" <?php if ($old_hrms_value == 43) echo "selected"; ?>>StarGarden HR Suite</option>
                                        <option value="45" <?php if ($old_hrms_value == 45) echo "selected"; ?>>SimpleX Payroll</option>
                                        <option value="16" <?php if ($old_hrms_value == 16) echo "selected"; ?>>TriNet</option>
                                        <option value="20" <?php if ($old_hrms_value == 20) echo "selected"; ?>>Ultimate Software UltiPro</option>
                                        <option value="48" <?php if ($old_hrms_value == 48) echo "selected"; ?>>Workforce PayHub</option>
                                        <option value="36" <?php if ($old_hrms_value == 36) echo "selected"; ?>>Workday Payroll</option>
                                        <option value="44" <?php if ($old_hrms_value == 44) echo "selected"; ?>>Wagepoint</option>
                                        <option value="10" <?php if ($old_hrms_value == 10) echo "selected"; ?>>Wave Payroll</option>
                                        <option value="21" <?php if ($old_hrms_value == 21) echo "selected"; ?>>Xero</option>
                                        <option value="14" <?php if ($old_hrms_value == 14) echo "selected"; ?>>Zenefits</option>
                                        <option value="0" <?php if ($old_hrms_value == 0) echo "selected"; ?>>Not Available</option>
                                    </select>
                                </div>
                            </div>
                        
                        <div class="resend-notes" style="border-bottom:1px solid #dee2e6;padding-left:16px;">
                            <div>
                            <ul>
                            <li>The Agreement will be resent with the Above details to the Mentioned Signatory. If you wish to Send a New Agreement kindly Void the current Agreement and Send a new one.</li>
                            <li>Are you sure you want to resend the agreement that was previously sent on <?php  echo $last_agreement_time.'?'; ?></li>
                            </ul>
                            </div>
                        </div>

                        <div class="buttion_next_prev" style="margin-top: 28px!important;">
                            
                                <button type="button" class="Opp_Closure resend_rdc_agreement " data-eventtype="resend_rdc_agreement" data-envelop_id="<?php  echo $old_envelop_id;?>" data-last_agreement_sent="<?php  echo $last_agreement_time; ?>" >Resend Agreement</button>
                            
                                <button type="button" class="close-popup-document close_btn_resendagreement" >Cancel</button>
                            
                        </div>

                        <div class="row mt-3">
                            <div class="col-xs-12">
                                <div id="resend_success_msg"></div>
                            </div>
                        </div>
                    </form>
            </div>
        </div>
    </div>
</div>


     <link href="https://cdn.rawgit.com/harvesthq/chosen/gh-pages/chosen.min.css" rel="stylesheet"/>
     <!-- <script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script> -->
     <script src="https://cdn.rawgit.com/harvesthq/chosen/gh-pages/chosen.jquery.min.js"></script>
<script>
    jQuery(document).on("keypress",".chosen-search-input", function() {
            var val = jQuery(this).val(); 
            var new_val = '';
            if(val.length>=4){
            for (var i = 0; i < 4; i++) {
                console.log(val[i]);
                new_val += val[i];
                jQuery('.chosen-search-input').val(new_val);
            }
            }
    });    

    jQuery(".tax_year_covered").chosen({
        no_results_text: "Oops, nothing found!"
    });
</script>

<script>
    jQuery(document).on('change', '.erc_type_radio', function() {
        var agreement_type = jQuery(this).val();
        jQuery('#agreement_type-id').val(agreement_type);
        if (agreement_type == 1) {
            jQuery('#ercagr_success_fee').val('20');
            jQuery('#ercagr_success_fee').prop('readonly', true);
        } else if (agreement_type == 2) {
            jQuery('#ercagr_success_fee').prop('readonly', false);
        }
    });


    let valid = 0,
        business_name_valid = 0,
        state_of_valid = 0,
        business_add_valid = 0,
        eligi_emp_valid = 0,
        auth_name_valid = 0,
        auth_phone_valid = 0,
        business_title_valid = 0,
        bank_name_valid = 0,
        routing_no_valid = 0,
        account_no_valid = 0,
        account_name_valid = 0,
        account_add_valid = 0,
        trigger_val = 0,
        success_fee_valid = 0,
        retainer_fee_type_valid = 0,
        completion_fee_type_valid = 0,
        retainer_fee_valid = 0,
        aggregate_service_fee_valid = 0,
        total_installment_valid = 0,
        first_installment_valid = 0,
        second_installment_valid = 0;

    jQuery('#erc_send_aggrement_submit').on('click', function() {

        checkValidation();
        console.log(valid + '=valid');
        let opportunityClosure = $(this).attr('current_status');
        // let noteMessage = $(this).attr('noteMessage');
        var noteMessage = "Send Agreement with following details:";
        // return false;
        if (valid == 1) {
            jQuery(this).html('Please wait..');
            jQuery(this).attr('disabled', true);
            var id = jQuery(this).attr('id');
            var form = '#send_erc_aggrement_form';
            var form_data = jQuery(form).serializeArray();

            // console.log(form_data.ercagr_business_legal_name);
                    let business_legal_name = '';
                    let business_email = '';
                    let state_formation = '';
                    let street_address = '';
                    let zip = 0;
                    let city = '';
                    let state = '';
                    let authorized_signatory_name = '';
                    let auths_mobile_number = '';
                    let business_title = '';
                    let hrms_value = 0;
                    let ercagr_estimated_emp = '';
                    let tax_years = '';

                    let erc_retainer = '';
                    let new_erc_retainer = '';
                    let erc_success_fee = '';
                    let erc_completion_fee = '';
                    let erc_first_installment = '';
                    let erc_second_installment = '';
                    let erc_calculate_fee_per = '';
        //     let opportunity_id = <?php echo $_GET['id']; ?>;
        // if(opportunity_id == 5585 || opportunity_id == 5586){
            jQuery(form_data).each(function( index,value ) {
                if(value.name=="ercagr_business_legal_name"){
                    business_legal_name = value.value;
                    noteMessage += ',business_name='+value.value;
                }
                if(value.name=="business_email"){
                    business_email = value.value;
                    noteMessage += ',email='+value.value;
                }
                if(value.name=="ercagr_state"){
                    state_formation = value.value;
                    noteMessage += ',state_formation='+value.value;
                }
                if(value.name=="ercarg_address"){
                    street_address = value.value;
                    noteMessage += ',street_address='+value.value;
                }
                if(value.name=="ercarg_zip"){
                    zip = value.value;
                    noteMessage += ',zip='+value.value;
                }
                if(value.name=="ercarg_city"){
                    city = value.value;
                    noteMessage += ',city='+value.value;
                }
                if(value.name=="ercarg_state"){
                    state = value.value;
                    noteMessage += ',state='+value.value;
                }
                if(value.name=="ercagr_authorized_signatory"){
                    authorized_signatory_name = value.value;
                    noteMessage += ',authorize_name='+value.value;
                }
                if(value.name=="ercagr_auths_mobile_number"){
                    auths_mobile_number = value.value;
                    noteMessage += ',auth_mobile='+value.value;
                }
                if(value.name=="ercagr_business_name"){
                    business_title = value.value;
                    noteMessage += ',title='+value.value;
                }
                if(value.name=="hrms_value"){
                    hrms_value = value.value;
                    noteMessage += ',hrms='+value.value;
                }
                if(value.name=="ercagr_estimated_emp"){
                    ercagr_estimated_emp = value.value;
                    noteMessage += ',employee='+value.value;
                }
                if(value.name=="tax_year_covered[]"){
                    if(tax_years == ''){
                        tax_years = value.value;    
                    }else{
                        tax_years = tax_years+','+value.value;     
                    }
                    noteMessage += ',tax years='+tax_years;
                }
                if(value.name=="erc_retainer"){
                    erc_retainer = value.value;
                    if(value.value==90){
                        var new_retainer = "$"+value.value +"Per EE Min $2,000";
                    }else if(value.value==0){
                        var new_retainer = "Retainer Fee $0";
                    }else{
                        var new_retainer = "$"+value.value +"Per EE";
                    }
                    noteMessage += ',retainer='+new_retainer;
                }
                if(value.name=="erc_success_fee"){
                    erc_success_fee = value.value;
                    noteMessage += ',success fee='+value.value;
                }
                if(value.name=="erc_completion_fee"){
                    erc_completion_fee = value.value;
                    noteMessage += ',completion fee='+value.value;
                }
                if(value.name=="erc_first_installment"){
                    erc_first_installment = value.value;
                    noteMessage += ',installment1='+value.value;
                }
                if(value.name=="erc_second_installment"){
                    erc_second_installment = value.value;
                    noteMessage += ',installment2='+value.value;
                }
                
                if(value.name=="erc_calculate_fee_per"){
                    erc_second_installment = value.value;
                    noteMessage += ',erc_calculate_fee_per='+value.value;
                }
            });
               
            // }
                
            jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                method: 'post',
                data: form_data,
                success(response) {
                    console.log(response);
                    var res = JSON.parse(response);
                    console.log(res.envelope_id);
                    console.log(res.message);
                    if (res.code == 'success') {
                        console.log('success');
                        console.log(res.code);

                        var agreementReponse = res.message;
                        var envelop_id = res.envelope_id;
                        
                        if(res.envelope_id){
                           noteMessage += ', envelop_id = '+envelop_id;
                        }

                        let OpportunityID = <?php echo $_GET['id']; ?>;
                        let NextStep = $('#NextStep').val();
                        let ExpectedCloseDate = $('#ExpectedCloseDate').val();
                        let OpportunityAmount = $('#opportunity_amount').val().replaceAll(',', '');
                        let Probability = $('#Probability').val();
                        var last_agreement_sent = "<?php echo date("Y-m-d H:i:s"); ?>";
                        
                        var product_id = "<?php echo $single_opportunity->product_ID;?>";

                        if(product_id == 935){ //ERC
                            var Milestone = 112;
                            var MilestoneStage= 192;
                        }else if(product_id == 932){ // RDC
                            var Milestone = 100;
                            var MilestoneStage = 326;
                        }else{
                            var Milestone = 112;
                            var MilestoneStage= 192;
                        }
                                var new_retainer = jQuery('#new_erc_retainer').val();
                                if(erc_first_installment.length==0){
                                    erc_first_installment = 0;
                                }
                                if(erc_second_installment.length==0){
                                    erc_second_installment = 0;
                                }

                        jQuery.ajax({
                            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-opportunity-optional-field',
                            method: 'post',
                            data: {
                                OpportunityID: OpportunityID,
                                opportunityClosure: opportunityClosure,
                                NextStep: NextStep,
                                ExpectedCloseDate: ExpectedCloseDate,
                                OpportunityAmount: OpportunityAmount,
                                Probability: Probability,
                                envelop_id: envelop_id,
                                last_agreement_sent:last_agreement_sent,
                                Milestone: Milestone,
                                MilestoneStage: MilestoneStage,
                                business_legal_name : business_legal_name,
                                business_email : business_email,
                                state_formation : state_formation,
                                street_address : street_address,
                                zip : zip,
                                city : city,
                                state : state,
                                authorized_signatory_name : authorized_signatory_name,
                                auths_mobile_number : auths_mobile_number,
                                business_title : business_title,
                                hrms_value : hrms_value,
                                estimated_emp : ercagr_estimated_emp,
                                tax_years : tax_years,
                                erc_retainer : new_retainer,
                                erc_success_fee : erc_success_fee,
                                erc_completion_fee : erc_completion_fee,
                                erc_first_installment : erc_first_installment,
                                erc_second_installment : erc_second_installment,
                            },
                            success(res) {
                                console.log(res);
                                if (res.status == true) {


                                    var opportunity_id = <?php echo $opportunity_id; ?>;
                                    textopportunityClosure = textopportunityClosure.replace("_", " ");
                                    var id = jQuery('#userDetails').attr('data-id');
                                    var name = jQuery('#userDetails').attr('data-name');
                                    var note = name + ' added a comment: ' + name + ' ' + noteMessage;
                                    var user_id = <?php echo get_current_user_id();  ?>;
                                    jQuery.ajax({
                                        url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/create_opportunity_notes',
                                        method: 'post',
                                        data: {
                                            note: note,
                                            opportunity_id: opportunity_id,
                                            user_id: user_id
                                        },
                                        success(response) {
                                            jQuery('.notes-listing').prepend(response);
                                            jQuery('#success_msg').html('<div class="alert alert-success" role="alert">' + agreementReponse + ' </div>');
                                            jQuery("#erc_send_aggrement_submit").html('Send Agreement');

                                            var offset = jQuery('#note-offset').val();
                                            var new_offset = parseInt(offset) + parseInt(1);
                                            jQuery('#note-offset').val(new_offset);
                                            location.reload();
                                            setTimeout(function() {
                                                $('.success_msg').html('');
                                            }, 3000);
                                        }
                                    });


                                } else {
                                    $('.success_msg').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                                }
                            },

                            error: function(jqXHR, textStatus, errorThrown) {
                                $('.success_msg').html('<p class="warning_message">Something went worng.</p>');
                            }
                        });
                    } else {
                        jQuery('#success_msg').html('<div class="alert alert-danger" role="alert">' + res.message + ' </div>');
                        jQuery("#erc_send_aggrement_submit").html('Send Agreement');
                    }


                    // jQuery("#erc_send_aggrement_submit").attr('disabled',false);
                }
            });
        } else { // validation if close   
            // if(trigger_val==0){
            //     jQuery('#erc_send_aggrement_submit').trigger('click');    
            //     trigger_val=1;
            //     console.log('tri'+trigger_val);
            // }
        }
    });

    function checkValidation() {
        var user_type = jQuery('#agreement_type-id').val();
        var business_name = jQuery('#business_name').val();
        var source = "<?php echo $source; ?>";
        var group = "<?php echo $group; ?>";
        if (!source) {
            source = "Test";
        }
        if (user_type != 3) {
            if (source.length == 0 && group.length == 0) {
                var con = confirm("Please Update Lead Source & Lead Group.");
            } else if (source.length != 0 && group.length == 0) {
                var con = confirm("Please Update Lead Group.");
            } else if (source.length == 0 && group.length != 0) {
                var con = confirm("Please Update Lead Source.");
            }
        }

        if (con) {
            // window.location.href ="<?php echo admin_url("admin.php?page=iris-fields-v1.php&lead_id=" . $lead_id); ?>"; 
            // return false;
        }

        if (business_name.length != 0) {
            jQuery('#business_name').trigger('blur');
        } else {
            jQuery("#business_nameErr").html("Business Name is required.").show();
            business_name_valid = 0;
        }

        var state_of_incorporation = jQuery('#state_of_incorporation').val();
        if (state_of_incorporation.length != 0) {
            jQuery('#state_of_incorporation').trigger('blur');
        } else {
            state_of_valid = 1;
        }

        var business_address = jQuery('#business_address').val();
        if (business_address.length != 0) {
            jQuery('#business_address').trigger('blur');
        } else {
            business_add_valid = 1;
        }

        var eligible_employee = jQuery('#eligi_employee').val();
        if(typeof(eligi_employee) != "undefined" && eligi_employee !== null) {
              eligi_emp_valid = 1;
        if (eligible_employee.length != 0) {
            jQuery('#eligi_employee').trigger('blur');
        }
        } else {
            eligi_emp_valid = 1;
        }

        var authorized_signatory_name = jQuery('#authorized_signatory_name').val();
        if (authorized_signatory_name.length != 0) {
            jQuery('#authorized_signatory_name').trigger('blur');
        } else {
            jQuery('#authorized_signatory_nameErr').html("Authorized Signatory Name is required.").show();
            auth_name_valid = 0;
        }

        var auth_sign_phone = jQuery('#auth_sign_phone').val();
        if (auth_sign_phone.length != 0) {
            jQuery('#auth_sign_phone').trigger('blur');
        } else {
            jQuery('#auth_sign_phoneErr').html("Authorized Signatory Phone is required.").show();
            auth_phone_valid = 0;
        }

        var business_title = jQuery('#business_title').val();
        if (business_title.length != 0) {
            jQuery('#business_title').trigger('blur');
        } else {
            jQuery('#business_titleErr').html("Business Title is required.").show();
            business_title_valid = 0;
        }

        
        var custom_agree_access = "<?php echo $custom_agree_access; ?>";
        var master_sales_access = "<?php echo $master_sales_access; ?>";
        var cmb_user = "<?php echo $cmb_user; ?>";
    
    <?php  if($current_product_id == $erc_product_id){ ?>
        var retainer_fee_type = jQuery('#erc_retainer').val();
        var success_fee = jQuery('#erc_success_fee').val();
        var completion_fee = jQuery('#erc_completion_fee').val();
        
        var first_installment = jQuery('#erc_first_installment').val();
        var second_installment = jQuery('#erc_second_installment').val();

        if(retainer_fee_type.length==0){
             jQuery('#erc_retainerErr').html("Retainer is required.").show().css('color','#ff0000');   
             retainer_fee_type_valid=0;         
        }else{
            if(retainer_fee_type >=91){
                jQuery('#erc_retainerErr').html("Retainer can't be greater than $90.").show().css('color','#ff0000');   
             retainer_fee_type_valid=0;         
            }else{
            jQuery('#erc_retainerErr').html("").hide();   
             retainer_fee_type_valid=1;         
            } 
        }

        if(success_fee.length==0){
             jQuery('#success_feeErr').html("Success Fee % is required.").show().css('color','#ff0000');   
             success_fee_valid=0;         
        }else{
            jQuery('#success_feeErr').html("").hide();   
             success_fee_valid=1;         
        }

        if(completion_fee.length==0){
             jQuery('#completion_feeErr').html("Completion Fee is required.").show().css('color','#ff0000');   
             completion_fee_type_valid=0;         
        }else{
            jQuery('#completion_feeErr').html("").hide();   
             completion_fee_type_valid=1;         
        }

        if(success_fee_valid == 1 && completion_fee_type_valid == 1){
            if(completion_fee>=parseInt(success_fee) + parseInt(1)){
                   jQuery('#completion_feeErr').html("Completion Fee should be less than success fee.").show().css('color','#ff0000');   
                    completion_fee_type_valid=0;          
            }else{
                jQuery('#completion_feeErr').html("").hide();   
                completion_fee_type_valid=1;         
            }
        }

        var eligible_employee = jQuery('#eligi_employee').val();
        var aggregate_service_fee = 0;
        
        if(retainer_fee_type == 0){
        
            retainer_fee_type = 'Retainer Fee @ $0';
            aggregate_service_fee = 0;
            min_msg = "";
        }else if(retainer_fee_type == 90){
            if(typeof(eligible_employee) != "undefined" && eligible_employee !== null) {
                if(eligible_employee>=1){
                    var new_aggregate_service_fee = retainer_fee_type * eligible_employee;
                    if(new_aggregate_service_fee <= 2000){
                        aggregate_service_fee = 2000;
                        min_msg =" or a Min $2,000";
                    }else{
                        aggregate_service_fee = new_aggregate_service_fee;
                        min_msg =" or a Min $"+new_aggregate_service_fee;
                    }
                }
            }else{
                aggregate_service_fee = 2000;
            }        
            retainer_fee_type = "$90 Per EE Min $2,000";
        
        }else{
            min_msg = '';
            retainer_fee_type_val = retainer_fee_type;
            
            retainer_fee_type = "$"+retainer_fee_type+" Per EE";

            if(typeof(eligible_employee) != "undefined" && eligible_employee !== null) {
                if(eligible_employee>=1){
                    aggregate_service_fee_val = eligible_employee * retainer_fee_type_val;
                    aggregate_service_fee = aggregate_service_fee_val;
             }
            }else{
                aggregate_service_fee = 2000;
            }
        }
             jQuery('#new_erc_retainer').val(retainer_fee_type);
             var total_installment = 0;

             second_installment_valid = 1;
             first_installment_valid = 1;   
             total_installment_valid = 1;

            
            /*
            if(first_installment.length!=0 && second_installment.length!=0){
                var total_installment = parseInt(first_installment)+parseInt(second_installment);
            }

            second_installment_valid = 1;
            first_installment_valid = 1;   
            if(parseInt(total_installment) >= parseInt(aggregate_service_fee)+1){
                jQuery('#first_installmentErr').html("Sum of First & Second Installment can't be greater than Retainer Fee "+min_msg).show().css('color','#ff0000');   
                jQuery('#second_installmentErr').html("Sum of First & Second Installment can't be greater than Retainer Fee "+min_msg).show().css('color','#ff0000');   
                total_installment_valid=0;
            }else if(aggregate_service_fee!=0){
            if(parseInt(total_installment) <= parseInt(aggregate_service_fee)-1){
                jQuery('#first_installmentErr').html("Sum of First & Second Installment can't be less than Retainer Fee "+min_msg).show().css('color','#ff0000');
                jQuery('#second_installmentErr').html("Sum of First & Second Installment can't be less than Retainer Fee "+min_msg).show().css('color','#ff0000');
                total_installment_valid=0; 
            }else{
                jQuery('#first_installmentErr').html("").hide();   
                jQuery('#second_installmentErr').html("").hide();   
                total_installment_valid=1;
            }
           }else if(aggregate_service_fee == 0){
                if(total_installment !=0 ){
                    jQuery('#first_installmentErr').html("Sum of First & Second Installment should be 0").show().css('color','#ff0000');
                    jQuery('#second_installmentErr').html("Sum of First & Second Installment should be 0").show().css('color','#ff0000');
                    total_installment_valid=0; 
                }else{
                    total_installment_valid=1; 
                }
            
        }else if(first_installment.length!=0 && second_installment.length==0){
            jQuery('#first_installmentErr').html("").hide();   
            jQuery('#second_installmentErr').html("Second Installment required.").show().css('color','#ff0000');
            second_installment_valid = 0;   
        }else if(first_installment.length==0 && second_installment.length!=0){
            jQuery('#second_installmentErr').html("").hide();   
            jQuery('#first_installmentErr').html("First Installment required.").show().css('color','#ff0000');
            first_installment_valid = 0;
        }else if(first_installment.length==0 && second_installment.length==0){
            jQuery('#second_installmentErr').html("Second Installment required.").show().css('color','#ff0000');
            jQuery('#first_installmentErr').html("First Installment required.").show().css('color','#ff0000');
            first_installment_valid = 0;
            second_installment_valid = 0;   
        }else{
            second_installment_valid = 1;
            first_installment_valid = 1;   
            total_installment_valid = 1;
        }
*/
        jQuery('#erc_aggregate_service_fee').val(aggregate_service_fee);

            
            if(completion_fee_type_valid==1 && success_fee_valid==1){
                    
                    var calculate_fee_per = 0;
                    if(success_fee!=0){
                        var minus_val = (success_fee - completion_fee);
                        var devide = minus_val/success_fee;
                        calculate_fee_per = devide*100;
                        calculate_fee_per = Math.trunc(calculate_fee_per);
                    }
                    
                  jQuery('#erc_calculate_fee_per').val(calculate_fee_per);  
            }

    <?php }else{  ?>
        
                success_fee_valid=1;         
                retainer_fee_type_valid=1;         
                completion_fee_type_valid=1;         
                total_installment_valid=1;
                second_installment_valid = 1;
                first_installment_valid = 1;   

    <?php } ?>    


        /*if(send_agreement_type != 'sbam'){
            // var retainer_fee = jQuery('#ercagr_retainer_fee').val();
            ret_msg = "Retainer Fee Type is required.";
        }else{
            var retainer_fee = ' 01';
            ret_msg = "SBAM Fee Type is required.";
        } 
       
        if(cmb_user!=0 && retainer_fee_type.length==0){
            retainer_fee_type = 1;
            retainer_fee_type_valid=1;         
        }
        

        if(retainer_fee_type_valid==1 && cmb_user==0){
            retainer_fee_valid = 1;
            if(retainer_fee_valid==1){
                if(aggregate_service_fee.length==0){
                     jQuery('#aggregate_service_feeErr').html("Retainer Fee is required.").show().css('color','#ff0000');   
                     aggregate_service_fee_valid=0;         
                     console.log('aggr==1');
                }else if(master_sales_access == 1 && aggregate_service_fee==0 ){
                        aggregate_service_fee_valid=1;         
                        console.log('aggr==2');
                }else if(custom_agree_access!=1 && aggregate_service_fee==0){
                    jQuery('#aggregate_service_feeErr').html("Retainer Fee should not be $0.").show().css('color','#ff0000');   
                     aggregate_service_fee_valid=0;         
                     console.log('aggr==3');
                }else if(master_sales_access != 1 && aggregate_service_fee == 0 && custom_agree_access!=1){
                    jQuery('#aggregate_service_feeErr').html("Retainer Fee should not be $0.").show().css('color','#ff0000');   
                     aggregate_service_fee_valid=0;         
                     console.log('aggr==5');
                }else if(retainer_fee_type==2 && aggregate_service_fee <=2499){
                    jQuery('#aggregate_service_feeErr').html("Retainer Fee should not be less than $2,500.").show().css('color','#ff0000');   
                     aggregate_service_fee_valid=0;         
                }else{
                    jQuery('#aggregate_service_feeErr').html("").hide();   
                     aggregate_service_fee_valid=1;         
                     console.log('aggr==4');
                }
            } 
            if(retainer_fee_type==2){
                retainer_fee_valid = 1;
            }*/

        //console.log(aggregate_service_fee.length);

        //var agg_type = "<?php echo $agreement_type; ?>";
        /*if(master_sales_access == 1 && aggregate_service_fee.length==0){
          jQuery('#aggregate_service_feeErr').html("Retainer Fee is required.").show().css('color','#ff0000');   
          console.log('aggr==5');
              aggregate_service_fee_valid=0;                  
        }else if(master_sales_access == 1 && aggregate_service_fee==0){
           jQuery('#aggregate_service_feeErr').html("").hide();   
              aggregate_service_fee_valid=1;                     
              console.log('aggr==6');
        }else if(agg_type == 3 && retainer_fee_valid == 1 && custom_agree_access == 1 && aggregate_service_fee ==0){
          console.log('aggr==7');
          if(aggregate_service_fee.length==0){
              jQuery('#aggregate_service_feeErr').html("Retainer Fee is required.").show().css('color','#ff0000');
              console.log('aggr==8');   
              aggregate_service_fee_valid=0;                  
          }else if(confirm("Are you sure to proceed with $0 retainer fee for CMB group lead.")){
                  aggregate_service_fee_valid=1;         
                  console.log('aggr==9');
          }else if(aggregate_service_fee <=2499){
              jQuery('#aggregate_service_feeErr').html("Retainer Fee can't be less than $2,500.").show().css('color','#ff0000');   
              aggregate_service_fee_valid=0;           
              console.log('aggr==10');
          }

        }else if(agg_type == 3 && retainer_fee_valid == 1 && custom_agree_access != 1){
          if(aggregate_service_fee.length!=0 && aggregate_service_fee <=2499){
              jQuery('#aggregate_service_feeErr').html("Retainer Fee can't be less than $2,500.").show().css('color','#ff0000');   
              aggregate_service_fee_valid=0;           
           }else{
              jQuery('#aggregate_service_feeErr').html("").hide();   
              aggregate_service_fee_valid=1;           
           }                 
        }else if(retainer_fee_type != 3 && retainer_fee_type != 4){
              //--- add new condition
              if(aggregate_service_fee.length!=0 && aggregate_service_fee <=2499){
                  jQuery('#aggregate_service_feeErr').html("Retainer Fee can't be less than $2,500.").show().css('color','#ff0000');   
              aggregate_service_fee_valid=0;           
              }else if(aggregate_service_fee <=2499){
                   jQuery('#aggregate_service_feeErr').html("Retainer Fee can't be less than $2,500.").show().css('color','#ff0000');   
              aggregate_service_fee_valid=0;              
              }else{
                  jQuery('#aggregate_service_feeErr').html("").hide();   
                  aggregate_service_fee_valid=1;           
              }                 
        }*/
        /*}else{
            aggregate_service_fee_valid=1;           
            retainer_fee_valid=1;
            aggregate_service_fee_valid=1;
          }*/


        //console.log('retainer_fee_type='+retainer_fee_type);

        /*if(aggregate_service_fee_valid==1 && retainer_fee_type != 3){
    if(first_installment.length!=0 && second_installment.length!=0){
        var total_installment = parseInt(first_installment)+parseInt(second_installment);
        second_installment_valid = 1;
        first_installment_valid = 1;   
        if(parseInt(total_installment) >= parseInt(aggregate_service_fee)+1){
            jQuery('#first_installmentErr').html("First & Second Installment can't be greater than Aggregate service fee.").show().css('color','#ff0000');   
            jQuery('#second_installmentErr').html("First & Second Installment can't be greater than Aggregate service fee.").show().css('color','#ff0000');   
            total_installment_valid=0;
       }else if(parseInt(total_installment) <= parseInt(aggregate_service_fee)-1){
           jQuery('#first_installmentErr').html("First & Second Installment can't be less than Aggregate service fee.").show().css('color','#ff0000');
            jQuery('#second_installmentErr').html("First & Second Installment can't be less than Aggregate service fee.").show().css('color','#ff0000');
            total_installment_valid=0; 
       }else{
            jQuery('#first_installmentErr').html("").hide();   
            jQuery('#second_installmentErr').html("").hide();   
            total_installment_valid=1;
       }


    }else if(first_installment.length!=0 && second_installment.length==0){
        // var total_installment = parseInt(first_installment);
        jQuery('#first_installmentErr').html("").hide();   
        jQuery('#second_installmentErr').html("Second Installment required.").show().css('color','#ff0000');
        second_installment_valid = 0;   
    }else if(first_installment.length==0 && second_installment.length!=0){
        // var total_installment = parseInt(second_installment);
        jQuery('#second_installmentErr').html("").hide();   
        jQuery('#first_installmentErr').html("First Installment required.").show().css('color','#ff0000');
        first_installment_valid = 0;
    }else if(first_installment.length==0 && second_installment.length==0){
        jQuery('#second_installmentErr').html("Second Installment required.").show().css('color','#ff0000');
        jQuery('#first_installmentErr').html("First Installment required.").show().css('color','#ff0000');
        first_installment_valid = 0;
        second_installment_valid = 0;   
    }
  }else{
        second_installment_valid = 1;
        first_installment_valid = 1;   
        total_installment_valid = 1;
  }*/

        // if(bank_name.length!=0){
        //      jQuery('#bank_name').trigger('blur');
        // }else{
        //      jQuery('#bank_nameErr').html("Bank Name is required.").show();   
        //      bank_name_valid=0;
        // }

        // var bank_name = jQuery('#bank_name').val();
        // if(bank_name.length!=0){
        //      jQuery('#bank_name').trigger('blur');
        // }else{
        //      jQuery('#bank_nameErr').html("Bank Name is required.").show();   
        //      bank_name_valid=0;
        // }

        // var routing_no = jQuery('#routing_no').val();
        // if(routing_no.length!=0){
        //      jQuery('#routing_no').trigger('blur');
        // }else{
        //      jQuery('#routing_noErr').html("Routing Number is required.").show();   
        //      routing_no_valid=0;
        // }

        // var account_no = jQuery('#account_no').val();
        // if(account_no.length!=0){
        //      jQuery('#account_no').trigger('blur');
        // }else{
        //      jQuery('#account_noErr').html("Account Number is required.").show();   
        //      account_no_valid=0;
        // }

        // var name_on_account = jQuery('#name_on_account').val();
        // if(name_on_account.length!=0){
        //      jQuery('#name_on_account').trigger('blur');
        // }else{
        //      jQuery('#name_on_accountErr').html("Name On Account is required.").show();   
        //      account_name_valid=0;
        // }

        // var account_add = jQuery('#account_add').val();
        // if(account_add.length!=0 && account_add.length > 50){
        //    jQuery("#account_addErr").html("Address on Account allow only 50 Characters & Special Characters").show();  
        //         account_add_valid=0;
        // }else{
        //     jQuery("#account_addErr").html("").hide();
        //     account_add_valid=1;
        // }
        if(first_installment_valid!=0 && second_installment_valid!=0){
             var first_second_installment_valid = 1;
         }else{
             var first_second_installment_valid = 0;
         }

        bank_name_valid = 1;
        routing_no_valid = 1;
        account_no_valid = 1;
        account_name_valid = 1;
        account_add_valid = 1;
        // console.log('aggregate_service_fee'+aggregate_service_fee);
        // console.log('aggregate_service_fee_valid='+aggregate_service_fee_valid);

        if (business_name_valid == 1 && state_of_valid == 1 && business_add_valid == 1 && eligi_emp_valid == 1 && auth_name_valid == 1 && auth_phone_valid == 1 && business_title_valid == 1 && bank_name_valid == 1 && routing_no_valid == 1 && account_no_valid == 1 && account_name_valid == 1 && account_add_valid == 1 && success_fee_valid==1 && retainer_fee_type_valid==1 && completion_fee_type_valid==1 && total_installment_valid==1 && first_second_installment_valid==1 /*&& aggregate_service_fee_valid==1 */ ) {
            valid = 1;
            jQuery('.error').hide();
        } else {
            valid = 0;
        }

    }

    jQuery(document).ready(function() {
        jQuery('#business_name').blur(function() {
            var name_string = jQuery('#business_name').val();
            var name = name_string.replace(/  +/g, ' ');
            jQuery('#business_name').val(name);
            var format = /^[A-Za-z0-9 \-]+$/;
            if (jQuery('#business_name').val().length > 40) {
                var business_name_val = jQuery('#business_name').val();
                var new_val='';
                console.log('length');
                for (var i = 0; i < 40; i++) {
                    new_val += business_name_val[i];
                    jQuery('#business_name').val(new_val);
                }
                // jQuery("#business_nameErr").html("Allow only up to 40 Characters").show();
                // business_name_valid = 0;
            } else {
                jQuery("#business_nameErr").html("").hide();
                jQuery('#business_name').val(name);
                business_name_valid = 1;
            }
        });

        jQuery('#name_on_account').blur(function() {
            var name_string = jQuery('#name_on_account').val();
            var name = name_string.replace(/  +/g, ' ');
            jQuery('#name_on_account').val(name);
            var format = /^[A-Za-z0-9 \-]+$/;
            if (!format.test(jQuery('#name_on_account').val()) && name.length != 0) {
                jQuery("#name_on_accountErr").html("Please Use Alphabets & Numbers only up to 20 Characters & Special Characters").show();
                account_name_valid = 0;
            } else if (jQuery('#name_on_account').val().length > 20) {
                jQuery("#name_on_accountErr").html("Please Use Alphabets & Numbers only up to 20 Characters & Special Characters").show();
                account_name_valid = 0;
            } else {
                jQuery("#name_on_accountErr").html("").hide();
                jQuery('#name_on_account').val(name);
                account_name_valid = 1;
            }
        });

        jQuery('#bank_name').blur(function() {
            var name_string = jQuery('#bank_name').val();
            var name = name_string.replace(/  +/g, ' ');
            jQuery('#bank_name').val(name);
            var format = /^[A-Za-z0-9 \-]+$/;
            if (!format.test(jQuery('#bank_name').val()) && name.length != 0) {
                jQuery("#bank_nameErr").html("Please Use Alphabets & Numbers only up to 60 Characters & Special Characters").show();
                bank_name_valid = 0;
            } else if (jQuery('#bank_name').val().length > 32) {
                jQuery("#bank_nameErr").html("Please Use Alphabets & Numbers only up to 60 Characters & Special Characters").show();
                bank_name_valid = 0;
            } else {
                jQuery("#bank_nameErr").html("").hide();
                jQuery('#bank_name').val(name);
                bank_name_valid = 1;
            }
        });

        jQuery('#business_title').blur(function() {
            var name_string = jQuery('#business_title').val();
            var name = name_string.replace(/  +/g, ' ');
            jQuery('#business_title').val(name);
            var format = /^[A-Za-z0-9 \-]+$/;
            // if (!format.test(jQuery('#business_title').val()) && name.length != 0) { 
            //     jQuery("#business_titleErr").html("Please Use Alphabets & Numbers only up to 60 Characters & Special Characters").show();
            //     business_title_valid = 0;
            // } else 
            if (jQuery('#business_title').val().length > 70) {
                jQuery("#business_titleErr").html("Please Use Alphabets & Numbers only up to 70 Characters & Special Characters").show();
                business_title_valid = 0;
            } else {
                jQuery("#business_titleErr").html("").hide();
                jQuery('#business_title').val(name);
                business_title_valid = 1;
            }
        });
    });

    jQuery('#authorized_signatory_name').blur(function() {
        var name = jQuery('#authorized_signatory_name').val();
        var name_string = jQuery('#authorized_signatory_name').val();
        var name = name_string.replace(/  +/g, ' ');

        var format = /^[A-Za-z0-9 \-]+$/;

        // test for special character
        if (!format.test(name) && name.length != 0) {
            jQuery("#authorized_signatory_nameErr").show();
            jQuery('#authorized_signatory_nameErr').html('Please Enter Name in the format of First Name, Middle Name , Last Name not exceeding 70 Characters , No Special Characters Allowed');
            auth_name_valid = 0;
        } else if (jQuery('#authorized_signatory_name').val().length > 60) {
            jQuery("#authorized_signatory_nameErr").show();
            jQuery("#authorized_signatory_nameErr").html("Please Enter Name in the format of First Name, Middle Name , Last Name not exceeding 70 Characters , No Special Characters Allowed");
            auth_name_valid = 0;
        } else {
            jQuery("#authorized_signatory_nameErr").html("").hide();
            jQuery('#authorized_signatory_name').val(name);
            auth_name_valid = 1;
        }

        // ValidationCheck();
    });

    jQuery('#auth_sign_phone').blur(function() {
        var phoneold = jQuery('#auth_sign_phone').val();
        var d = phoneold.replace(/ /g, "").replace(/[^a-zA-Z 0-9]+/g, "");
        var c = 10;
        if (jQuery.isNumeric(d)) {
            if (d.length != c && d.length != 0) {
                jQuery("#auth_sign_phoneErr").show();
                jQuery("#auth_sign_phoneErr").html('Invalid phone number. Please enter a 10 digit phone number - digits only!');
                auth_phone_valid = 0;
            } else if (d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********* || d == *********0) {
                jQuery("#auth_sign_phoneErr").html('Invalid phone number. Please enter a 10 digit phone number - digits only!').show();
                auth_phone_valid = 0;
            } else if (d['0'] == 0) {
                jQuery("#auth_sign_phoneErr").html('Invalid phone number. Please enter a valid number & first number should not be zero!').show();
                auth_phone_valid = 0;
            } else {
                jQuery('#phone').val(d);
                jQuery("#auth_sign_phoneErr").hide();
                jQuery("#auth_sign_phoneErr").html("");
                auth_phone_valid = 1;
            }
        } else if (d.length != 0) {
            jQuery("#auth_sign_phoneErr").show();
            jQuery("#auth_sign_phoneErr").html('Invalid phone number. Please enter a 10 digit phone number - digits only!');
            auth_phone_valid = 0;
        }
    });

    jQuery('#routing_no').blur(function() {
        var phoneold = jQuery('#routing_no').val();
        var d = phoneold.replace(/ /g, "").replace(/[^a-zA-Z 0-9]+/g, "");

        if (jQuery.isNumeric(d)) {
            if (d.length != 0 && d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********* || d == *********0) {
                jQuery("#routing_noErr").html('Invalid Rounting Number.').show();
                routing_no_valid = 0;
            } else if (d['0'] == 0) {
                jQuery("#routing_noErr").html('Invalid routing number. Please enter a valid number & first number should not be zero!').show();
                routing_no_valid = 0;
            } else {
                jQuery('#routing_no').val(d);
                jQuery("#routing_noErr").hide();
                jQuery("#routing_noErr").html("");
                routing_no_valid = 1;
            }
        } else if (d.length != 0) {
            jQuery("#routing_noErr").show();
            jQuery("#routing_noErr").html('Invalid rounting number.');
            routing_no_valid = 0;
        }
    });

    jQuery('#account_no').blur(function() {
        var phoneold = jQuery('#account_no').val();
        var d = phoneold.replace(/ /g, "").replace(/[^a-zA-Z 0-9]+/g, "");

        if (jQuery.isNumeric(d)) {
            if (d.length != 0 && d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********** || d == ********* || d == *********0) {
                jQuery("#account_noErr").html('Invalid Account Number.').show();
                account_no_valid = 0;
            } else if (d['0'] == 0) {
                jQuery("#account_noErr").html('Invalid Account number. Please enter a valid number & first number should not be zero!').show();
                account_no_valid = 0;
            } else {
                jQuery('#account_no').val(d);
                jQuery("#account_noErr").hide();
                jQuery("#account_noErr").html("");
                account_no_valid = 1;
            }
        } else if (d.length != 0) {
            jQuery("#account_noErr").show();
            jQuery("#account_noErr").html('Invalid Account number.');
            account_no_valid = 0;
        }
    });

    jQuery('#eligi_employee').blur(function() {
        var eligi_employee = jQuery('#eligi_employee').val();

        if (eligi_employee.length != 0 && !jQuery.isNumeric(eligi_employee)) {
            jQuery("#eligi_employeeErr").show();
            jQuery("#eligi_employeeErr").html('Invalid Estimated # of Eligible Employees. Please enter digits only!');
            eligi_emp_valid = 0;
        } else {
            jQuery('#eligi_employee').val(eligi_employee);
            jQuery("#eligi_employeeErr").html("").hide();
            eligi_emp_valid = 1;
        }
        // ValidationCheck();
    });
    jQuery('#state_of_incorporation').blur(function() {
        var name = jQuery('#state_of_incorporation').val();
        var format = /^[A-Za-z0-9 \-]+$/;

        // test for special character
        if (name.length != 0 && !format.test(name)) {
            jQuery("#state_of_incorporationErr").show();
            jQuery('#state_of_incorporationErr').html('No Special Characters Allowed');
            state_of_valid = 0;
        } else if (jQuery('#state_of_incorporation').val().length > 40) {
            jQuery("#state_of_incorporationErr").show();
            jQuery("#state_of_incorporationErr").html("Please Use Alphabets & Numbers only up to 40 Characters, No Special Characters Allowed");
            state_of_valid = 0;
        } else {
            jQuery("#state_of_incorporationErr").hide();
            jQuery("#state_of_incorporationErr").html("");
            jQuery('#state_of_incorporation').val(name);
            state_of_valid = 1;
        }
    });

    jQuery('#business_address').blur(function() {
        var name_string = jQuery('#business_address').val();
        var name = name_string.replace(/  +/g, ' ');
        jQuery('#business_address').val(name);

        var format = /^[A-Za-z0-9 \-]+$/;

        // test for special character
        if (name.length != 0 && name.length > 60) {
            jQuery("#business_addressErr").show();
            jQuery("#business_addressErr").html("Business Address accept only up to 40 Characters.");
            business_add_valid = 0;
        } else {
            jQuery("#business_addressErr").hide();
            jQuery("#business_addressErr").html("");
            business_add_valid = 1;
        }
    });

    jQuery('#account_add').blur(function() {
        var name_string = jQuery('#account_add').val();
        var name = name_string.replace(/  +/g, ' ');
        jQuery('#account_add').val(name);

        if (name.length != 0 && name.length > 50) {
            jQuery("#account_addErr").html("Address on Account accept only up to 50 Characters.").show();;
            account_add_valid = 0;
        } else {
            jQuery("#account_addErr").hide();
            jQuery("#account_addErr").html("");
            account_add_valid = 1;
        }
    });

    document.querySelector("#auth_sign_phone").addEventListener("keypress", function(evt) {
        if (evt.which != 8 && evt.which != 0 && evt.which < 48 || evt.which > 57) {
            evt.preventDefault();
        }
    });

    jQuery('#business_name').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 50) {
            for (var i = 0; i < 50; i++) {
                new_val += value[i];
                jQuery('#business_name').val(new_val);
            }
        }
    });

    jQuery('#state_of_incorporation').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 40) {
            for (var i = 0; i < 40; i++) {
                new_val += value[i];
                jQuery('#state_of_incorporation').val(new_val);
            }
        }
    });

    jQuery('#business_address').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 40) {
            for (var i = 0; i < 40; i++) {
                new_val += value[i];
                jQuery('#business_address').val(new_val);
            }
        }
    });
    
    jQuery('#zip').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 6) {
            for (var i = 0; i < 6; i++) {
                new_val += value[i];
                jQuery('#zip').val(new_val);
            }
        }
    });
    
    jQuery('#city').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 30) {
            for (var i = 0; i < 30; i++) {
                new_val += value[i];
                jQuery('#city').val(new_val);
            }
        }
    });
    jQuery('#state').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 30) {
            for (var i = 0; i < 30; i++) {
                new_val += value[i];
                jQuery('#state').val(new_val);
            }
        }
    });
    
    jQuery('#authorized_signatory_name').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 40) {
            for (var i = 0; i < 40; i++) {
                new_val += value[i];
                jQuery('#authorized_signatory_name').val(new_val);
            }
        }
    });

    jQuery('#auth_sign_phone').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 11) {
            for (var i = 0; i < 10; i++) {
                // console.log(value[i]);      
                new_val += value[i];
                jQuery('#auth_sign_phone').val(new_val);
            }
        }
    });
    jQuery('#business_title').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 70) {
            for (var i = 0; i < 70; i++) {                
                new_val += value[i];
                jQuery('#business_title').val(new_val);
            }
        }
    });
    jQuery('#success_fee').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 8) {
            for (var i = 0; i < 8; i++) {
                // console.log(value[i]);      
                new_val += value[i];
                jQuery('#success_fee').val(new_val);
            }
        }
    });

    jQuery('#retainer_fee').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 8) {
            for (var i = 0; i < 8; i++) {
                // console.log(value[i]);      
                new_val += value[i];
                jQuery('#retainer_fee').val(new_val);
            }
        }
    });

    jQuery('#routing_no').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 10) {
            for (var i = 0; i < 10; i++) {
                // console.log(value[i]);      
                new_val += value[i];
                jQuery('#routing_no').val(new_val);
            }
        }
    });


    jQuery('#eligi_employee').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 10) {
            for (var i = 0; i < 10; i++) {
                // console.log(value[i]);      
                new_val += value[i];
                jQuery('#eligi_employee').val(new_val);
            }
        }
    });

    jQuery('#account_no').on('keyup', function(e) {
        var value = e.target.value;
        var new_val = '';
        if (value.length >= 10) {
            for (var i = 0; i < 10; i++) {
                // console.log(value[i]);      
                new_val += value[i];
                jQuery('#account_no').val(new_val);
            }
        }
    });

    var btn = document.getElementById('does-not-exist');
    if (btn) {
    document.querySelector("#eligi_employee").addEventListener("keypress", function(evt) {
        if (evt.which != 8 && evt.which != 0 && evt.which < 48 || evt.which > 57) {
            evt.preventDefault();
        }
    });
    }
    // document.querySelector("#success_fee").addEventListener("keypress", function (evt) {
    //     if (evt.which != 8 && evt.which != 0 && evt.which < 48 || evt.which > 57)
    //     {
    //         evt.preventDefault();
    //     }
    // });
    // document.querySelector("#retainer_fee").addEventListener("keypress", function (evt) {
    //     if (evt.which != 8 && evt.which != 0 && evt.which < 48 || evt.which > 57)
    //     {
    //         evt.preventDefault();
    //     }
    // });

    // document.querySelector("#routing_no").addEventListener("keypress", function (evt) {
    //     if (evt.which != 8 && evt.which != 0 && evt.which < 48 || evt.which > 57)
    //     {
    //         evt.preventDefault();
    //     }
    // });

    // document.querySelector("#account_no").addEventListener("keypress", function (evt) {
    //     if (evt.which != 8 && evt.which != 0 && evt.which < 48 || evt.which > 57)
    //     {
    //         evt.preventDefault();
    //     }
    // });


    // --------------- calculate aggregate service fee -----

    jQuery(document).on('change', '#select-retainer-fee', function() {
        var retainer_type = jQuery('#select-retainer-fee').val();
        jQuery('#retainer_fee_type-val').val(retainer_type);
        if (retainer_type == 'member' || retainer_type == 'non_member') {
            if (retainer_type == 'member') {
                jQuery('#aggregate_service_fee').val('2500');
                jQuery('#ercagr_success_fee').val('20');
                jQuery('#send_agreement_type').val('sbam_member');
            } else {
                jQuery('#aggregate_service_fee').val('3000');
                jQuery('#ercagr_success_fee').val('22');
                jQuery('#send_agreement_type').val('sbam_non_member');
            }
        } else {
            var eligi_employee = jQuery('#eligi_employee').val();
            if (retainer_type == 1) {
                retainer_fee = 2500;
                if (eligi_employee.length == 0 || eligi_employee == 0) {
                    eligi_employee = 1;
                }
                jQuery('#ercagr_retainer_fee').val(retainer_fee);
                var agg_val = parseInt(retainer_fee) * parseInt(eligi_employee);
            } else if (retainer_type == 2) {
                var agg_val = 2500;
                jQuery('#ercagr_retainer_fee').val('');
            } else if (retainer_type == 3) {
                var agg_val = 0;
                jQuery('#ercagr_retainer_fee').val('');
            } else if (retainer_type == 4) {
                jQuery('#ercagr_retainer_fee').val('');
            }
            // var retainer_fee = jQuery('#ercagr_retainer_fee').val();
            calculate_agreegate_val(agg_val, retainer_type);
        }
    });

    // jQuery(document).on('keyup','#ercagr_retainer_fee',function(){
    //     var retainer_type = jQuery('#select-retainer-fee').val();    
    //     var eligi_employee = jQuery('#eligi_employee').val();
    //     var retainer_fee = jQuery('#ercagr_retainer_fee').val();
    //     calculate_agreegate_val(eligi_employee,retainer_type,retainer_fee);
    // });

    function calculate_agreegate_val(agg_val, retainer_type) {
        var agg_type = "<?php echo $agreement_type; ?>";
        if (retainer_type != 3 && retainer_type != 4) {
            if (agg_val <= 2500) {
                agg_val = 2500;
            }
        }
        jQuery('#aggregate_service_fee').val(agg_val);
    }

    //------------- zip code start here -----

    jQuery("#zip").keyup(function() {
        // clearTimeout(timeout);
        timeout = setTimeout(() => {
            var zip = jQuery(this).val();
            var ajaxurl = '<?php echo admin_url('admin-ajax.php'); ?>';
            jQuery.ajax({
                url: ajaxurl,
                data: {
                    action: 'getaddressbyzip',
                    zip: zip
                },
                type: 'POST',
                dataType: 'json',
                success: function(data) {
                    var add_json = jQuery.parseJSON(data);
                    // console.log(add_json);
                    jQuery('#city').val(add_json.city);
                    jQuery('#state').val(add_json.state);
                    // jQuery('#country').val(add_json.country);
                    return false;
                }
            });
        }, 500);
    })

    jQuery(document).ready(function() {
        var agreement_type = <?php echo isset($agreement_type) ? $agreement_type : '0'; ?>;
        //$('#field4, #field5, #field6').hide();

        // Show the field based on the agreement type
        if (agreement_type == 6) {
            $('#state_of_incorporation_clm').hide();
            $('#hrms_value_clm').hide();
            $('#eligi_employee_clm').hide();
            $('#ercarg_state_clm').hide();
            $('#ercarg_city_clm').hide();
            $('#ercarg_zip_clm').hide();
            $('#ercarg_address_clm').hide();
          
        }

    })
</script>