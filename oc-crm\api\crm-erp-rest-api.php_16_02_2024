<?php
/**
 * Handles REST API endpoints for the  CRM ERP plugin.
 */

class CRM_ERP_REST_API {

    public function __construct() {

        add_action('rest_api_init', array($this, 'register_api_routes'));
    }

    public function register_api_routes() {
        // Products Endpoints
        register_rest_route('productsplugin/v1', '/products', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'get_products'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product Info Endpoints
        register_rest_route('productsplugin/v1', '/product', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_product'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Endpoints
        register_rest_route('productsplugin/v1', '/create-product', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_product'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Product Endpoints
        register_rest_route('productsplugin/v1', '/edit-product', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_product'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Unit Types Endpoints
        register_rest_route('productsplugin/v1', '/unit-types', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'unit_types'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

         // Unit Type Info Endpoints
        register_rest_route('productsplugin/v1', '/unit-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'unit_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));
        // Create Unit Type Endpoints
        register_rest_route('productsplugin/v1', '/create-unit-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_unit_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Unit Type
        register_rest_route('productsplugin/v1', '/edit-unit-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_unit_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Type Endpoints
        register_rest_route('productsplugin/v1', '/create-product-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_product_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product Types Endpoints
        register_rest_route('productsplugin/v1', '/product-types', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'product_types'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Type Endpoints
        register_rest_route('productsplugin/v1', '/product-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'product_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Product Type
        register_rest_route('productsplugin/v1', '/edit-product-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_product_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product Categories Endpoints
        register_rest_route('productsplugin/v1', '/product-categories', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'product_categories'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Category Endpoints
        register_rest_route('productsplugin/v1', '/product-category', array(
            'methods' => 'POST',
            'callback' => array($this, 'product_category'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Category Endpoints
        register_rest_route('productsplugin/v1', '/create-product-category', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_product_category'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Product Category
        register_rest_route('productsplugin/v1', '/edit-product-category', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_product_category'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Currency List Endpoints
        register_rest_route('productsplugin/v1', '/currency-list', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'currency_list'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Currency Endpoints
        register_rest_route('productsplugin/v1', '/create-currency', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_currency'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Currency Endpoints
        register_rest_route('productsplugin/v1', '/edit-currency', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_currency'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Currency Info Endpoints
        register_rest_route('productsplugin/v1', '/currency', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_currency'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product Status List Endpoints
        register_rest_route('productsplugin/v1', '/product-status-list', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'product_status_list'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone Status List Endpoints
        register_rest_route('productsplugin/v1', '/milestone-stage-list', array(
            'methods' => 'POST',
            'callback' => array($this, 'milestone_stage_list')
        ));
        
        // Milestone list List Endpoints
        register_rest_route('productsplugin/v1', '/milestone-list', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'milestone_list'),
            //'permission_callback' => array($this, 'check_permissions')
        ));
        
        // Milestone all List Endpoints
        register_rest_route('productsplugin/v1', '/milestone-all-list', array(
            'methods' => 'POST',
            'callback' => array($this, 'milestone_all_list'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone search List Endpoints
        register_rest_route('productsplugin/v1', '/milestone-search', array(
            'methods' => 'POST',
            'callback' => array($this, 'milestone_search'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone detail Info Endpoints
        register_rest_route('productsplugin/v1', '/milestone-detail', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_milestone_detail'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone search List Endpoints
        register_rest_route('productsplugin/v1', '/check-milestone-unique', array(
            'methods' => 'POST',
            'callback' => array($this, 'check_milestone_unique'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone Stages Uniqueness Check Endpoints
        register_rest_route('productsplugin/v1', '/check-milestage-unique', array(
            'methods' => 'POST',
            'callback' => array($this, 'check_milestage_unique')
        ));

        // Opportunity search List Endpoints
        register_rest_route('productsplugin/v1', '/check-opportunity-unique', array(
            'methods' => 'POST',
            'callback' => array($this, 'check_opportunity_unique'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Product Status Endpoints
        register_rest_route('productsplugin/v1', '/create-product-status', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_product_status'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Product Status Endpoints
        register_rest_route('productsplugin/v1', '/edit-product-status', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_product_status'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product Status Info Endpoints
        register_rest_route('productsplugin/v1', '/product-status', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_product_status'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Product All Milestones Info Endpoints
        register_rest_route('productsplugin/v1', '/product-milestones', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_product_all_milestones'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Milestone All Status Info Endpoints
        register_rest_route('productsplugin/v1', '/milestone-status', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_product_all_milestone_status'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/create-milestone', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_milestone'),
        ));

        // Create Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/edit-milestone', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_milestone'),
        ));

        // Create Milestone Status Endpoints
        register_rest_route('productsplugin/v1', '/create-milestone-stage', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_milestone_stage'),
        ));

        // Edit Milestone Status Endpoints
        register_rest_route('productsplugin/v1', '/edit-milestone-stage', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_milestone_stage'),
        ));

        // Opportunity All api listing 
        // Create Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/create-opportunity', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_opportunity'),
        ));

        // Edit Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/edit-opportunity', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_opportunity'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/edit-opportunity-optional-field', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_opportunity_optional_field'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        register_rest_route('productsplugin/v1', '/opportunities', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_opportunities'),
        ));

        // Opportunity Info Endpoints
        register_rest_route('productsplugin/v1', '/opportunity', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_opportunity'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Delere Opportunity Endpoints
        register_rest_route('productsplugin/v1', '/delete_opportunity', array(
            'methods' => 'POST',
            'callback' => array($this, 'delete_opportunity'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Next Step Endpoints
        register_rest_route('productsplugin/v1', '/next-step-list', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'next_step_list'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Next Step Endpoints
        register_rest_route('productsplugin/v1', '/create-next-step', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_next_step'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Currency Endpoints
        register_rest_route('productsplugin/v1', '/edit-next-step', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_next_step'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Currency Info Endpoints
        register_rest_route('productsplugin/v1', '/next-step', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_next_step'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Get product detail Info Endpoints
        register_rest_route('productsplugin/v1', '/get-product-name', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_product_name'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Get lead contacts Endpoints
        register_rest_route('productsplugin/v1', '/lead-contacts', array(
            'methods' => 'POST',
            'callback' => array($this, 'lead_contacts'),
            //'permission_callback' => array($this, 'check_permissions')
        ));
        // Get opportunity notes Endpoints
        register_rest_route('productsplugin/v1', '/fetch_opportunity_notes', array(
            'methods' => 'POST',
            'callback' => array($this, 'fetch_opportunity_notes'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create opportunity notes Endpoints
        register_rest_route('productsplugin/v1', '/create_opportunity_notes', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_opportunity_notes'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Create Fee Type Endpoints
        register_rest_route('productsplugin/v1', '/create-fee-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'create_fee_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Fee Type Info Endpoints
        register_rest_route('productsplugin/v1', '/fee-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'get_fee_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Fee Types Endpoints
        register_rest_route('productsplugin/v1', '/fee-types', array(
            'methods' => WP_REST_Server::READABLE,
            'callback' => array($this, 'fee_types'),
            //'permission_callback' => array($this, 'check_permissions')
        ));

        // Edit Fee Type Endpoints
        register_rest_route('productsplugin/v1', '/edit-fee-type', array(
            'methods' => 'POST',
            'callback' => array($this, 'edit_fee_type'),
            //'permission_callback' => array($this, 'check_permissions')
        ));
    }

    // Permissions check callback
    public function check_permissions($request) {
        // Implement permission checks
        //return current_user_can('manage_options');
    }

    /**
     * Create a new product.
     *
     * @param array $data Product data.
     * @return int|WP_Error The ID of the newly created product, or WP_Error on failure.
     */
    public function create_product(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'crm_products';
        /*check product sku*/
        $product_data = $wpdb->get_results("SELECT SKU,DeletedAt FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.SKU = '".$data['SKU']."'");
        if(empty($product_data)){
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to create product into database', $wpdb->last_error);
            }else{
                $product_id = $wpdb->insert_id;
                $fields[0]['Title'] = $data['Title'];
                $fields[1]['SKU'] = $data['SKU'];
                $fields[2]['currency_id'] = $data['currency_id'];
                $fields[3]['UnitPrice'] = $data['UnitPrice'];
                $fields[4]['HourlyPrice'] = $data['HourlyPrice'];
                $fields[5]['UnitTypeID'] = $data['UnitTypeID'];
                $fields[6]['OwnerID'] = $data['OwnerID'];
                $fields[7]['ProductTypeID'] = $data['ProductTypeID'];
                $fields[8]['CategoryID'] = $data['CategoryID'];
                $fields[9]['LaunchDate'] = $data['LaunchDate'];
                $fields[10]['ExpiryDate'] = $data['ExpiryDate'];
                $fields[11]['Description'] = $data['Description'];
                $fields[12]['Status'] = $data['Status'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['CreatedAt'];
                        $logs_data['CreatedBy'] = $data['CreatedBy'];
                        $logs_data['TableName'] = $wpdb->prefix.'crm_products';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'SKU Already Exist';
            echo json_encode($results);die;
        }
    }

    /**
     * Edit a new product.
     *
     * @param array $data Product data.
     * @return int|WP_Error The ID of the previously created product, or WP_Error on failure.
     */
    public function edit_product(WP_REST_Request $request) {
        global $wpdb;;
        $product_id = $request->get_param('product_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'crm_products';
        unset($data['product_id']);
        /*check product sku*/
        $product_data = $wpdb->get_results("SELECT SKU FROM $table_name WHERE DeletedAt IS NULL AND  SKU = '".$data['SKU']."' AND ProductID != ".$product_id."");
        if(empty($product_data)){
            //Audit Logs Start
            $fields[0]['Title'] = $data['Title'];
            $fields[1]['SKU'] = $data['SKU'];
            $fields[2]['currency_id'] = $data['currency_id'];
            $fields[3]['UnitPrice'] = $data['UnitPrice'];
            $fields[4]['HourlyPrice'] = $data['HourlyPrice'];
            $fields[5]['UnitTypeID'] = isset($data['UnitTypeID']) ? $data['UnitTypeID'] : 0;
            $fields[6]['OwnerID'] = $data['OwnerID'];
            $fields[7]['ProductTypeID'] = $data['ProductTypeID'];
            $fields[8]['CategoryID'] = isset($data['CategoryID']) ? $data['CategoryID'] : 0;
            $fields[9]['LaunchDate'] = $data['LaunchDate'];
            $fields[10]['ExpiryDate'] = !empty($data['ExpiryDate']) ? $data['ExpiryDate'] : '0000-00-00';
            $fields[11]['Description'] = $data['Description'];
            $fields[12]['Status'] = $data['Status'];
            //$fields[13]['ProductLogo'] = isset($data['ProductLogo']) ? $data['ProductLogo'] : (isset($data['exist_logo']) ? $data['exist_logo'] : '';
            //$fields[14]['ProductImages'] = isset($data['ProductImages']) ? $data['ProductImages'] : isset($data['exist_image']) ? $data['exist_image'] : '';
            $i = 0;
            $audit_logs = [];
            if(isset($data['exist_logo'])){
                unset($data['exist_logo']);
            }
            if(isset($data['exist_image'])){
                unset($data['exist_image']);
            }
            
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."'  AND ProductID = ".$product_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE ProductID = ".$product_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['ModifiedAt'];
                        $logs_data['CreatedBy'] = $data['ModifiedBy'];
                        $logs_data['TableName'] = $wpdb->prefix.'crm_products';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            

            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('ProductID' => $product_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update product into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Updated';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'SKU Already Exist';
            echo json_encode($results);die;
        }
    }

    // Callbacks for Products Endpoints
    public function get_products($request) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'crm_products';
        $unit_type_table = $wpdb->prefix . 'unit_types';
        $product_type_table = $wpdb->prefix . 'product_types';
        $product_category_table = $wpdb->prefix . 'product_categories';
        $user_table = $wpdb->prefix . 'users';
        $query = $wpdb->prepare("SELECT 
                                $table_name.ProductID,$table_name.Title,$table_name.SKU,$table_name.UnitPrice,$table_name.HourlyPrice,$table_name.OwnerID,$table_name.Status,$table_name.LaunchDate,$table_name.ExpiryDate,$unit_type_table.UnitName,$product_type_table.TypeName as ProductType,$product_category_table.Name as ProductCategory,$user_table.display_name as OwnerName
                                FROM $table_name 
                                LEFT JOIN $unit_type_table ON $table_name.UnitTypeID = $unit_type_table.UnitTypeID
                                LEFT JOIN $product_type_table ON $table_name.ProductTypeID = $product_type_table.ProductTypeID
                                LEFT JOIN $product_category_table ON $table_name.CategoryID = $product_category_table.CategoryID
                                JOIN $user_table ON $table_name.OwnerID = $user_table.ID
                                WHERE $table_name.DeletedAt IS NULL
                                ORDER BY $table_name.ProductID DESC
                                ");
        return $wpdb->get_results($query);
    }

    // Callbacks for Products Endpoints
    public function get_product(WP_REST_Request $request) {
        global $wpdb;
        $product_id = $request->get_param('id');
        $table_name = $wpdb->prefix . 'crm_products';
        $unit_type_table = $wpdb->prefix . 'unit_types';
        $product_type_table = $wpdb->prefix . 'product_types';
        $product_category_table = $wpdb->prefix . 'product_categories';
        $user_table = $wpdb->prefix . 'users';
        $query = $wpdb->prepare("SELECT 
                                $table_name.*,$unit_type_table.UnitName,$product_type_table.TypeName as ProductType,$product_category_table.Name as ProductCategory,$user_table.display_name as OwnerName
                                FROM $table_name 
                                LEFT JOIN $unit_type_table ON $table_name.UnitTypeID = $unit_type_table.UnitTypeID
                                LEFT JOIN $product_type_table ON $table_name.ProductTypeID = $product_type_table.ProductTypeID
                                LEFT JOIN $product_category_table ON $table_name.CategoryID = $product_category_table.CategoryID
                                JOIN $user_table ON $table_name.OwnerID = $user_table.ID
                                WHERE $table_name.ProductID = '".$product_id."'
                                ");
        return $wpdb->get_row($query);
    }

    /**
     * Create a new unit type.
     *
     * @param array $data unit type data.
     * @return int|WP_Error The ID of the newly created unit type, or WP_Error on failure.
     */
    public function create_unit_type(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'unit_types';
        $unit_data = $wpdb->get_results("SELECT UnitName FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.UnitName = '".$data['UnitName']."'");
        if(empty($unit_data)){
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to insert unit type into database', $wpdb->last_error);
            }else{
                $unit_type_id = $wpdb->insert_id;
                $fields[0]['UnitName'] = $data['UnitName'];
                $fields[1]['Description'] = $data['Description'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['CreatedAt'];
                        $logs_data['CreatedBy'] = $data['CreatedBy'];
                        $logs_data['TableName'] = $wpdb->prefix.'unit_types';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $unit_type_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Unit Type Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Unit Name Already Exist';
            echo json_encode($results);die;
        }
        //return $wpdb->insert_id;
    }

    /**
     * Edit unit type.
     *
     * @param array $data unit type data.
     * @return int|WP_Error The ID of the previously created product, or WP_Error on failure.
     */
    public function edit_unit_type(WP_REST_Request $request) {
        global $wpdb;
        $unit_type_id = $request->get_param('unit_type_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'unit_types';
        $unit_data = $wpdb->get_results("SELECT UnitName FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.UnitName = '".$data['UnitName']."' AND UnitTypeID != ".$unit_type_id."");
        if(empty($unit_data)){
            unset($data['unit_type_id']);
            //Audit Logs Start
            $fields[0]['UnitName'] = $data['UnitName'];
            $fields[1]['Description'] = $data['Description'];
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."'  AND UnitTypeID = ".$unit_type_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE UnitTypeID = ".$unit_type_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['ModifiedAt'];
                        $logs_data['CreatedBy'] = $data['ModifiedBy'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $unit_type_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('UnitTypeID' => $unit_type_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update unit type into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Unit Type Update Successfully';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Unit Name Already Exist';
            echo json_encode($results);die;
        }
        //return $unit_type_id;
    }

    // Callbacks for Unit Types Endpoints
    public function unit_types($request) {
        global $wpdb;
        $unit_type_table = $wpdb->prefix . 'unit_types';
        $query = $wpdb->prepare("SELECT 
                                $unit_type_table.UnitTypeID,$unit_type_table.UnitName,$unit_type_table.Description
                                FROM $unit_type_table 
                                WHERE $unit_type_table.DeletedAt IS NULL
                                ORDER BY $unit_type_table.UnitTypeID DESC
                                ");
        return $wpdb->get_results($query);
    }

    // Callbacks for Unit Type Endpoints
    public function unit_type($request) {
        global $wpdb;
        $UnitTypeID = $request->get_param('id');
        $unit_type_table = $wpdb->prefix . 'unit_types';
        $query = $wpdb->prepare("SELECT 
                                $unit_type_table.UnitTypeID,$unit_type_table.UnitName,$unit_type_table.Description
                                FROM $unit_type_table 
                                WHERE $unit_type_table.UnitTypeID = '".$UnitTypeID."'
                                ");
        return $wpdb->get_row($query);
    }


    /**
     * Create a new product type.
     *
     * @param array $data product type data.
     * @return int|WP_Error The ID of the newly created product type, or WP_Error on failure.
     */
    public function create_product_type(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_types';
        $product_type_data = $wpdb->get_results("SELECT TypeName FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.TypeName = '".$data['TypeName']."'");
        if(empty($product_type_data)){
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to insert product type into database', $wpdb->last_error);
            }else{
                $product_type_id = $wpdb->insert_id;
                $fields[0]['TypeName'] = $data['TypeName'];
                $fields[1]['Description'] = $data['Description'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['CreatedAt'];
                        $logs_data['CreatedBy'] = $data['CreatedBy'];
                        $logs_data['TableName'] = $wpdb->prefix.'product_types';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_type_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Type Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Product Type Already Exist';
            echo json_encode($results);die;
        }
        //return $wpdb->insert_id;
    }

    

    // Callbacks for Product Types Endpoints
    public function product_types($request) {
        global $wpdb;
        $product_type_table = $wpdb->prefix . 'product_types';
        $query = $wpdb->prepare("SELECT 
                                $product_type_table.ProductTypeID,$product_type_table.TypeName,$product_type_table.Description
                                FROM $product_type_table 
                                WHERE $product_type_table.DeletedAt IS NULL
                                ORDER BY $product_type_table.ProductTypeID DESC
                                ");
        return $wpdb->get_results($query);
    }

    // Callbacks for Product Type Info Endpoints
    public function product_type($request) {
        global $wpdb;
        $product_type_id = $request->get_param('id');
        $product_type_table = $wpdb->prefix . 'product_types';
        $query = $wpdb->prepare("SELECT 
                                $product_type_table.ProductTypeID,$product_type_table.TypeName,$product_type_table.Description
                                FROM $product_type_table 
                                WHERE $product_type_table.ProductTypeID = '".$product_type_id."'
                                ");
        return $wpdb->get_row($query);
    }

    /**
     * Edit product type.
     *
     * @param array $data product type data.
     * @return int|WP_Error The ID of the previously created product, or WP_Error on failure.
     */
    public function edit_product_type(WP_REST_Request $request) {
        global $wpdb;
        $product_type_id = $request->get_param('product_type_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_types';
        $product_type_data = $wpdb->get_results("SELECT TypeName FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.TypeName = '".$data['TypeName']."' AND ProductTypeID != ".$product_type_id."");
        if(empty($product_type_data)){
            unset($data['product_type_id']);
            //Audit Logs Start
            $fields[0]['TypeName'] = $data['TypeName'];
            $fields[1]['Description'] = $data['Description'];
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."' AND ProductTypeID = ".$product_type_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE ProductTypeID = ".$product_type_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['ModifiedAt'];
                        $logs_data['CreatedBy'] = $data['ModifiedBy'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_type_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
        
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('ProductTypeID' => $product_type_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update product type into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Type Update Successfully';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Product Type Already Exist';
            echo json_encode($results);die;
        }
        //return $product_type_id;
    }

    // Callbacks for Product Categories Endpoints
    public function product_categories($request) {
        global $wpdb;
        $product_categories_table = $wpdb->prefix . 'product_categories';
        $query = $wpdb->prepare("SELECT 
                                c1.CategoryID,c1.Name,c1.Description,c2.Name as ParentCategory
                                FROM $product_categories_table c1
                                LEFT JOIN $product_categories_table c2 on c2.CategoryID = c1.ParentCategoryID
                                WHERE c1.DeletedAt IS NULL
                                ORDER BY c1.CategoryID DESC
                                ");
        return $wpdb->get_results($query);
    }

    // Callbacks for Product Category Info Endpoints
    public function product_category($request) {
        global $wpdb;
        $CategoryID = $request->get_param('id');
        $product_categories_table = $wpdb->prefix . 'product_categories';
        $query = $wpdb->prepare("SELECT 
                                $product_categories_table.CategoryID,$product_categories_table.Name,$product_categories_table.Description,$product_categories_table.ParentCategoryID,$product_categories_table.ProductId
                                FROM $product_categories_table 
                                WHERE $product_categories_table.CategoryID = '".$CategoryID."'
                                ");
        return $wpdb->get_row($query);
    }

     /**
     * Create a new product category.
     *
     * @param array $data product category data.
     * @return int|WP_Error The ID of the newly created product category, or WP_Error on failure.
     */
    public function create_product_category(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_categories';
        $product_category_data = $wpdb->get_results("SELECT Name FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.Name = '".$data['Name']."'");
        if(empty($product_category_data)){
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to insert product category into database', $wpdb->last_error);
            }else{
                $product_category_id = $wpdb->insert_id;
                $fields[0]['Name'] = $data['Name'];
                $fields[1]['Description'] = $data['Description'];
                //$fields[2]['ProductId'] = $data['ProductId'];
                if(isset($data['ParentCategoryID'])){
                    $fields[3]['ParentCategoryID'] = $data['ParentCategoryID'];
                }
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['CreatedAt'];
                        $logs_data['CreatedBy'] = $data['CreatedBy'];
                        $logs_data['TableName'] = $wpdb->prefix.'product_categories';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_category_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Category Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Product Category Already Exist';
            echo json_encode($results);die;
        }
        //return $wpdb->insert_id;
    }

    /**
     * Edit product category.
     *
     * @param array $data product category data.
     * @return int|WP_Error The ID of the previously created product category, or WP_Error on failure.
     */
    public function edit_product_category(WP_REST_Request $request) {
        global $wpdb;
        $product_category_id = $request->get_param('product_category_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_categories';
        $product_category_data = $wpdb->get_results("SELECT Name FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.Name = '".$data['Name']."' AND CategoryID != ".$product_category_id."");
        if(empty($product_category_data)){
            unset($data['product_category_id']);
            //Audit Logs Start
            $fields[0]['Name'] = $data['Name'];
            $fields[1]['Description'] = $data['Description'];
            //$fields[2]['ProductId'] = $data['ProductId'];
            if(isset($data['ParentCategoryID'])){
                $fields[3]['ParentCategoryID'] = $data['ParentCategoryID'];
            }
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."' AND CategoryID = ".$product_category_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE CategoryID = ".$product_category_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['ModifiedAt'];
                        $logs_data['CreatedBy'] = $data['ModifiedBy'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $product_category_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('CategoryID' => $product_category_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update product category into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Product Category Updated';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Product Category Already Exist';
            echo json_encode($results);die;
        }
        //return $product_category_id;
    }

    // Callbacks for Currency List Endpoints
    public function currency_list($request) {
        global $wpdb;
        $currency_table = $wpdb->prefix . 'currencies';
        $query = $wpdb->prepare("SELECT 
                                $currency_table.currency_id,$currency_table.currency_code,$currency_table.currency_name
                                FROM $currency_table 
                                WHERE $currency_table.deleted_at IS NULL
                                ORDER BY $currency_table.currency_id DESC
                                ");
        return $wpdb->get_results($query);
    }

    /**
     * Create a new currency.
     *
     * @param array $data currency data.
     * @return int|WP_Error The ID of the newly created currency, or WP_Error on failure.
     */
    public function create_currency(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'currencies';
        $currency_data = $wpdb->get_results("SELECT currency_code FROM $table_name WHERE $table_name.deleted_at IS NULL AND $table_name.currency_code = '".$data['currency_code']."'");
        if(empty($currency_data)){
            $result = $wpdb->insert($table_name, $data);

            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to insert currency into database', $wpdb->last_error);
            }else{
                $currency_id = $wpdb->insert_id;
                $fields[0]['currency_code'] = $data['currency_code'];
                $fields[1]['currency_name'] = $data['currency_name'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['created_at'];
                        $logs_data['CreatedBy'] = $data['created_by'];
                        $logs_data['TableName'] = $wpdb->prefix.'currencies';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $currency_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Currency Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Currency Code Already Exist';
            echo json_encode($results);die;
        }
        //return $wpdb->insert_id;
    }

    /**
     * Edit currency.
     *
     * @param array $data currency data.
     * @return int|WP_Error The ID of the previously created currency, or WP_Error on failure.
     */
    public function edit_currency(WP_REST_Request $request) {
        global $wpdb;
        $currency_id = $request->get_param('currency_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'currencies';
        $currency_data = $wpdb->get_results("SELECT currency_code FROM $table_name WHERE $table_name.deleted_at IS NULL AND $table_name.currency_code = '".$data['currency_code']."' AND currency_id != ".$currency_id."");
        if(empty($currency_data)){
            unset($data['currency_id']);
            //Audit Logs Start
            $fields[0]['currency_code'] = $data['currency_code'];
            $fields[1]['currency_name'] = $data['currency_name'];
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."' AND currency_id = ".$currency_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE currency_id = ".$currency_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['modified_at'];
                        $logs_data['CreatedBy'] = $data['modified_by'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $currency_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('currency_id' => $currency_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update currency into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Currency Updated';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Currency Code Already Exist';
            echo json_encode($results);die;
        }
        //return $product_type_id;
    }

    // Callbacks for currency Endpoints
    public function get_currency($request) {
        global $wpdb;
        $currency_id = $request->get_param('id');
        $currency_table = $wpdb->prefix . 'currencies';
        $query = $wpdb->prepare("SELECT 
                                $currency_table.currency_id,$currency_table.currency_code,$currency_table.currency_name
                                FROM $currency_table 
                                WHERE $currency_table.currency_id = '".$currency_id."'
                                ");
        return $wpdb->get_row($query);
    }

    // Callbacks for Product Status List Endpoints
    public function product_status_list($request) {
        global $wpdb;
        $product_status_table = $wpdb->prefix . 'product_status';
        $product_table = $wpdb->prefix . 'crm_products';
        $query = $wpdb->prepare("SELECT 
                                $product_status_table.product_status_id,$product_status_table.status,$product_table.Title as product_name
                                FROM $product_status_table 
                                JOIN $product_table ON $product_status_table.product_id = $product_table.ProductID
                                WHERE $product_status_table.deleted_at IS NULL
                                ORDER BY $product_status_table.product_status_id DESC
                                ");
        return $wpdb->get_results($query);
    }
    
    // Callbacks for Milestone Status List Endpoints
    public function milestone_stage_list($request) {
        global $wpdb;
        $milestone_stages_table = $wpdb->prefix . 'milestone_stages';
        $milestone_id = $request->get_param('mid');

        $where = '';
        if(isset($milestone_id) && !empty($milestone_id)){
            $where .= " AND $milestone_stages_table.milestone_id='".$milestone_id."'";
        }
        $query = $wpdb->prepare("SELECT $milestone_stages_table.*
                                FROM $milestone_stages_table
                                WHERE $milestone_stages_table.deleted_at IS NULL $where
                                ORDER BY $milestone_stages_table.milestone_stage_id DESC
                                ");
        return $wpdb->get_results($query);
    }

    // Callbacks for Milestone List Endpoints
    public function milestone_list($request) {
        global $wpdb;
        $milestones_table = $wpdb->prefix . 'milestones';
        $milestone_status_table = $wpdb->prefix . 'milestone_status';
        $query = $wpdb->prepare("SELECT $milestones_table.*
                                FROM $milestones_table
                                WHERE $milestones_table.deleted_at IS NULL
                                ORDER BY $milestones_table.milestone_id DESC
                                ");
        return $wpdb->get_results($query);
    }

    
    // Callbacks for Milestone Search List Endpoints
    public function milestone_all_list($request) {
        global $wpdb;
        $milestones_table = $wpdb->prefix . 'milestones';
                ini_set('display_errors', 1);
        ini_set('display_startup_errors', 1);
        error_reporting(E_ALL);

        $milestone_id = $request->get_param('search_milestone_id');
        $milestone_name = $request->get_param('search_milestone_name');
        
        $where = '';  
        $order_by_query="ORDER BY $milestones_table.milestone_id DESC";

        // if( isset($request->get_param('orderby')) && isset($request->get_param('order')) ){
            $orderby = $request->get_param('orderby');
            if(!empty($orderby)){
                $order = $request->get_param('order');
                $order_by_query = " ORDER BY $milestones_table.".$orderby." ".$order;
            }
        // }

        $query = "SELECT $milestones_table.* FROM $milestones_table WHERE $milestones_table.deleted_at IS NULL ".$order_by_query;

        // $query = "SELECT $milestones_table.*
        //                         FROM $milestones_table
        //                         WHERE $milestones_table.deleted_at IS NULL $where
        //                         ORDER BY $milestones_table.milestone_id DESC
        //                         ";
        // // echo 'query=';
        // echo $query;
        return $wpdb->get_results($query);
        // return true;
    }

    // Callbacks for Milestone Search List Endpoints
    public function milestone_search($request) {
        global $wpdb;
        $milestones_table = $wpdb->prefix . 'milestones';
        /*
         $data = $request->get_json_params();
        $milestone_id = $data['search_milestone_id'];
        $milestone_name = $data['search_milestone_name'];
        $map_lead = isset($data['search_map_lead']) ? $data['search_map_lead'] : '';
        $map_opportunity = isset($data['search_map_opportunity']) ? $data['search_map_opportunity'] : '';
        $map_account = isset($data['search_map_account']) ? $data['search_map_account'] : '';
        $status = $data['search_status'];
        $search_date = $data['search_created_at'];
        */
        $milestone_id = $request->get_param('search_milestone_id');
        $milestone_name = $request->get_param('search_milestone_name');
        $search_map_lead = $request->get_param('search_map_lead');
        $map_lead = isset($search_map_lead) ? $search_map_lead : '';
        $search_map_opportunity = $request->get_param('search_map_opportunity');
        $map_opportunity = isset($search_map_opportunity) ? $search_map_opportunity : '';
        $search_map_account = $request->get_param('search_map_account');
        $map_account = isset($search_map_account) ? $search_map_account : '';
        $status = $request->get_param('search_status');
        $search_date = $request->get_param('search_created_at');
        $search_product_id = $request->get_param('search_product_id');
        
        // $k=1;
        $where = '';  
        foreach ($search_product_id as $pkey => $pvalue) {
            $where .= "  AND FIND_IN_SET($pvalue,$milestones_table.product_id)";
        }
        // echo "==1where1==";
        //     echo $where;

        if(isset($milestone_id) && $milestone_id != ''){
            $where .= " AND $milestones_table.milestone_id = '".$milestone_id."'";
        }

        if (!empty($milestone_name)) {
            // Escape special characters in milestone_name
            $escaped_milestone_name = $wpdb->esc_like($milestone_name);
            $escaped_milestone_name = '%' . $escaped_milestone_name . '%';
            $where .= $wpdb->prepare(" AND $milestones_table.milestone_name LIKE %s", $escaped_milestone_name);
        }

        if ($map_lead == 'on') {
            $where .= " AND $milestones_table.map LIKE '%\"lead\"%'";
        }
        
        if ($map_opportunity == 'on') {
            $where .= " AND $milestones_table.map LIKE '%\"opportunity\"%'";
        }
        
        if ($map_account == 'on') {
            $where .= " AND $milestones_table.map LIKE '%\"account\"%'";
        }

        if(isset($status) && !empty($status)){
            $where .= " AND $milestones_table.status='".$status."'";
        }

        if(isset($search_date) && $search_date != ''){
            // Calculate the start and end of the day
            $start_of_day = $search_date . ' 00:00:00';
            $end_of_day = date('Y-m-d H:i:s', strtotime($search_date . ' +1 day'));
            
            $where .= " AND $milestones_table.created_at >= '$start_of_day' AND $milestones_table.created_at < '$end_of_day'";
        }

            $order_by_query=" ORDER BY $milestones_table.milestone_id DESC";
            $orderby = $request->get_param('orderby');
            if(!empty($orderby)){
                $order = $request->get_param('order');
                $order_by_query = " ORDER BY $milestones_table.".$orderby." ".$order;
            }

        $query = "SELECT $milestones_table.* FROM $milestones_table WHERE $milestones_table.deleted_at IS NULL $where $order_by_query";
        //return " The query is $query";
        // echo 'query=';
        // echo $query;
        return $wpdb->get_results($query);
    }

    // Callbacks for product status Endpoints
    public function get_milestone_detail($request) {
        global $wpdb;
        $mile_id = $request->get_param('id');
        $milestones_table = $wpdb->prefix . 'milestones';
        $query = $wpdb->prepare("SELECT 
                                $milestones_table.*
                                FROM $milestones_table 
                                WHERE $milestones_table.milestone_id = ".$mile_id."
                                ");
        return $wpdb->get_row($query);
    }
    
    // check milestone unique or not
    public function check_milestone_unique($request){
        global $wpdb;
        
        $milestone_name = $request->get_param('milestone_name');
        $product_id = $request->get_param('product_id');
        $milestone_id = $request->get_param('milestone_id');
        
        $milestones_table = $wpdb->prefix . 'milestones';
    
        if(isset($milestone_id) && !empty($milestone_id)){
            $miles_data = $wpdb->get_results($wpdb->prepare("SELECT milestone_id, milestone_name, product_id FROM $milestones_table WHERE milestone_name = %s AND deleted_at IS NULL AND milestone_id != %d", $milestone_name, $milestone_id));
        } else {
            $miles_data = $wpdb->get_results($wpdb->prepare("SELECT milestone_id, milestone_name, product_id FROM $milestones_table WHERE milestone_name = %s AND deleted_at IS NULL", $milestone_name));
        }
        
        $match = count($miles_data) != 0 ? 1 : 0;
    
        $response = array();
        if($match == 1){
            $response['status'] = 400;
            $response['message'] = 'Milestone Already Created.';
        } else {
            $response['status'] = 200;
            $response['message'] = 'Milestone Not Created.';
        }
        
        echo json_encode($response);
        die;
    }    

    // check Milestone Stage is unique or not
    public function check_milestage_unique($request){
        global $wpdb;
        
        $milestone_id = $request->get_param('milestone_id');
        $stage_name = $request->get_param('stage_name');
        $milestone_stage_id = $request->get_param('milestone_stage_id');
        
        $table = $wpdb->prefix . 'milestone_stages';

        $query = $wpdb->prepare("SELECT milestone_stage_id FROM $table WHERE stage_name = %s AND deleted_at IS NULL AND milestone_id = %d", $stage_name, $milestone_id);

        if(isset($milestone_stage_id) && !empty($milestone_stage_id)){
            $query .= $wpdb->prepare(" AND milestone_stage_id != %d", $milestone_stage_id);
        }

        $miles_data = $wpdb->get_results($query);

        $match = count($miles_data) > 0 ? 1 : 0;

        $response = array();
        if($match == 1){
            $response['status'] = 400;
            $response['message'] = 'Stage Name Already Exists.';
        } else {
            $response['status'] = 200;
            $response['message'] = 'Valid Stage Name.';
        }
        
        echo json_encode($response);
        die;
    }

    public function check_opportunity_unique($request){
        global $wpdb;
    
        $OpportunityName = $request->get_param('OpportunityName');
        $Opportunity_id = $request->get_param('Opportunity_id');
        $LeadID = $request->get_param('lead_id');
        $product_id = $request->get_param('product_id');
    
        $table_name = $wpdb->prefix . 'opportunities';
        $oppo_product = $wpdb->prefix . 'opportunity_products';
    
        if(isset($Opportunity_id) && $Opportunity_id > 0){
            $opps_data = $wpdb->get_results($wpdb->prepare("SELECT OpportunityID FROM $table_name WHERE OpportunityName = %s AND DeletedAt IS NULL AND OpportunityID != %d", $OpportunityName, $Opportunity_id));
        }else{
            $opps_data = $wpdb->get_results($wpdb->prepare("SELECT OpportunityID FROM $table_name WHERE OpportunityName = %s AND DeletedAt IS NULL", $OpportunityName));
        }
    
        try {
            $lead_oppo_data = $wpdb->get_results($wpdb->prepare("SELECT $table_name.OpportunityID FROM $table_name JOIN $oppo_product ON $table_name.OpportunityID = $oppo_product.Opportunity_id WHERE $table_name.LeadID = %s AND $table_name.DeletedAt IS NULL AND $oppo_product.product_id = %s", $LeadID, $product_id));
        } catch(Exception $e){
            echo $e->getMessage();
        }
    
        $match = 0;
        if(count($opps_data) != 0){
            $match = 1;
        }else if(count($lead_oppo_data) != 0){
            $match = 2;
        }
    
        $response = array();
        if($match == 1){
            $response['status'] = 400;
            $response['message'] = "This Opportunity is already created.";
        }else if($match == 2){
            $response['status'] = 400;
            $response['message'] = "Can't create Opportunity with same Product & Lead.";
        }else{
            $response['status'] = 200;
            $response['message'] = 'Opportunity Not Created.';
        }
    
        echo json_encode($response);
        die;
    }    

    /**
     * Create a new product status.
     *
     * @param array $data product status data.
     * @return int|WP_Error The ID of the newly created product status, or WP_Error on failure.
     */
    public function create_product_status(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_status';
        $result = $wpdb->insert($table_name, $data);

        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to insert product status into database', $wpdb->last_error);
        }else{
            $product_status_id = $wpdb->insert_id;
            $fields[0]['product_id'] = $data['product_id'];
            $fields[1]['status'] = $data['status'];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['created_at'];
                    $logs_data['CreatedBy'] = $data['created_by'];
                    $logs_data['TableName'] = $wpdb->prefix.'product_status';
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = 'N/A';
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $product_status_id;
                    $logs_data['Action'] = 'Create';
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                }
            }
        }

        return $wpdb->insert_id;
    }
    /**
     * Edit Product Status.
     *
     * @param array $data Product Status data.
     * @return int|WP_Error The ID of the previously created Product Status, or WP_Error on failure.
     */
    public function edit_product_status(WP_REST_Request $request) {
        global $wpdb;
        $product_status_id = $request->get_param('product_status_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'product_status';
        unset($data['currency_id']);
        //Audit Logs Start
        $fields[0]['product_id'] = $data['product_id'];
        $fields[1]['status'] = $data['status'];
        $i = 0;
        $audit_logs = [];
        foreach($fields as $field){
            foreach($field as $FieldName => $fieldValue){
                $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."' AND product_status_id = ".$product_status_id."");
                if(empty($checkPreviousData)){
                    $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE product_status_id = ".$product_status_id."");
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['modified_at'];
                    $logs_data['CreatedBy'] = $data['modified_by'];
                    $logs_data['TableName'] = $table_name;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $product_status_id;
                    $logs_data['Action'] = 'Update';
                    $audit_logs[$i]['logs_data'] = $logs_data;
                    $i++;
                }
            }
        }
        //Audit Logs End
        $result = $wpdb->update($table_name, $data, array('product_status_id' => $product_status_id));
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to update product status into database', $wpdb->last_error);
        }else{
            if(!empty($audit_logs)){
                foreach($audit_logs as $audit_log){
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                }
            }
        }

        return $product_status_id;
    }
    // Callbacks for product status Endpoints
    public function get_product_status($request) {
        global $wpdb;
        $product_status_id = $request->get_param('id');
        $product_status_table = $wpdb->prefix . 'product_status';
        $query = $wpdb->prepare("SELECT 
                                $product_status_table.product_status_id,$product_status_table.product_id,$product_status_table.status
                                FROM $product_status_table 
                                WHERE $product_status_table.product_status_id = '".$product_status_id."'
                                ");
        return $wpdb->get_row($query);
    }

    // Callbacks for milestone Endpoints
    public function get_product_all_milestones($request) {
        
        global $wpdb;
        $product_id = $request->get_param('id');
        $milestones_table = $wpdb->prefix . 'milestones';
        $milestone_stages_table = $wpdb->prefix . 'milestone_stages';
        $query = "SELECT $milestones_table.milestone_id,$milestones_table.milestone_name FROM $milestones_table WHERE FIND_IN_SET($product_id,$milestones_table.product_id) AND $milestones_table.deleted_at IS NULL AND $milestones_table.status = 'active' AND $milestones_table.map LIKE '%\"opportunity\"%' ";
        
        $all_milestones = $wpdb->get_results($query);
        
        if(!empty($all_milestones)){
            $data['milestone_results'] = $all_milestones;
            $milestone_id = $all_milestones[0]->milestone_id;
            $stage_results = $wpdb->get_results("SELECT $milestone_stages_table.milestone_stage_id,$milestone_stages_table.stage_name FROM $milestone_stages_table WHERE milestone_id = ".$milestone_id."");
            if(!empty($stage_results)){
                $data['stage_results'] = $stage_results;
            }else{
                $data['stage_results'] = array();
            }
        }else{
            $data['milestone_results'] = array();
            $data['stage_results'] = array();
        }
        //return $wpdb->get_results($query);
        echo json_encode($data);die;
    }

    // Callbacks for milestone status Endpoints
    public function get_product_all_milestone_status($request) {
        
        global $wpdb;
        $milestone_id = $request->get_param('id');
        $milestone_status_table = $wpdb->prefix . 'milestone_stages';
        $query = $wpdb->prepare("SELECT $milestone_status_table.milestone_stage_id,$milestone_status_table.stage_name FROM $milestone_status_table WHERE $milestone_status_table.milestone_id = ".$milestone_id." AND $milestone_status_table.deleted_at IS NULL AND $milestone_status_table.status = 'active'");
        return $wpdb->get_results($query);

    }

    
    public function create_milestone(WP_REST_Request $request){
            global $wpdb;
            $data = $request->get_json_params();
            $milestones_table = $wpdb->prefix . 'milestones';
            
            $fields[0]['milestone_name'] = $data['milestone_name'];
            $fields[1]['product_id'] = $data['product_id'];
            $fields[2]['map'] = $data['map'];
            $fields[3]['status'] = $data['status'];
            $fields[4]['created_at'] = $data['created_at'];
            $fields[5]['created_by'] = $data['created_by'];

            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$milestones_table."' AND COLUMN_NAME = '".$FieldName."'");
                        
                        $logs_data['DateCreated'] = $data['created_at'];
                        $logs_data['CreatedBy'] = $data['created_by'];
                        $logs_data['TableName'] = $milestones_table;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = '';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = '';
                        $logs_data['Action'] = 'Create';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                }
            }

            $result = $wpdb->insert($milestones_table, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to create product into database', $wpdb->last_error);
            }else{
            
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }

                $reuslts['status'] = 1;
                $reuslts['message'] = 'Milestone Created.';
                echo json_encode($reuslts);die;
            }
    }

    public function edit_milestone(WP_REST_Request $request){
            global $wpdb;
            $data = $request->get_json_params();
            $milestones_table = $wpdb->prefix . 'milestones';
            $milestone_id = $data['milestone_id'];
            
            $fields[0]['milestone_name'] = $data['milestone_name'];
            $fields[1]['product_id'] = $data['product_id'];
            $fields[2]['status'] = $data['status'];
            $fields[3]['modified_at'] = $data['modified_at'];
            $fields[4]['modified_by'] = $data['modified_by'];

            $audit_logs = [];

            foreach($fields as $field){
            foreach($field as $FieldName => $fieldValue){
                $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $milestones_table WHERE $FieldName = '".$fieldValue."'  AND milestone_id = ".$milestone_id."");
                if(empty($checkPreviousData)){
                    $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $milestones_table WHERE milestone_id = ".$milestone_id."");
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$milestones_table."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['modified_at'];
                    $logs_data['CreatedBy'] = $data['modified_by'];
                    $logs_data['TableName'] = $milestones_table;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $milestone_id;
                    $logs_data['Action'] = 'Update';
                    $audit_logs[$i]['logs_data'] = $logs_data;
                    $i++;
                }
            }
        }


            $result = $wpdb->update($milestones_table, $data, array('milestone_id' => $milestone_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to create product into database', $wpdb->last_error);
            }else{
                
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }

                $reuslts['status'] = 1;
                $reuslts['message'] = 'Milestone Updated.';
                echo json_encode($reuslts);die;
            }

    }

    public function create_milestone_stage(WP_REST_Request $request){
        global $wpdb;
        $data = $request->get_json_params();
        $milestatus_table = $wpdb->prefix . 'milestone_stages';
        // Execute the query and get a single value
        $fields[0]['milestone_id'] = $data['milestone_id'];
        $fields[1]['stage_name'] = $data['stage_name'];
        $fields[2]['status'] = $data['status'];
        $fields[3]['created_at'] = $data['created_at'];
        $fields[4]['created_by'] = $data['created_by'];

        $i = 0;
        $audit_logs = [];
        foreach($fields as $field){
            foreach($field as $FieldName => $fieldValue){
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$milestatus_table."' AND COLUMN_NAME = '".$FieldName."'");
                    
                    $logs_data['DateCreated'] = $data['created_at'];
                    $logs_data['CreatedBy'] = $data['created_by'];
                    $logs_data['TableName'] = $milestatus_table;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = '';
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = '';
                    $logs_data['Action'] = 'Create';
                    $audit_logs[$i]['logs_data'] = $logs_data;
                    $i++;
            }
        }
        
        $result = $wpdb->insert($milestatus_table, $data);
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to create Milestone Stage into database', $wpdb->last_error);
        }else{
        
            if(!empty($audit_logs)){
                foreach($audit_logs as $audit_log){
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                }
            }

            $reuslts['status'] = 1;
            $reuslts['message'] = 'Milestone Stage Created.';
            echo json_encode($reuslts);die;
        }
    }

    public function edit_milestone_stage(WP_REST_Request $request){
        global $wpdb;
        $data = $request->get_json_params();
        $milestatus_table = $wpdb->prefix . 'milestone_stages';
        $milestone_stage_id = $data['milestone_stage_id'];
        
        $fields[0]['milestone_id'] = $data['milestone_id'];
        $fields[1]['stage_name'] = $data['stage_name'];
        $fields[2]['status'] = $data['status'];
        $fields[3]['modified_at'] = $data['modified_at'];
        $fields[4]['modified_by'] = $data['modified_by'];

        $audit_logs = [];

        foreach($fields as $field){
            foreach($field as $FieldName => $fieldValue){
                $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $milestatus_table WHERE $FieldName = '".$fieldValue."'  AND milestone_stage_id = ".$milestone_stage_id."");
                if(empty($checkPreviousData)){
                    $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $milestatus_table WHERE milestone_stage_id = ".$milestone_stage_id."");
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$milestatus_table."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['modified_at'];
                    $logs_data['CreatedBy'] = $data['modified_by'];
                    $logs_data['TableName'] = $milestatus_table;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $milestone_stage_id;
                    $logs_data['Action'] = 'Update';
                    $audit_logs[$i]['logs_data'] = $logs_data;
                    $i++;
                }
            }
        }
        
        $result = $wpdb->update($milestatus_table, $data, array('milestone_stage_id' => $milestone_stage_id));
        
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to create Milestone Stage into database', $wpdb->last_error);
        }else{
            
            if(!empty($audit_logs)){
                foreach($audit_logs as $audit_log){
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                }
            }

            $reuslts['status'] = 1;
            $reuslts['message'] = 'Milestone Stage Updated.';
            echo json_encode($reuslts);die;
        }

    }


    public function create_opportunity(WP_REST_Request $request) {
        global $wpdb;
        
        $data = $request->get_json_params();
        $datas = json_encode($data);
        $table_name = $wpdb->prefix . 'opportunities';
        $opportunity_product_table = $wpdb->prefix . 'opportunity_products';
        
            $post_data['OpportunityName'] = $data['OpportunityName'];
            $post_data['LeadID'] = $data['LeadID'];
            $post_data['ContactID'] = $data['ContactID'];
            $post_data['ExpectedCloseDate'] = date("Y-m-d", strtotime($data['ExpectedCloseDate']));
            $post_data['OpportunityCurrency'] = $data['OpportunityCurrency'];
            $post_data['OpportunityAmount'] = $data['OpportunityAmount'];
            $post_data['Probability'] = $data['Probability'];
            // $post_data['OpportunityCategory'] = $data['OpportunityCategory'];
            $post_data['NextStep'] = $data['NextStep'];
            $post_data['Description'] = $data['Description'];
            $post_data['CreatedBy'] = $data['CreatedBy'];
            $post_data['CreatedAt'] = $data['CreatedAt'];

            // $results = $wpdb->get_results('DESCRIBE '.$table_name);

            // echo 'i am here4';print_r($results);exit;
            $result = $wpdb->insert($table_name, $post_data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to create product into database', $wpdb->last_error);
            }else{
                $opportunity_id = $wpdb->insert_id;
                // wp_mail("<EMAIL>","opportunity_id",$opportunity_id);
                $note = $data['Notes'];
                if ($note) {
                    $post_data['Notes'] = $data['Notes']; // Add Notes field to $post_data for audit log purpose
                    $user_id = $data['CreatedBy'];
                    $time = date("Y-m-d h:i:sa");                   
                    $notes_table = $wpdb->prefix.'erc_opportunity_notes';
                    $result = $wpdb->get_results("INSERT INTO $notes_table (`opportunity_id`, `created_by`, `note`, `created`) VALUES ('".$opportunity_id."', '".$user_id."', '".$note."', '".$time."')");
                }

                $total_product = 1;
                $product_da['opportunity_id'] = $opportunity_id;
                $product_da['CreatedAt'] = $data['CreatedAt'];
                $product_da['CreatedBy'] = $data['CreatedBy'];
                
                $total_products = 2;
                $set_product_no = 0;
                $i = 1;


                // for($i=1;$i<=$total_products;$i++){
                    if(isset($data['product_id-'.$i])){
                        $product_da['product_id'] = $data['product_id-'.$i];
                        $product_da['milestone_id'] = $data['milestone-'.$i];
                        $product_da['milestone_stage_id'] = $data['milestone_status-'.$i];
                        //$product_da['product_amount'] = $data['product_amount-'.$i];
                        try{
                            $pro_result = $wpdb->insert($opportunity_product_table, $product_da);
                            $product_insert_id = $wpdb->insert_id;    
                            $set_product_no +=1;
                        
                            foreach($product_da as $pFieldName => $pfieldValue){
                                $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$opportunity_product_table."' AND COLUMN_NAME = '".$pFieldName."'");
                                    $pro_logs_data['DateCreated'] = $data['CreatedAt'];
                                    $pro_logs_data['CreatedBy'] = $data['CreatedBy'];
                                    $pro_logs_data['TableName'] = $opportunity_product_table;
                                    $pro_logs_data['FieldName'] = $pFieldName;
                                    $pro_logs_data['DataType'] = $DATA_TYPE;
                                    $pro_logs_data['BeforeValueString'] = 'N/A';
                                    $pro_logs_data['AfterValueString'] = $pfieldValue;
                                    $pro_logs_data['FieldID'] = $product_insert_id;
                                    $pro_logs_data['Action'] = 'Create';
                                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                                    $audit_log_data = $audit_log_manager->record_audit_log($pro_logs_data);
                            }
                        }catch(EXCEPTION $e){
                            echo $e->getMessage();
                            wp_mail("<EMAIL>","test error",$e->getMessage());
                        }
                    }
                //     if($total_product==$set_product_no){
                //         break;
                //     }
                // }//loop    
                
                foreach($post_data as $FieldName => $fieldValue){
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");

                    $logs_data['DateCreated'] = $data['CreatedAt'];
                    $logs_data['CreatedBy'] = $data['CreatedBy'];
                    $logs_data['TableName'] = $table_name;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = '';
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $opportunity_id;
                    $logs_data['Action'] = 'Create';
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                }
                $reuslts['status'] = 1;
                $reuslts['message'] = 'Opportunity Created';
                echo json_encode($reuslts);die;
            }    
    }// create opportunity function end

    //Edit Opportunity
    public function edit_opportunity(WP_REST_Request $request) {
        global $wpdb;
        $i = 0;
        $data = $request->get_json_params();
        $opportunity_id = $request->get_param('opportunity_id');
        $table_name = $wpdb->prefix . 'opportunities';
        $opportunity_product_table = $wpdb->prefix . 'opportunity_products';
        unset($data['OpportunityID']);
        $fields[0]['OpportunityName'] = $data['OpportunityName'];
        $fields[0]['LeadID'] = $data['LeadID'];
        $fields[0]['ExpectedCloseDate'] = $data['ExpectedCloseDate'];
        $fields[0]['OpportunityCurrency'] = $data['OpportunityCurrency'];
        $fields[0]['OpportunityAmount'] = $data['OpportunityAmount'];
        $fields[0]['Probability'] = $data['Probability'];
        //$fields[0]['OpportunityCategory'] = $data['OpportunityCategory'];
        $fields[0]['NextStep'] = $data['NextStep'];
        $fields[0]['Description'] = $data['Description'];
        $audit_logs = [];
        $post_data['OpportunityName'] = $data['OpportunityName'];
        $post_data['LeadID'] = $data['LeadID'];
        $post_data['ExpectedCloseDate'] = $data['ExpectedCloseDate'];
        $post_data['OpportunityCurrency'] = $data['OpportunityCurrency'];
        $post_data['OpportunityAmount'] = $data['OpportunityAmount'];
        $post_data['Probability'] = $data['Probability'];
        $post_data['OpportunityCategory'] = $data['OpportunityCategory'];
        $post_data['NextStep'] = $data['NextStep'];
        $post_data['Description'] = $data['Description'];
        $post_data['ModifiedBy'] = $data['ModifiedBy'];
        $post_data['ModifiedAt'] = $data['ModifiedAt'];
        foreach($fields as $field){
            foreach($field as $FieldName => $fieldValue){
                $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."'  AND OpportunityID = ".$opportunity_id."");
                if(empty($checkPreviousData)){
                    $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE OpportunityID = ".$opportunity_id."");
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['ModifiedAt'];
                    $logs_data['CreatedBy'] = $data['ModifiedBy'];
                    $logs_data['TableName'] = $wpdb->prefix.'opportunities';
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $opportunity_id;
                    $logs_data['Action'] = 'Update';
                    $audit_logs[$i]['logs_data'] = $logs_data;
                    $i++;
                }
            }
        }   
        //Audit Logs End
        $result = $wpdb->update($table_name, $post_data, array('OpportunityID' => $opportunity_id));
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to update opportunity into database', $wpdb->last_error);
        }else{
            $total_product = $data['all_product-listing'];
            $product_da['opportunity_id'] = $opportunity_id;
            $product_ids = '';
            for($i=1;$i<=$total_product;$i++){
                if(isset($data['product_id-'.$i])){
                    $product_ids .= $data['product_id-'.$i].',';
                    $opportunity_product_id = $data['opportunity_product_id-'.$i];
                    $product_da['product_id'] = $data['product_id-'.$i];
                    $product_da['milestone_id'] = $data['milestone-'.$i];
                    $product_da['milestone_status_id'] = $data['milestone_status-'.$i];
                    //$product_da['product_amount'] = $data['product_amount-'.$i];
                    $pro_fields[0]['product_id'] = $data['product_id-'.$i];
                    $pro_fields[0]['milestone_id'] = $data['milestone-'.$i];
                    $pro_fields[0]['milestone_status_id'] = $data['milestone_status-'.$i];
                    $pro_fields[0]['product_amount'] = $data['product_amount-'.$i];
                    $j = 0;
                    foreach($pro_fields as $pro_field){
                        foreach($pro_field as $ProFieldName => $profieldValue){
                            $checkProPreviousData = $wpdb->get_row("SELECT $ProFieldName FROM $opportunity_product_table WHERE $ProFieldName = '".$profieldValue."'  AND opportunity_product_id = ".$opportunity_product_id."");
                            if(empty($checkProPreviousData)){
                                $getProPreviousVal = $wpdb->get_row("SELECT $ProFieldName FROM $opportunity_product_table WHERE opportunity_product_id = ".$opportunity_product_id."");
                                $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$opportunity_product_table."' AND COLUMN_NAME = '".$ProFieldName."'");
                                if($opportunity_product_id > 0 ){
                                    $pro_logs_data['DateCreated'] = $data['ModifiedAt'];
                                    $pro_logs_data['CreatedBy'] = $data['ModifiedBy'];
                                    $pro_logs_data['BeforeValueString'] = $getProPreviousVal->$ProFieldName;
                                }else{
                                    $pro_logs_data['DateCreated'] = $data['CreatedAt'];
                                    $pro_logs_data['CreatedBy'] = $data['CreatedBy'];
                                    $pro_logs_data['BeforeValueString'] = 'N/A';
                                }
                                $pro_logs_data['TableName'] = $opportunity_product_table;
                                $pro_logs_data['FieldName'] = $ProFieldName;
                                $pro_logs_data['DataType'] = $DATA_TYPE;
                                $pro_logs_data['AfterValueString'] = $profieldValue;
                                $pro_logs_data['FieldID'] = $opportunity_product_id;
                                $pro_logs_data['Action'] = 'Update';
                                $pro_audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                                $pro_audit_log_data = $pro_audit_log_manager->record_audit_log($pro_logs_data);
                            }
                        }
                    }
                    if($opportunity_product_id > 0 ){
                        $product_da['ModifiedAt'] = $data['ModifiedAt'];
                        $product_da['ModifiedBy'] = $data['ModifiedBy'];
                        $result = $wpdb->update($opportunity_product_table, $product_da, array('opportunity_product_id' => $opportunity_product_id));
                    }else{
                        /*$product_da['CreatedAt'] = $data['CreatedAt'];
                        $product_da['CreatedBy'] = $data['CreatedBy'];
                        $result = $wpdb->insert($opportunity_product_table, $product_da);*/
                    }
                }
            }
            $product_ids = rtrim($product_ids,',');
            if($product_ids != ''){
                //echo "UPDATE $opportunity_product_table SET DeletedAt = '".$data['ModifiedAt']."', DeletedBy = ".$data['ModifiedBy']." WHERE product_id NOT IN (".$product_ids.")";die;
                $wpdb->query("UPDATE ".$opportunity_product_table." SET DeletedAt = '".$data['ModifiedAt']."', DeletedBy = ".$data['ModifiedBy']." WHERE product_id NOT IN (".$product_ids.")");
            }
            if(!empty($audit_logs)){
                foreach($audit_logs as $audit_log){
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                }
            }
            $reuslts['status'] = 1;
            $reuslts['message'] = 'Opportunity Updated';
            echo json_encode($reuslts);die;
        }
    }



    //Edit Opportunity with optional field
    public function edit_opportunity_optional_field($request) {
        global $wpdb;
        $reuslts = array();
        $opportunity_table = $wpdb->prefix . 'opportunities';

        $OpportunityID = $request->get_param('OpportunityID');
        $NextStep = $request->get_param('NextStep');
        $ExpectedCloseDate = $request->get_param('ExpectedCloseDate');
        $OpportunityAmount = $request->get_param('OpportunityAmount');
        $Probability = $request->get_param('Probability');
        $OwnerList = $request->get_param('OwnerList');
        $ContactList = $request->get_param('ContactList');
        $opportunityClosure = $request->get_param('opportunityClosure');

        //print_r($request->get_param('OpportunityID'));exit;
        if(!empty($OpportunityID)){
            $opportunity_id = $OpportunityID;
            $post_data = array();

            $edit = 0;
            if(!empty($NextStep) || $NextStep == 0){
            	if($NextStep == 0){
            		$NextStep = '';
            	}
                $post_data['NextStep'] = $NextStep;
                $post_data['nextStep_deletedat'] = NULL;
                $post_data['nextStep_deletedby'] = NULL;
                $edit = 1;
            }
            if(!empty($ExpectedCloseDate)){
                $post_data['ExpectedCloseDate'] = date("Y-m-d", strtotime($ExpectedCloseDate));
                $edit = 1;
            }
            if(!empty($OpportunityAmount)){
                $post_data['OpportunityAmount'] = str_replace( ',', '', $OpportunityAmount);
                $edit = 1;
            }
            if(!empty($Probability) || $Probability == 0){
            	if($Probability == 0){
            		$Probability = '';
            	}
                $post_data['Probability'] = $Probability;
                $edit = 1;
            }
            if(!empty($OwnerList)){
                $post_data['CreatedBy'] = $OwnerList;
                $edit = 1;
            }

            if(!empty($ContactList)){
                $post_data['ContactID'] = $ContactList;
                $post_data['contact_deletedat'] = NULL;
                $post_data['contact_deletedby'] = NULL;
                $edit = 1;
            }

            if(!empty($opportunityClosure)){
                $post_data['opportunityClosure'] = $opportunityClosure;
                $edit = 1;
            }
            
            if($edit){
                $result = $wpdb->update($opportunity_table, $post_data, array('OpportunityID' => $opportunity_id));
            }

            //print_r($post_data);

            //update milestone
            $milestone_data = array();
            $opportunity_product_table = $wpdb->prefix.'opportunity_products';
            $Milestone = $request->get_param('Milestone');
            $MilestoneStage = $request->get_param('MilestoneStage');

            $edit = 0;
            if(!empty($Milestone)){
                $milestone_data['milestone_id'] = $Milestone;
                $milestone_data['milestone_deletedat'] = NULL;
                $milestone_data['milestone_deletedby'] = NULL;
                $edit = 1;
            }
            if(!empty($MilestoneStage)){
                $milestone_data['milestone_stage_id'] = $MilestoneStage;
                $milestone_data['stage_deletedat'] = NULL;
                $milestone_data['stage_deletedby'] = NULL;
                $edit = 1;
            }

            if($edit){
                $result = $wpdb->update($opportunity_product_table, $milestone_data, array('opportunity_id' => $opportunity_id));
            }


            //get opportunity data
            $additional_table = $wpdb->prefix.'erc_iris_leads_additional_info';
            $business_info_table = $wpdb->prefix.'erc_business_info';
            $opportunity_table = $wpdb->prefix.'opportunities';
            $opportunity_product_table = $wpdb->prefix.'opportunity_products';
            $product_table = $wpdb->prefix.'crm_products';
            $milestone_table = $wpdb->prefix.'milestones';
            $milestone_status_table = $wpdb->prefix.'milestone_stages';
            $next_step_table = $wpdb->prefix.'next_step';
            $currency_table = $wpdb->prefix.'currencies';
            $contacts_table = $wpdb->prefix.'op_contacts';
            $where = ' WHERE 1=1 ';

            if(!empty($OpportunityID)){
                $where .= " AND opp.OpportunityID ='".$OpportunityID."'";
            }

            $sql = "SELECT opp.*, prod.Title as productnName,prod.ProductID as product_ID, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus, ns.next_step  as NextStepName, ns.next_step_id, buss.business_legal_name as AccountName, mile.milestone_id, mile_status.milestone_stage_id, ns.next_step, curr.currency_code as currencyName FROM $opportunity_table opp left join $opportunity_product_table oppPro on opp.OpportunityID = oppPro.opportunity_id left join $product_table prod on oppPro.product_id = prod.ProductID left join $milestone_table mile on oppPro.milestone_id = mile.milestone_id left join $milestone_status_table mile_status on oppPro.milestone_stage_id = mile_status.milestone_stage_id left join $next_step_table ns on opp.NextStep = ns.next_step_id left join $business_info_table buss on opp.LeadID = buss.lead_id left join $currency_table curr on opp.OpportunityCurrency = curr.currency_id " . $where;
            $opportunity = $wpdb->get_results($sql);
            $single_opportunity = $opportunity[0];

            $user_details = get_user_by( 'id', $single_opportunity->CreatedBy );
            $single_opportunity->ownerName = $user_details->display_name;
            $contact_data = $wpdb->get_row("SELECT first_name,last_name FROM $contacts_table WHERE id = ".$single_opportunity->ContactID."");
            if(!empty($contact_data)){
                $primary_contact = $contact_data->first_name.' '.$contact_data->last_name;
            }else{
                $primary_contact = '';
            }
            $single_opportunity->ContactName = $primary_contact;

            
            $reuslts['status'] = true;
            $reuslts['message'] = 'Opportunity Updated';
            $reuslts['data'] = $single_opportunity;
            
        }else{
            $reuslts['status'] = false;
            $reuslts['message'] = 'Something Went Worng';
        }
        

        echo json_encode($reuslts);die;
        
    }
    // Opportunity listing
    public function get_opportunities($request) {
        global $wpdb;
        
        $additional_table = $wpdb->prefix.'erc_iris_leads_additional_info';
        $business_info_table = $wpdb->prefix.'erc_business_info';
        $opportunity_table = $wpdb->prefix.'opportunities';
        $opportunity_product_table = $wpdb->prefix.'opportunity_products';
        $product_table = $wpdb->prefix.'crm_products';
        $milestone_table = $wpdb->prefix.'milestones';
        $milestone_status_table = $wpdb->prefix.'milestone_stages';
        $next_step_table = $wpdb->prefix.'next_step';
        $currency_table = $wpdb->prefix.'currencies';

        $where = " WHERE opp.DeletedAt IS NULL ";

        $sql = "SELECT opp.*, prod.Title as productName,prod.ProductID as product_ID, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus, ns.next_step  as NextStepName, buss.business_legal_name as AccountName, mile.milestone_id, mile_status.milestone_stage_id, ns.next_step, curr.currency_code as currencyName FROM $opportunity_table opp left join $opportunity_product_table oppPro on opp.OpportunityID = oppPro.opportunity_id left join $product_table prod on oppPro.product_id = prod.ProductID left join $milestone_table mile on oppPro.milestone_id = mile.milestone_id left join $milestone_status_table mile_status on oppPro.milestone_stage_id = mile_status.milestone_stage_id left join $next_step_table ns on opp.NextStep = ns.next_step_id left join $business_info_table buss on opp.LeadID = buss.lead_id   left join $currency_table curr on opp.OpportunityCurrency = curr.currency_id " . $where;
        
        $order_by = ' ORDER BY OpportunityID DESC ';
        $sql .= $order_by;
        // echo $sql;
        $opportunity = $wpdb->get_results($sql);
        return $opportunity;
    }

     // Opportunity get
    public function get_opportunity($request) {
        global $wpdb;
        $opportunity_id = $request->get_param('id');
        $opportunity_table = $wpdb->prefix . 'opportunities';
        $opportunity_products_table = $wpdb->prefix . 'opportunity_products';
        $audit_logs = $wpdb->prefix . 'audit_logs';
        $user_table = $wpdb->prefix . 'users';
        $query = $wpdb->prepare("SELECT 
                                $opportunity_table.*
                                FROM $opportunity_table 
                                WHERE $opportunity_table.DeletedAt IS NULL AND $opportunity_table.OpportunityID = ".$opportunity_id."
                                ORDER BY $opportunity_table.OpportunityID DESC
                                ");
        $data = $wpdb->get_row($query);

        $pro_query = $wpdb->prepare("SELECT 
                                $opportunity_products_table.*
                                FROM $opportunity_products_table 
                                WHERE $opportunity_products_table.DeletedAt IS NULL AND $opportunity_products_table.opportunity_id = ".$opportunity_id);
        // print_r($data);
        $products = $wpdb->get_results($pro_query);
        // print_r($products);die();
        $data->products = $products;
        return $data;
    }

    // Delete Opportunity get
    public function delete_opportunity($request) {
        global $wpdb;
        $response = array();
        $opportunity_id = $request->get_param('id');
        
        if(!empty($opportunity_id)){
            $opportunity_table = $wpdb->prefix.'opportunities';
            $opportunity_product_table = $wpdb->prefix.'opportunity_products';

            $sql = "Delete FROM $opportunity_product_table where opportunity_id  = ".$opportunity_id;
            $result = $wpdb->get_results($sql);
            $sql = "Delete FROM $opportunity_table where OpportunityID  = ".$opportunity_id;
            $result = $wpdb->get_results($sql);
            $response['status'] = true;
            $response['message'] = "Opportunity Successfully Deleted";
        }else{
            $response['status'] = false;
            $response['message'] = "Please provide the opportunity ID.";
        }
        
        

        return json_encode($response);
    }

    // Callbacks for Next Step Endpoints
    public function next_step_list($request) {
        global $wpdb;
        $next_step_table = $wpdb->prefix . 'next_step';
        $query = $wpdb->prepare("SELECT 
                                $next_step_table.next_step_id,$next_step_table.next_step,$next_step_table.created_at
                                FROM $next_step_table 
                                WHERE $next_step_table.deleted_at IS NULL
                                ORDER BY $next_step_table.next_step_id DESC
                                ");
        return $wpdb->get_results($query);
    }

    /**
     * Create a new next step.
     *
     * @param array $data next step data.
     * @return int|WP_Error The ID of the newly created next step, or WP_Error on failure.
     */
    public function create_next_step(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'next_step';
        $next_step_data = $wpdb->get_results("SELECT next_step FROM $table_name WHERE $table_name.deleted_at IS NULL AND $table_name.next_step = '".$data['next_step']."'");
        if(empty($next_step_data)){
            $result = $wpdb->insert($table_name, $data);
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to insert next step into database', $wpdb->last_error);
            }else{
                $next_step_id = $wpdb->insert_id;
                $fields[0]['next_step'] = $data['next_step'];
                foreach($fields as $field){
                    foreach($field as $FieldName => $fieldValue){
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['created_at'];
                        $logs_data['CreatedBy'] = $data['created_by'];
                        $logs_data['TableName'] = $wpdb->prefix.'next_step';
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = 'N/A';
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $next_step_id;
                        $logs_data['Action'] = 'Create';
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Next Step Created';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Next Step Already Exist';
            echo json_encode($results);die;
        }
        //return $wpdb->insert_id;
    }

    /**
     * Edit next step.
     *
     * @param array $data next step data.
     * @return int|WP_Error The ID of the previously created next step, or WP_Error on failure.
     */
    public function edit_next_step(WP_REST_Request $request) {
        global $wpdb;
        $next_step_id = $request->get_param('next_step_id');
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'next_step';
        $next_step_data = $wpdb->get_results("SELECT next_step FROM $table_name WHERE $table_name.deleted_at IS NULL AND $table_name.next_step = '".$data['next_step']."' AND next_step_id != ".$next_step_id."");
        if(empty($next_step_data)){
            unset($data['next_step_id']);
            //Audit Logs Start
            $fields[0]['next_step'] = $data['next_step'];
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."' AND next_step_id = ".$next_step_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE next_step_id = ".$next_step_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['modified_at'];
                        $logs_data['CreatedBy'] = $data['modified_by'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $next_step_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('next_step_id' => $next_step_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update next step into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Next Step Updated';
                echo json_encode($results);die;
            }
        }else{
            $results['status'] = 0;
            $results['message'] = 'Next Step Already Exist';
            echo json_encode($results);die;
        }
        //return $next_step_id;
    }

    // Callbacks for next step Endpoints
    public function get_next_step($request) {
        global $wpdb;
        $next_step_id = $request->get_param('id');
        $next_step_table = $wpdb->prefix . 'next_step';
        $query = $wpdb->prepare("SELECT 
                                $next_step_table.next_step_id,$next_step_table.next_step,$next_step_table.created_at
                                FROM $next_step_table 
                                WHERE $next_step_table.next_step_id = '".$next_step_id."'
                                ");
        return $wpdb->get_row($query);
    }

    public function get_product_name($request) {
        global $wpdb;
        $product_id = $request->get_param('id');
        $table_name = $wpdb->prefix . 'crm_products';

        $query = $wpdb->prepare("SELECT $table_name.Title
                                FROM $table_name 
                                WHERE $table_name.ProductID = $product_id AND $table_name.DeletedAt IS NULL
                                ");
        return $wpdb->get_row($query);
    }

    public function lead_contacts($request) {
        global $wpdb;
        $lead_id = $request->get_param('id');
        $table_name = $wpdb->prefix . 'op_contacts';

        $query = $wpdb->prepare("SELECT $table_name.id,$table_name.first_name,$table_name.last_name FROM $table_name WHERE $table_name.report_to_id = $lead_id AND $table_name.active = 1 AND $table_name.trash = 0");
        return $wpdb->get_results($query);
    }

    public function fetch_opportunity_notes($request) {
        global $wpdb;
        //print_r($request);
        $opportunity_id = $request->get_param('opportunity_id');
        $offset = $request->get_param('offset');

       

		if(!empty($opportunity_id)){
		   
		    global $wpdb;
		    $notes_table = $wpdb->prefix.'erc_opportunity_notes';

		    $all_notes = $wpdb->get_results("SELECT * FROM $notes_table WHERE opportunity_id='".$opportunity_id."' ORDER BY id DESC ");

		        $html = ''; 
		        $i = $offset+1;
		                $from='UTC';
		                $to='America/New_York';
		                $format='Y-m-d h:i:s A';
		        foreach ($all_notes as $n_key => $n_value) { 
		            $date = $n_value->created;//UTC time
		            date_default_timezone_set($from);
		            $newDatetime = strtotime($date);
		            date_default_timezone_set($to);
		            $newDatetime = date($format, $newDatetime);
		            date_default_timezone_set('UTC');
		            $datetime = date_create($newDatetime);
		            // $datetime = date_create($n_value->created);
		            $time = date_format($datetime,"h:ia"); 
		            $day = date_format($datetime," D ");
		            $month = date_format($datetime," M ");
		            $date = date_format($datetime,"dS,");
		            $year = date_format($datetime," Y ");
		            $actual_date = $time." on ".$day.$month.$date.$year;
		            
		            $notes = $n_value->note;

		            $html .='<div class="note-listing-div shadow">';   
		            $html .='<p id="'.$n_value->id.'" class="notes">'.$notes.'</p>';
                    $html .='<p class="date-time">('.$actual_date.')</p>';
		            $html .='</div>';
		                $i++; 
			        } 

			 return $html;
		}
    }

    public function create_opportunity_notes($request) {
        global $wpdb;
        //print_r($request);
        $opportunity_id = $request->get_param('opportunity_id');
        $note = $request->get_param('note');
        $user_id = $request->get_param('user_id');
        $time = date("Y-m-d h:i:sa");

		if(!empty($opportunity_id)){
		   
		    global $wpdb;
		    $notes_table = $wpdb->prefix.'erc_opportunity_notes';

		    $result = $wpdb->get_results("INSERT INTO `eccom_erc_opportunity_notes` (`opportunity_id`, `created_by`, `note`, `created`) VALUES ('".$opportunity_id."', '".$user_id."', '".$note."', '".$time."');");

		    $all_notes = $wpdb->get_results("SELECT * FROM $notes_table WHERE opportunity_id='".$opportunity_id."' ORDER BY id DESC LIMIT 1");

		        $html = ''; 
		        $i = $offset+1;
		                $from='UTC';
		                $to='America/New_York';
		                $format='Y-m-d h:i:s A';
		        foreach ($all_notes as $n_key => $n_value) { 
		            $date = $n_value->created;//UTC time
		            date_default_timezone_set($from);
		            $newDatetime = strtotime($date);
		            date_default_timezone_set($to);
		            $newDatetime = date($format, $newDatetime);
		            date_default_timezone_set('UTC');
		            $datetime = date_create($newDatetime);
		            // $datetime = date_create($n_value->created);
		            $time = date_format($datetime,"h:ia"); 
		            $day = date_format($datetime," D ");
		            $month = date_format($datetime," M ");
		            $date = date_format($datetime,"dS,");
		            $year = date_format($datetime," Y");
		            $actual_date = $time." on ".$day.$month.$date.$year;
		            
		            $notes = $n_value->note;

		            $html .='<div class="note-listing-div shadow">';   
		            $html .='<p id="'.$n_value->id.'" class="notes">'.$notes.'</p>';
                    $html .='<p class="date-time">('.$actual_date.')</p>';
		            $html .='</div>';
		                $i++; 
			        } 

			 return $html;
		}
    }

    // Callbacks for fee type info Endpoints
    public function get_fee_type($request) {
        global $wpdb;
        $fee_type_id = $request->get_param('id');
        $fee_type_table = $wpdb->prefix . 'fee_structure';
        $query = $wpdb->prepare("SELECT 
                                $fee_type_table.*
                                FROM $fee_type_table 
                                WHERE $fee_type_table.fee_type_id = '".$fee_type_id."'
                                ");
        return $wpdb->get_row($query);
    }

    // Callbacks for Fee Types Endpoints
    public function fee_types($request) {
        global $wpdb;
        $fee_type_table = $wpdb->prefix . 'fee_structure';
        $query = $wpdb->prepare("SELECT 
                                $fee_type_table.*
                                FROM $fee_type_table 
                                WHERE $fee_type_table.deleted_at IS NULL
                                ORDER BY $fee_type_table.fee_type_id DESC
                                ");
        return $wpdb->get_results($query);
    }

    /**
     * Create a new fee type.
     *
     * @param array $data fee type data.
     * @return int|WP_Error The ID of the newly created fee type, or WP_Error on failure.
     */
    public function create_fee_type(WP_REST_Request $request) {
        global $wpdb;
        $data = $request->get_json_params();
        $table_name = $wpdb->prefix . 'fee_structure';
        $data['fee_structure'] = str_replace("Plus", "+", $data['fee_structure']);
        $result = $wpdb->insert($table_name, $data);
        if ($result === false) {
            return new WP_Error('db_insert_error', 'Failed to insert fee type into database', $wpdb->last_error);
        }else{
            $fee_type_id = $wpdb->insert_id;
            $fields[0]['fee_structure'] = $data['fee_structure'];
            if($data['retainer_fee_name'] != ''){
                $fields[1]['retainer_fee_options'] = $data['retainer_fee_options'];
                $fields[2]['retainer_fee_name'] = $data['retainer_fee_name'];
                if($data['retainer_fee_options'] != 'Basis of'){
                    $fields[3]['retainer_amount_per'] = $data['retainer_amount_per'];
                }else{
                    $fields[3]['retainer_identifier'] = $data['retainer_identifier'];
                }
                $fields[4]['selected_fee_options'] = $data['selected_fee_options'];
                $fields[5]['selected_fee_name'] = $data['selected_fee_name'];
                if($data['selected_fee_options'] != 'Basis of'){
                    $fields[6]['selected_amount_per'] = $data['selected_amount_per'];
                }else{
                    $fields[6]['selected_identifier'] = $data['selected_identifier'];
                }
            }else{
                $fields[1]['selected_fee_options'] = $data['selected_fee_options'];
                $fields[2]['selected_fee_name'] = $data['selected_fee_name'];
                if($data['selected_fee_options'] != 'Basis of'){
                    $fields[3]['selected_amount_per'] = $data['selected_amount_per'];
                }else{
                    $fields[3]['selected_identifier'] = $data['selected_identifier'];
                }
            }
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                    $logs_data['DateCreated'] = $data['created_at'];
                    $logs_data['CreatedBy'] = $data['created_by'];
                    $logs_data['TableName'] = $table_name;
                    $logs_data['FieldName'] = $FieldName;
                    $logs_data['DataType'] = $DATA_TYPE;
                    $logs_data['BeforeValueString'] = 'N/A';
                    $logs_data['AfterValueString'] = $fieldValue;
                    $logs_data['FieldID'] = $product_type_id;
                    $logs_data['Action'] = 'Create';
                    $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                    $audit_log_data = $audit_log_manager->record_audit_log($logs_data);
                }
            }
            $results['status'] = 1;
            $results['message'] = 'Fee Type Created';
            echo json_encode($results);
        }
    }

    /**
     * Edit fee type.
     *
     * @param array $data fee type data.
     * @return int|WP_Error The ID of the previously edit fee type, or WP_Error on failure.
     */
    public function edit_fee_type(WP_REST_Request $request) {
        global $wpdb;
        $fee_type_id = $request->get_param('fee_type_id');
        $data = $request->get_json_params();
        $data['fee_structure'] = str_replace("Plus", "+", $data['fee_structure']);
        $table_name = $wpdb->prefix . 'fee_structure';
        /*$unit_data = $wpdb->get_results("SELECT UnitName FROM $table_name WHERE $table_name.DeletedAt IS NULL AND $table_name.UnitName = '".$data['UnitName']."' AND UnitTypeID != ".$unit_type_id."");
        if(empty($unit_data)){*/
            unset($data['fee_type_id']);
            //Audit Logs Start
            $fields[0]['fee_structure'] = $data['fee_structure'];
            if($data['retainer_fee_name'] != ''){
                $fields[1]['retainer_fee_options'] = $data['retainer_fee_options'];
                $fields[2]['retainer_fee_name'] = $data['retainer_fee_name'];
                if($data['retainer_fee_options'] != 'Basis of'){
                    $fields[3]['retainer_amount_per'] = $data['retainer_amount_per'];
                }else{
                    $fields[3]['retainer_identifier'] = $data['retainer_identifier'];
                }
                $fields[4]['selected_fee_options'] = $data['selected_fee_options'];
                $fields[5]['selected_fee_name'] = $data['selected_fee_name'];
                if($data['selected_fee_options'] != 'Basis of'){
                    $fields[6]['selected_amount_per'] = $data['selected_amount_per'];
                }else{
                    $fields[6]['selected_identifier'] = $data['selected_identifier'];
                }
            }else{
                $fields[1]['selected_fee_options'] = $data['selected_fee_options'];
                $fields[2]['selected_fee_name'] = $data['selected_fee_name'];
                if($data['selected_fee_options'] != 'Basis of'){
                    $fields[3]['selected_amount_per'] = $data['selected_amount_per'];
                }else{
                    $fields[3]['selected_identifier'] = $data['selected_identifier'];
                }
            }
            $i = 0;
            $audit_logs = [];
            foreach($fields as $field){
                foreach($field as $FieldName => $fieldValue){
                    $checkPreviousData = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE $FieldName = '".$fieldValue."'  AND fee_type_id = ".$fee_type_id."");
                    if(empty($checkPreviousData)){
                        $getPreviousVal = $wpdb->get_row("SELECT $FieldName FROM $table_name WHERE fee_type_id = ".$fee_type_id."");
                        $DATA_TYPE = $wpdb->get_var("SELECT DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE table_name = '".$table_name."' AND COLUMN_NAME = '".$FieldName."'");
                        $logs_data['DateCreated'] = $data['modified_at'];
                        $logs_data['CreatedBy'] = $data['modified_by'];
                        $logs_data['TableName'] = $table_name;
                        $logs_data['FieldName'] = $FieldName;
                        $logs_data['DataType'] = $DATA_TYPE;
                        $logs_data['BeforeValueString'] = $getPreviousVal->$FieldName;
                        $logs_data['AfterValueString'] = $fieldValue;
                        $logs_data['FieldID'] = $fee_type_id;
                        $logs_data['Action'] = 'Update';
                        $audit_logs[$i]['logs_data'] = $logs_data;
                        $i++;
                    }
                }
            }
            //Audit Logs End
            $result = $wpdb->update($table_name, $data, array('fee_type_id' => $fee_type_id));
            if ($result === false) {
                return new WP_Error('db_insert_error', 'Failed to update fee type into database', $wpdb->last_error);
            }else{
                if(!empty($audit_logs)){
                    foreach($audit_logs as $audit_log){
                        $audit_log_manager = new CRM_ERP_Audit_Log_Manager();
                        $audit_log_data = $audit_log_manager->record_audit_log($audit_log['logs_data']);
                    }
                }
                $results['status'] = 1;
                $results['message'] = 'Fee Type Update Successfully';
                echo json_encode($results);die;
            }
        /*}else{
            $results['status'] = 0;
            $results['message'] = 'Unit Name Already Exist';
            echo json_encode($results);die;
        }*/
        //return $unit_type_id;
    }
}

$crm_erp_rest_api = new CRM_ERP_REST_API();
