<!-- invoice-card-start -->
<!-- <style>
    /* Card container */
    .contact_tab_data .card-exam {
        padding: 10px;
        height: 100%;
    }

    /* Circle for displaying initials */
    .contact_tab_data .circle {
        width: 60px;
        height: 60px;
        line-height: 60px;
        border-radius: 50%;
        text-align: center;
        color: #0f67ae;
        font-size: 20px;
        text-transform: uppercase;
        font-weight: 700;
        background: #f3f0f2;
        border: 1px solid rgba(231, 231, 231, 0.7);
        margin-right: 10px;
    }

    /* Title section in the card */
    .contact_tab_data .card-exam-title {
        text-align: left;
        width: 80%;
    }

    .contact_tab_data .card-exam-title p {
        font-size: 13px;
        color: #000000;
        font-weight: 500;
        margin-bottom: 7px;
        word-break: break-all;
    }

    .contact_tab_data .card-exam-title p:first-child {
        color: #000;
        line-height: 18px;
    }

    /* Link styling */
    .contact_tab_data .card-link {
        justify-content: center;
        margin-top: 20px;
    }

    /* Header section in the card */
    .custom_opp_tab_header {
        border-bottom: 1px solid #d5d5d5;
        padding-bottom: 5px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .custom_opp_tab_header h5 {
        font-weight: 600;
        font-size: 15px;
    }

    .custom_opp_tab_header h5 i {
        font-size: 13px;
    }

    /* Edit and delete buttons */
    .opp_edit_dlt_btn .edit_contact,
    .opp_edit_dlt_btn .delete_contact {
        background: #ff5c00;
        border: 0;
        padding: 5px;
        font-weight: 600;
        font-size: 12px;
        color: #fff;
        border-radius: 5px;
        width: 25px;
        height: 25px;
        display: inline-block;
        text-align: center;
    }

    .opp_edit_dlt_btn .delete_contact {
        margin-left: 5px;
    }

    /* Message styles */
    .success_message {
        background: #7cbb7c;
        padding: 5px;
        color: white;
        font-weight: 800;
    }

    .alert_message {
        background: #b1af7e;
        padding: 5px;
        color: white;
        font-weight: 800;
    }

    .warning_message {
        background: #ea584c;
        padding: 5px;
        color: white;
        font-weight: 800;
    }

    /* Button styling */
    .custom_opp_create_btn .btn {
        background: #ff5c00;
        border: 0;
        padding: 8px;
        font-weight: 600;
    }

    .buttion_next_prev {
        width: 100%;
        text-align: center;
        margin: 40px auto 0;
    }

    .nxt_btn {
        padding: 11px 25px;
        color: #FF5F00;
        font-size: 16px;
        font-weight: 600;
        background: #FFFFFF;
        box-shadow: 0px 3px 6px #00000029;
        border: 2px solid #FF5F00;
        border-radius: 11px;
    }

    /* Modal styles */
    .modal-backdrop.show {
        display: none !important;
    }

    .edit_contact_popup .modal-header {
        display: block !important;
    }

    .edit_contact_popup .modal-header .modal-title {
        margin-top: 5px;
        font-weight: 700;
        color: #1261ab;
        font-size: 17px;
    }

    .edit_contact_popup .modal-header .close,
    .link_contact_popup .modal-header .close {
        position: absolute;
        right: -15px;
        top: -15px;
        background: red;
        text-align: center;
        width: 28px;
        height: 28px;
        cursor: pointer;
        border-radius: 50%;
        line-height: 10px;
        color: #fff;
        padding: 0;
        margin: 0;
    }

    .edit_contact_popup .modal-header .close span,
    .link_contact_popup .modal-header .close span {
        display: block;
        margin-top: -4px;
    }

    .edit_contact_popup .close:not(:disabled):not(.disabled):focus,
    .edit_contact_popup .close:not(:disabled):not(.disabled):hover {
        opacity: 1;
    }

    .floating label {
        font-weight: 600;
        font-family: "Mulish", sans-serif;
        font-size: 14px;
    }
</style> -->

<style>

.custom_opp_tab {
    padding: 0px 0px 12px 0px;
    border: 1px solid #d8d8d882;
    box-shadow: 5px 5px 10px 0px rgb(178 178 178 / 40%) !important;
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 20px;
    border-bottom: 2px solid #ff5c00;
}
 
.custom_opp_tab .custom_opp_tab_header {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #d5d5d5;
    padding-bottom: 10px;
    padding-top: 10px;
    align-items: baseline;
}
.custom_opp_tab .custom_opp_tab_header h5 {
    font-weight: 600;
    font-size: 15px;
    width: 90%;
}
 
.opp_edit_dlt_btn.projects-iris {
    width: auto !important;
    display: flex;
    align-items: center;
}

.opp_edit_dlt_btn.projects-iris select {
    height: 25px;
    width: 160px;
}

.custom_opp_tab .lead_des {
    margin-top: 10px;
}
.custom_opp_tab .lead_des p {
    color: #000;
    font-weight: 400;
    margin-bottom: 5px;
}

.expand_pp_div {
    margin-top: 5px;
    margin-bottom: 5px;
    font-weight: 600;
}

.expand_pp_div i {
    padding-left: 5px;
    vertical-align: middle;
}

.opp_edit_dlt_btn .edit_opportunity {
    background: #ff5c00 !important;
    border: 0 !important;
    padding: 5px;
    font-weight: 600;
    font-size: 12px;
    color: #fff !important;
    border-radius: 5px;
    width: 25px;
    height: 25px;
    display: inline-block;
    text-align: center;
    margin-left: 10px;
}

.nav-pills .nav-link.active {
    padding: 7px 25px !important;
}

</style>

<?php
$current_user_id = get_current_user_id();
$user_data = get_user_by('id', $current_user_id);
$user_roles = $user_data->roles;
if(in_array('iris_affiliate_users', $user_roles) || in_array("iris_employee", $user_roles) || in_array("account_manager", $user_roles)){
    $is_affiliate = 1;
    $is_view = 0;
    $readonly = 'readonly';
}else{
    $is_affiliate = 0;
    $is_view = 1;
    $readonly = '';
}

$users = get_users( array( 
    'role__in' => array('master_ops','echeck_staff','echeck_admin','echeck_client','iris_affiliate_users','master_sales','iris_sales_agent','iris_sales_agent_rep' ),
    'orderby' => 'display_name',
    'order' => 'ASC'  ) );


// -------- confidence notes code -------
     $confidence_user = 0;
     
     $confidence_check = '';
     if(get_current_user_id() == 44019){ // nedeen id 
        $confidence_check = 'checked';
     }
     $option_table = $wpdb->prefix.'onedrive_options';
      $selected_user = $wpdb->get_var("SELECT meta_value FROM $option_table WHERE meta_key='notes_confidence_users' ");
      if(!empty($selected_user)){
        $selected_users = explode(",",$selected_user);
        $current_user_id = get_current_user_id();
        if(in_array($current_user_id,$selected_users)){
            $confidence_user = 1;
        }
      } 
	  
$additional_table = $wpdb->prefix . 'erc_iris_leads_additional_info';
$business_info_table = $wpdb->prefix . 'erc_business_info';
$product_table = $wpdb->prefix . 'crm_products';
$milestone_table = $wpdb->prefix . 'milestones';
$milestone_status_table = $wpdb->prefix . 'milestone_stages';
$project_id = $_REQUEST['id'];
$project = $wpdb->get_row("SELECT {$wpdb->prefix}projects.*,{$wpdb->prefix}erc_business_info.business_legal_name,{$wpdb->prefix}erc_business_info.authorized_signatory_name,{$wpdb->prefix}erc_business_info.business_phone,{$wpdb->prefix}erc_business_info.business_email,{$wpdb->prefix}erc_business_info.business_title,{$wpdb->prefix}erc_business_info.zip,{$wpdb->prefix}erc_business_info.street_address,{$wpdb->prefix}erc_business_info.state,{$wpdb->prefix}erc_business_info.city,{$wpdb->prefix}crm_products.Title as product_name,{$wpdb->prefix}milestones.milestone_name, {$wpdb->prefix}milestones.status as milestoneActiveStatus, {$wpdb->prefix}milestones.map as milestoneMap,{$wpdb->prefix}milestone_stages.stage_name as milestoneStatus,{$wpdb->prefix}milestone_stages.status as StageActiveStatus,{$wpdb->prefix}milestone_stages.deleted_at as StageDeleteStatus
                                                                    FROM {$wpdb->prefix}projects 
                                                                    JOIN {$wpdb->prefix}erc_business_info 
                                                                    ON {$wpdb->prefix}projects.lead_id = {$wpdb->prefix}erc_business_info.lead_id
                                                                    JOIN {$wpdb->prefix}crm_products 
                                                                    ON {$wpdb->prefix}projects.product_id = {$wpdb->prefix}crm_products.ProductID 
                                                                    LEFT JOIN {$wpdb->prefix}milestones 
                                                                    ON {$wpdb->prefix}projects.milestone_id = {$wpdb->prefix}milestones.milestone_id 
                                                                    LEFT JOIN {$wpdb->prefix}milestone_stages
                                                                    ON {$wpdb->prefix}projects.milestone_stage_id = {$wpdb->prefix}milestone_stages.milestone_stage_id 
                                                                    WHERE {$wpdb->prefix}projects.deleted_at IS NULL 
                                                                    AND {$wpdb->prefix}projects.project_id = " . $project_id . " ");
$contact_id = $project->contact_id;
$lead_id = $project->lead_id;
if ($contact_id > 0) {
    $contactdata = $wpdb->get_row("SELECT first_name,last_name,title FROM {$wpdb->prefix}op_contacts WHERE id = " . $contact_id . "");
    $full_name = $contactdata->first_name . ' ' . $contactdata->last_name;
    $contact_title = $contactdata->title;
} else {
    $full_name = '';
    $contact_title = '';
}
$phonedata = $wpdb->get_row("SELECT {$wpdb->prefix}op_phone.phone 
                                    FROM {$wpdb->prefix}op_phone_contact 
                                    JOIN {$wpdb->prefix}op_phone ON {$wpdb->prefix}op_phone_contact.phone_id = {$wpdb->prefix}op_phone.id
                                    WHERE {$wpdb->prefix}op_phone_contact.contact_id = " . $contact_id . "");
if (!empty ($phonedata)) {
    $phone = $phonedata->phone;
} else {
    $phone = '';
}

$emaildata = $wpdb->get_row("SELECT {$wpdb->prefix}op_emails.email 
                                    FROM {$wpdb->prefix}op_email_contact 
                                    JOIN {$wpdb->prefix}op_emails ON {$wpdb->prefix}op_email_contact.email_id = {$wpdb->prefix}op_emails.id
                                    WHERE {$wpdb->prefix}op_email_contact.contact_id = " . $contact_id . "");
if (!empty ($emaildata)) {
    $email = $emaildata->email;
} else {
    $email = '';
}

/*$addressdata = $wpdb->get_row("SELECT {$wpdb->prefix}op_address.primary_address_postalcode,{$wpdb->prefix}op_address.primary_address_street,{$wpdb->prefix}op_address.primary_address_city,{$wpdb->prefix}op_address.primary_address_state 
                                    FROM {$wpdb->prefix}op_address_contact 
                                    JOIN {$wpdb->prefix}op_address ON {$wpdb->prefix}op_address_contact.address_id = {$wpdb->prefix}op_address.id
                                    WHERE {$wpdb->prefix}op_address_contact.contact_id = " . $contact_id . "");
if (!empty ($addressdata)) {
    $zip = $addressdata->primary_address_postalcode;
    $street_address = $addressdata->primary_address_street;
    $city = $addressdata->primary_address_city;
    $state = $addressdata->primary_address_state;
} else {
    $zip = '';
    $street_address = '';
    $city = '';
    $state = '';
}*/


$where_milestone = ' 1=1 ';
if (isset ($project->product_id) && !empty ($project->product_id)) {
    $where_milestone .= ' AND FIND_IN_SET(' . $project->product_id . ',' . $milestone_table . '.product_id) ';
}
$all_milestones = $wpdb->get_results("SELECT $milestone_table.milestone_id,$milestone_table.milestone_name, $milestone_table.map,$milestone_table.status,$milestone_table.deleted_at FROM $milestone_table WHERE $where_milestone  AND $milestone_table.status = 'active' AND $milestone_table.deleted_at IS NULL AND $milestone_table.map LIKE '%\"project\"%'");
$where_stage = ' 1=1 ';
if (isset ($project->milestone_id) && !empty ($project->milestone_id)) {
    $where_stage .= ' AND ' . $milestone_status_table . '.milestone_id = ' . $project->milestone_id . ' ';
}
$all_milestone_status = $wpdb->get_results("SELECT $milestone_status_table.milestone_stage_id,$milestone_status_table.stage_name,$milestone_status_table.status,$milestone_status_table.deleted_at FROM $milestone_status_table WHERE $where_stage AND $milestone_status_table.status = 'active' AND $milestone_status_table.deleted_at IS NULL");
$user_details = get_user_by('id', $project->created_by);
$owner_name = $user_details->display_name;

$login_user_details = get_user_by('id', get_current_user_id());
$login_user_name = $login_user_details->display_name;

$all_users = get_users(array('role__in' => array('Master_Sales', 'Iris_Sales_Agent')));
$active_sales_agents = $wpdb->get_results("SELECT userid,full_name FROM {$wpdb->prefix}erc_sales_team WHERE active_sales_agent = 1");
$contacts_table = $wpdb->prefix . 'op_contacts';
if ($project->contact_id > 0) {
    $contact_data = $wpdb->get_row("SELECT first_name,last_name,trash FROM $contacts_table WHERE id = " . $project->contact_id . "");
    if (!empty ($contact_data)) {

        if ($contact_data->trash == 1) {
            $primary_contact = $contact_data->first_name . ' ' . $contact_data->last_name . ' (disabled)';
        } else {
            $primary_contact = $contact_data->first_name . ' ' . $contact_data->last_name;
        }
    } else {
        $primary_contact = '';
    }
} else {
    $primary_contact = '';
}

$lead_id = $project->lead_id;
// $lead_id = 8666;
$business_legal_name = $project->business_legal_name;

// Encode the values before setting the cookies
// $encoded_lead_id = urlencode($lead_id);
// $encoded_business_name = urlencode($business_legal_name);

if (isset($_COOKIE['iris_business_lead_id'])) {
    // $lead_id = $project->lead_id;
    // Encode the value before setting the cookie
    
    setcookie("iris_business_lead_id", $lead_id); // 30 days expiration
}else{
    
    setcookie("iris_business_lead_id", $lead_id); // 30 days expiration
}

if (isset($_COOKIE['iris_business_name'])) {    
    // Encode the value before setting the cookie
    // $encoded_business_name = urldecode($business_legal_name);
    setcookie("iris_business_name", $business_legal_name ); // 30 days expiration
}else{
    // $encoded_business_name = urldecode($business_legal_name);
    setcookie("iris_business_name", $business_legal_name); // 30 days expiration
}


$table_name = $wpdb->prefix . 'op_contacts';
$query = $wpdb->prepare("SELECT $table_name.id,$table_name.first_name,$table_name.last_name, $table_name.trash FROM $table_name WHERE $table_name.report_to_id = $lead_id AND $table_name.active = 1 AND $table_name.trash = 0");
$all_contacts = $wpdb->get_results($query);

$project_id = $project->project_id;
$authorized_signatory_name = $project->authorized_signatory_name;
$business_phone = $project->business_phone;
$business_email = $project->business_email;
$business_title = $project->business_title;
$street_address = $project->street_address;
$city = $project->city;
$state = $project->state;
$zip = $project->zip;
$notes_table = $wpdb->prefix . 'erc_project_notes';
$total_notes = $wpdb->get_results("SELECT id FROM $notes_table WHERE project_id='" . $project_id . "' ORDER BY id DESC ");
$total_notes_count = count($total_notes);
//$all_notes = $wpdb->get_results("SELECT * FROM $notes_table WHERE project_id='" . $project_id . "' ORDER BY id DESC LIMIT 10 OFFSET 0");
$note_where = "";
if($confidence_user==0){
    $note_where = " AND confidential_notes=0";
}
$all_notes = $wpdb->get_results("SELECT * FROM $notes_table WHERE project_id='" . $project_id . "'". $note_where." ORDER BY id DESC LIMIT 10 OFFSET 0");

//collaborators
$collaborators = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}collaborators WHERE project_id = " . $project_id . "");
$collaborator_users = array(
    'role__in' => array('iris_sales_agent', 'iris_sales_agent_rep', 'master_sales', 'echeck_staff', 'echeck_admin', 'master_ops'),
    'orderby' => 'display_name',
    'order' => 'ASC'
);
$listed_users = get_users($collaborator_users);
//Get Fee from user log data
$fee_amount = '';
$log_user_data = $wpdb->get_row("SELECT amount FROM {$wpdb->prefix}log_user_data WHERE lead_id = " . $lead_id . " AND agreement_type = 'tax' AND aggrement_status = 'accepted'");
if (!empty ($log_user_data)) {
    $fee_amount = $log_user_data->amount;
}
//lead mapping data
$document_folder = '';
$agreement_folder = '';
$lead_mapping_data = $wpdb->get_row("SELECT document_folder,agreement_folder FROM {$wpdb->prefix}lead_folder_mapping WHERE lead_id = ".$lead_id."");
if(!empty($lead_mapping_data)){
    $document_folder = $lead_mapping_data->document_folder;
    $agreement_folder = $lead_mapping_data->agreement_folder;
}


//process impacted tab data

    if($lead_id){
        $intake_info = $wpdb->get_row("SELECT interest_percentage,net_no FROM {$wpdb->prefix}erc_erc_intake WHERE lead_id = ".$lead_id."");
        $interest_percentage = $intake_info->interest_percentage;
        $net_no = $intake_info->net_no;
    }

    if(empty($interest_percentage) || $interest_percentage==0.00){
        $interest_percentage = 15;
    }

    if(empty($net_no)){
        $net_no = 30;
    }

?>
<style>
    .confidential-notes-div{
        background: #FFA500;
    }
    .confidential-notes-div .date-time , .confidential-notes-div .edit_self_notes, .confidential-notes-div .delete_self_notes{
        color: #000 !important;   
    }
  .edit_self_notes , .delete_self_notes{
    font-size: 18px;
    color: #ff5c00 !important;
  }    
  .edit_self_notes{
    padding-right:5px;
  }
  .swal-footer{
    text-align: center;
  }
 .confidential-notes-div .edit_self_notes,.confidential-notes-div .delete_self_notes{
    display: block!important;
}
.note-listing-div .edit_self_notes,.note-listing-div .delete_self_notes{
    display: none;
}  

.owner-main-div div{
    display: none !important;
}
 .custom-ownership{
    min-width: 100%;   
 }

.custom-ownership .dropdown-toggle{
    font-size: 14px;
    line-height: 2;
    color: #32373c;
    border-color: #7e8993;
    box-shadow: none;
    border-radius: 3px;
    padding: 0 24px 0 8px;
    min-height: 40px;
    max-width: 25rem;
    -webkit-appearance: none;
    background: #fff url(data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23555%22%2F%3E%3C%2Fsvg%3E) no-repeat right 5px top 55%;
    background-size: 16px 16px;
    cursor: pointer;
    vertical-align: middle;
}  
</style>
<div class="main_content_iner">
    <div class="container-fluid p-0">
        <div class="row justify-content-center">
            <div class="col-lg-12">
                <div class="white_card card_height_100 mb_30">
                    <div class="white_card_header">
                        <div class="box_header m-0 new_report_header">
                            <div class="title_img">
                                <img src="<?php echo get_site_url(); ?>/wp-content/plugins/oc-crm/assets/img/opportunity-icon.png"
                                    class="page-title-img" alt="">
                                <h4>Manage Tax Amendment Project</h4>
                            </div>
                        </div>
                        <div class="d-flex">
                            <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                <li class="nav-item active">
                                    <a class="nav-link tab-btn" id="eleve-project-tab" data-toggle="tab" href="#eleve-project"
                                        role="tab" aria-controls="first" aria-selected="true">Project</a>
                                </li>
                                <?php //if(!in_array('iris_affiliate_users', $user_roles) && !in_array('iris_employee', $user_roles)){?>
                                <li class="nav-item">
                                    <a class="nav-link tab-btn" id="eleve-documents-tab" data-toggle="tab"
                                        href="#eleve-documents" role="tab" aria-controls="second"
                                        aria-selected="false">Documents</a>
                                </li>
                                <?php //} ?>
                                <li class="nav-item">
                                    <a class="nav-link tab-btn" id="eleve-invoice-tab" data-toggle="tab" href="#eleve-invoices" role="tab" aria-controls="third" aria-selected="true">Invoices</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link tab-btn" id="pills-audit-logs" data-toggle="pill" href="#pills-logs" role="tab" aria-controls="pills-logs" aria-selected="false">Audit Logs</a>
                                </li>								
                            </ul>
                        </div>
                    </div>
                    <div class="white_card_body p-15">
                        <div class="row">
                            <div class="col-md-9" id="left_section">
                                <div class="show_message_popup"></div>
                                <form action="<?= admin_url("admin-post.php") ?>" id="lead_update_form" method="post">
                                    <input type="hidden" name="action" value="update_erc_project_info">
                                    <input type="hidden" name="lead_id" value="<?php echo $lead_id; ?>">
                                    <input type="hidden" name="product_id" value="<?php echo $project->product_id; ?>">
                                    <input type="hidden" name="project_id" value="<?php echo $project_id; ?>">
                                    <div class="tab-content" id="pills-tabContent">
                                        <div class="tab-pane active erc-project-scroll" id="eleve-project"
                                            role="tabpanel" aria-labelledby="eleve-project-tab">
                                            <div class="erc-project-view">
                                                <fieldset>
                                                    <legend>Project Details</legend>
                                                    <div class="row mb-3 align-items-center">
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Name</label>
                                                            <input type="text" id="project_name" name="project_name"
                                                                class="crm-erp-field form-control"
                                                                value="<?php echo $project->project_name; ?>" readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Business</label>
                                                            <a href="<?php echo get_site_url();  ?>/wp-admin/admin.php?page=iris-fields-v1.php&lead_id=<?php echo $lead_id;  ?>" target="_blank" class="btn btn-primary view_business">View</a>
                                                            <input type="text" class="crm-erp-field form-control"
                                                                value="<?php echo $project->business_legal_name; ?>"
                                                                readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Products</label>
                                                            <input type="text" class="crm-erp-field form-control"
                                                                value="<?php echo $project->product_name; ?>" readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Fee</label>
                                                            <input type="text" id="project_fee"
                                                                class="crm-erp-field form-control"
                                                                value="<?php echo $fee_amount; ?>" readonly>
                                                        </div>
                                                        <!-- <div class="floating col-sm-4 mb-3">
                                                            <label>Estimated Fee</label>
                                                            <input type="text" id="estimated_fee"
                                                                class="crm-erp-field form-control"
                                                                value="<?php echo $project->estimated_fee; ?>" <?php echo $readonly;?>>
                                                        </div> -->
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Review Status</label>
                                                          <?php
                                                            $review_status = $project->review_status;
                                                          ?>
                                                            <select class="crm-erp-field form-control" name="review_status" id="review_status">
                                                                <option value="" <?php if($review_status == ''){echo "selected";}?>>Select Review Status</option>
                                                                <option value="pending" <?php if($review_status == 'pending'){echo "selected";}?>>Pending</option>
                                                                <option value="yes" <?php if($review_status == 'yes'){echo "selected";}?>>Yes</option>
                                                            </select>
                                                        </div>

                                                        <div class="floating col-sm-4 mb-3 review_link" style="<?php if($review_status == 'yes'){echo "display:block;";}else{echo "display:none;";}?>">
                                                            <label>Review link</label>
                                                            <input type="text" id="review_link" class="crm-erp-field form-control" name="review_link" value="<?php echo $project->review_link;?>">
                                                            <p class="review_link_error" style="color:#ff0010;"></p>
                                                        </div>
                                                    </div>
                                                </fieldset>
                                            </div>

                                            <div class="erc-project-view">
                                                <fieldset>
                                                    <legend>Account Info</legend>
                                                    <div class="row mb-3">
                                                        <div class="col-sm-12 erc-project-view-title">
                                                            <h2>Contact Info Section</h2>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Full Name</label>
                                                            <input type="text" class="crm-erp-field form-control"
                                                                name="full_name" value="<?php echo $authorized_signatory_name; ?>"
                                                                readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Contact No.</label>
                                                            <input type="text" class="crm-erp-field form-control"
                                                                name="contact_no" value="<?php echo $business_phone; ?>"
                                                                readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Email</label>
                                                            <input type="text" class="crm-erp-field form-control"
                                                                name="email" value="<?php echo $business_email; ?>" readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Title</label>
                                                            <input type="text" class="crm-erp-field form-control"
                                                                name="title" value="<?php echo $business_title; ?>"
                                                                readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Zip</label>
                                                            <input type="text" class="crm-erp-field form-control"
                                                                name="zip" value="<?php echo $zip; ?>" readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Street Address</label>
                                                            <input type="text" class="crm-erp-field form-control"
                                                                name="street_address"
                                                                value="<?php echo $street_address; ?>" readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>City</label>
                                                            <input type="text" class="crm-erp-field form-control"
                                                                name="city" value="<?php echo $city; ?>" readonly>
                                                        </div>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>State</label>
                                                            <input type="text" class="crm-erp-field form-control"
                                                                name="state" value="<?php echo $state; ?>" readonly>
                                                        </div>
                                                    </div>
                                                </fieldset>
                                            </div>
                                            <?php if($is_affiliate == 0){?>
                                            <div class="erc-project-view">
                                                <fieldset>
                                                    <legend>Folder Info</legend>
                                                    <div class="row mb-3">
                                                        <div class="col-sm-12 erc-project-view-title">
                                                            <h2>Folder Info Section</h2>
                                                        </div>
                                                        <?php if($agreement_folder != ''){?>
                                                        <div class="floating col-sm-4 mb-3">
                                                            <label>Agreement & invoice folder</label><br/>
                                                            <a href="https://occams-my.sharepoint.com/personal/admin_occamsparadigm_com/_layouts/15/onedrive.aspx?id=%2Fpersonal%2Fadmin%5Foccamsparadigm%5Fcom%2FDocuments%2FOccams%2FUSA%2FBusiness%20Segments%2FFinancial%20%26%20Tax%20Advisory%20%28FTA%29%2FTax%20Credits%2FERC%2FERC%20Client%27s%2FAutoX/<?php echo $agreement_folder;?>">
                                                                Agreement & invoice folder
                                                            </a>
                                                        </div>
                                                        <?php } ?>
                                                        <?php if($document_folder != ''){?>
                                                            <div class="floating col-sm-4 mb-3">
                                                                <label>Documents folder</label><br/>
                                                                <a href="https://occams-my.sharepoint.com/personal/admin_occamsparadigm_com/_layouts/15/onedrive.aspx?id=%2Fpersonal%2Fadmin%5Foccamsparadigm%5Fcom%2FDocuments%2FOccams%2FUSA%2FBusiness%20Segments%2FFinancial%20%26%20Tax%20Advisory%20%28FTA%29%2FTax%20Credits%2FERC%2FERC%20Client%27s%2FAutoX/<?php echo $document_folder;?>">
                                                                    Documents folder
                                                                </a>
                                                            </div>
                                                        <?php } ?>
                                                    </div>
                                                </fieldset>
                                            </div>
                                        <?php } ?>
                                        
                                        <div class="erc-project-view">
                                            <fieldset>
                                                <legend>Payment Terms</legend>
                                                <div class="row mb-3">
                                                <div class="floating col-sm-4 mb-3">
                                                    <label>Net No.</label>
                                                    <input name='net_no' id='net_no' value='<?php echo $net_no; ?>' class="crm-erp-field form-control" type="number" <?= $readonly ?> min="0">
                                                </div>
                                               <div class="floating col-sm-4 mb-3">
                                                    <label>Interest Percentage</label>
                                                    <input name='interest_percentage' id='interest_percentage' value='<?php echo $interest_percentage; ?>' class="crm-erp-field form-control" type="number" <?= $readonly ?> step=".01" max="100" min="0">
                                                </div>
                                                </div>
                                            </fieldset>
                                        </div>

                                            <div class="">
                                                <div class="button-container button_container" style="">
                                                    <button type="submit" class="create_project_btn update_project"
                                                        data-projectid="<?php echo $project_id; ?>">Update</button>
                                                </div>
                                            </div>
                                        
                                        </div>
                                        <div class="tab-pane erc-project-scroll" id="eleve-documents" role="tabpanel" aria-labelledby="eleve-documents-tab">                                            
                                            <style>
                                                .manage_project_sub_heading{
                                                    background: transparent linear-gradient( 89deg , #1658A5 0%, #0089C2 100%) 0% 0% no-repeat padding-box !important;
                                                    margin-top: 0px !important;
                                                    padding: 10px;
                                                    border-radius: 10px;
                                                    margin: auto 14px;
                                                }
                                            </style>

                                            <ul class="nav nav-pills manage_project_sub_heading stc_document_tabs" id="pills-tab" role="tablist">
                                                <li class="nav-item active">
                                                    <a class="nav-link" id="required-documents-tab" data-toggle="tab" href="#required-documents"
                                                        role="tab" aria-controls="first" aria-selected="true" >Required Document</a>
                                                </li>
                                                <li class="nav-item">
                                                    <a class="nav-link" id="additional-documents-tab" data-toggle="tab"
                                                        href="#additional-documents" role="tab" aria-controls="second"
                                                        aria-selected="false" >Additional Document</a>
                                                </li>
                                            </ul>

                                            <div class="tab-content" id="nav-tabContent">
                                                <div class="tab-pane active" id="required-documents" role="tabpanel" aria-labelledby="required-documents-tab">
                                                    <style>
                                                        input[type="file"] {
                                                            display: none;
                                                        }

                                                        .custom_button {
                                                            font-size: 18px;
                                                        }

                                                        .fa-solid.fa-cloud-arrow-up {
                                                            font-size: 18px;
                                                        }

                                                        .material-symbols-outlined {
                                                            font-variation-settings: 'FILL' 0,
                                                            'wght' 400,
                                                            'GRAD' 0,
                                                            'opsz' 48
                                                        }

                                                        .cmp_col {
                                                            margin-left: 50px;
                                                        }

                                                        .button_group .next {
                                                            color: #fff;
                                                            padding: 12px 25px !important;
                                                            font-weight: 600 !important;
                                                            background: #FF5C00 0% 0% no-repeat padding-box !important;
                                                            box-shadow: 0px 0px 5px #0000001a;
                                                            border-radius: 10px !important;
                                                            margin-left: 10px !important;
                                                            font-size: 16px;
                                                        }


                                                    </style>
                                                    <?php
                                                    global $wpdb;
                                                    // Table name
                                                    $table_name = $wpdb->prefix . 'erc_business_info';
                                                    // $lead_id = isset($_COOKIE['iris_business_lead_id']) ? $_COOKIE['iris_business_lead_id'] : '1';
                                                    // Check if the form is submitted
                                                    $is_business_data = $wpdb->get_row($wpdb->prepare("SELECT business_entity_type FROM $table_name WHERE  lead_id = %s", $lead_id));

                                                    $t1 = 0;
                                                    if($is_business_data && !empty($is_business_data->business_entity_type)){
                                                        if($is_business_data->business_entity_type == "S-Corporation"){
                                                            $t1 = 1;
                                                        }
                                                        else if($is_business_data->business_entity_type == "Partnerships/Multimember LLC"){
                                                            $t1 = 2;
                                                        }
                                                        else if($is_business_data->business_entity_type == "C-Corporation"){
                                                            $t1 = 3;
                                                        }
                                                        else if($is_business_data->business_entity_type == "Sole Proprietor"){
                                                            $t1 = 4;
                                                        }
                                                        else{
                                                            $t1 = 0;
                                                        }
                                                    }
                                                    ?>
                                                    <div class="main_content_iner">
                                                        <div class="container-fluid p-0">
                                                            <div class="row justify-content-center mb-4">
                                                                <div class="col-lg-12">
                                                                    <div class="white_card">

                                                                        <div class="white_card_body document-impact">

                                                                            <?php
                                                                            global $wpdb;
                                                                            $parent_folder = 'Required Documents';
                                                                            $form_id = 7;
                                                                            // isset($_COOKIE['iris_business_lead_id']) ? $lead_id = $_COOKIE['iris_business_lead_id'] : '';
                                                                            $doc_mapping_table = $wpdb->prefix . 'leads_document_mapping';
                                                                            $doc_uplod_table = $wpdb->prefix . 'leads_document_upload';
                                                                            $doc_uplod_status = $wpdb->prefix . 'leads_document_status';
                                                                            $doc_uplod_notes = $wpdb->prefix . 'leads_document_notes';
                                                                            $company_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `form_id` = %d", $form_id));
                                                                            $test = "doc_master_User";
                                                                            ?>
                                                                            <?php
                                                                            $userdata = get_user_by('id', get_current_user_id());
                                                                            $user_roles = $userdata->roles;
                                                                            if (in_array("lead1", $user_roles) || in_array("lead_associate1", $user_roles)) { ?>
                                                                                <h5 class="mt-3 mb-3 title_text"><b>For Self</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Documents</th>
                                                                                                <th>Files</th>
                                                                                                <th class="hideshow_onupload">Status</th>
                                                                                                <th class="hideshow_onupload">Comments</th>
                                                                                            </tr>
                                                                                            </thead>
                                                                                            <tbody>
                                                                                            <?php foreach ($company_docs as $key => $value) {
                                                                                                $doc_type_id = $value->doc_type_id;
                                                                                                $doc_key = $value->doc_key;
                                                                                                $doc_label = $value->doc_label;
                                                                                                ?>
                                                                                                <tr>

                                                                                                    <?php

                                                                                                    $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));

                                                                                                    $file_url = $result[0]->uploded_documents;
                                                                                                    $file_name = $result[0]->local_path_filename;
                                                                                                    $doc_id = $result[0]->id;
                                                                                                    $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                    ?>
                                                                                                    <?php

                                                                                                    ?>
                                                                                                    <td class="first_column">
                                                                                                        <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <?php if ($file_url == '') {

                                                                                                            ?>
                                                                                                            <label class="custom-file-upload">
                                                                                                                <span class="material-symbols-outlined">upload_file</span>
                                                                                                                <input type="file" class="custom_button"
                                                                                                                    name="<?php echo $doc_key; ?>"
                                                                                                                    data-parent_folder="<?php echo $parent_folder; ?>"
                                                                                                                    data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                    data-file_type="<?php if ($doc_key == 'ein') {
                                                                                                                        echo 'word';
                                                                                                                    } ?>">Upload
                                                                                                            </label>
                                                                                                            <label class="file_remove_label" style="display:none;">
                                                                                                                <span class="material-symbols-outlined">download_done</span>
                                                                                                                <a class="text_overflow_ellipse" href=""
                                                                                                                target="_blank"><span class="custom_file">File1.pdf</a>
                                                                                                                &nbsp;<span class="material-symbols-outlined cancel_icon"
                                                                                                                            style="color: initial;">cancel</span></span>
                                                                                                            </label>
                                                                                                        <?php } else {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <label class="custom-file-upload" style="display:none;">
                                                                                                                <span class="material-symbols-outlined">upload_file</span>
                                                                                                                <input type="file" class="custom_button"
                                                                                                                    name="<?php echo $doc_key; ?>"
                                                                                                                    data-parent_folder="<?php echo $parent_folder; ?>"
                                                                                                                    data-doc_type_id="<?php echo $doc_type_id; ?>">Upload
                                                                                                            </label>
                                                                                                            <label class="file_remove_label">
                                                                                                                <span class="material-symbols-outlined">download_done</span>
                                                                                                                <a class="text_overflow_ellipse"
                                                                                                                <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                            class="custom_file"><?php echo $file_name; ?>
                                                                                                                </a> &nbsp;<span
                                                                                                                        class="material-symbols-outlined cancel_icon"
                                                                                                                        data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-folder_name="<?php echo $parent_folder; ?>"
                                                                                                                        data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                        data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                        data-file_name="<?php echo $file_url; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                            </label>
                                                                                                        <?php } ?>
                                                                                                    </td>
                                                                                                    <td class="hideshow_onupload">
                                                                                                        <span class="progress_status"
                                                                                                            style="display: <?php echo isset($file_url) && $file_status === 'In review' ? 'block' : 'none'; ?>">In review</span>
                                                                                                        <span class="approved_status"
                                                                                                            style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'block' : 'none'; ?>">Approved</span>
                                                                                                        <span class="rejected_status"
                                                                                                            style="display: <?php echo isset($file_url) && $file_status === 'Rejected' ? 'block' : 'none'; ?>">Rejected</span>
                                                                                                        <span class="not_uploaded"
                                                                                                            style="display: <?php echo empty($file_url) ? 'block' : 'none'; ?>">Yet to upload</span>
                                                                                                    </td>
                                                                                                    <?php if (!empty($comment_data)) { ?>
                                                                                                        <td>
                                                                                                            <div style="display: flex;">
                                                                                            <span class="view_ta_comment" data-doc_type_id="<?php echo $doc_type_id; ?>" data-doc_id="<?php echo $doc_id; ?>" title="View">
                                                                                            <i class="fa-regular fa-comment"></i>
                                                                                            </span>
                                                                                            <?php if($is_affiliate == 0){?>
                                                                                                                <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                    data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                    title="Add">
                                                                                            <i class="fa-solid fa-comment-medical"></i>
                                                                                            </span>
                                                                                            <?php } ?>
                                                                                                            </div>
                                                                                                        </td>
                                                                                                    <?php } ?>
                                                                                                </tr>
                                                                                            <?php } ?>
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </div>
                                                                                </div>
                                                                            <?php }
                                                                            else { ?>

                                                                                <?php //include 'tax_business_name_filter.php'; ?>

                                                                                <!-- For OPS user table Start -->
                                                                            <?php if($t1==1){ ?>
                                                                                <h5 class="mt-3 mb-3 title_text"><b>Tax Form 1120-S (must include K-1's)</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single-other">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Years</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>
                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                $doc_type_id = $value->doc_type_id;
                                                                                                $doc_key = $value->doc_key;
                                                                                                $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                                                if ($m >= 1 && $m <= 2) {
                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php }?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>
                                                                                                    <?php
                                                                                                }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>
                                                                            <?php }
                                                                            else if($t1 == 2){ ?>
                                                                                <h5 class="mt-3 mb-3 title_text"><b>Tax Form 1065 (must include K-1's)</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single-other">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Years</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>
                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                $doc_type_id = $value->doc_type_id;
                                                                                                $doc_key = $value->doc_key;
                                                                                                $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                                                if ($m >= 3 && $m <= 4) {
                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php } ?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>
                                                                                                    <?php
                                                                                                }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>
                                                                            <?php }
                                                                            else if($t1 == 3){ ?>
                                                                                <h5 class="mt-3 mb-3 title_text"><b>Tax Form 1120</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single-other">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Years</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>
                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                $doc_type_id = $value->doc_type_id;
                                                                                                $doc_key = $value->doc_key;
                                                                                                $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                                                if ($m >= 5 && $m <= 6) {
                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } }?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php }?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment"  data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>
                                                                                                    <?php
                                                                                                }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>
                                                                            <?php }
                                                                            else if($t1 == 4){ ?>
                                                                                <h5 class="mt-3 mb-3 title_text"><b>Tax Form 1040 (must include Schedule C)</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single-other">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Years</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>
                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                $doc_type_id = $value->doc_type_id;
                                                                                                $doc_key = $value->doc_key;
                                                                                                $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                                                if ($m >= 7 && $m <= 8) {
                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php }?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment"  data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>
                                                                                                    <?php
                                                                                                }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>
                                                                            <?php } ?>
                                                                                <h5 class="mt-3 mb-3 title_text"><b>All 941-X's filed</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single-other">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Years</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>

                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                if ($m >= 9 && $m <= 10) {
                                                                                                    $doc_type_id = $value->doc_type_id;
                                                                                                    $doc_key = $value->doc_key;
                                                                                                    $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);

                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php }?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment"  data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>

                                                                                                <?php }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>

                                                                                <h5 class="mt-3 mb-3 title_text"><b>All Letters of overpayment received</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single-other">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Years</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>

                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                if ($m >= 11 && $m <= 12) {
                                                                                                    $doc_type_id = $value->doc_type_id;
                                                                                                    $doc_key = $value->doc_key;
                                                                                                    $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);

                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment"  data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php } ?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>

                                                                                                <?php }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>

                                                                                <h5 class="mt-3 mb-3 title_text"><b>Photo ID, preferably driver license, of the authorized signer</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single-other">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Document</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>

                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                if ($m >= 13 && $m <= 13) {
                                                                                                    $doc_type_id = $value->doc_type_id;
                                                                                                    $doc_key = $value->doc_key;
                                                                                                    $doc_label = $value->doc_label;

                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php } ?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>

                                                                                                <?php }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>

                                                                                <h5 class="mt-3 mb-3 title_text"><b>In the case of a joint 1040 filer, the photo ID of the spouse, as well</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single-other">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Document</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>

                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                if ($m >= 14 && $m <= 14) {
                                                                                                    $doc_type_id = $value->doc_type_id;
                                                                                                    $doc_key = $value->doc_key;
                                                                                                    $doc_label = $value->doc_label;
                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php } ?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>

                                                                                                <?php }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- For OPS user table END -->
                                                                                <?php

                                                                            } ?>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                   
                                                </div>
                                                <div class="tab-pane " id="additional-documents" role="tabpanel" aria-labelledby="additional-documents-tab">
                                                    <style>
                                                        input[type="file"] {
                                                            display: none;
                                                        }

                                                        .custom_button {
                                                            font-size: 18px;
                                                        }

                                                        .fa-solid.fa-cloud-arrow-up {
                                                            font-size: 18px;
                                                        }

                                                        .material-symbols-outlined {
                                                            font-variation-settings: 'FILL' 0,
                                                            'wght' 400,
                                                            'GRAD' 0,
                                                            'opsz' 48
                                                        }

                                                        .cmp_col {
                                                            margin-left: 50px;
                                                        }

                                                        .button_group .next {
                                                            color: #fff;
                                                            padding: 12px 25px !important;
                                                            font-weight: 600 !important;
                                                            background: #FF5C00 0% 0% no-repeat padding-box !important;
                                                            box-shadow: 0px 0px 5px #0000001a;
                                                            border-radius: 10px !important;
                                                            margin-left: 10px !important;
                                                            font-size: 16px;
                                                        }


                                                    </style>
                                                    <div class="main_content_iner">
                                                        <div class="container-fluid p-0">
                                                            <div class="row justify-content-center mb-4">
                                                                <div class="col-lg-12">
                                                                    <div class="white_card">

                                                                        <div class="white_card_body document-impact">

                                                                            <?php
                                                                            global $wpdb;
                                                                            $parent_folder_additional = 'Additional Documents';
                                                                            $form_id_additional = 8;
                                                                            // isset($_COOKIE['iris_business_lead_id']) ? $lead_id = $_COOKIE['iris_business_lead_id'] : '';
                                                                            $doc_mapping_table = $wpdb->prefix . 'leads_document_mapping';
                                                                            $doc_uplod_table = $wpdb->prefix . 'leads_document_upload';
                                                                            $doc_uplod_status = $wpdb->prefix . 'leads_document_status';
                                                                            $doc_uplod_notes = $wpdb->prefix . 'leads_document_notes';
                                                                            $company_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `form_id` = %d", $form_id_additional));
                                                                            $test = "doc_master_User";
                                                                            ?>
                                                                            <?php
                                                                            $userdata = get_user_by('id', get_current_user_id());
                                                                            $user_roles = $userdata->roles;
                                                                            if (in_array("lead1", $user_roles) || in_array("lead_associate1", $user_roles)) { ?>
                                                                                <h5 class="mt-3 mb-3 title_text"><b>For Self</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Documents</th>
                                                                                                <th>Files</th>
                                                                                                <th class="hideshow_onupload">Status</th>
                                                                                                <th class="hideshow_onupload">Comments</th>
                                                                                            </tr>
                                                                                            </thead>
                                                                                            <tbody>
                                                                                            <?php foreach ($company_docs as $key => $value) {
                                                                                                $doc_type_id = $value->doc_type_id;
                                                                                                $doc_key = $value->doc_key;
                                                                                                $doc_label = $value->doc_label;
                                                                                                ?>
                                                                                                <tr>

                                                                                                    <?php

                                                                                                    $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));

                                                                                                    $file_url = $result[0]->uploded_documents;
                                                                                                    $file_name = $result[0]->local_path_filename;
                                                                                                    $doc_id = $result[0]->id;
                                                                                                    $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                    ?>
                                                                                                    <?php

                                                                                                    ?>
                                                                                                    <td class="first_column">
                                                                                                        <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                    </td>
                                                                                                    <td>
                                                                                                        <?php if ($file_url == '') {

                                                                                                            ?>
                                                                                                            <label class="custom-file-upload">
                                                                                                                <span class="material-symbols-outlined">upload_file</span>
                                                                                                                <input type="file" class="custom_button"
                                                                                                                    name="<?php echo $doc_key; ?>"
                                                                                                                    data-parent_folder="<?php echo $parent_folder_additional; ?>"
                                                                                                                    data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                    data-file_type="<?php if ($doc_key == 'ein') {
                                                                                                                        echo 'word';
                                                                                                                    } ?>">Upload
                                                                                                            </label>
                                                                                                            <label class="file_remove_label" style="display:none;">
                                                                                                                <span class="material-symbols-outlined">download_done</span>
                                                                                                                <a class="text_overflow_ellipse" href=""
                                                                                                                target="_blank"><span class="custom_file">File1.pdf</a>
                                                                                                                &nbsp;<span class="material-symbols-outlined cancel_icon"
                                                                                                                            style="color: initial;">cancel</span></span>
                                                                                                            </label>
                                                                                                        <?php } else {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <label class="custom-file-upload" style="display:none;">
                                                                                                                <span class="material-symbols-outlined">upload_file</span>
                                                                                                                <input type="file" class="custom_button"
                                                                                                                    name="<?php echo $doc_key; ?>"
                                                                                                                    data-parent_folder="<?php echo $parent_folder_additional; ?>"
                                                                                                                    data-doc_type_id="<?php echo $doc_type_id; ?>">Upload
                                                                                                            </label>
                                                                                                            <label class="file_remove_label">
                                                                                                                <span class="material-symbols-outlined">download_done</span>
                                                                                                                <a class="text_overflow_ellipse"
                                                                                                                <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                            class="custom_file"><?php echo $file_name; ?>
                                                                                                                </a> &nbsp;<span
                                                                                                                        class="material-symbols-outlined cancel_icon"
                                                                                                                        data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-folder_name="<?php echo $parent_folder_additional; ?>"
                                                                                                                        data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                        data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                        data-file_name="<?php echo $file_url; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                            </label>
                                                                                                        <?php } ?>
                                                                                                    </td>
                                                                                                    <td class="hideshow_onupload">
                                                                                                        <span class="progress_status"
                                                                                                            style="display: <?php echo isset($file_url) && $file_status === 'In review' ? 'block' : 'none'; ?>">In review</span>
                                                                                                        <span class="approved_status"
                                                                                                            style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'block' : 'none'; ?>">Approved</span>
                                                                                                        <span class="rejected_status"
                                                                                                            style="display: <?php echo isset($file_url) && $file_status === 'Rejected' ? 'block' : 'none'; ?>">Rejected</span>
                                                                                                        <span class="not_uploaded"
                                                                                                            style="display: <?php echo empty($file_url) ? 'block' : 'none'; ?>">Yet to upload</span>
                                                                                                    </td>
                                                                                                    <?php if (!empty($comment_data)) { ?>
                                                                                                        <td>
                                                                                                            <div style="display: flex;">
                                                                                            <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="View">
                                                                                            <i class="fa-regular fa-comment"></i>
                                                                                            </span>
                                                                                            <?php if($is_affiliate == 0){?>
                                                                                                                <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                    data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                    title="Add">
                                                                                            <i class="fa-solid fa-comment-medical"></i>
                                                                                            </span>
                                                                                            <?php } ?>
                                                                                                            </div>
                                                                                                        </td>
                                                                                                    <?php } ?>
                                                                                                </tr>
                                                                                            <?php } ?>
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </div>
                                                                                </div>
                                                                            <?php }
                                                                            else { ?>
                                                                                <?php //include 'tax_business_name_filter.php'; ?>

                                                                                <!-- For OPS user table Start -->

                                                                                <h5 class="mt-3 mb-3 title_text"><b>Original 1040 filed</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Years</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>
                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                $doc_type_id = $value->doc_type_id;
                                                                                                $doc_key = $value->doc_key;
                                                                                                $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                                                if ($m >= 1 && $m <= 2) {
                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder_additional; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                    <i class="fa-regular fa-comment"></i>
                                                                                    </span>
                                                                                    <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                    <i class="fa-solid fa-comment-medical"></i>
                                                                                    </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder_additional; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder_additional; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder_additional; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php } ?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                    <i class="fa-regular fa-comment"></i>
                                                                                    </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>
                                                                                                    <?php
                                                                                                }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>

                                                                                <h5 class="mt-3 mb-3 title_text"><b>Original K-1 received</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Years</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>

                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                if ($m >= 3 && $m <= 4) {
                                                                                                    $doc_type_id = $value->doc_type_id;
                                                                                                    $doc_key = $value->doc_key;
                                                                                                    $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder_additional; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder_additional; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder_additional; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder_additional; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php } ?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>

                                                                                                <?php }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>

                                                                                <h5 class="mt-3 mb-3 title_text"><b>Amended K-1 received</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Years</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>

                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                if ($m >= 5 && $m <= 6) {
                                                                                                    $doc_type_id = $value->doc_type_id;
                                                                                                    $doc_key = $value->doc_key;
                                                                                                    $doc_label = substr($value->doc_label, strrpos($value->doc_label, '-') + 1);
                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder_additional; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder_additional; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder_additional; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder_additional; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php } ?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>

                                                                                                <?php }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>

                                                                                <h5 class="mt-3 mb-3 title_text"><b>Photo ID of the authorized signer
                                                                                    </b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-single">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Document</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>

                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                if ($m >= 7 && $m <= 7) {
                                                                                                    $doc_type_id = $value->doc_type_id;
                                                                                                    $doc_key = $value->doc_key;
                                                                                                    $doc_label = $value->doc_label;
                                                                                                    ?>
                                                                                                    <tr>

                                                                                                        <?php

                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for=""
                                                                                                                class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder_additional; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder_additional; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder_additional; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder_additional; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php } ?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>

                                                                                                <?php }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>

                                                                                        </table>
                                                                                    </div>
                                                                                </div>

                                                                                <h5 class="mt-3 mb-3 title_text"><b>Photo ID of spouse (for joint 1040 filers)</b></h5>
                                                                                <div class="row">
                                                                                    <div class="col-md-12 table-responsive">
                                                                                        <table class="table com_doc_display payroll_sec tax-photo-id">
                                                                                            <thead>
                                                                                            <tr>
                                                                                                <th style="text-align: left;">Document</th>
                                                                                                <th>Is Applicable?</th>
                                                                                                <th>Files</th>
                                                                                                <th>Status</th>
                                                                                                <th>Comments</th>
                                                                                            </tr>
                                                                                            </thead>

                                                                                            <tbody>
                                                                                            <?php
                                                                                            $m = 1;
                                                                                            foreach ($company_docs as $key => $value) {
                                                                                                if ($m >= 8 && $m <= 8) {
                                                                                                    $doc_type_id = $value->doc_type_id;
                                                                                                    $doc_key = $value->doc_key;
                                                                                                    $doc_label = $value->doc_label;
                                                                                                    $isapplicable = $wpdb->get_row(
                                                                                                        $wpdb->prepare("SELECT * FROM eccom_tax_amendment_additional_documents WHERE  document_id = %s AND lead_id = %d", $doc_key, $lead_id)
                                                                                                    );
                                                                                                    ?>
                                                                                                    <tr>
                                                                                                        <?php
                                                                                                        $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                                                                                                        $file_url = $result[0]->uploded_documents;
                                                                                                        $file_name = $result[0]->local_path_filename;
                                                                                                        $doc_id = $result[0]->id;
                                                                                                        $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                                                                                                        ?>
                                                                                                        <td class="first_column">
                                                                                                            <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                                                                                                        </td>
                                                                                                        <td><?php echo strtoupper($isapplicable->applicable); ?></td>
                                                                                                        <?php if ($file_url != '') {
                                                                                                            $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                            $file_status = $status_data[0]->doc_status;
                                                                                                            ?>
                                                                                                            <td style="text-align: center;">
                                                                                                                <label class="file_remove_label">
                                                                                                                    <span class="material-symbols-outlined">download_done</span>
                                                                                                                    <a class="text_overflow_ellipse"
                                                                                                                    <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span
                                                                                                                                class="custom_file"><?php echo $file_name; ?>
                                                                                                                    </a> &nbsp;<span
                                                                                                                            class="material-symbols-outlined cancel_icon"
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-folder_name="<?php echo $parent_folder_additional; ?>"
                                                                                                                            data-lead_id="<?php echo $lead_id; ?>"
                                                                                                                            data-doc_key="<?php echo $doc_key; ?>"
                                                                                                                            data-file_name="<?php echo $file_url; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                            style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                                                                                                                </label>
                                                                                                            </td>
                                                                                                            <td>
                                                                                                                <select name="change_document_status"
                                                                                                                        id="change_ta_document_status"
                                                                                                                        class="change_doc" <?php echo ((isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test) || $is_affiliate == 1) ? 'disabled' : ''; ?> >
                                                                                                                    <option value="">- Select Status -</option>
                                                                                                                    <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>">
                                                                                                                        In
                                                                                                                        review
                                                                                                                    </option>
                                                                                                                    <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                                                                                                                    <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                                                                                                                            data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                            data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                                                                                                                </select>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;">
                                                                                                                <?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                        <?php if($is_affiliate == 0){?>
                                                                                                                    <span class="add_ta_comment_btn" data-doc_id="<?php echo $doc_id; ?>"
                                                                                                                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="Add">
                                                                                        <i class="fa-solid fa-comment-medical"></i>
                                                                                        </span>
                                                                                                                <?php } } ?>

                                                                                                            </td>
                                                                                                        <?php } else { ?>
                                                                                                            <td>
                                                                                                                <?php if($file_url == ''){
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" >
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder_additional; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                                                                                                                    </label>
                                                                                                                <?php }else{
                                                                                                                    $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                                                                                                                    $file_status = $status_data[0]->doc_status;
                                                                                                                    ?>
                                                                                                                    <label class="custom-file-upload" style="display:none;">
                                                                                                                        <span class="material-symbols-outlined">upload_file</span> <input type="file" class="custom_button" name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder_additional; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                                                                                                                    </label>
                                                                                                                    <label class="file_remove_label" >
                                                                                                                        <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" <?php if($is_view == 1){ echo 'href="'.$file_url.'"';}?> target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder_additional; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                                                                                                                    </label>
                                                                                                                <?php }  ?>
                                                                                                            </td>
                                                                                                            <td style="text-align: center;"><span class="not_uploaded">Yet to upload</span></td>
                                                                                                            <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                                                                                                                    <span class="view_ta_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>"
                                                                                                                        title="View">
                                                                                        <i class="fa-regular fa-comment"></i>
                                                                                        </span>
                                                                                                                <?php } ?>   </td>
                                                                                                        <?php } ?>
                                                                                                    </tr>

                                                                                                <?php }
                                                                                                $m++;
                                                                                            } ?>
                                                                                            </tbody>
                                                                                        </table>
                                                                                    </div>
                                                                                </div>
                                                                                <!-- For OPS user table END -->
                                                                                <?php

                                                                            } ?>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- Modal -->
                                                   
                                                </div>
                                            </div>

                                        </div>
                                        <div class="tab-pane erc-project-scroll " id="eleve-invoices" role="tabpanel" aria-labelledby="eleve-invoice-tab">
										<!-- Invoice card -->
										<?php
										// Fetch invoices
										$crm_erp_admin = new CRM_ERP_Admin_Interface();
										$lead_id_project = $lead_id;
										$product_ids = $project->product_id; 
										$invoicesCard = $crm_erp_admin->get_invoices_by_lead_and_product($lead_id_project, $product_ids);
										//echo '<pre>'; print_r($invoicesCard); echo '</pre>';
										// Display invoices
										$crm_erp_admin->display_invoices_data($invoicesCard); 

                                        echo do_shortcode("[productpage_invoice_modals]");
										?>
										<!--/ Invoice card -->
                                        </div>

                                        <!-- Audit logs tab start -->
                                            <div class="container tab-pane scroll-common fade" id="pills-logs" role="tabpanel" aria-labelledby="pills-audit-logs">
                                                <img src="<?php echo get_site_url().'/wp-content/plugins/wp-iris/page-templates/loader.gif';  ?>" style="width:80px;margin-left: 35%;" />
                                            </div>
                                        <!-- Audit logs tab end -->

                                    </div>
                                </form>

                                <div class="row">

                                    <?php

                                    if ($project->contact_id > 0) {
                                        // Replace 'your_table_name' with the actual table name where your data is stored
                                    
                                        $contact_id = $project->contact_id;

                                        $query_new = $wpdb->prepare("
                                SELECT 
                                    c.id AS contact_id,
                                    cl.lead_id,
                                    CONCAT(c.first_name, ' ', c.last_name) AS name,
                                    c.title,
                                    c.contact_type,
                                    (
                                        SELECT 
                                            p.phone
                                        FROM 
                                            {$wpdb->prefix}op_phone_contact AS pc
                                        INNER JOIN 
                                            {$wpdb->prefix}op_phone AS p ON pc.phone_id = p.id
                                        WHERE 
                                            pc.contact_id = c.id
                                        ORDER BY 
                                            p.created_date DESC
                                        LIMIT 1
                                    ) AS phone,
                                    (
                                        SELECT 
                                            e.email
                                        FROM 
                                            {$wpdb->prefix}op_email_contact AS ec
                                        INNER JOIN 
                                            {$wpdb->prefix}op_emails AS e ON ec.email_id = e.id
                                        WHERE 
                                            ec.contact_id = c.id
                                        ORDER BY 
                                            e.created_date DESC
                                        LIMIT 1
                                    ) AS email,
                                    c.trash 
                                FROM 
                                    {$wpdb->prefix}op_contacts AS c
                                LEFT JOIN 
                                    {$wpdb->prefix}op_contact_lead AS cl ON c.id = cl.contact_id
                                WHERE 
                                    c.id = %d
                                ORDER BY 
                                    c.trash ASC, c.id DESC
                            ", $contact_id);

                                        $query_t = $wpdb->get_results($query_new);

                                        if (!empty ($query_t)) {
                                            $con_data = $query_t[0];

                                            echo do_shortcode('[CommLog_activity_by_lead lead_id="0" lead_phone="' . $con_data->phone . '" lead_email="' . $con_data->email . '" ] ');
                                        }

                                    }


                                    ?>


                                    <?php //echo do_shortcode('[CommLog_activity_by_lead lead_id="' . $lead_id . '"]');   ?>
                                    <div class="custom-opportunity-notes">
                                        <div class="col-sm-12">
                                            <h5>Notes
                                                <?php if($is_affiliate == 0){?>
                                                <a href="javascript:void(0)" class="opp-add-notes"><i
                                                        class="fa fa-plus-circle create-note-btn"></i></a>
                                                <?php } ?>
                                            </h5>
                                        </div>
                                        <div class="col-sm-12 custom-opp-notes-scroll">
                                            <div class="notes-listing">
                                                            <?php 
                                                            $i = 1;
                                                            $from='UTC';
                                                            $to='America/New_York';
                                                            $format='Y-m-d h:i:s A';
                                                            foreach ($all_notes as $n_key => $n_value) {
                                                                $current_user_id = get_current_user_id();
                                                                $confidence_notes_access = $n_value->confidential_notes;

                                                                $date = $n_value->created;//UTC time
                                                                date_default_timezone_set($from);
                                                                $newDatetime = strtotime($date);
                                                                date_default_timezone_set($to);
                                                                $newDatetime = date($format, $newDatetime);
                                                                date_default_timezone_set('UTC');
                                                                $datetime = date_create($newDatetime);
                                                                $time = date_format($datetime, "h:ia");
                                                                $day = date_format($datetime, " D ");
                                                                $month = date_format($datetime, " M ");
                                                                $date = date_format($datetime, "dS,");
                                                                $year = date_format($datetime, " Y");
                                                                $actual_date = $time . " on " . $day . $month . $date . $year;
                                                                $note_array = explode("https", $n_value->note);
                                                                $notes = $note_array[0];
                                                                if (isset($note_array[1]) && !empty($note_array[1])) {
                                                                   $url_array = explode(" ", $note_array[1]);
                                                                   if(isset($url_array[0]) && !empty($url_array[0])){
                                                                        $url = 'https' . $url_array[0];
                                                                        $notes .= '<a href="' . $url . '">' . $url . '</a>';
                                                                    }
                                                                    for($i=1; $i<count($url_array);$i++){
                                                                        if(isset($url_array[$i]) && !empty($url_array[$i])){
                                                                            $notes .= " ".$url_array[$i];
                                                                        }
                                                                    }
                                                                }
                                                                
                                                                  $conf_class ='';
                                                                    if($confidence_notes_access){ 
                                                                        $conf_class='confidential-notes-div';    
                                                                     } ?>  

                                                                <div class="note-listing-div shadow <?php echo $conf_class;?>" id="note-list-<?= $n_value->id; ?>">  
                                                                    <p class="notes" id="note-<?= $n_value->id; ?>" data-confidece="<?php echo $confidence_notes_access;?>"><?= $notes; ?></p>
                                                                    <p class="date-time">(<?= $actual_date;?>)</p>
                                                                  <?php 
                                                                    // check confidence_user and self notes
                                                                    if($confidence_user == 1 && $current_user_id == $n_value->created_by && $confidence_notes_access){ ?>  
                                                                    <a href="javascript:void(0)" title="Edit Notes" class="edit_self_notes" data-note_id="<?= $n_value->id; ?>">
                                                                        <i class="fa-regular fa-pen-to-square"></i>
                                                                    </a>
                                                                    <a href="javascript:void(0)" title="Delete Notes" class="delete_self_notes" data-note_id="<?= $n_value->id; ?>">
                                                                        <i class="fa fa-trash" aria-hidden="true"></i>
                                                                    </a>
                                                                <?php } ?>
                                                                </div>
                                                                <?php $i++;
                                                            } ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="col-md-3" id="right_section">
                                <div class="custom-opportunity-view-sidebar">
                                    <?php if($is_affiliate == 0){?>
                                    <div class="row edit-icon-milestone">
                                        <a href="javascript:void(0)" title="Edit"><i
                                                class="fa-regular fa-pen-to-square edit_project_milestone"
                                                style="font-weight: 700"></i></a>
                                    </div>
                                    <?php } ?>
                                    <div class="show_message_milestone"></div>
                                    <div class="d-flex opp-owner mb-3 ">
                                        <label><b>Milestone:</b></label>
                                        <p id="showMilestone">
                                            <?php echo ucwords($project->milestone_name);
                                            $MilestoneIssue = '';
                                            if ($project->milestoneActiveStatus == 'inactive') {
                                                $MilestoneIssue = 'Inactive';
                                            }
                                            if ($project->milestone_deletedat != '') {
                                                $MilestoneIssue = 'Deleted';
                                            }
                                            $milestoneMmap = unserialize($project->milestoneMap);
                                            if (empty ($milestoneMmap)) {
                                                $MilestoneIssue = 'Unassigned';
                                            } elseif (!in_array("project", $milestoneMmap)) {
                                                $MilestoneIssue = 'Unassigned';
                                            }
                                            if (!empty ($MilestoneIssue)) {
                                                echo '(' . $MilestoneIssue . ')';
                                            }
                                            ?>
                                        </p>
                                        <select class="form-control custom-mile-status select-milestone" id="Milestone"
                                            on='23' style="display: none;">
                                            <?php
                                            if (count($all_milestones) > 0) {
                                                foreach ($all_milestones as $all_milestone) {
                                                    $milestone_id = $all_milestone->milestone_id;
                                                    $MilestoneIssue = '';
                                                    if ($all_milestone->status == 'inactive') {
                                                        $MilestoneIssue = 'Inactive';
                                                    }
                                                    if ($all_milestone->deleted_at !== null) {
                                                        $MilestoneIssue = 'Deleted';
                                                    }
                                                    $milestoneMmap = unserialize($all_milestone->map);

                                                    if (empty ($milestoneMmap)) {
                                                        $MilestoneIssue = 'Unassigned';
                                                    } elseif (!in_array("project", $milestoneMmap)) {
                                                        $MilestoneIssue = 'Unassigned';
                                                    }

                                                    $milestone_name = '';
                                                    $MilestoneID = '';
                                                    if (!empty ($MilestoneIssue)) {
                                                        $milestone_name = $all_milestone->milestone_name . '(' . $MilestoneIssue . ')';
                                                        $MilestoneID = '';
                                                    } else {
                                                        $milestone_name = $all_milestone->milestone_name;
                                                        $MilestoneID = $all_milestone->milestone_id;
                                                    }
                                                    $sel = '';
                                                    if ($project->milestone_id == $milestone_id) {
                                                        $sel = 'selected';
                                                    } else {
                                                        $sel = '';
                                                    }
                                                    echo '<option value="' . $MilestoneID . '" ' . $sel . '>' . ucwords($milestone_name) . '</option>';
                                                }
                                            } else {
                                                ?>
                                                <option value="">Select Milestone</option>
                                                <?php
                                            }
                                            ?>
                                        </select>
                                    </div>

                                    <div class="d-flex opp-owner">
                                        <label><b>Stage:</b></label>
                                        <p id="showMilestoneStage">
                                            <?php
                                            echo ucwords($project->milestoneStatus);
                                            $StageIssue = '';
                                            if ($project->StageActiveStatus == 'inactive') {
                                                $StageIssue = 'Inactive';
                                            }
                                            if ($project->StageDeleteStatus != '') {
                                                $StageIssue = 'Deleted';
                                            }
                                            if (!empty ($StageIssue)) {
                                                echo '(' . $StageIssue . ')';
                                            }
                                            ?>
                                        </p>
                                        <select class="form-control custom-mile-status" id="MilestoneStage"
                                            name='milestone_status-23' style="display: none;">
                                            <?php
                                            if (count($all_milestone_status) > 0) {
                                                foreach ($all_milestone_status as $allmilestonestatus) {
                                                    $milestone_stage_id = $allmilestonestatus->milestone_stage_id;
                                                    $sel = '';
                                                    if ($allmilestonestatus->status == 'inactive') {
                                                        $StageIssue = 'Inactive';
                                                    }
                                                    if ($allmilestonestatus->deleted_at !== null) {
                                                        $StageIssue = 'Deleted';
                                                    }
                                                    $stageID = '';
                                                    $stage_name = '';
                                                    if (!empty ($StageIssue)) {
                                                        $stage_name = $allmilestonestatus->stage_name . '(' . $StageIssue . ')';
                                                        $stageID = '';
                                                    } else {
                                                        $stage_name = $allmilestonestatus->stage_name;
                                                        $stageID = $allmilestonestatus->milestone_stage_id;
                                                    }
                                                    if ($project->milestone_stage_id == $milestone_stage_id) {
                                                        $sel = 'selected';
                                                    } else {
                                                        $sel = '';
                                                    }
                                                    echo '<option value="' . $milestone_stage_id . '" ' . $sel . '>' . ucwords($stage_name) . '</option>';
                                                }
                                            } else {
                                                ?>
                                                <option value="">Select Stage</option>
                                                <?php
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    <?php if($is_affiliate == 0){?>
                                    <div class="row">
                                        <div class="button-container milestone_button_container" style="display: none;">
                                            <button type="submit" class="create_product_btn update_milestone"
                                                data-projectID="<?php echo $project->project_id; ?>"
                                                data-milestoneid="<?php echo $project->milestone_id; ?>"
                                                data-milestonestageid="<?php echo $project->milestone_stage_id; ?>">Update</button>
                                            <button type="button"
                                                class="create_product_btn cancel_milestone">Cancel</button>
                                        </div>
                                    </div>
                                    <?php } ?>
                                </div>
                                <div class="custom-opportunity-view-sidebar assign_collaborators_section">
                                    <div class="row edit-icon-owner">
                                        <?php if($is_affiliate == 0){?>
                                        <a href="javascript:void(0)" title="Edit"><i
                                                class="fa-regular fa-pen-to-square edit_project_collaborator"
                                                style="font-weight: 700"></i></a>
                                        <?php } ?>
                                    </div>
                                    <input type="hidden" id="show_collaborators" value="0">
                                    <div class="d-flex opp-owner">
                                        <label><b>Assigned Collaborators:</b></label>
                                    </div>
                                    <div class="all_assigned_users">
                                        <?php
                                        $unsigned_user = [];
                                        foreach ($collaborators as $collaborator) {
                                            $user_data = get_user_by('id', $collaborator->user_id);
                                            $user_roles = $user_data->roles;
                                            $user_role = '';
                                            if(isset($user_roles[0])){
                                                $userrole = $user_roles[0];
                                                if($userrole == 'echeck_staff'){
                                                    $user_role = ' (Echeck Staff)';
                                                }
                                                elseif($userrole == 'echeck_admin'){
                                                    $user_role = ' (Echeck Admin)';
                                                }
                                                elseif($userrole == 'master_ops'){
                                                    $user_role = ' (Master Ops)';
                                                }
                                                elseif($userrole == 'master_sales'){
                                                    $user_role = ' (Master Sales)';
                                                }
                                                elseif($userrole == 'iris_sales_agent'){
                                                    $user_role = ' (Sales Agent)';
                                                }
                                                elseif($userrole == 'iris_sales_agent_rep'){
                                                    $user_role = ' (Sales Agent Rep)';
                                                }
                                                elseif($userrole == 'iris_affiliate_users'){
                                                    $user_role = ' (Affiliate)';
                                                }
                                                elseif($userrole == 'iris_employee'){
                                                    $user_role = ' (Employee)';
                                                }
                                            }
                                            $unsigned_user[count($unsigned_user)] = $collaborator->user_id;
                                            ?>
                                            <p class='col-form-label assign_users'
                                                id="assign-users<?= $collaborator->user_id ?>">
                                                <?php echo $user_data->data->display_name.$user_role; ?>
                                                <?php if($is_affiliate == 0){?>
                                                <span unassign-user="<?= $collaborator->user_id ?>"
                                                    unassign-user-name="<?= $user_data->data->display_name ?>"
                                                    class="unassign-user glyphicon glyphicon-minus-sign" style="padding: 0 5px;
                                            cursor: pointer;"></span>
                                                <?php } ?>
                                            </p>
                                            <?php
                                        }
                                        ?>
                                        <div class="show_message_collaborator"></div>
                                        <select <?= $disabled ?> class="user-select form-control" name="assigned_user_id"
                                            id="assign_collaborator" style="display: none;">
                                            <option value="">Select Collaborator to Assign</option>
                                            <?php foreach ($listed_users as $user) {
                                                if (in_array($user->ID, $unsigned_user)) {
                                                } else { ?>
                                                    <option id="user-option<?= $user->ID ?>" value="<?= $user->ID ?>">
                                                        <?= $user->display_name; ?>
                                                    </option>;
                                                <?php }
                                            } ?>
                                        </select>
                                    </div>
                                    <?php if($is_affiliate == 0){?>
                                    <div class="row">
                                        <div class="button-container collaborator_button_container"
                                            style="display: none;">
                                            <button type="button" class="create_product_btn assign-user-btn"
                                                id="assign-user-1">Assign Collaborator</button>
                                            <button type="button"
                                                class="create_product_btn cancel_collaborator">Cancel</button>
                                        </div>
                                    </div>
                                    <?php } ?>
                                </div>

                                <!-- select owner -->
                                <div class="custom-opportunity-view-sidebar">
                                    <div class="row edit-icon-owner">
                                        <a href="javascript:void(0)" title="Edit" ><i class="fa-regular fa-pen-to-square edit_opportunity_owner" style="font-weight: 700"></i></a>
                                    </div>
                                    <div class="show_message_owner"></div>
                                    <div class="d-flex opp-owner">
                                        <label><b>Select Owner:</b></label>
                                        <p id="showOwnerList"><?php  echo $owner_name; ?></p>
                                    </div>
                                    <div class="d-flex1 opp-owner owner-main-div" id="owner-div-id">
                                        <select  class="ewc-filter-sub selectpicker custom-ownership" data-live-search="true" title="Select Owner" id="OwnerList" style="display: none;">
                                        <?php    
                                        $groups = [];
                                        foreach ($users as $user) {
                                            $userid = $user->ID;
                                            $display_name = $user->display_name;
                                            if(isset($user->roles[0])){
                                                $user_role = $user->roles[0];
                                            }else{
                                                foreach ($user->roles as $key => $value) {
                                                        $user_role = $value;
                                                }    
                                            }  

                                           $selected = '';
                                           if($project->created_by == $userid){
                                                       $selected='selected';
                                                    }
                                                
                                                //'echeck_staff','echeck_admin','echeck_client','iris_affiliate_users','master_sales','iris_sales_agent','iris_sales_agent_rep'
                                                
                                                if($user_role=='master_ops'){
                                                    $user_role = 'Master OPS';
                                                }else if($user_role=='echeck_staff' || $user_role=='echeck_admin'){
                                                    $user_role = 'OPS';
                                                }else if($user_role=='echeck_client'){
                                                    $user_role = 'Finance';    
                                                }else if($user_role=='iris_affiliate_users'){
                                                    $user_role = 'Affiliate';
                                                }else if($user_role=='master_sales'){
                                                    $user_role = 'Master Sales';
                                                }else if($user_role=='iris_sales_agent' || $user_role=='iris_sales_agent_rep'){
                                                    $user_role = 'Sales';
                                                }

                                               // Create an array to store options for each group
                                                 if (!isset($groups[$user_role])) {
                                                    $groups[$user_role] = [];
                                                 }
                                                 $test_display_name = ' '.$display_name;
                                                 if(strpos($test_display_name,"Test") || strpos($test_display_name,"test")){

                                                 }else{
                                                    if(trim($display_name)!=''){
                                                        $option = "<option value='{$userid}' {$selected}>{$display_name}</option>";
                                                        $groups[$user_role][] = $option;
                                                    }  
                                        }        }

                                            // Generate the HTML for each optgroup
                                            foreach ($groups as $group => $options) {
                                                echo "<optgroup label='{$group}'>";
                                            foreach ($options as $option) {
                                                echo $option;
                                            }
                                            echo "</optgroup>";
                                            }
                                            
                                        ?>
                                        </select>
                                    </div>
                                    <div class="row">
                                        <div class="button-container owner_button_container mt-0" style="display: none;">
                                                <button type="submit" class="create_product_btn update_owner" data-projectID="<?php echo $project->project_id; ?>">Update</button>
                                                <button type="button" class="create_product_btn cancel_owner" >Cancel</button>
                                        </div>
                                    </div>
                                </div>
                                <!-- select owner end -->        

                                <div class="custom-opportunity-view-sidebar">
                                    <?php if($is_affiliate == 0){?>
                                    <div class="row edit-icon-owner">
                                        <a href="javascript:void(0)" title="Edit"><i
                                                class="fa-regular fa-pen-to-square edit_project_contact"
                                                style="font-weight: 700"></i></a>
                                    </div>
                                    <?php } ?>
                                    <div class="show_message_contact"></div>
                                    <div class="d-flex opp-owner">
                                        <label><b>Select Contact:</b></label>
                                        <p id="showContactList">
                                            <?php echo ucwords($primary_contact); ?>
                                        </p>
                                        <select class="form-control custom-contactList" style="display: none;"
                                            id="ContactList">
                                            <?php
                                            if (count($all_contacts) > 0) {
                                                foreach ($all_contacts as $all_contact) {
                                                    $contact_id = $all_contact->id;
                                                    $sel = '';
                                                    if ($project->contact_id == $contact_id) {
                                                        $sel = 'selected';
                                                    } else {
                                                        $sel = '';
                                                    }
                                                    $contact_name = $all_contact->first_name . ' ' . $all_contact->last_name;
                                                    echo '<option value="' . $contact_id . '" ' . $sel . '>' . $contact_name . '</option>';
                                                }
                                            } else {
                                                ?>
                                                <option value="">Select Contact</option>
                                                <?php
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    
                                    <div class="row">
                                        <div id="userDetails" data-id='<?php echo get_current_user_id(); ?>'
                                            data-name='<?php echo $login_user_name; ?>' style="display:none;"></div>
                                        <?php if($is_affiliate == 0){?>
                                        <div class="button-container contact_button_container" style="display: none;">
                                            <button type="submit" class="create_product_btn update_contacts"
                                                data-projectID="<?php echo $project->project_id; ?>"
                                                data-contactid="<?php echo $project->contact_id; ?>">Update</button>
                                            <button type="button"
                                                class="create_product_btn cancel_contact">Cancel</button>
                                        </div>
                                        <?php } ?>
                                    </div>
                                    
                                </div>

                            </div>

                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div><!-- .site-main -->
</div><!-- .content-area -->
 <!-- Modal -->
<div class="modal fade" id="exampleModal_ercdoc_view" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Comments</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height:200px; overflow-y:auto;">
                <input type="hidden" name="view_comment_id" class="view_comment_id" id="view_comment_id">
                <span class="comment_username" style="font-size:14px">User Name</span>
                <p class="mt-2">consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
                    aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                    commodo consequat.</p>
                <span class="comment_date" style="float: right; font-size:12px">Comment Date:- 05-12-2023</span>
                <br>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-warning">View Log</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal -->
<div class="modal fade" id="exampleModal_otherdoc_adding_comment" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Comments</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <input type="hidden" name="doc_id_comment" id="doc_id_comment" class="doc_id_comment">
                        <input type="hidden" name="doc_type_id_comment" id="doc_type_id_comment"
                            class="doc_type_id_comment">
                        <textarea name="comment" id="other-comment-textarea"
                                data-doc_id="<?php echo $result[0]->doc_id; ?>" maxlength="100"
                                placeholder="Only 100 characters are allowed"
                                style="width: 100%;height: 150px;"></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" id="ta-submit-comment-btn" class="btn btn-primary">Submit</button>
            </div>
        </div>
    </div>
</div>
<div class="modal opportunity-add-new-notes" id="add-new-opp-notes" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
<div class="modal-dialog modal-dialog-centered">
<div class="modal-content">
<div class="modal-header">
<h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-comment"></i> Notes</h5>
<button type="button" class="close-popup-notes close">
<span aria-hidden="true">×</span>
</button>
</div>
<div class="modal-body">
<div class="show_notes_message">
<p class="note-response" style="display: none;"></p>
<p class="error-response" style="display: none;">Notes is required.</p>
</div>
 
                <div class="row">
<div class="floating col-md-12">
<label>Notes:</label>
<textarea id="notes-input" class="form-control" rows="5" maxlength="1000"></textarea>
<?php if($confidence_user==1){?>
                            Mark As Confidential  <input type="checkbox" name="confidence_user_check" id="confidence_user_check" value="1" style="margin-top:0px;" <?php echo $confidence_check; ?>> 
<?php } ?>
<p class="remaining-msg" id="remaining-msg" style="float:right;">1000/1000 characters remaining.</p>
</div>
</div>
<div class="buttion_next_prev">
<button type="button" class="nxt_btn" id="create-note">Submit</button>
</div>
</div>
</div>
</div>
</div>
 
    <!-- edit notes popup -->
<div class="modal opportunity-add-new-notes" id="edit-new-opp-notes" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
<div class="modal-dialog modal-dialog-centered">
<div class="modal-content">
<div class="modal-header">
<h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-comment"></i> Notes</h5>
<button type="button" class="close-popup-notes close">
<span aria-hidden="true">×</span>
</button>
</div>
<div class="modal-body">
<div class="show_message_popup">
<p class="note-response" style="display: none;"></p>
<p class="error-response" style="display: none;">Notes is required.</p>
</div>
<div class="row">
<div class="floating col-md-12">
<label>Notes:</label>
<textarea id="edit-notes-input" maxlength="1000" style="resize:none;margin-bottom:2%;" class="form-control" rows="5"></textarea>
<?php if($confidence_user==1){?>
                                Mark As Confidential  <input type="checkbox" name="confidence_user_check" id="edit_confidence_user_check" value="1" style="margin-top:0px;"> 
<?php } ?>
<p class="edit-remaining-msg" id="edit-remaining-msg" style="float:right;">1000/1000 characters remaining.</p>
</div>
</div>
<div class="buttion_next_prev">
<button type="button" class="nxt_btn" id="update-note" data-note_id="">Submit</button>
</div>
</div>
</div>
</div>
</div>
<!-- Modal -->
<div class="modal fade" id="exampleModal_ercdoc_view" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel">Comments</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="max-height:200px; overflow-y:auto;">
                <input type="hidden" name="view_stc_comment_id" class="view_stc_comment_id" id="view_stc_comment_id">
                <span class="comment_username" style="font-size:14px">User Name</span>
                <p class="mt-2">consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna
                    aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
                    commodo consequat.</p>
                <span class="comment_date" style="float: right; font-size:12px">Comment Date:- 05-12-2023</span>
                <br>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-warning">View Log</button>
            </div>
        </div>
    </div>
</div>

<?php //require plugin_dir_path(__FILE__) . 'iris-send-erc-agreement.php'; ?>

<style type="text/css">
    .modal-header .close-popup-appr-reject {
      padding: 0px !important;
      margin: 0px !important;
      background-color:transparent;
      border: 0;
      font-size: 20px !important;
      border-radius: 10px !important;
      font-weight: 700 !important;
      height: 32px !important;
      opacity: 1 !important;
      line-height: 30px !important;
      float: right;
      color: #000;
      text-shadow: 0 1px 0 #fff;
    } */
    #exampleModal_otherdoc_adding_comment .modal-title, #exampleModal_ercdoc_view .modal-title {
        margin-top: 5px;
        font-weight: 700;
        color: #1261ab;
        font-size: 17px;
    }
    #exampleModal_ercdoc_view .modal-header .close, #exampleModal_otherdoc_adding_comment .modal-header .close {
        position: absolute;
        right: -15px;
        top: -15px;
        background: red;
        text-align: center;
        width: 28px;
        height: 28px !important;
        cursor: pointer;
        border-radius: 50% !important;
        line-height: 10px !important;
        color: #fff;
        opacity: 1 !important;
        padding: 0 !important;
        margin: 0 !important;
        text-shadow: none !important;
    }
    #exampleModal_otherdoc_adding_comment .modal-header .close span, #exampleModal_ercdoc_view .modal-header .close span {
        display: block;
        margin-top: -4px;
    }
</style>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"
        integrity="sha384-9/reFTGAW83EW2RDu2S0VKaIzap3H66lZH81PoYlFhbGU+6BZp6G7niu735Sk7lN"
        crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.3/dist/js/bootstrap.min.js"
        integrity="sha384-w1Q4orYjBQndcko6MimVbzY0tgp4pWB4lZ7lr30WKz0vr/aWKhXdBNmNb5D92v7s"
        crossorigin="anonymous"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-daterangepicker/3.0.5/daterangepicker.min.js"></script>

<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.8.1/css/bootstrap-select.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.8.1/js/bootstrap-select.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js" ></script> -->

<script>
    jQuery(document).ready(function () {
        $(".opp-add-notes").click(function() {
            $('.note-response').html("");
            $('.note-response').css('display', 'none');
            $('.error-response').css('display', 'none');
            $("#add-new-opp-notes").modal('show');
 
        });
        $('.close-popup-notes').click(function() {
            $('.note-response').css('display', 'none');
            $('.error-response').css('display', 'none');
            $("#add-new-opp-notes").modal('hide');
            $("#edit-new-opp-notes").modal('hide');
        });
    });
</script>
<script type="text/javascript">
    $(document).ready(function () {
        var lead_id = "<?php echo $lead_id; ?>";
        jQuery(".user-select").on('select2:select', function (e) {
            jQuery("#assign-user-1").show();
        });
        jQuery(".user-select").select2({
            placeholder: "Select Collaborator to Assign",
            allowClear: true
        });
        jQuery(document).on('click', '.unassign-user', function () {
            jQuery(".show_message_collaborator").hide();
            unassign_user_id = jQuery(this).attr("unassign-user");
            unassign_user_name = jQuery(this).attr("unassign-user-name");
            var project_id = "<?php echo $project_id; ?>";
            var data = {
                'action': 'assign_collaborators',
                'project_id': project_id,
                'user_id': unassign_user_id,
                'type': 'unassign-user'
            };
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'post',
                data: data,
                success(response) {
                    console.log(response);
                    var name = jQuery('#userDetails').attr('data-name');
                    var noteMessage = name + ' added a comment: ' + name + ' unassign collaborator to ' + unassign_user_name;
                    saveProject(noteMessage);
                    jQuery("#assign-users" + unassign_user_id).css("display", "none");
                }
            }).done(function () {
                console.log("completed");
            });
        });
        jQuery(document).on('click', '.assign-user-btn', function () {
            jQuery(".show_message_collaborator").hide();

            var selected_user = jQuery(".user-select :selected").val();
            if (selected_user != '' && selected_user > 0) {
                jQuery('.assign-user-btn').html("Please wait ..");
                var project_id = "<?php echo $project_id; ?>";
                var assignedUsers = [];
                jQuery(".all_assigned_users p").each(function () {
                    assignedUsers.push(jQuery(this).attr("id"))
                });
                if (jQuery.inArray(selected_user, assignedUsers) == -1) {
                    var data = {
                        'action': 'assign_collaborators',
                        'user_id': selected_user,
                        'project_id': project_id,
                        'type': 'assign-user'
                    };
                    jQuery.ajax({
                        url: '<?php echo admin_url("admin-ajax.php"); ?>',
                        method: 'post',
                        data: data,
                        success(response) {
                            var selected_user_name = jQuery(".user-select :selected").text();
                            /*update note start*/
                            var name = jQuery('#userDetails').attr('data-name');
                            var noteMessage = name + ' added a comment: ' + name + ' assign collaborator to ' + selected_user_name;
                            saveProject(noteMessage);
                            /*update note end*/

                            var selected_user_id = jQuery(".user-select :selected").val();
                            var Dynamic_user = '<p class="col-form-label" id="assign-users' + selected_user_id + '">' + selected_user_name + ' <span unassign-user="' + selected_user_id + '" class="unassign-user glyphicon glyphicon-minus-sign"></span></p>';
                            jQuery(".all_assigned_users").prepend(Dynamic_user);
                            jQuery(".user-select").val('').change();
                            jQuery('.assign-user-btn').html("Assign User");
                        }
                    }).done(function () {
                        //jQuery(".assign-user-btn").css("display", "none");
                        console.log("completed");
                    });

                } else {
                    console.log("user already assigned");
                }
            } else {
                jQuery(".show_message_collaborator").html('Please select collaborator');
            }
        })
        jQuery(document).on('click', '.update_project', function () {
            var valid = checkValidation();
            if(valid){
            var form = '#lead_update_form';
            var form_data = jQuery(form).serialize();
            var form_data_val = form_data;
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'post',
                data: form_data_val,
                success(response) {
                    var res = JSON.parse(response);
                    jQuery(".update_project").html('Save');
                    jQuery(".update_project").attr('disabled', false);
                    if (res.status == 200) {
                        alert("Project saved successfully");
                    } else {
                        alert(res.message);
                    }
                }
            });
         }else{
            return false;
        }  
        })
        jQuery("#pills-intake").click(function () {
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                data: {
                    action: "erc_project_intake_data",
                    lead_id: lead_id
                },
                success: function (response) {
                    jQuery("#pills-intke .intake_data").html(response);
                },
                error: function () {
                    console.log("Error in getting intake data");
                }
            });
        })
        jQuery("#pills-fees").click(function () {
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                data: {
                    action: "lead_detail_fees_data",
                    lead_id: lead_id
                },
                success: function (response) {
                    jQuery("#pills-fee .fee_data").html(response);
                },
                error: function () {
                    console.log("Error in getting fee data");
                }
            });
        })
        jQuery(document).on('change', '.select-milestone', function () {
            var no = jQuery(this).data('no');
            var val = jQuery(this).val();
            var product_name = $(this).find(":selected").html();
            console.log(product_name);
            var url = "<?php echo site_url() . '/wp-json/productsplugin/v1/milestone-status'; ?>";
            jQuery.ajax({
                type: "POST",
                url: url,
                data: {
                    id: val
                },
                beforeSend: function () {
                    $('#MilestoneStage').html('<option value="">Loading Stages...</option>');
                    $('.show_message_milestone').html('');
                },
                success: function (response) {
                    jQuery('#MilestoneStage').html('');

                    if (response.length != 0) {
                        jQuery.each(response, function (indexes, values) {
                            var optionHTML = `<option value="${values.milestone_stage_id}"> ${values.stage_name} </option>`;
                            jQuery('#MilestoneStage').append(optionHTML);
                        });
                    } else {

                        jQuery('#MilestoneStage').html(`<option value="">Select Stage</option>`);
                    }
                }
            });
        });
        $(document).on('click', '.update_project', function () {
            var valid = checkValidation();
            if(valid){
            $('.show_message_popup').html('');
            $(this).text('Please wait..');
            $(this).attr('disabled', true);
            let lead_id = "<?php echo $lead_id; ?>";
            let projectID = $(this).attr('data-projectid');
            let project_name = $('#project_name').val();
            let project_fee = $('#project_fee').val();
            let maximum_credit = $('#maximum_credit').val();
            let estimated_fee = $('#estimated_fee').val();
            let self_time_off_days_april_2020 = $("#self_time_off_days_april_2020").val();
            let self_time_off_days_january_2021 = $("#self_time_off_days_january_2021").val();
            let self_time_off_days_april_2021 = $("#self_time_off_days_april_2021").val();
            let other_time_off_days_april_2020 = $("#other_time_off_days_april_2020").val();
            let other_time_off_days_january_2021 = $("#other_time_off_days_january_2021").val();
            let other_time_off_days_april_2021 = $("#other_time_off_days_april_2021").val();
            $('.show_message_popup').html('<p class="alert_message">Please Wait</p>');
            jQuery.ajax({
                url: '<?php echo get_site_url(); ?>/wp-json/productsplugin/v1/edit-project-optional-field',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    project_name: project_name,
                    project_fee: project_fee,
                    maximum_credit: maximum_credit,
                    estimated_fee: estimated_fee,
                    self_time_off_days_april_2020: self_time_off_days_april_2020,
                    self_time_off_days_january_2021: self_time_off_days_january_2021,
                    self_time_off_days_april_2021: self_time_off_days_april_2021,
                    other_time_off_days_april_2020: other_time_off_days_april_2020,
                    other_time_off_days_january_2021: other_time_off_days_january_2021,
                    other_time_off_days_april_2021: other_time_off_days_april_2021
                },
                success(res) {
                    console.log(res);
                    $(".update_project").text('Update');
                    $(".update_project").attr('disabled', false);
                    if (res.status == true) {
                        $('.show_message_popup').html('<p class="success_message">Project successfully updated</p>');
                        setTimeout(function () {
                            $('.show_message_popup').html('');

                        }, 3000)
                    } else {
                        $('.show_message_popup').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    $(".update_project").text('Update');
                    $(".update_project").attr('disabled', false);
                    $('.show_message_popup').html('<p class="warning_message">Something went worng.</p>');
                }
            });
          }else{
            return false;
          }
        });
        $(document).on('click', '.update_milestone', function () {
            let lead_id = "<?php echo $lead_id; ?>";
            let projectID = $(this).attr('data-projectID');
            let oldmilestoneid = $(this).attr('data-milestoneid');
            let oldmilestonestageid = $(this).attr('data-milestonestageid');
            let project_name = $('#project_name').val();
            let project_fee = $('#project_fee').val();
            let maximum_credit = $('#maximum_credit').val();
            let estimated_fee = $('#estimated_fee').val();
            let self_time_off_days_april_2020 = $("#self_time_off_days_april_2020").val();
            let self_time_off_days_january_2021 = $("#self_time_off_days_january_2021").val();
            let self_time_off_days_april_2021 = $("#self_time_off_days_april_2021").val();
            let other_time_off_days_april_2020 = $("#other_time_off_days_april_2020").val();
            let other_time_off_days_january_2021 = $("#other_time_off_days_january_2021").val();
            let other_time_off_days_april_2021 = $("#other_time_off_days_april_2021").val();
            let Milestone = $('#Milestone').val();
            let MilestoneStage = $('#MilestoneStage').val();
            if (Milestone == '') {
                $('.show_message_milestone').html('<p class="warning_message">Please select the milestone.</p>');
                return;
            }
            if (MilestoneStage == '') {
                $('.show_message_milestone').html('<p class="warning_message">Please select the stage.</p>');
                return;
            }

            var milestone_stage_name = jQuery("#MilestoneStage :selected").text();
            var user_id = "<?php echo get_current_user_id(); ?>";
            
            $('.show_message_milestone').html('<p class="alert_message">Please Wait</p>');

            jQuery.ajax({
                url: '<?php echo get_site_url(); ?>/wp-json/productsplugin/v1/edit-project-optional-field',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    project_name: project_name,
                    project_fee: project_fee,
                    maximum_credit: maximum_credit,
                    estimated_fee: estimated_fee,
                    self_time_off_days_april_2020: self_time_off_days_april_2020,
                    self_time_off_days_january_2021: self_time_off_days_january_2021,
                    self_time_off_days_april_2021: self_time_off_days_april_2021,
                    other_time_off_days_april_2020: other_time_off_days_april_2020,
                    other_time_off_days_january_2021: other_time_off_days_january_2021,
                    other_time_off_days_april_2021: other_time_off_days_april_2021,
                    Milestone: Milestone,
                    MilestoneStage: MilestoneStage,
                    milestone_stage_name:milestone_stage_name,
                    user_id:user_id,
                },
                success(res) {
                    console.log(res);
                    if (res.status == true) {
                        $('.show_message_milestone').html('<p class="success_message">Successfully updated</p>');
                        $('#showMilestone').html(res.data.milestoneName);
                        $('#showMilestoneStage').html(res.data.milestoneStatus);
                        var name = jQuery('#userDetails').attr('data-name');

                        if (Milestone != oldmilestoneid && MilestoneStage != oldmilestonestageid) {
                            var noteMessage = name + ' added a comment: ' + name + ' changed milestone to ' + res.data.milestoneName + ' and changed stage to ' + res.data.milestoneStatus;
                            $(".update_milestone").attr('data-milestoneid', Milestone);
                            $(".update_milestone").attr('data-milestonestageid', MilestoneStage);
                            saveProject(noteMessage);
                        }
                        else if (Milestone != oldmilestoneid) {
                            var noteMessage = name + ' added a comment: ' + name + ' changed milestone to ' + res.data.milestoneName;
                            $(".update_milestone").attr('data-milestoneid', Milestone);
                            saveProject(noteMessage);
                        }
                        else if (MilestoneStage != oldmilestonestageid) {
                            var noteMessage = name + ' added a comment: ' + name + ' changed stage to ' + res.data.milestoneStatus;
                            $(".update_milestone").attr('data-milestonestageid', MilestoneStage);
                            saveProject(noteMessage);
                        }
                        $('.show_message_milestone').html('');
                        $('.show_message_popup').html('');
                        $('.milestone_button_container').hide();
                        $('#Milestone').hide();
                        $('#MilestoneStage').hide();
                        $('#showMilestone').show();
                        $('#showMilestoneStage').show();
                    } else {
                        $('.show_message_milestone').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    $('.show_message_milestone').html('<p class="warning_message">Something went worng.</p>');
                }
            });
        });
        
        $(document).on('click', '.update_contacts', function () {
            let lead_id = "<?php echo $lead_id; ?>";
            let projectID = $(this).attr('data-projectID');
            let old_contact_id = $(this).attr('data-contactid');
            let project_name = $('#project_name').val();
            let project_fee = $('#project_fee').val();
            let maximum_credit = $('#maximum_credit').val();
            let estimated_fee = $('#estimated_fee').val();
            let self_time_off_days_april_2020 = $("#self_time_off_days_april_2020").val();
            let self_time_off_days_january_2021 = $("#self_time_off_days_january_2021").val();
            let self_time_off_days_april_2021 = $("#self_time_off_days_april_2021").val();
            let other_time_off_days_april_2020 = $("#other_time_off_days_april_2020").val();
            let other_time_off_days_january_2021 = $("#other_time_off_days_january_2021").val();
            let other_time_off_days_april_2021 = $("#other_time_off_days_april_2021").val();
            let ContactList = $('#ContactList').val();
            $('.show_message_contact').html('<p class="alert_message">Please Wait</p>');
            jQuery.ajax({
                url: '<?php echo get_site_url(); ?>/wp-json/productsplugin/v1/edit-project-optional-field',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    project_name: project_name,
                    project_fee: project_fee,
                    maximum_credit: maximum_credit,
                    estimated_fee: estimated_fee,
                    self_time_off_days_april_2020: self_time_off_days_april_2020,
                    self_time_off_days_january_2021: self_time_off_days_january_2021,
                    self_time_off_days_april_2021: self_time_off_days_april_2021,
                    other_time_off_days_april_2020: other_time_off_days_april_2020,
                    other_time_off_days_january_2021: other_time_off_days_january_2021,
                    other_time_off_days_april_2021: other_time_off_days_april_2021,
                    ContactList: ContactList
                },
                success(res) {
                    console.log(res);
                    if (res.status == true) {
                        $('.show_message_contact').html('<p class="success_message">Successfully updated</p>');
                        $('#showContactList').html(res.data.ContactName);
                        var name = jQuery('#userDetails').attr('data-name');
                        var noteMessage = name + ' added a comment: ' + name + ' changed contact to ' + res.data.ContactName;
                        if (old_contact_id != ContactList) {
                            $(".update_contacts").attr('data-contactid', ContactList);
                            saveProject(noteMessage);
                        }
                        $('.show_message_milestone').html('');
                        $('.show_message_popup').html('');
                        $('.show_message_contact').html('');
                        $('.contact_button_container').hide();
                        $('#ContactList').hide();
                        $('#showContactList').show();
                    } else {
                        $('.show_message_contact').html('<p class="warning_message">Something went worng ' + res.message + '</p>');
                    }
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    $('.show_message_contact').html('<p class="warning_message">Something went worng.</p>');
                }
            });
        });
        $(document).on('click', '.edit_project_collaborator', function () {
            var show_collaborators = $("#show_collaborators").val();
            $('.show_message_popup').html('');
            $('.show_message_collaborator').html('');
            $('.collaborator_button_container').toggle();
            if(show_collaborators == 0){
                $("#show_collaborators").val(1);
                $('#assign_collaborator').css('display','inline-block');
                $(".assign_collaborators_section .select2-container").css('display', 'inline-block');
            }else{
                $("#show_collaborators").val(0);
                $('#assign_collaborator').css('display','none');
                $(".assign_collaborators_section .select2-container").css('display', 'none');
            }
            
            //$(".assign_collaborators_section .select2-container").css('display', 'inline-block');
            //$('#showContactList').toggle();
        });
        $(document).on('click', '.cancel_collaborator', function () {
            $('.show_message_popup').html('');
            $('.show_message_collaborator').html('');
            $('.collaborator_button_container').hide();
            $('#assign_collaborator').hide();
            $('.assign_collaborators_section .select2-container').hide();
        });
        $(document).on('click', '.cancel_project', function () {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.button_container').hide();
        });
        $(document).on('click', '.edit_project_milestone', function () {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.milestone_button_container').toggle();
            $('#Milestone').toggle();
            $('#MilestoneStage').toggle();
            $('#showMilestone').toggle();
            $('#showMilestoneStage').toggle();
        });
        $(document).on('click', '.cancel_milestone', function () {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.milestone_button_container').hide();
            $('#Milestone').hide();
            $('#MilestoneStage').hide();
            $('#showMilestone').show();
            $('#showMilestoneStage').show();
        });
        
        $(document).on('click', '.edit_project_contact', function () {
            $('.show_message_popup').html('');
            $('.show_message_contact').html('');
            $('.contact_button_container').toggle();
            $('#ContactList').toggle();
            $('#showContactList').toggle();
        });
        $(document).on('click', '.cancel_contact', function () {
            $('.show_message_popup').html('');
            $('.show_message_contact').html('');
            $('.contact_button_container').hide();
            $('#ContactList').hide();
            $('#showContactList').show();
        });
        // ---------- load notes jquery functionality --
        jQuery(document).on('click', '#load-more-notes', function () {
            jQuery(this).html('Loading..');
            jQuery(this).attr('disabled', true);
            var offset = jQuery('#note-offset').val();
            var new_offset = parseInt(offset) + parseInt(10);
            var total_notes_c = <?php echo $total_notes_count; ?>;
            var project_id = <?php echo $project_id; ?>;
            jQuery('#note-offset').val(new_offset);
            jQuery.ajax({
                url: '<?php echo get_site_url(); ?>/wp-json/productsplugin/v1/fetch_project_notes',
                method: 'post',
                data: {
                    offset: offset,
                    project_id: project_id
                },
                success(response) {
                    if (new_offset >= total_notes_c) {
                        jQuery('#load-more-notes').css('display', 'none');
                    }
                    jQuery('.notes-listing').append(response);
                    jQuery('#load-more-notes').html('M');
                    jQuery('#load-more-notes').attr('disabled', false);
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    console.log('Something went worng.');
                }

            });
        });
        //close pop up
        $(document).on('click', '.close_statuspopup', function () {
            jQuery('#notes_box').hide();
        });
        jQuery(document).on('keyup', '#notes-input', function () {
            $('.error-response').css('display', 'none');
            $('.note-response').css('display', 'none');
        });
        //create project note
        //create project note
        jQuery(document).on('click', '#create-note', function() {
            var noteInput = jQuery('#notes-input').val();
            var notes = jQuery.trim(noteInput);
            if (noteInput == '' || notes.length == 0) {
                $('.error-response').css('display', 'block');
                $('.note-response').css('display', 'none');
                return false;
            } else {
                var confidence_notes_access = jQuery("#confidence_user_check:checked").val();
                if(typeof confidence_notes_access === "undefined"){
                    confidence_notes_access = 0;
                    var note_label = 'comment';
                }else{
                    confidence_notes_access = 1;
                    var note_label = 'CONFIDENTIAL note';
                }
                jQuery(this).html('Creating..');
                jQuery(this).prop('disabled', true);
                var project_id = <?php echo $project_id; ?>;
 
                var name = jQuery('#userDetails').attr('data-name');
                var note = name+' added a '+ note_label +': '+noteInput;
                var user_id = <?php echo get_current_user_id();  ?>;
                jQuery.ajax({
                    url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/add_edit_notes',
                    method: 'post',
                    dataType: 'Json',
                    data: {note:note,opp_product_id:project_id,user_id: user_id,confidence_notes_access:confidence_notes_access,note_type:'project'},
                    success(response) {
                       var res = JSON.parse(response);
                        console.log(res);
                        var note_id = res.note_id;
<?php
                        $from='UTC';
                        $to='America/New_York';
                        $format='Y-m-d h:i:s A';
                        $date = date("Y-m-d H:i:s");
                        date_default_timezone_set($from);
                        $newDatetime = strtotime($date);
                        date_default_timezone_set($to);
                        $newDatetime = date($format, $newDatetime);
                        date_default_timezone_set('UTC');
                        $datetime = date_create($newDatetime);
                        $time = date_format($datetime, "h:ia");
                        $day = date_format($datetime, " D ");
                        $month = date_format($datetime, " M ");
                        $date = date_format($datetime, "dS,");
                        $year = date_format($datetime, " Y");
                        $actual_date = $time . " on " . $day . $month . $date . $year;
                        ?>
                        var curr_date = "<?php echo $actual_date; ?>";
 
                        if(confidence_notes_access){
                          var conf_class='confidential-notes-div';
                        }else{
                          var conf_class='';  
                        }        
                        var confidence_user = "<?php echo $confidence_user; ?>";
                        if(confidence_user == 1 && confidence_notes_access==1){ 
                            var not_edit_btn = '<a href="javascript:void(0)" title="Edit Notes" class="edit_self_notes" data-note_id="'+note_id+'"><i class="fa-regular fa-pen-to-square"></i></a><a href="javascript:void(0)" title="Delete Notes" class="delete_self_notes" data-note_id="'+note_id+'"><i class="fa fa-trash" aria-hidden="true"></i></a>';
                        }else{
                            var not_edit_btn = '';
                        }
 
                        var not_div = '<div class="note-listing-div shadow '+conf_class+'" id="note-list-'+note_id+'"><p class="notes" id="note-'+note_id+'" data-confidece="'+confidence_notes_access+'">'+note+'</p><p class="date-time">('+curr_date+')</p>'+not_edit_btn+'</div>';
 
                        jQuery('.notes-listing').prepend(not_div);
 
                       /*
                        jQuery('.notes-listing').prepend(response);
                        var offset = jQuery('#note-offset').val();
                        var new_offset = parseInt(offset) + parseInt(1);
                        jQuery('#note-offset').val(new_offset);
<?php
                        $date = date("Y-m-d H:i:s");
                        $datetime = date_create($date);
                        $time = date_format($datetime, "h:ia");
                        $day = date_format($datetime, " D ");
                        $month = date_format($datetime, " M ");
                        $date = date_format($datetime, "dS,");
                        $year = date_format($datetime, " Y ");
                        $actual_date = $time . " on " . $day . $month . $date . $year;
                        ?>
                        var curr_date = "<?php echo $actual_date; ?>";
                        var not_div = '<div class="note-listing-div"><p class="notes">' + note + '</p><p class="date-time">' + curr_date + '</p></div>';
                        */
 
                        jQuery('#notes-input').val('');
                        jQuery("#confidence_user_check").prop('checked',false);
                        jQuery('.note-response').css('display', 'block');
                        jQuery('.note-response').html("Notes created successfully.");
                        jQuery('#create-note').html('Submit').attr('disabled', false);
                        jQuery('.error-response').css('display', 'none');
                        setTimeout(function() {
                            jQuery('#add-new-opp-notes').modal('hide')
                        }, 2000);
                    }
                });
            }
        });
		
    });
    function saveProject(noteMessage) {
        var project_id = <?php echo $project_id; ?>;
        var id = jQuery('#userDetails').attr('data-id');
        var name = jQuery('#userDetails').attr('data-name');
        var note = noteMessage;
        var user_id = <?php echo get_current_user_id(); ?>;
        jQuery.ajax({
            url: '<?php echo get_site_url(); ?>/wp-json/productsplugin/v1/create_project_notes',
            method: 'post',
            data: {
                note: note,
                project_id: project_id,
                user_id: user_id
            },
            success(response) {
                jQuery('.notes-listing').prepend(response);
                var offset = jQuery('#note-offset').val();
                var new_offset = parseInt(offset) + parseInt(1);
                jQuery('#note-offset').val(new_offset);
            }
        });
    }
    jQuery(document).on('change', '#change_ta_document_status', function () {
            var input = $(this);
            var value = $(this).val();
            var input = $(this);
            var doc_id = $(this).find("option:selected").data('doc_id');
            var doc_type_id = $(this).find("option:selected").data('doc_type_id');
            var label = $(this).closest('tr').find('label.questions_Q').text().trim();
            var reason = '';
            if (value == 'Approved') {

                var text = "Are you sure you want to approve this document? You will not be able to reject it later!";
                var icon = "success";

            } else if (value == 'Rejected') {

                var text = "Are you sure you want to reject this document?";
                var icon = "error";

            } else if (value == 'In review') {
                var text = "Are you sure you want to change the status of this document to In review again?";
                var icon = "warning";
            } else if (value == '' || value == 'Select Status') {
                swal("Error", "Please select a status", "error");
                return false;
            }
            if (value == 'In review') {
                //pop up
                swal({
                    title: "",
                    text: text,
                    icon: icon,
                    buttons: true,
                    dangerMode: true,
                })
                    .then((willDelete) => {
                        
                        if (willDelete) {

                            $.ajax({
                                url: doc_upload.ajaxurl,
                                type: 'POST',
                                data: {
                                    action: 'update_stc_document_status',
                                    value: value,
                                    doc_id: doc_id,
                                    doc_type_id: doc_type_id,
                                    label: label,
                                    reason: reason
                                },
                                success: function (response) {
                                    swal("Success", "Document status updated successfully", "success").then(function () {
                                        location.reload();
                                    });
                                }
                            });

                        } else {

                            swal("Document status not changed!").then(function () {
                                location.reload();
                            });
                        }
                    });
            }

            if (value == 'Rejected' || value == 'Approved') {
                $.ajax({
                    url: doc_upload.ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'check_rejected_reason',
                        doc_id: doc_id,
                        doc_type_id: doc_type_id,
                        value: value,
                    },
                    success: function (response) {

                        var data = JSON.parse(response);
                        var uploadok = data.uploadok;

                        if (uploadok == 'false') {

                            $(input).closest('tr').find('span.comment_btn').trigger('click');

                            if (value == 'Rejected') {

                                var modal_title = 'Please add a comment before rejecting the document.';
                                var modal_btn = 'Reject';
                            }
                            else if (value == 'Approved') {

                                var modal_title = 'Please add a comment before approving the document.';
                                var modal_btn = 'Approve';
                            }

                            $('#exampleModal_otherdoc_adding_comment .modal-header h5').html(modal_title);
                            $("#exampleModal_otherdoc_adding_comment #doc_id_comment").val(doc_id);
                            $("#exampleModal_otherdoc_adding_comment #doc_type_id_comment").val(doc_type_id);
                            $('#exampleModal_otherdoc_adding_comment .modal-footer').html('<input type="hidden" class="comment_status" value="' + value + '"></input> <button type="button" class="btn btn-secondary ts-close-modal" data-dismiss="modal">Close</button><button type="button" class="btn btn-primary add_approved_project_rejected_comment_btn">' + modal_btn + '</button>');
                            $("#exampleModal_otherdoc_adding_comment").addClass('in');
                            $("#exampleModal_otherdoc_adding_comment").modal('show');
                            $('.ts-close-modal').on('click', function () {
                                location.reload();
                            });
                            return true;

                        } else {
                            swal({
                                title: "",
                                text: text,
                                icon: icon,
                                buttons: true,
                                dangerMode: true,
                            })
                                .then((willDelete) => {
                                    if (willDelete) {

                                        $.ajax({
                                            url: doc_upload.ajaxurl,
                                            type: 'POST',
                                            data: {
                                                action: 'update_stc_document_status',
                                                value: value,
                                                doc_id: doc_id,
                                                doc_type_id: doc_type_id,
                                                label: label,
                                                reason: reason
                                            },
                                            success: function (response) {
                                                swal("Success", "Document status updated successfully", "success").then(function () {
                                                    location.reload();
                                                });
                                            }
                                        });

                                    } else {

                                        swal("Document status not changed!").then(function () {
                                            location.reload();
                                        });
                                    }
                                });
                            reason = data.reason;
                        }

                    }
                });
            }
            $('#exampleModal_otherdoc_adding_comment .close').on('click', function () {
                location.reload();
            });
        });
        $(document).on('click', '.add_approved_project_rejected_comment_btn', function () {
            var comment = jQuery('#other-comment-textarea').val();
            var doc_id = jQuery('#doc_id_comment').val();
            var doc_type_id = jQuery('#doc_type_id_comment').val();
            var comment_status = jQuery('.comment_status').val();
            if (comment == '' || comment == ' ' || comment == null) {
                jQuery("#other-comment-textarea").css("border", "1px solid red");
                jQuery("#other-comment-textarea").focus();
                jQuery("#other-comment-textarea").attr("placeholder", "Please enter comment");
                jQuery("#other-comment-textarea").next().remove();
                jQuery("#other-comment-textarea").after('<span class="error">Please enter comment</span>');
                return false;
            }
            $(this).text('Please wait..');
            $(this).attr('disabled',true);
            $.ajax({
                url: doc_upload.ajaxurl,
                type: 'POST',
                data: {
                    action: 'approved_rejected_comment_data',
                    comment: comment,
                    doc_id: doc_id,
                    doc_type_id: doc_type_id,
                    comment_status: comment_status
                },
                success: function (response) {
                    var data = JSON.parse(response);
                    var uploadok = data.uploadok;
                    var reason = data.reason;
                    var label = data.label;
                    if (uploadok == 'true') {
                        $.ajax({
                            url: doc_upload.ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'update_stc_document_status',
                                value: comment_status,
                                doc_id: doc_id,
                                doc_type_id: doc_type_id,
                                label: label,
                                reason: reason
                            },
                            success: function (response) {
                                swal("Success", "Document status updated successfully", "success").then(function () {
                                    location.reload();
                                });
                            }
                        });
                    };
                }
            });
        });
    jQuery(document).on('click', '.view_ta_comment', function () {
        var doc_type_id = jQuery(this).data('doc_type_id');
        var doc_id = jQuery(this).data('doc_id');
        //var doc_id = jQuery(this).closest('td').find('.add_ta_comment_btn').data('doc_id');
        jQuery('#exampleModal_ercdoc_view .modal-body').html('<div style="text-align-center;"></div><i aria-hidden="true" class="fa fa-spinner fa-spin"></i></div>');
        $.ajax({
            url: doc_upload.ajaxurl,
            type: 'POST',
            data: {
                action: 'view_stc_comments',
                doc_type_id: doc_type_id,
                doc_id: doc_id
            },
            success: function (response) {
                var json_response = JSON.parse(response);
                jQuery('#exampleModal_ercdoc_view .modal-body').html('');
                var inc = 0;
                jQuery.each(json_response, function (index, value) {
                    if (index != 0) {
                        var prepare_html_op = "<div style='display:none;' class='iris_comment_view_all iris_comment_" + index + "'>" + value + "</div>";
                        jQuery('#exampleModal_ercdoc_view .modal-body').append(prepare_html_op);
                    }
                    else {
                        var prepare_html_op = "<div class='iris_comment_view_all iris_comment_" + index + "'>" + value + "</div>";
                        jQuery('#exampleModal_ercdoc_view .modal-body').append(prepare_html_op);
                    }
                    inc++;
                });
                if (inc == 1) {
                    jQuery('#exampleModal_ercdoc_view .modal-footer .btn').hide();
                }
                else {
                    jQuery('#exampleModal_ercdoc_view .modal-footer .btn').show();
                }
                jQuery("#exampleModal_ercdoc_view").addClass('in');
                jQuery("#exampleModal_ercdoc_view").modal('show');
            }
        });
        jQuery(document).on('click', '#exampleModal_ercdoc_view .modal-footer .btn', function () {
            jQuery('.iris_comment_view_all').show();
            jQuery('#exampleModal_ercdoc_view .modal-footer .btn').hide();
        });
    });
    $(document).on('click', '.add_ta_comment_btn', function () {
        $("#other-comment-textarea").val("");
        var doc_id = $(this).data('doc_id');
        var doc_type_id = $(this).data('doc_type_id');
        var label = $(this).closest('tr').find('label.questions_Q').text().trim();
        $("#exampleModal_otherdoc_adding_comment #doc_id_comment").val(doc_id);
        $("#exampleModal_otherdoc_adding_comment #doc_type_id_comment").val(doc_type_id);
        $("#exampleModal_otherdoc_adding_comment").attr('data-label', label);
        $("#exampleModal_otherdoc_adding_comment").addClass('in');
        $("#exampleModal_otherdoc_adding_comment").modal('show');
    });
    $(document).on('click', '#ta-submit-comment-btn', function () {
        var comment = $("#other-comment-textarea").val().trim();
        if (comment == '' || comment == ' ' || comment == null) {
            jQuery("#other-comment-textarea").css("border", "1px solid red");
            jQuery("#other-comment-textarea").focus();
            jQuery("#other-comment-textarea").attr("placeholder", "Please enter comment");
            jQuery("#other-comment-textarea").next().remove();
            jQuery("#other-comment-textarea").after('<span class="error">Please enter comment</span>');
            return false;
        }
        var doc_id = $("#doc_id_comment").val();
        var doc_type_id = $("#doc_type_id_comment").val();
        var label = $("#exampleModal_otherdoc_adding_comment").data('label');

        $.ajax({
            url: doc_upload.ajaxurl,
            type: 'POST',
            data: {
                action: 'project_doc_comment_data',
                comment: comment,
                doc_id: doc_id,
                doc_type_id: doc_type_id,
                label: label
            },
            success: function (response) {
                swal("Success", "Comment added successfully", "success");
                location.reload();
            }
        });
    });
    $('#exampleModal_ercdoc_view .close').on('click', function () {
        location.reload();
    });
    $('#exampleModal_otherdoc_adding_comment .close').on('click', function () {
        location.reload();
    });
</script>

<script type="text/javascript">
    jQuery(document).ready(function(){
        jQuery('#notes-input').keypress(function (event) {
            var Length = jQuery("#notes-input").val().length;
              maxLen = 1000;
            if (Length >= maxLen) {
                if (event.which != 8) {
                    //swal("Error", "Only 70 characters are allowed", "error");
                    return false;
                }
            }
        });
 
            const maxLength = 1000;
            $('#notes-input').on('input', function() {
                const remainingChars = maxLength - $(this).val().length;
                $('#remaining-msg').text(remainingChars + '/1000 characters remaining.');
            });
 
            
            jQuery('#edit-notes-input').keypress(function (event) {
            var Length = jQuery("#edit-notes-input").val().length;
              maxLen = 1000-Length;
            if (Length >= maxLen) {
                if (event.which != 8) {
                    //swal("Error", "Only 70 characters are allowed", "error");
                    return false;
                }
            }
        });
 
            $('#edit-notes-input').on('input', function() {
                const remainingChars = maxLength - $(this).val().length;
                $('#edit-remaining-msg').text(remainingChars + '/1000 characters remaining.');
            });
 
    });
 
    jQuery(document).on('click', '.delete_self_notes', function() {
                swal({
                    title: "Are you sure?",
                    text: "Do you want to delete this notes?",
                    icon: "warning",
                    buttons: {
                        cancel: "No",
                        confirm: {
                          text: "Yes",
                          value: true,
                          visible: true,
                          className: "",
                          closeModal: true
                        }
                      },
                      dangerMode: true,     
                }).then((isConfirm) => {
                    if(isConfirm){
                    var note_id = jQuery(this).data('note_id');
                    var ajax_url = '<?php echo admin_url('admin-ajax.php'); ?>';
                    jQuery.ajax({
                      url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/delete_notes',
                      method: 'post',
                      dataType: 'json',
                      data: {note_id:note_id,note_type:'project'},
                      success: function(response) {
                        jQuery('#note-list-'+note_id).hide();
                        swal({
                          title: 'Success!',
                          text: 'Notes deleted successfully!',
                          icon: 'success'
                        });
                    }
                    });
                    }
                });
    });            
     // on click edit notes
     jQuery(document).on('click', '.edit_self_notes', function() {
        jQuery('.note-response').hide();
        jQuery('.error-response').hide();
        jQuery('#update-note').html('Update');
         jQuery('#update-note').prop('disabled', false);    
 
        var note_id = jQuery(this).data('note_id');
        var notes = jQuery('#note-'+note_id).html();
        var all_notes = notes.split(':');
        var new_notes = '';
        var note_prefix = '';
 
        if (typeof all_notes[0] !== 'undefined') {
            note_prefix = all_notes[0];
        }
 
        if (typeof all_notes[1] !== 'undefined') {
                new_notes = new_notes + all_notes[1];
        }
 
        if(typeof all_notes[2] !== 'undefined'){
            new_notes = new_notes + all_notes[2];
        }    
 
        if(new_notes == ''){
            new_notes = notes;
        }    
        notes = new_notes;
        notes = jQuery.trim( notes );
 
        var remainingChars = 1000 - notes.length;
        $('#edit-remaining-msg').text(remainingChars + '/1000 characters remaining.');

        var data_confidece = jQuery('#note-'+note_id).attr('data-confidece');
        console.log(data_confidece);
        jQuery('#update-note').data('note_id',note_id); 
        jQuery("#edit-notes-input").val(notes);
        jQuery("#edit-notes-input").data('note_prefix',note_prefix);
        if(data_confidece==1){
            jQuery("#edit_confidence_user_check").prop('checked',true);
        }else{
            jQuery("#edit_confidence_user_check").prop('checked',false);
        }    
        jQuery("#edit-new-opp-notes").modal('show'); 
     });   
 
     // on submit update notes
     jQuery(document).on('click', '#update-note', function() {
        var note_id = jQuery(this).data('note_id');
        var project_id = <?php echo $project_id; ?>; 
        var note = jQuery('#edit-notes-input').val();
 
        var notes = jQuery.trim(note);
        if (note == '' || notes.length == 0){
            $('.error-response').css('display', 'block');
            $('.note-response').css('display', 'none');
            return false;
    }else{
        var note_prefix = jQuery('#edit-notes-input').data('note_prefix');
 
        var user_id = <?php echo get_current_user_id();  ?>;    
        var confidence_notes_access = jQuery("#edit_confidence_user_check:checked").val();
        if(typeof confidence_notes_access === "undefined"){
            confidence_notes_access = 0;
            var note_type = 'comment';
            note_prefix = note_prefix.replace("added a CONFIDENTIAL note", "added a comment");
        }else{
            confidence_notes_access = 1;
            var note_type = 'CONFIDENTIAL note';
            note_prefix = note_prefix.replace("added a comment","added a CONFIDENTIAL note");
        }
 
 
        jQuery(this).html('Updating..');
         jQuery(this).prop('disabled', true);
         note = note_prefix +" : "+ note;
         // var name = jQuery('#userDetails').attr('data-name');
         // var note = name+' added a '+note_type+' : '+note;
        // var data = {action:'edit_self_notes',note_id:note_id,note_val:note,user_id:user_id,confidence_notes_access:confidence_notes_access};
        var ajax_url = '<?php echo admin_url('admin-ajax.php'); ?>';
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/add_edit_notes',
                method: 'post',
                dataType: 'Json',
                data: {note:note,opp_product_id:project_id,user_id:user_id,confidence_notes_access:confidence_notes_access,note_type:'project',note_id:note_id},
              success: function(response) {
                jQuery('#note-'+note_id).html(note);

 
                if(confidence_notes_access==0){
                    jQuery('#note-list-'+note_id).removeClass('confidential-notes-div');
                    jQuery('#note-'+note_id).attr('data-confidece','0');
                }else{
                    jQuery('#note-list-'+note_id).addClass('confidential-notes-div');
                    jQuery('#note-'+note_id).attr('data-confidece','1');
                }
                    swal({
                      title: 'Success!',
                      text: 'Notes updated successfully!',
                      icon: 'success'
                    });
                    $('.close-popup-notes').trigger('click');
                    jQuery(this).html('Update');
                    jQuery(this).prop('disabled', false);
              }
        });
       }
    });
 
</script>

<script type="text/javascript">
           $(document).on('click','.edit_opportunity_owner',function(){
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.show_message_owner').html('');
            $('.owner_button_container').toggle();
            $('#OwnerList').toggle();
            $('#showOwnerList').toggle();
            $('#owner-div-id').removeClass('owner-main-div');
            $('.owner_button_container').show();
        });

        $(document).on('click', '.cancel_owner', function() {
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.show_message_owner').html('');
            $('.owner_button_container').hide();
            $('#OwnerList').hide();
            $('#showOwnerList').show();
            $('#owner-div-id').addClass('owner-main-div');
            $('.owner_button_container').hide();
        });

        $(document).on('click','.update_owner',function(){
        var lead_id = "<?php echo $lead_id;?>";
        var projectID = $(this).attr('data-projectID');
        var OwnerList = $('#OwnerList').val();
        var ownerName = $("#OwnerList option:selected").text(); 

        var product_id = <?php echo $project->product_id;?>;

        $('.show_message_owner').html('<p class="alert_message">Please Wait</p>');
        jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-opportunity-project-owner',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    projectID: projectID,
                    OwnerList: OwnerList,
                    product_id:product_id,
                },
            success(res) {
                console.log(res);
                if(res.status==true){
                    $('.show_message_owner').html('<p class="success_message">Successfully updated</p>');

                    $('#showOwnerList').html(ownerName);
                    var name = jQuery('#userDetails').attr('data-name');
                    // var noteMessage = name+' added a comment: '+name+' changed owener to '+res.data.ownerName;
                    var noteMessage = name+' added a comment: Owner updated by '+name;
                    saveNotes(noteMessage);
                    $('.show_message_owner').html('');
                    $('#OwnerList').hide();
                    $('#showOwnerList').show();
                    $('#owner-div-id').addClass('owner-main-div');
                    $('.owner_button_container').hide();
                }else{
                    $('.show_message_owner').html('<p class="warning_message">Something went worng '+res.message+'</p>');
                }
            },

            error: function(jqXHR, textStatus, errorThrown) {        
                    $('.show_message_owner').html('<p class="warning_message">Something went worng.</p>');  
                }
            });
        });   

        function saveNotes(noteMessage){
        var project_id = <?php echo $project->project_id;?>;
        var id = jQuery('#userDetails').attr('data-id');
        var name = jQuery('#userDetails').attr('data-name');
        var note = noteMessage;
        var user_id = <?php echo get_current_user_id();  ?>;
        jQuery.ajax({
            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/create_project_notes',
            method: 'post',
            data: {note:note,project_id:project_id,user_id:user_id},
            success(response) {
                jQuery('.notes-listing').prepend(response);
                var offset = jQuery('#note-offset').val();
                var new_offset = parseInt(offset)+parseInt(1);
                jQuery('#note-offset').val(new_offset);
            }
        });
    }
</script>

<script>
            // Audit log script
            jQuery(document).on("click","#pills-audit-logs",function(){
            var project_id = "<?php echo $project_id; ?>";
            var product_id = "<?php echo $project->product_id; ?>";
            var lead_id = "<?php echo $lead_id; ?>";

            jQuery(".update_project").hide();
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'post',
                data: { 
                    action: "projects_audit_log", 
                    project_id: project_id,
                    product_id:product_id,
                    lead_id:lead_id,
                },
                success: function(response) {
                    jQuery("#pills-logs").html(response);
                    jQuery("#project_audit_log_table").DataTable({"ordering": false});
                    jQuery("#project_milestone_log_table").DataTable({"ordering": false});
                    jQuery("#project_invoice_log_table").DataTable({"ordering": false});
                    jQuery("#project_document_log_table").DataTable({"ordering": false});
                    jQuery("#lead_log_table").DataTable({"ordering": false});
                },
                error: function () {
                    console.log("Error in getting document data");
                }
            });
        })

       // on tab click audit log hide right section and comment
        jQuery(document).on('click', '.tab-btn', function(){
            var id = jQuery(this).attr('id');
            if(id =='pills-audit-logs'){
                jQuery("#right_section").hide();
                jQuery("#comm-log-activity-by-lead").hide();
                jQuery(".custom-opportunity-notes").hide();
                jQuery("#left_section").removeClass('col-md-9');
                jQuery("#left_section").addClass('col-md-12');
            }else{
                jQuery("#right_section").show();
                jQuery("#comm-log-activity-by-lead").show();
                jQuery(".custom-opportunity-notes").show();
                jQuery("#left_section").removeClass('col-md-12');
                jQuery("#left_section").addClass('col-md-9');
            }
        });

        jQuery(document).on("click","#eleve-project-tab",function(){
            jQuery(".update_project").show();
        });  

        $(document).on('change','#review_status',function(){
        var review_status_val = $(this).val();
        if(review_status_val=='yes'){
            $('.review_link').show();
        }else{
            $('.review_link').hide();
            $('#review_link').val('');
        }
    });

        $(document).on('input','#review_link',function(){
        var review_link_val = $(this).val();
        if(review_link_val.length!=0){
            var validurl = checkValidlink(review_link_val);   
            if(validurl){
                $('.review_link_error').text('');
            }else{
                $('.review_link_error').text('Please enter valid link.');
            }
        }else{
            $('.review_link_error').text('');
        }
    });

    function checkValidlink(website_url){
         var url_regax = /^(https?|s?ftp):\/\/(((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:)*@)?(((\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d\d|2[0-4]\d|25[0-5]))|((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.?)(:\d*)?)(\/((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)+(\/(([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)*)*)?)?(\?((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|[\uE000-\uF8FF]|\/|\?)*)?(#((([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(%[\da-f]{2})|[!\$&'\(\)\*\+,;=]|:|@)|\/|\?)*)?$/i;

           var return_val = url_regax.test(website_url);
            
            if(return_val==false){
                website_url = 'https://'+website_url;    
            }

            var return_val = url_regax.test(website_url);

            if(return_val==false){
                return false;
            }else{
                return true;
            }
    }

    function checkValidation(){
        var valid = 0;
        var review_link_val = $('#review_link').val();
        if(review_link_val.length!=0){
            var validurl = checkValidlink(review_link_val);   
            if(validurl){
                $('.review_link_error').text('');
                valid = 1;
            }else{
                $('.review_link_error').text('Please enter valid link.');
                valid = 0;
            }
        }else{
            $('.review_link_error').text('');
            valid = 1;
        }
        return valid;
    }  
    </script>