<?php
/**

 * Create a new table class that will extend the WP_List_Table

 */
class product_types extends WP_List_Table {
    private $userRole;
    private $limitPerpage;
    function __construct() {
        global $status, $page;
        $this->limitPerpage = 15;
        parent::__construct(array(
            'singular' => 'bulk-delete', //singular name of the listed records
            'plural' => 'bulk-deletes', //plural name of the listed records
            'ajax' => false
                //does this table support ajax?
        ));
    }

    public function column_default($item, $column_name) {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        switch ($column_name) {
            case 'ProductTypeID':
                return $item->$column_name;
            case 'TypeName':
                return ucfirst($item->$column_name);
            case 'Description':
                return ucfirst($item->$column_name);
            case 'Action':
                return $item->$column_name;
            default:
                return print_r($item, true);
        }
    }
    function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" class="chkbox" name="deleteItem[]" value="'.$item->ProductTypeID.'"/>',

            $this->_args['singular'], //Let's simply repurpose the table's singular label ("plugin")
            /* $2%s */
            $item->ProductTypeID //The value of the checkbox should be the record's id
        );
    }
    function column_ProductTypeID($item){
        //Build row actions
        $actions = array(
            'edit'      => sprintf('<a href="?page=add_edit_product_type&action=edit&id='.$item->ProductTypeID.'">Edit</a>',$_REQUEST['page'],'edit',$item->ProductTypeID),
        );
        
        //Return the title contents
        return sprintf('%1$s %2$s',$item->ProductTypeID,$this->row_actions($actions));
    }

    function process_bulk_action() {
        if ('delete' === $this->current_action()) {
            $nonce = esc_attr($_REQUEST['_wpnonce']);
            if (isset($_REQUEST['action2']) && $_REQUEST['action2']=='delete'){
                
                if(!empty($_REQUEST['deleteItem'])){
                    $product_type_manager = new CRM_ERP_Product_Manager();
                    foreach($_REQUEST['deleteItem'] as $singleProductType){
                        $product_type_manager->delete_product_type($singleProductType);
                    }
                    wp_redirect('?page=product_types');
                    exit;
                }
                
            }
        } 
    }
    /**
     * Prepare the items for the table to process
     *
     * @return Void
     */
    public function prepare_items() {   
        $this->process_bulk_action(); 
        ?>
        <form method="get" action="" id="search_all_form">
            <?php

        $this->search_box_new($_GET); 
    ?>
        <div class="wrap woocommerce">
            <div class="white_card card_height_100 mb_30">
                <div class="white_card_header">
                    <div class="box_header m-0 new_report_header">
                        <div class="title_img">
                            <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/product-type-icon.png" class="page-title-img" alt="">
                            <h4>Product Types</h4>
                        </div>
                        <div class="invoice_exports">
                            <a href="admin.php?page=add_edit_product_type&action=add" class="add-opp-custom-icon"><i class="fa-solid fa-plus"></i> New Product Type</a>
                            <a href="javascript:void(0);" class="status_btn_export export_invoice_excel export_product_types">Export</a>
                        </div>
                    </div>
                </div>
                <div class="loader_box" id="loader_box" style="display: none;">
                    <div class="loading">
                        <p class="loading__text">Please Wait. Deleting Product Type.</p>
                        <div class="loading__bar"></div>
                    </div>
                </div>
                <div class="white_card_body custom-crm-erp-prdouct-type-report p-15" id="echeck_report_table_wrap">
            <?php
            $columns = $this->get_columns();
            $hidden = $this->get_hidden_columns();
            $sortable = $this->get_sortable_columns();
            $this->_column_headers = array(
                $columns,
                $hidden,
                $sortable
            );
            //$this->process_bulk_action();
            $search = $_GET;
            $data = $this->table_data($search);
            usort($data, array($this, 'sort_data'));
            $perPage = $this->limitPerpage;
            $currentPage = $this->get_pagenum();
            $totalItems = count($data);
            $this->set_pagination_args(array(
                'total_items' => $totalItems,
                'per_page' => $perPage
            ));
            $data = array_slice($data, (($currentPage - 1) * $perPage), $perPage);
            $this->items = $data;
            $this->display();
           
            ?>
                        </div>
                    </div>
                </div>
            </form>
            <script type="text/javascript">
                $(document).ready(function() {
                    // Handler for the change event of #agreeCheckbox
                    $(document).on('change', '#agreeCheckbox', function() {
                        // Check if #agreeCheckbox is checked
                        if ($(this).prop('checked')) {
                            $(".swal-button--confirm").css("pointer-events", "auto");
                        } else {
                            $(".swal-button--confirm").css("pointer-events", "none");
                        }
                    });

                    $(document).on('click', '.swal-button-container:has(.swal-button--confirm)', function() {
                        if ($('#agreeCheckbox').prop('checked')) {
                            return true;
                        } else {
                            // Check if error message already exists
                            if (!$('.swal-content + p.error-message').length) {
                                // Append the error message if it doesn't exist
                                $('.swal-content').after('<p class="error-message" style="color: red; margin: 40px;">Please agree to delete the product type!</p>');
                            }
                        }
                    });
                });

                jQuery(document).on('click','.delete_product_type',function(){
                    swal({
                        title: "Are you sure?",
                        text: "Yes, I want to delete this product type. On deleting product type, product will unlink respective to this product type.",
                        icon: "warning",
                        buttons: {
                            cancel: "Cancel",
                            confirm: "Delete",
                        },
                        content: {
                            element: "input",
                            attributes: {
                                type: "checkbox",
                                id: "agreeCheckbox"
                            },
                        },
                        dangerMode: true,
                    })
                    .then((willDelete) => {
                        if (willDelete && $('#agreeCheckbox').prop('checked')) {
                            $(this).attr('disabled',true);
                            $("#loader_box").show();
                            var product_type_id = jQuery(this).data('product_type_id');
                            jQuery.ajax({
                                url:'<?php echo admin_url('admin-ajax.php'); ?>',
                                method:'post',
                                data:{action: 'delete_product_type',product_type_id:product_type_id},
                                success(response){
                                    location.reload(true);
                                }
                            });
                        }
                        $('.swal-modal').removeClass('crm-erp-products-types-delete-swal');
                    });
                    $('.swal-modal').addClass('crm-erp-products-types-delete-swal');
                });   
                
                jQuery(document).on("click", ".export_product_types",function(){
                    jQuery(this).text('Please wait..');
                    jQuery(this).css('pointer-events','none');
                    var product_type = jQuery("#product_type").val();
                    jQuery.ajax({
                        url:'<?php echo admin_url('admin-ajax.php'); ?>',
                        method:'post',
                        data:{action: 'export_product_types',product_type: product_type},
                        success(response){
                            jQuery(".export_product_types").text('Export');
                            jQuery(".export_product_types").css('pointer-events','');
                            var downloadLink = document.createElement("a");
                            var responseData = jQuery.trim(response);
                            var fileData = ['\ufeff'+responseData];
                            var blobObject = new Blob(fileData,{
                            type: "text/csv;charset=utf-8;"
                            });
                            var url = URL.createObjectURL(blobObject);
                            downloadLink.href = url;
                            var currentDate = new Date();
                            var dateString = currentDate.toISOString().slice(0, 10); // YYYY-MM-DD
                            var timeString = currentDate
                                .toLocaleTimeString("en-US", {
                                    hour12: false,
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    second: "2-digit",
                                })
                                .replace(/:/g, "-");
                            var filename = "Product_Types_" + dateString + "_" + timeString + ".csv";
                            downloadLink.download = filename;
                            document.body.appendChild(downloadLink);
                            downloadLink.click();
                            document.body.removeChild(downloadLink);
                        }
                    })
                })
            </script>
            
    <?php
    }

    public function get_columns() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        $columns = array(
            'cb' => '<input type="checkbox" />',
            'ProductTypeID' => 'Product Type ID',
            'TypeName' => 'Product Type',
            'Description' => 'Description',
            'Action' => 'Action'
        );
        return $columns;
    }
    public function get_sortable_columns() {
        $sortable = array(
            'ProductTypeID'     => array('ProductTypeID',true),
            'TypeName'     => array('TypeName',true),
            'Description'     => array('Description',true)
        );
        return $sortable;
    }
    function get_bulk_actions() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
          if(in_array('administrator', $user_roles) || in_array('echeck_staff', $user_roles) || in_array('echeck_client', $user_roles) || in_array('master_sales', $user_roles)){ 
            return array(
                'delete' => __( 'Delete', ''),
            );
          }else{
            return array();
          }      
    }
    /**
     * Define which columns are hidden
     *
     * @return Array
     */

    public function get_hidden_columns() {
        return array();
    }

    /**

     * Define the sortable columns

     *

     * @return Array

     */
    public function get_display_name($user_id) {
        if (!$user = get_userdata($user_id))
            return false;
        return $user->data->display_name;
    }

    /**

     * Get the table data

     *

     * @return Array

     */

    private function table_data($search) {
        global $wpdb;
        if( isset($search['product_type']) && $search['product_type'] != ''){
            $product_manager = new CRM_ERP_Product_Manager();
            $product_types_data = $product_manager->search_product_types($search);
        }else{
            $product_manager = new CRM_ERP_Product_Manager();
            $product_types_data = $product_manager->get_product_types();
        }
        if(!empty($product_types_data)){
            foreach($product_types_data as $key => $product_type_data){
                $product_type_data->Action = '<button data-product_type_id ="'.$product_type_data->ProductTypeID.'"  class="delete_product_type" type="button" title="Remove"> <i class="fa-solid fa-trash"></i></button>';
            }
        }
        return $product_types_data;
    }
    /**

     * Allows you to sort the data by the variables set in the $_GET

     *

     * @return Mixed

     */
    
    private function sort_data($a, $b) {
        // Set defaults
        $orderby = isset($_GET['orderby']) ? sanitize_text_field($_GET['orderby']) : 'ProductTypeID';
        $order = isset($_GET['order']) ? sanitize_text_field($_GET['order']) : 'desc';
        
        $result = strnatcmp($a->$orderby, $b->$orderby);
    
        return ($order === 'asc') ? $result : -$result;
    }

    protected function pagination( $which ) {
        if ( empty( $this->_pagination_args ) ) {
            return;
        }
        $total_items     = $this->_pagination_args['total_items'];
        $total_pages     = $this->_pagination_args['total_pages'];
        $infinite_scroll = false;
        if ( isset( $this->_pagination_args['infinite_scroll'] ) ) {
            $infinite_scroll = $this->_pagination_args['infinite_scroll'];
        }
        if ( 'top' === $which && $total_pages > 1 ) {
            $this->screen->render_screen_reader_content( 'heading_pagination' );
        }
        $output = '<span class="displaying-num">' . sprintf(
            /* translators: %s: Number of items. */
            _n( '%s item', '%s items', $total_items ),
            number_format_i18n( $total_items )
        ) . '</span>';
        $current              = $this->get_pagenum();
        $removable_query_args = wp_removable_query_args();
        $current_url = set_url_scheme( 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] );
        $current_url = remove_query_arg( $removable_query_args, $current_url );
        $page_links = array();
        $total_pages_before = '<span class="paging-input">';
        $total_pages_after  = '</span></span>';
        $disable_first = false;
        $disable_last  = false;
        $disable_prev  = false;
        $disable_next  = false;
        if ( 1 == $current ) {
            $disable_first = true;
            $disable_prev  = true;
        }
        if ( 2 == $current ) {
            $disable_first = true;
        }
        if ( $total_pages == $current ) {
            $disable_last = true;
            $disable_next = true;
        }
        if ( $total_pages - 1 == $current ) {
            $disable_last = true;
        }
        if ( $disable_first ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&laquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='first-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( remove_query_arg( 'paged', $current_url ) ),
                __( 'First page' ),
                '&laquo;'
            );
        }
        if ( $disable_prev ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&lsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='prev-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( 'paged', max( 1, $current - 1 ), $current_url ) ),
                __( 'Previous page' ),
                '&lsaquo;'
            );
        }
        if ( 'bottom' === $which ) {
            $html_current_page  = $current;
            $total_pages_before = '<span class="screen-reader-text">' . __( 'Current Page' ) . '</span><span id="table-paging" class="paging-input"><span class="tablenav-paging-text">';
        } else {
            $html_current_page = sprintf(
                "%s<input class='current-page' id='current-page-selector' type='text' name='paged' value='%s' size='%d' aria-describedby='table-paging' /><span class='tablenav-paging-text'>",
                '<label for="current-page-selector" class="screen-reader-text">' . __( 'Current Page' ) . '</label>',
                $current,
                strlen( $total_pages )
            );
        }
        $html_total_pages = sprintf( "<span class='total-pages'>%s</span>", number_format_i18n( $total_pages ) );
        $page_links[]     = $total_pages_before . sprintf(
            _x( '%1$s of %2$s', 'paging' ),
            $html_current_page,
            $html_total_pages
        ) . $total_pages_after;
        if ( $disable_next ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&rsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='next-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=> min( $total_pages, $current + 1 )), $current_url ) ),
                __( 'Next page' ),
                '&rsaquo;'
            );
        }
        if ( $disable_last ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&raquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='last-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=>$total_pages), $current_url ) ),
                __( 'Last page' ),
                '&raquo;'
            );
        }
        $pagination_links_class = 'pagination-links';
        if ( ! empty( $infinite_scroll ) ) {
            $pagination_links_class .= ' hide-if-js';
        }
        $output .= "\n<span class='$pagination_links_class'>" . implode( "\n", $page_links ) . '</span>';
        if ( $total_pages ) {
            $page_class = $total_pages < 2 ? ' one-page' : '';
        } else {
            $page_class = ' no-pages';
        }
        $this->_pagination = "<div class='tablenav-pages{$page_class}'>$output</div>";
        echo $this->_pagination;
    }
    public function search_box_new($search_data)
    {
    ?>
        
            <input type="hidden" name="page" value="product_types">
            <div id="overlay" onclick="overlay_off()"></div>
            <div class="row align-items-center mb-3">
                <div class="col-md-3 search_field">
                    <img src="https://design.echeck.money/portal/wp-content/plugins/echeck-theme111/assets/img/icon/Search.svg" alt="" class="icon">
                    <input type="text" placeholder="Search.." id="show_lead_search" autocomplete="off" class="open">
                    <input type="submit" style="visibility: hidden;position:absolute;">
                </div>
            </div>
            <div class="popup-overlay">
                <div class="popup-content">
                    <div class="search_header">
                        <h4>Search</h4>
                        <span class="close">
                            <i class="fa-solid fa-xmark"></i>
                        </span>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <input type="text" id="product_type" name="product_type" placeholder="Product Type" value="<?php if (isset($search_data['product_type'])) {echo $search_data['product_type'];} ?>" class="search-popup-input-select">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12 text-center">
                        <input type="submit" id="btn_search_submit" class="search-popup-submit-btn" value="Search" />
                        <input type="button" class="search-popup-reset-btn" value="Clear" id="reset_Form_button" onclick="resetAffForm()"/>
                        </div>
                    </div>  
                </div>
            </div>
        
        <script type="text/javascript">
            jQuery(".open").on("click", function() {
              jQuery('.status_box').hide(); 
              jQuery(".popup-overlay, .popup-content").addClass("active");
              jQuery(".search_lead_id").focus();
              jQuery('#overlay').show();
            });

            jQuery(".close").on("click", function() {
              jQuery(".popup-overlay, .popup-content").removeClass("active");
              jQuery('#overlay').hide();
            });

            function overlay_off(){
              jQuery(".close").trigger("click");
              jQuery('#overlay').hide();
            }
            function resetAffForm(){
                jQuery("#reset_Form_button").val('Clearing...');
                var site_url = '<?php  echo get_site_url() ?>';
                window.location.href = site_url+'/wp-admin/admin.php?page=product_types';
            }
            jQuery(document).on("click",'#doaction2', function(e) {
                e.preventDefault();
                var is_check = 0;
                $(".chkbox").each(function(){
                    if($(this).is(":checked")){
                        is_check = 1;
                    }
                })
                if($('#bulk-action-selector-bottom').val()=='delete'){
                    if(is_check == 1){
                        swal({
                            title: "Are you sure?",
                            text: "Yes, I want to delete this product type. On deleting product type, product will unlink respective to this product type.",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                                confirm: "Delete",
                            },
                            content: {
                                element: "input",
                                attributes: {
                                    type: "checkbox",
                                    id: "agreeCheckbox"
                                },
                            },
                            dangerMode: true,
                        })
                        .then((willDelete) => {
                            if (willDelete && $('#agreeCheckbox').prop('checked')) {
                                $(this).attr('disabled',true);
                                $("#loader_box").show();
                                $('#search_all_form').submit();
                            }
                        });
                    }else{
                        swal({
                            title: "Please select atleast one checkbox",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                        //alert('Please select some option.');
                    }
                }else{ 
                    swal({
                            title: "Please select some action.",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                }
            });
        </script>
    <?php
    }
}
?>