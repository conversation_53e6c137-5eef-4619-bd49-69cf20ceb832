<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
global $wpdb;
$record = 1;
$project_id = $_POST['project_id'];
$lead_id = $_POST['lead_id'];
$created_by = get_current_user_id();
if(isset($project_id)){
	
	if(isset($_POST['review_status'])){
		$wpdb->query("UPDATE {$wpdb->prefix}projects SET review_status = '".$_POST['review_status']."' WHERE project_id=$project_id");
	}

	if(isset($_POST['review_link'])){
		$wpdb->query("UPDATE {$wpdb->prefix}projects SET review_link='".$_POST['review_link']."' WHERE project_id=$project_id");
	}
	
	//die;
	//check project intake info exist for erc product
	$rdc_intake_info = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}rdc_intake WHERE lead_id = ".$lead_id." AND project_id = ".$project_id."");
	

	if(!empty($rdc_intake_info)){
		$new_intake_data = array();
		$new_intake_data['internal_sales_agent'] = $_POST['internal_sales_agent'];
		$new_intake_data['affiliate_name'] = $_POST['affiliate_name'];		
		$new_intake_data['rnd_year'] = $_POST['rnd_year'];		
		$new_intake_data['federal_rnd_amount'] = $_POST['federal_rnd_amount'];		
		$new_intake_data['state_rnd_amount'] = $_POST['state_rnd_amount'];		
		$new_intake_data['rnd_state_code'] = $_POST['rnd_state_code'];		
		$new_intake_data['fee_type'] = $_POST['fee_type'];		
		$new_intake_data['created_by'] = $_POST['created_by'];		

		$int_table = $wpdb->prefix.'rdc_intake';
		log_audit_entry($lead_id, $rdc_intake_info, $new_intake_data , $int_table);	
		
		$query="UPDATE {$wpdb->prefix}rdc_intake SET internal_sales_agent='".$_POST['internal_sales_agent']."', affiliate_name='".$_POST['affiliate_name']."', rnd_year='".$_POST['rnd_year']."', federal_rnd_amount='".$_POST['federal_rnd_amount']."', state_rnd_amount='".$_POST['state_rnd_amount']."', rnd_state_code='".$_POST['rnd_state_code']."', fee_type='".$_POST['fee_type']."', created_by='".$created_by."' WHERE lead_id='".$lead_id."' AND project_id='".$project_id."'";
		$wpdb->query($query);
	}
	else{
		
		$wpdb->query($wpdb->prepare(
			"INSERT INTO {$wpdb->prefix}rdc_intake (
				lead_id,project_id, internal_sales_agent,
				affiliate_name, rnd_year, federal_rnd_amount, state_rnd_amount,
				rnd_state_code, fee_type, created_by 
				) VALUES (
				".$lead_id.",'".$project_id."', '".$_POST['internal_sales_agent']."',
				'".$_POST['affiliate_name']."', '".$_POST['rnd_year']."', '".$_POST['federal_rnd_amount']."', '".$_POST['state_rnd_amount']."','".$_POST['rnd_state_code']."', '".$_POST['fee_type']."', '".$created_by."')"
		));

	}
	// intake value insert update end here

}// project id check 


function log_audit_entry($lead_id, $old_data, $new_data,$table_name) {
	// echo $lead_id; 
	// print_r($old_data);
	// print_r($new_data);
	// echo $table_name;

    global $wpdb;
    // Get the current user ID
    $user_id = get_current_user_id();

    // Ensure old data is retrieved
    if ($old_data) {
        // Compare old and new data to find changes
 
        $changes = array();
        foreach ($new_data as $field_name => $new_value) {
            $old_value = isset($old_data->$field_name) ? $old_data->$field_name : null;
            if (trim($new_value) !== trim($old_value) ) {
            	 if($new_value !='N/A' && $old_value==''){
            	      //if(($new_value !='N/A' && $old_value!='N/A') && ($new_value !='' && $old_value!='N/A') && ($new_value !='N/A' && $old_value!='')){
                		$changes[$field_name] = array('old' => $old_value, 'new' => $new_value);
            	   // }
                }
           }
        }
        
        // echo "-------changes-----";
        // echo $table_name;
        // print_r($changes);
	// die();
        
        // Insert each change into the audit log
        foreach ($changes as $field_name => $change) {
            $audit_data = array(
                            'DateCreated' => current_time('mysql'),
                            'CreatedBy' => $user_id,
                            'TableName' => $table_name,
                            'FieldName' => $field_name,
                            'DataType' => gettype($change['new']),
                            'BeforeValueString' => $change['old'],
                            'AfterValueString' => $change['new'],
                            'FieldID' => $lead_id,
                            'Action' => 'Update',
                        );

            // Insert the audit log entry
            $result = $wpdb->insert($wpdb->prefix . 'audit_logs', $audit_data);
            
            if ($result === false) {
                error_log("Error inserting audit log: " . $wpdb->last_error);
            }
        }
    } else {
        // Log an error or take action if old data cannot be retrieved
        error_log("Error: Could not retrieve old data for lead_id $lead_id");
    }
}

die;