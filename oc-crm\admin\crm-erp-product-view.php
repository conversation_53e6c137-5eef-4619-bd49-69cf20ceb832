<?php

if($_REQUEST['action'] == 'edit' || $_REQUEST['action'] == 'view'){
    $product_id = $_REQUEST['id'];
    $product_manager = new CRM_ERP_Product_Manager();
    $products_data = $product_manager->get_product($product_id);
    $Title = $products_data->Title;
    $SKU = $products_data->SKU;
    $currency_id = $products_data->currency_id;
    $UnitPrice = $products_data->UnitPrice;
    $HourlyPrice = $products_data->HourlyPrice;
    $UnitTypeID = $products_data->UnitTypeID;
    $OwnerID = $products_data->OwnerID;
    $ProductTypeID = $products_data->ProductTypeID;
    $CategoryID = $products_data->CategoryID;
    $ProductLogo = $products_data->ProductLogo;
    $ProductImage = $products_data->ProductImages;
    $LaunchDate = $products_data->LaunchDate;
    $ExpiryDate = $products_data->ExpiryDate;
    $Description = $products_data->Description;
    $Status = $products_data->Status;
    $heading_text = 'View';
    $btn_text = 'Update';
}else{
	$product_id = '';
	$Title = '';
	$SKU = '';
    $currency_id = '';
	$UnitPrice = '';
	$HourlyPrice = '';
	$UnitTypeID = '';
	$OwnerID = '';
	$ProductTypeID = '';
	$CategoryID = '';
    $ProductLogo = '';
    $ProductImage = '';
	$LaunchDate = '';
	$ExpiryDate = '';
	$Description = '';
	$Status = '';
	$heading_text = 'New';
	$btn_text = 'Submit';
}
?>
<div class="main_content_iner ">
    <div class="container-fluid p-0">
         <div class="row justify-content-center">
            <div class="col-lg-12">
                <div id="form_2" class="white_card card_height_100 mb_30">
                     <div class="white_card_header">
                        <div class="box_header m-0 new_report_header">
                            <div class="title_img">
                            <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/view-products-icon.png" class="page-title-img" alt="">
                            <h4><?php echo $heading_text;?> Product</h4>
                            </div>
                        </div>
                    </div>
                    <div class="white_card_body p-15">
                        <div class="container-fluid">
                            <div class="tab-content" id="pills-tabContent">
                                <div class="tab-pane scroll_height-business scroll-common active" id="pills-home" role="tabpanel" aria-labelledby="pills-erc-edit-info">
                                    <form id="product-form" method="post" enctype="multipart/form-data">
                                        <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="title">Title:*</label>
                                                <input type="text" name="Title" id="Title" class="crm-erp-field form-control" value=" <?php echo $Title;?>" disabled>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="title">SKU:*</label>
                                                <input type="text" name="SKU" id="SKU" class="crm-erp-field form-control" value=" <?php echo $SKU;?>" disabled>
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="title">Currency:*</label>
                                                <select id="currency_id" class="crm-erp-field form-control" disabled>
                                                    <option value="">Select Currency</option>
                                                    <?php foreach($currencies as $currency): 
                                                            if($currency->currency_id == $currency_id){
                                                            echo '<option value= '.$currency->currency_id.'>'.$currency->currency_code.' ('.$currency->currency_name.')</option>'; 
                                                            }
                                                     endforeach; ?>
                                                </select>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="title">Owner:*</label>
                                                <select id="OwnerID" class="crm-erp-field form-control" disabled>
                                                    <?php foreach($users as $user): 
                                                            if($user->ID == $OwnerID){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = "";
                                                            }
                                                            echo '<option value= '.$user->ID.' '.$sel.'>'.$user->display_name.'</option>'; 
                                                     endforeach; ?>
                                                </select>
                                            </div>
                                            <!-- <div class="floating col-md-6">     
                                                <label for="title">Unit Price:*</label>
                                                <input type="text" id="UnitPrice" class="crm-erp-field form-control" value=" <?php echo $UnitPrice;?>" disabled>
                                            </div> -->
                                            
                                        </div>
                                        <div class="row mb-2">
                                        	<!-- <div class="floating col-md-6">     
                                                <label for="title">Hourly Price:</label>
                                                <input type="text" id="HourlyPrice" class="crm-erp-field form-control" value=" <?php echo $HourlyPrice;?>" disabled>
                                            </div> -->
                                            
                                            
                                        </div>
                                        <div class="row mb-2">
                                        	<div class="floating col-md-6">     
                                                <label for="title">Unit Type:</label>
                                                <select id="UnitTypeID" class="crm-erp-field form-control" disabled>
                                                    <option value="">Select Unit Type</option>
                                                    <?php foreach($unit_types as $unit): 
                                                            if($unit->UnitTypeID == $UnitTypeID){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = "";
                                                            }
                                                            echo '<option value= '.$unit->UnitTypeID.' '.$sel.'>'.$unit->UnitName.'</option>'; 
                                                     endforeach; ?>
                                                </select>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="title">Product Type:*</label>
                                                <select id="ProductTypeID" class="crm-erp-field form-control" disabled>
                                                    <option value="">Select Product Type</option>
                                                    <?php foreach($product_types as $product_type): 
                                                            if($product_type->ProductTypeID == $ProductTypeID){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = "";
                                                            }
                                                            echo '<option value= '.$product_type->ProductTypeID.' '.$sel.'>'.$product_type->TypeName.'</option>'; 
                                                     endforeach; ?>
                                                </select>
                                            </div>
                                            
                                        </div>
                                        
                                        <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                    <label for="title">Product Logo:</label>
                                                    <?php if ($ProductLogo) {
                                                        $image_file = plugins_url( 'uploads/' . $ProductLogo, dirname(__FILE__) );
                                                        echo "<div id='logo-preview' class='product-preview'><img src='$image_file' alt='Preview'></div>";
                                                    }  ?>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="productImages">Product Image:</label>
                                                <?php if ($ProductImage) {
                                                    $image_file = plugins_url( 'uploads/' . $ProductImage, dirname(__FILE__) );
                                                    echo "<div id='images-preview' class='product-preview'><img src='$image_file' alt='Preview'></div>";
                                                } ?>
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="title">Launch Date:*</label>
                                                <input type="text" placeholder="mm/dd/yyyy" id="LaunchDate" class="crm-erp-field form-control date_field" value=" <?php
												if(isset($LaunchDate)){											
													echo $LaunchDate = date('m/d/Y', strtotime($LaunchDate));
												}else{
													echo $LaunchDate;
												}
												?>" disabled>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="title">Expiry Date:</label>
                                                <input type="text" placeholder="mm/dd/yyyy" id="ExpiryDate" class="crm-erp-field form-control date_field" value=" <?php
												if(isset($ExpiryDate)){											
													echo $ExpiryDate = date('m/d/Y', strtotime($ExpiryDate));
												}else{
													echo $ExpiryDate;
												}
												?>" disabled>
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                        <div class="floating col-md-6">     
                                                <label for="title">Product Category:</label>
                                                <select id="CategoryID" class="crm-erp-field form-control" disabled>
                                                    <option value="">Select Product Category</option>
                                                    <?php foreach($product_categories as $product_category): 
                                                            if($product_category->CategoryID == $CategoryID){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = "";
                                                                
                                                            }
                                                            echo '<option value= '.$product_category->CategoryID.'>'.$product_category->Name.'</option>'; 
                                                     endforeach; ?>
                                                </select>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="Status">Status:</label>
                                                <select id="Status" class="crm-erp-field form-control" disabled>
                                                    <option value="active" <?php if($Status == 'active'){echo "selected";}?>>Active</option>
                                                    <option value="inactive" <?php if($Status == 'inactive'){echo "selected";}?>>Inactive</option>
                                                </select>
                                            </div>
                                        </div>

                                        <div class="row mb-2">
                                        	<div class="floating col-md-12">     
                                                <label for="description">Description:</label>
                                                <textarea  maxlength="2000" id="Description" rows="5" class="form-control" value="<?php echo $Description;?>" disabled></textarea>
                                            </div>
                                        </div>
                                        <input type="hidden" name="product_id" id="product_id" value="<?php echo $product_id;?>">
                                        
                                    </form>
                                    <div id="form-response"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- .site-main -->
</div><!-- .content-area -->
<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>