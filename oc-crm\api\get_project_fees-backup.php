<?php
global $wpdb;
$table_name = $wpdb->prefix.'projects';
$data = $request->get_json_params();
$project_id = $data['project_id'];

$project = $wpdb->get_row("SELECT {$wpdb->prefix}projects.lead_id
                                                                    FROM {$wpdb->prefix}projects 
                                                                    WHERE {$wpdb->prefix}projects.deleted_at IS NULL 
                                                                    AND {$wpdb->prefix}projects.project_id = ".$project_id." ");
if(!empty($project)){
	$lead_id = $project->lead_id;
	$ercfee_error_discovered_date = "";
	$ercfee_q2_2020_941_wages = "";
	$ercfee_q3_2020_941_wages = "";
	$ercfee_q4_2020_941_wages = "";
	$ercfee_q1_2021_941_wages = "";
	$ercfee_q2_2021_941_wages = "";
	$ercfee_q3_2021_941_wages = "";
	$ercfee_q4_2021_941_wages = "";
	$ercfee_sales_agent_name = "";
	$ercfee_sales_support_name = "";
	$ercfee_affiliate_name = "";
	$ercfee_affiliate_percentage = "";
	$ercfee_erc_claim_filed = "";
	$ercfee_erc_amount_received = "";
	$ercfee_total_erc_fees = "";
	$ercfee_legal_fees = "";
	$ercfee_total_erc_fees_paid = "";
	$ercfee_total_erc_fees_pending = "";
	$ercfee_total_occams_share = "";
	$ercfee_total_aff_ref_share = "";
	$ercfee_retain_occams_share = "";
	$ercfee_retain_aff_ref_share = "";
	$ercfee_bal_retain_occams_share = "";
	$ercfee_bal_retain_aff_ref_share = "";
	$ercfee_total_occams_share_paid = "";
	$ercfee_total_aff_ref_share_paid = "";
	$ercfee_total_occams_share_pendin = "";
	$ercfee_total_aff_ref_share_pend = "";
	$ercfee_q1_2020_max_erc_amount = '';
	$ercfee_q2_2020_max_erc_amount = '';
	$ercfee_q3_2020_max_erc_amount = '';
	$ercfee_q4_2020_max_erc_amount = '';
	$ercfee_q1_2021_max_erc_amount = '';
	$ercfee_q2_2021_max_erc_amount = '';
	$ercfee_q3_2021_max_erc_amount = '';
	$ercfee_q4_2021_max_erc_amount = '';
	$ercfee_q1_2020_eligibility_basis = '';
	$ercfee_q2_2020_eligibility_basis = '';
	$ercfee_q3_2020_eligibility_basis = '';
	$ercfee_q4_2020_eligibility_basis = '';
	$ercfee_q1_2021_eligibility_basis = '';
	$ercfee_q2_2021_eligibility_basis = '';
	$ercfee_q3_2021_eligibility_basis = '';
	$ercfee_q4_2021_eligibility_basis = '';
	$ercfee_q1_2020_filed_status = "";
	$ercfee_q1_2020_filing_date = "";
	$ercfee_q1_2020_amount_filed = "";
	$ercfee_q1_2020_benefits = "";
	$ercfee_q2_2020_filed_status = "";
	$ercfee_q2_2020_filing_date = "";
	$ercfee_q2_2020_amount_filed = "";
	$ercfee_q2_2020_benefits = "";
	$ercfee_q3_2020_filed_status = "";
	$ercfee_q3_2020_filing_date = "";
	$ercfee_q3_2020_amount_filed = "";
	$ercfee_q3_2020_benefits = "";
	$ercfee_q4_2020_filed_status = "";
	$ercfee_q4_2020_filing_date = "";
	$ercfee_q4_2020_amount_filed = "";
	$ercfee_q4_2020_benefits = "";
	$ercfee_q1_2021_filed_status = "";
	$ercfee_q1_2021_filing_date = "";
	$ercfee_q1_2021_amount_filed = "";
	$ercfee_q1_2021_benefits = "";
	$ercfee_q2_2021_filed_status = "";
	$ercfee_q2_2021_filing_date = "";
	$ercfee_q2_2021_amount_filed = "";
	$ercfee_q2_2021_benefits = "";
	$ercfee_q3_2021_filed_status = "";
	$ercfee_q3_2021_filing_date = "";
	$ercfee_q3_2021_amount_filed = "";
	$ercfee_q3_2021_benefits = "";
	$ercfee_q4_2021_filed_status = "";
	$ercfee_q4_2021_filing_date = "";
	$ercfee_q4_2021_amount_filed = "";
	$ercfee_q4_2021_benefits = "";
	$ercfee_q1_2020_loop = "";
	$ercfee_q1_2020_letter = "";
	$ercfee_q1_2020_check = "";
	$ercfee_q1_2020_chq_amt = "";
	$ercfee_q2_2020_loop = "";
	$ercfee_q2_2020_letter = "";
	$ercfee_q2_2020_check = "";
	$ercfee_q2_2020_chq_amt = "";
	$ercfee_q3_2020_loop = "";
	$ercfee_q3_2020_letter = "";
	$ercfee_q3_2020_check = "";
	$ercfee_q3_2020_chq_amt = "";
	$ercfee_q4_2020_loop = "";
	$ercfee_q4_2020_letter = "";
	$ercfee_q4_2020_check = "";
	$ercfee_q4_2020_chq_amt = "";
	$ercfee_q1_2021_loop = "";
	$ercfee_q1_2021_letter = "";
	$ercfee_q1_2021_check = "";
	$ercfee_q1_2021_chq_amt = "";
	$ercfee_q2_2021_loop = "";
	$ercfee_q2_2021_letter = "";
	$ercfee_q2_2021_check = "";
	$ercfee_q2_2021_chq_amt = "";
	$ercfee_q3_2021_loop = "";
	$ercfee_q3_2021_letter = "";
	$ercfee_q3_2021_check = "";
	$ercfee_q3_2021_chq_amt = "";
	$ercfee_q4_2021_loop = "";
	$ercfee_q4_2021_letter = "";
	$ercfee_q4_2021_check = "";
	$ercfee_q4_2021_chq_amt = "";
	$ercfee_i_invoice_number = "";
	$ercfee_i_invoice_amount = "";
	$ercfee_i_invoiced_qtrs = "";
	$ercfee_i_invoice_sent_date = "";
	$ercfee_i_invoice_payment_type = "";
	$ercfee_i_invoice_payment_date = "";
	$ercfee_i_invoice_pay_cleared = "";
	$ercfee_i_invoice_pay_returned = "";
	$ercfee_i_invoice_return_reason = "";
	$ercfee_i_invoice_occams_share = "";
	$ercfee_i_invoice_aff_ref_share = "";
	$ercfee_ii__invoice_number = "";
	$ercfee_ii_invoice_amount = "";
	$ercfee_ii_invoiced_qtrs = "";
	$ercfee_ii_invoice_sent_date = "";
	$ercfee_ii_invoice_payment_type = "";
	$ercfee_ii_invoice_payment_date = "";
	$ercfee_ii_invoice_pay_cleared = "";
	$ercfee_ii_invoice_pay_returned = "";
	$ercfee_ii_invoice_return_reason = "";
	$ercfee_ii_invoice_occams_share = "";
	$ercfee_ii_invoice_aff_ref_share = "";
	$ercfee_iii_invoice_number = "";
	$ercfee_iii_invoice_amount = "";
	$ercfee_iii_invoiced_qtrs = "";
	$ercfee_iii_invoice_sent_date = "";
	$ercfee_iii_invoice_payment_type = "";
	$ercfee_iii_invoice_payment_date = "";
	$ercfee_iii_invoice_pay_cleared = "";
	$ercfee_iii_invoice_pay_returned = "";
	$ercfee_iii_invoice_return_reason = "";
	$ercfee_iii_invoice_occams_share = "";
	$ercfee_iii_invoice_aff_ref_share = "";
	$ercfee_iv_invoice_number = "";
	$ercfee_iv_invoice_amount = "";
	$ercfee_iv_invoiced_qtrs = "";
	$ercfee_iv_invoice_sent_date = "";
	$ercfee_iv_invoice_payment_type = "";
	$ercfee_iv_invoice_payment_date = "";
	$ercfee_iv_invoice_pay_cleared = "";
	$ercfee_iv_invoice_pay_returned = "";
	$ercfee_iv_invoice_return_reason = "";
	$ercfee_iv_invoice_occams_share = "";
	$ercfee_iv_invoice_aff_ref_share = "";
	
	$sales_agent_user_id = 0;
	$sales_support_user_id = 0;

	$sales_data = $wpdb->get_row("SELECT sales_user_id,sales_support_id FROM {$wpdb->prefix}erc_iris_leads_additional_info WHERE lead_id = ".$lead_id."");
	if(!empty($sales_data)){
	    $sales_agent_user_id = $sales_data->sales_user_id;
	    $sales_support_user_id = $sales_data->sales_support_id;
	}

	$sales_agent_name = '';
	if($sales_agent_user_id > 0){
	    $sales_user_data = get_user_by('id',$sales_agent_user_id);
	    $sales_agent_name = $sales_user_data->data->display_name;
	}

	$sales_support_name = '';
	if($sales_support_user_id > 0){
	    $sales_users_data = get_user_by('id',$sales_support_user_id);
	    $sales_support_name = $sales_users_data->data->display_name;
	}

	// ----------------- Get data from db ------
	global $wpdb;
	$intake_table_name = "{$wpdb->prefix}erc_erc_intake";
	$in_sql = "SELECT * FROM $intake_table_name WHERE `lead_id`=$lead_id";
	$intake_data = $wpdb->get_results($in_sql);

	$data = [];

	$invoice_data = [
	    "ercfee_sdgr" => "sdgr",
	    "ercfee_error_discovered_date" => "error_discovered_date",
	    "ercfee_q2_2020_941_wages" => "q2_2020_941_wages",
	    "ercfee_q3_2020_941_wages" => "q3_2020_941_wages",
	    "ercfee_q4_2020_941_wages" => "q4_2020_941_wages",
	    "ercfee_q1_2021_941_wages" => "q1_2021_941_wages",
	    "ercfee_q2_2021_941_wages" => "q2_2021_941_wages",
	    "ercfee_q3_2021_941_wages" => "q3_2021_941_wages",
	    "ercfee_q4_2021_941_wages" => "q4_2021_941_wages",
	    "ercfee_affiliate_name" => "affiliate_name",
	    "ercfee_affiliate_percentage" => "affiliate_percentage",
	    "ercfee_erc_claim_filed" => "erc_claim_filed",
	    "ercfee_erc_amount_received" => "erc_amount_received",
	    "ercfee_total_erc_fees" => "total_erc_fees",
	    "ercfee_legal_fees" => "legal_fees",
	    "ercfee_total_erc_fees_paid" => "total_erc_fees_paid",
	    "ercfee_total_erc_fees_pending" => "total_erc_fees_pending",
	    "ercfee_total_occams_share" => "total_occams_share",
	    "ercfee_total_aff_ref_share" => "total_aff_ref_share",
	    "ercfee_retain_occams_share" => "retain_occams_share",
	    "ercfee_retain_aff_ref_share" => "retain_aff_ref_share",
	    "ercfee_bal_retain_occams_share" => "bal_retain_occams_share",
	    "ercfee_bal_retain_aff_ref_share" => "bal_retain_aff_ref_share",
	    "ercfee_total_occams_share_paid" => "total_occams_share_paid",
	    "ercfee_total_aff_ref_share_paid" => "total_aff_ref_share_paid",
	    "ercfee_total_occams_share_pendin" => "total_occams_share_pendin",
	    "ercfee_total_aff_ref_share_pend" => "total_aff_ref_share_pend",
	    "ercfee_q1_2020_filed_status" => "q1_2020_filed_status",
	    "ercfee_q1_2020_filing_date" => "filing_date_4267",
	    "ercfee_q1_2020_amount_filed" => "amount_filed_4263",
	    "ercfee_q1_2020_benefits" => "q1_2020_benefits",
	    "ercfee_q2_2020_filed_status" => "q2_2020_filed_status",
	    "ercfee_q2_2020_filing_date" => "filing_date_4268",
	    "ercfee_q2_2020_amount_filed" => "amount_filed_4269",
	    "ercfee_q2_2020_benefits" => "q2_2020_benefits",
	    "ercfee_q3_2020_filed_status" => "q3_2020_filed_status",
	    "ercfee_q3_2020_filing_date" => "filing_date_4270",
	    "ercfee_q3_2020_amount_filed" => "amount_filed_4266",
	    "ercfee_q3_2020_benefits" => "q3_2020_benefits",
	    "ercfee_q4_2020_filed_status" => "q4_2020_filed_status",
	    "ercfee_q4_2020_filing_date" => "filing_date_4272",
	    "ercfee_q4_2020_amount_filed" => "amount_filed_4273",
	    "ercfee_q4_2020_benefits" => "q4_2020_benefits",
	    "ercfee_q1_2021_filed_status" => "q1_2021_filed_status",
	    "ercfee_q1_2021_filing_date" => "filing_date_4276",
	    "ercfee_q1_2021_amount_filed" => "amount_filed_4277",
	    "ercfee_q1_2021_benefits" => "q1_2021_benefits",
	    "ercfee_q2_2021_filed_status" => "Q2_2021_filed_status",
	    "ercfee_q2_2021_filing_date" => "filing_date_4279",
	    "ercfee_q2_2021_amount_filed" => "amount_filed_4280",
	    "ercfee_q2_2021_benefits" => "q2_2021_benefits",
	    "ercfee_q3_2021_filed_status" => "q3_2021_filed_status",
	    "ercfee_q3_2021_filing_date" => "filing_date_4282",
	    "ercfee_q3_2021_amount_filed" => "amount_filed_4283",
	    "ercfee_q3_2021_benefits" => "q3_2021_benefits",
	    "ercfee_q4_2021_filed_status" => "q4_2021_filed_status",
	    "ercfee_q4_2021_filing_date" => "filing_date_4285",
	    "ercfee_q4_2021_amount_filed" => "amount_filed_4286",
	    "ercfee_q4_2021_benefits" => "q4_2021_benefits",
	    "ercfee_q1_2020_loop" => "q1_2020_loop",
	    "ercfee_q1_2020_letter" => "q1_2020_letter",
	    "ercfee_q1_2020_check" => "q1_2020_check",
	    "ercfee_q1_2020_chq_amt" => "q1_2020_chq_amt",
	    "ercfee_q2_2020_loop" => "q2_2020_loop",
	    "ercfee_q2_2020_letter" => "q2_2020_letter",
	    "ercfee_q2_2020_check" => "q2_2020_check",
	    "ercfee_q2_2020_chq_amt" => "q2_2020_chq_amt",
	    "ercfee_q3_2020_loop" => "q3_2020_loop",
	    "ercfee_q3_2020_letter" => "q3_2020_letter",
	    "ercfee_q3_2020_check" => "q3_2020_check",
	    "ercfee_q3_2020_chq_amt" => "q3_2020_chq_amt",
	    "ercfee_q4_2020_loop" => "q4_2020_loop",
	    "ercfee_q4_2020_letter" => "q4_2020_letter",
	    "ercfee_q4_2020_check" => "q4_2020_check",
	    "ercfee_q4_2020_chq_amt" => "q4_2020_chq_amt",
	    "ercfee_q1_2021_loop" => "q1_2021_loop",
	    "ercfee_q1_2021_letter" => "q1_2021_letter",
	    "ercfee_q1_2021_check" => "q1_2021_check",
	    "ercfee_q1_2021_chq_amt" => "q1_2021_chq_amt",
	    "ercfee_q2_2021_loop" => "q2_2021_loop",
	    "ercfee_q2_2021_letter" => "q2_2021_letter",
	    "ercfee_q2_2021_check" => "q2_2021_check",
	    "ercfee_q2_2021_chq_amt" => "q2_2021_chq_amt",
	    "ercfee_q3_2021_loop" => "q3_2021_loop",
	    "ercfee_q3_2021_letter" => "q3_2021_letter",
	    "ercfee_q3_2021_check" => "q3_2021_check",
	    "ercfee_q3_2021_chq_amt" => "q3_2021_chq_amt",
	    "ercfee_q4_2021_loop" => "q4_2021_loop",
	    "ercfee_q4_2021_letter" => "q4_2021_letter",
	    "ercfee_q4_2021_check" => "q4_2021_check",
	    "ercfee_q4_2021_chq_amt" => "q4_2021_chq_amt",
	    "ercfee_i_invoice_number" => "i_invoice_no",
	    "ercfee_i_invoice_amount" => "i_invoice_amount",
	    "ercfee_i_invoiced_qtrs" => "i_invoiced_qtrs",
	    "ercfee_i_invoice_sent_date" => "i_invoice_sent_date",
	    "ercfee_i_invoice_payment_type" => "i_invoice_payment_type",
	    "ercfee_i_invoice_payment_date" => "i_invoice_payment_date",
	    "ercfee_i_invoice_pay_cleared" => "i_invoice_pay_cleared",
	    "ercfee_i_invoice_pay_returned" => "i_invoice_pay_returned",
	    "ercfee_i_invoice_return_reason" => "i_invoice_return_reason",
	    "ercfee_i_invoice_occams_share" => "i_invoice_occams_share",
	    "ercfee_i_invoice_aff_ref_share" => "i_invoice_aff_ref_share",
	    "ercfee_ii_invoice_number" => "ii_invoice_no",
	    "ercfee_ii_invoice_amount" => "ii_invoice_amount",
	    "ercfee_ii_invoiced_qtrs" => "ii_invoiced_qtrs",
	    "ercfee_ii_invoice_sent_date" => "ii_invoice_sent_date",
	    "ercfee_ii_invoice_payment_type" => "ii_invoice_payment_type",
	    "ercfee_ii_invoice_payment_date" => "ii_invoice_payment_date",
	    "ercfee_ii_invoice_pay_cleared" => "ii_invoice_pay_cleared",
	    "ercfee_ii_invoice_pay_returned" => "ii_invoice_pay_returned",
	    "ercfee_ii_invoice_return_reason" => "ii_invoice_return_reason",
	    "ercfee_ii_invoice_occams_share" => "ii_invoice_occams_share",
	    "ercfee_ii_invoice_aff_ref_share" => "ii_invoice_aff_ref_share",
	    "ercfee_iii_invoice_number" => "iii_invoice_no",
	    "ercfee_iii_invoice_amount" => "iii_invoice_amount",
	    "ercfee_iii_invoiced_qtrs" => "iii_invoiced_qtrs",
	    "ercfee_iii_invoice_sent_date" => "iii_invoice_sent_date",
	    "ercfee_iii_invoice_payment_type" => "iii_invoice_payment_type",
	    "ercfee_iii_invoice_payment_date" => "iii_invoice_payment_date",
	    "ercfee_iii_invoice_pay_cleared" => "iii_invoice_pay_cleared",
	    "ercfee_iii_invoice_pay_returned" => "iii_invoice_pay_returned",
	    "ercfee_iii_invoice_return_reason" => "iii_invoice_return_reason",
	    "ercfee_iii_invoice_occams_share" => "iii_invoice_occams_share",
	    "ercfee_iii_invoice_aff_ref_share" => "iii_invoice_aff_ref_share",
	    "ercfee_iv_invoice_number" => "iv_invoice_no",
	    "ercfee_iv_invoice_amount" => "iv_invoice_amount",
	    "ercfee_iv_invoiced_qtrs" => "iv_invoiced_qtrs",
	    "ercfee_iv_invoice_sent_date" => "iv_invoice_sent_date",
	    "ercfee_iv_invoice_payment_type" => "iv_invoice_payment_type",
	    "ercfee_iv_invoice_payment_date" => "iv_invoice_payment_date",
	    "ercfee_iv_invoice_pay_cleared" => "iv_invoice_pay_cleared",
	    "ercfee_iv_invoice_pay_returned" => "iv_invoice_pay_returned",
	    "ercfee_iv_invoice_return_reason" => "iv_invoice_return_reason",
	    "ercfee_iv_invoice_occams_share" => "iv_invoice_occams_share",
	    "ercfee_iv_invoice_aff_ref_share" => "iv_invoice_aff_ref_share",
	    "ercfee_q1_2020_max_erc_amount" => "q1_2020_max_erc_amount",
	    "ercfee_q2_2020_max_erc_amount" => "q2_2020_max_erc_amount",
	    "ercfee_q3_2020_max_erc_amount" => "q3_2020_max_erc_amount",
	    "ercfee_q4_2020_max_erc_amount" => "q4_2020_max_erc_amount",
	    "ercfee_q1_2021_max_erc_amount" => "q1_2021_max_erc_amount",
	    "ercfee_q2_2021_max_erc_amount" => "q2_2021_max_erc_amount",
	    "ercfee_q3_2021_max_erc_amount" => "q3_2021_max_erc_amount",
	    "ercfee_q4_2021_max_erc_amount" => "q4_2021_max_erc_amount",
	    "ercfee_q1_2020_eligibility_basis" => "q1_2020_eligibility_basis",
	    "ercfee_q2_2020_eligibility_basis" => "q2_2020_eligibility_basis",
	    "ercfee_q3_2020_eligibility_basis" => "q3_2020_eligibility_basis",
	    "ercfee_q4_2020_eligibility_basis" => "q4_2020_eligibility_basis",
	    "ercfee_q1_2021_eligibility_basis" => "q1_2021_eligibility_basis",
	    "ercfee_q2_2021_eligibility_basis" => "q2_2021_eligibility_basis",
	    "ercfee_q3_2021_eligibility_basis" => "q3_2021_eligibility_basis",
	    "ercfee_q4_2021_eligibility_basis" => "q4_2021_eligibility_basis"
	];
	foreach ($invoice_data as $var_name => $column_name) {
	    if ($column_name != "") {
	        $leadData = $wpdb->get_row(
	            "SELECT $column_name FROM {$wpdb->prefix}erc_erc_fees WHERE lead_id = $lead_id"
	        );
	        if (!empty($leadData)) {
	            $column_val = $leadData->$column_name;
	        } else {
	            $column_val = "";
	        }
	        $$var_name = $column_val;
	    } else {
	        $$var_name = "__N/A";
	    }
	} // erc fees loop
	$results['status'] = 1;
	$results['message'] = 'Fees Detail';
	$results['result'][0]['error_discovered_date'] = $ercfee_error_discovered_date;
	$results['result'][0]['q2_2020_941_wages'] = $ercfee_q2_2020_941_wages;
	$results['result'][0]['q3_2020_941_wages'] = $ercfee_q3_2020_941_wages;
	$results['result'][0]['q4_2020_941_wages'] = $ercfee_q4_2020_941_wages;
	$results['result'][0]['q1_2021_941_wages'] = $ercfee_q1_2021_941_wages;
	$results['result'][0]['q2_2021_941_wages'] = $ercfee_q2_2021_941_wages;
	$results['result'][0]['q3_2021_941_wages'] = $ercfee_q3_2021_941_wages;
	$results['result'][0]['q4_2021_941_wages'] = $ercfee_q4_2021_941_wages;
	$results['result'][0]['sales_agent_name'] = $sales_agent_name;
	$results['result'][0]['sales_support_name'] = $sales_support_name;
	$results['result'][0]['affiliate_name'] = $ercfee_affiliate_name;
	$results['result'][0]['affiliate_percentage'] = $ercfee_affiliate_percentage;
	$results['result'][0]['erc_claim_filed'] = $ercfee_erc_claim_filed;
	$results['result'][0]['erc_amount_received'] = $ercfee_erc_amount_received;
	$results['result'][0]['total_erc_fees'] = $ercfee_total_erc_fees;
	$results['result'][0]['legal_fees'] = $ercfee_legal_fees;
	$results['result'][0]['total_erc_fees_paid'] = $ercfee_total_erc_fees_paid;
	$results['result'][0]['total_erc_fees_pending'] = $ercfee_total_erc_fees_pending;
	$results['result'][0]['total_occams_share'] = $ercfee_total_occams_share;
	$results['result'][0]['total_aff_ref_share'] = $ercfee_total_aff_ref_share;
	$results['result'][0]['retain_occams_share'] = $ercfee_retain_occams_share;
	$results['result'][0]['retain_aff_ref_share'] = $ercfee_retain_aff_ref_share;
	$results['result'][0]['bal_retain_occams_share'] = $ercfee_bal_retain_occams_share;
	$results['result'][0]['bal_retain_aff_ref_share'] = $ercfee_bal_retain_aff_ref_share;
	$results['result'][0]['total_occams_share_paid'] = $ercfee_total_occams_share_paid;
	$results['result'][0]['total_aff_ref_share_paid'] = $ercfee_total_aff_ref_share_paid;
	$results['result'][0]['total_occams_share_pendin'] = $ercfee_total_occams_share_pendin;
	$results['result'][0]['total_aff_ref_share_pend'] = $ercfee_total_aff_ref_share_pend;
	$results['result'][0]['q1_2020_max_erc_amount'] = $ercfee_q1_2020_max_erc_amount;
	$results['result'][0]['q2_2020_max_erc_amount'] = $ercfee_q2_2020_max_erc_amount;
	$results['result'][0]['q3_2020_max_erc_amount'] = $ercfee_q3_2020_max_erc_amount;
	$results['result'][0]['q4_2020_max_erc_amount'] = $ercfee_q4_2020_max_erc_amount;
	$results['result'][0]['q1_2021_max_erc_amount'] = $ercfee_q1_2021_max_erc_amount;
	$results['result'][0]['q2_2021_max_erc_amount'] = $ercfee_q2_2021_max_erc_amount;
	$results['result'][0]['q3_2021_max_erc_amount'] = $ercfee_q3_2021_max_erc_amount;
	$results['result'][0]['q4_2021_max_erc_amount'] = $ercfee_q4_2021_max_erc_amount;
	$results['result'][0]['q1_2020_filed_status'] = $ercfee_q1_2020_filed_status;
	$results['result'][0]['q1_2020_filing_date'] = $ercfee_q1_2020_filing_date;
	$results['result'][0]['q1_2020_amount_filed'] = $ercfee_q1_2020_amount_filed;
	$results['result'][0]['q1_2020_benefits'] = $ercfee_q1_2020_benefits;
	$results['result'][0]['q1_2020_eligibility_basis'] = $ercfee_q1_2020_eligibility_basis;
	$results['result'][0]['q2_2020_filed_status'] = $ercfee_q2_2020_filed_status;
	$results['result'][0]['q2_2020_filing_date'] = $ercfee_q2_2020_filing_date;
	$results['result'][0]['q2_2020_amount_filed'] = $ercfee_q2_2020_amount_filed;
	$results['result'][0]['q2_2020_benefits'] = $ercfee_q2_2020_benefits;
	$results['result'][0]['q2_2020_eligibility_basis'] = $ercfee_q2_2020_eligibility_basis;
	$results['result'][0]['q3_2020_filed_status'] = $ercfee_q3_2020_filed_status;
	$results['result'][0]['q3_2020_filing_date'] = $ercfee_q3_2020_filing_date;
	$results['result'][0]['q3_2020_amount_filed'] = $ercfee_q3_2020_amount_filed;
	$results['result'][0]['q3_2020_benefits'] = $ercfee_q3_2020_benefits;
	$results['result'][0]['q3_2020_eligibility_basis'] = $ercfee_q3_2020_eligibility_basis;
	$results['result'][0]['q4_2020_filed_status'] = $ercfee_q4_2020_filed_status;
	$results['result'][0]['q4_2020_filing_date'] = $ercfee_q4_2020_filing_date;
	$results['result'][0]['q4_2020_amount_filed'] = $ercfee_q4_2020_amount_filed;
	$results['result'][0]['q4_2020_benefits'] = $ercfee_q4_2020_benefits;
	$results['result'][0]['q4_2020_eligibility_basis'] = $ercfee_q4_2020_eligibility_basis;
	$results['result'][0]['q1_2021_filed_status'] = $ercfee_q1_2021_filed_status;
	$results['result'][0]['q1_2021_filing_date'] = $ercfee_q1_2021_filing_date;
	$results['result'][0]['q1_2021_amount_filed'] = $ercfee_q1_2021_amount_filed;
	$results['result'][0]['q1_2021_benefits'] = $ercfee_q1_2021_benefits;
	$results['result'][0]['q1_2021_eligibility_basis'] = $ercfee_q1_2021_eligibility_basis;
	$results['result'][0]['q2_2021_filed_status'] = $ercfee_q2_2021_filed_status;
	$results['result'][0]['q2_2021_filing_date'] = $ercfee_q2_2021_filing_date;
	$results['result'][0]['q2_2021_amount_filed'] = $ercfee_q2_2021_amount_filed;
	$results['result'][0]['q2_2021_benefits'] = $ercfee_q2_2021_benefits;
	$results['result'][0]['q2_2021_eligibility_basis'] = $ercfee_q2_2021_eligibility_basis;
	$results['result'][0]['q3_2021_filed_status'] = $ercfee_q3_2021_filed_status;
	$results['result'][0]['q3_2021_filing_date'] = $ercfee_q3_2021_filing_date;
	$results['result'][0]['q3_2021_amount_filed'] = $ercfee_q3_2021_amount_filed;
	$results['result'][0]['q3_2021_benefits'] = $ercfee_q3_2021_benefits;
	$results['result'][0]['q3_2021_eligibility_basis'] = $ercfee_q3_2021_eligibility_basis;
	$results['result'][0]['q4_2021_filed_status'] = $ercfee_q4_2021_filed_status;
	$results['result'][0]['q4_2021_filing_date'] = $ercfee_q4_2021_filing_date;
	$results['result'][0]['q4_2021_amount_filed'] = $ercfee_q4_2021_amount_filed;
	$results['result'][0]['q4_2021_benefits'] = $ercfee_q4_2021_benefits;
	$results['result'][0]['q4_2021_eligibility_basis'] = $ercfee_q4_2021_eligibility_basis;
	$results['result'][0]['q1_2020_loop'] = $ercfee_q1_2020_loop;
	$results['result'][0]['q1_2020_letter'] = $ercfee_q1_2020_letter;
	$results['result'][0]['q1_2020_check'] = $ercfee_q1_2020_check;
	$results['result'][0]['q1_2020_chq_amt'] = $ercfee_q1_2020_chq_amt;
	$results['result'][0]['q2_2020_loop'] = $ercfee_q2_2020_loop;
	$results['result'][0]['q2_2020_letter'] = $ercfee_q2_2020_letter;
	$results['result'][0]['q2_2020_check'] = $ercfee_q2_2020_check;
	$results['result'][0]['q2_2020_chq_am'] = $ercfee_q2_2020_chq_amt;
	$results['result'][0]['q3_2020_loop'] = $ercfee_q3_2020_loop;
	$results['result'][0]['q3_2020_letter'] = $ercfee_q3_2020_letter;
	$results['result'][0]['q3_2020_check'] = $ercfee_q3_2020_check;
	$results['result'][0]['q3_2020_chq_amt'] = $ercfee_q3_2020_chq_amt;
	$results['result'][0]['q4_2020_loop'] = $ercfee_q4_2020_loop;
	$results['result'][0]['q4_2020_letter'] = $ercfee_q4_2020_letter;
	$results['result'][0]['q4_2020_check'] = $ercfee_q4_2020_check;
	$results['result'][0]['q4_2020_chq_amt'] = $ercfee_q4_2020_chq_amt;
	$results['result'][0]['q1_2021_loop'] = $ercfee_q1_2021_loop;
	$results['result'][0]['q1_2021_letter'] = $ercfee_q1_2021_letter;
	$results['result'][0]['q1_2021_check'] = $ercfee_q1_2021_check;
	$results['result'][0]['q1_2021_chq_amt'] = $ercfee_q1_2021_chq_amt;
	$results['result'][0]['q2_2021_loop'] = $ercfee_q2_2021_loop;
	$results['result'][0]['q2_2021_letter'] = $ercfee_q2_2021_letter;
	$results['result'][0]['q2_2021_check'] = $ercfee_q2_2021_check;
	$results['result'][0]['q2_2021_chq_amt'] = $ercfee_q2_2021_chq_amt;
	$results['result'][0]['q3_2021_loop'] = $ercfee_q3_2021_loop;
	$results['result'][0]['q3_2021_letter'] = $ercfee_q3_2021_letter;
	$results['result'][0]['q3_2021_check'] = $ercfee_q3_2021_check;
	$results['result'][0]['q3_2021_chq_amt'] = $ercfee_q3_2021_chq_amt;
	$results['result'][0]['q4_2021_loop'] = $ercfee_q4_2021_loop;
	$results['result'][0]['q4_2021_letter'] = $ercfee_q4_2021_letter;
	$results['result'][0]['q4_2021_check'] = $ercfee_q4_2021_check;
	$results['result'][0]['q4_2021_chq_amt'] = $ercfee_q4_2021_chq_amt;
	$results['result'][0]['i_invoice_number'] = $ercfee_i_invoice_number;
	$results['result'][0]['i_invoice_amount'] = $ercfee_i_invoice_amount;
	$results['result'][0]['i_invoiced_qtrs'] = $ercfee_i_invoiced_qtrs;
	$results['result'][0]['i_invoice_sent_date'] = $ercfee_i_invoice_sent_date;
	$results['result'][0]['i_invoice_payment_type'] = $ercfee_i_invoice_payment_type;
	$results['result'][0]['i_invoice_payment_date'] = $ercfee_i_invoice_payment_date;
	$results['result'][0]['i_invoice_pay_cleared'] = $ercfee_i_invoice_pay_cleared;
	$results['result'][0]['i_invoice_pay_returned'] = $ercfee_i_invoice_pay_returned;
	$results['result'][0]['i_invoice_return_reason'] = $ercfee_i_invoice_return_reason;
	$results['result'][0]['i_invoice_occams_share'] = $ercfee_i_invoice_occams_share;
	$results['result'][0]['i_invoice_aff_ref_share'] = $ercfee_i_invoice_aff_ref_share;
	$results['result'][0]['ii_invoice_number'] = $ercfee_ii_invoice_number;
	$results['result'][0]['ii_invoice_amount'] = $ercfee_ii_invoice_amount;
	$results['result'][0]['ii_invoiced_qtrs'] = $ercfee_ii_invoiced_qtrs;
	$results['result'][0]['ii_invoice_sent_date'] = $ercfee_ii_invoice_sent_date;
	$results['result'][0]['ii_invoice_payment_type'] = $ercfee_ii_invoice_payment_type;
	$results['result'][0]['ii_invoice_payment_date'] = $ercfee_ii_invoice_payment_date;
	$results['result'][0]['ii_invoice_pay_cleared'] = $ercfee_ii_invoice_pay_cleared;
	$results['result'][0]['ii_invoice_pay_returned'] = $ercfee_ii_invoice_pay_returned;
	$results['result'][0]['ii_invoice_return_reason'] = $ercfee_ii_invoice_return_reason;
	$results['result'][0]['ii_invoice_occams_share'] = $ercfee_ii_invoice_occams_share;
	$results['result'][0]['ii_invoice_aff_ref_share'] = $ercfee_ii_invoice_aff_ref_share;
	$results['result'][0]['iii_invoice_number'] = $ercfee_iii_invoice_number;
	$results['result'][0]['iii_invoice_amount'] = $ercfee_iii_invoice_amount;
	$results['result'][0]['iii_invoiced_qtrs'] = $ercfee_iii_invoiced_qtrs;
	$results['result'][0]['iii_invoice_sent_date'] = $ercfee_iii_invoice_sent_date;
	$results['result'][0]['iii_invoice_payment_type'] = $ercfee_iii_invoice_payment_type;
	$results['result'][0]['iii_invoice_payment_date'] = $ercfee_iii_invoice_payment_date;
	$results['result'][0]['iii_invoice_pay_cleared'] = $ercfee_iii_invoice_pay_cleared;
	$results['result'][0]['iii_invoice_pay_returned'] = $ercfee_iii_invoice_pay_returned;
	$results['result'][0]['iii_invoice_return_reason'] = $ercfee_iii_invoice_return_reason;
	$results['result'][0]['iii_invoice_occams_share'] = $ercfee_iii_invoice_occams_share;
	$results['result'][0]['iii_invoice_aff_ref_share'] = $ercfee_iii_invoice_aff_ref_share;
	$results['result'][0]['iv_invoice_number'] = $ercfee_iv_invoice_number;
	$results['result'][0]['iv_invoice_amount'] = $ercfee_iv_invoice_amount;
	$results['result'][0]['iv_invoiced_qtrs'] = $ercfee_iv_invoiced_qtrs;
	$results['result'][0]['iv_invoice_sent_date'] = $ercfee_iv_invoice_sent_date;
	$results['result'][0]['iv_invoice_payment_type'] = $ercfee_iv_invoice_payment_type;
	$results['result'][0]['iv_invoice_payment_date'] = $ercfee_iv_invoice_payment_date;
	$results['result'][0]['iv_invoice_pay_cleared'] = $ercfee_iv_invoice_pay_cleared;
	$results['result'][0]['iv_invoice_pay_returned'] = $ercfee_iv_invoice_pay_returned;
	$results['result'][0]['iv_invoice_return_reason'] = $ercfee_iv_invoice_return_reason;
	$results['result'][0]['iv_invoice_occams_share'] = $ercfee_iv_invoice_occams_share;
	$results['result'][0]['iv_invoice_aff_ref_share'] = $ercfee_iv_invoice_aff_ref_share;
}else{
	$results['status'] = 0;
    $results['message'] = 'No project found.';
}
echo json_encode($results);die;