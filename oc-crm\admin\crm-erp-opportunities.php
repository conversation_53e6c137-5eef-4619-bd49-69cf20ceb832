<?php
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
//  error_reporting(E_ALL);
/**
 *
 * WP_List_Table class inclusion
 *
 */
if ( ! class_exists( 'WP_List_Table' ) )
    require_once( ABSPATH . 'wp-admin/includes/class-wp-list-table.php' );

/**
 *
 * As Charlie suggested in its plugin https://github.com/Askelon/Custom-AJAX-List-Table-Example
 * it's better to set the error_reporting in order to hiding notices to avoid AJAX errors
 *
 */

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

error_reporting( ~E_NOTICE );
class Opportunity_Report_Table extends WP_List_Table {

    /**
     *
     * For this sample we'll use a dataset in a static array
     *
     */

    private $userRole;
    private $limitPerpage;
    private $master_sales_user_id;
    private $master_ops_user_id;

    /**
     *
     * @Override of constructor
     * Constructor take 3 parameters:
     * singular : name of an element in the List Table
     * plural : name of all of the elements in the List Table
     * ajax : if List Table supports AJAX set to true
     *
     */

    function __construct() {

        global $status, $page;
        $this->limitPerpage = 50;
        $this->master_sales_user_id = array('43456', '43613');
        $this->master_ops_user_id = array('44025');
        parent::__construct(
            array(
                'singular' => 'id',
                //singular name of the listed records
                'plural' => 'ids',
                //plural name of the listed records
                'ajax' => false
                //does this table support ajax?
            )
        );

    }

    

        /**
     * Define what data to show on each column of the table
     *
     * @param  Array $item        Data
     * @param  String $column_name - Current column name
     *
     * @return Mixed
     */
    public function column_default($item, $column_name)
    {
            switch ($column_name) {
                case 'OpportunityID':
                    return $item[$column_name];
                case 'CreatedAt':
                    return $item[$column_name]; 
                case 'AccountName':
                    return ucfirst($item[$column_name]);
                case 'OpportunityName':
                    return ucfirst($item[$column_name]);
                case 'productName':
                    return ucfirst($item[$column_name]);
                case 'Probability':
                    return $item[$column_name];
                case 'CreatedBy':
                    return ucfirst($item[$column_name]);
                case 'milestoneName':
                    return ucfirst($item[$column_name]);
                case 'milestoneStatus':
                    return ucfirst($item[$column_name]);
                case 'OpportunityOwner':
                    return ucfirst($item[$column_name]);
                case 'ExpectedCloseDate':
                    return $item[$column_name];
                case 'ModifiedAt':
                     if(!empty($item[$column_name])&&$item[$column_name]!='0000-00-00 00:00:00'){
                        return date('m/d/Y',strtotime($item[$column_name]));
                    }else{
                        return 'None';
                    }
                case 'action':
                    return $item[$column_name];
                case 'notes':
                    return $item[$column_name];
                case 'OpportunityAmount':
                    return $item['OpportunityCurrency'].''.number_format($item[$column_name], 2, '.', ',');
                case 'AffiliateName':
                    
                    $current_user = wp_get_current_user();
                    $user_roles = $current_user->roles;
                    if (!in_array('iris_affiliate_users', $user_roles)) {
                        return $item[$column_name];
                    } else {
                        return '';
                    }
                    return $item[$column_name];
                case 'LeadGroup':
                    return $item[$column_name];
                default:
                    return print_r($item, true);
        }
    } 

    function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" class="deleteItemID" name="deleteItem[]" value="'.$item['OpportunityIDs'].'"/>',
            $this->_args['singular'], //Let's simply repurpose the table's singular label ("plugin")
            /* $2%s */
            $item['OpportunityID'] //The value of the checkbox should be the record's id
        );
    }

    function column_OpportunityID($item){
        return '<a target="_blank" href="admin.php?page=manage-opportunity&id='.$item['OpportunityID'].'">'.$item['OpportunityID'].'</a>';
    }

    
    function column_action($item){
        $current_user = wp_get_current_user();
        $user_roles = $current_user->roles;
        if(in_array('administrator', $user_roles)){ 
            return '<button data-opportunity_id="'.$item['OpportunityID'].'" class="delete_opportunity" type="button" title="Remove"> <i class="fa-solid fa-trash"></i></button>';
        }
    }

    /**
     * Override the parent columns method. Defines the columns to use in your listing table
     *
     * @return Array
     */
    public function get_columns()
    {
        
        $current_user = wp_get_current_user();
        $user_roles = $current_user->roles;
        
        
        
        
        
        if(!in_array("iris_affiliate_users", $user_roles) ) {
           
            
            $columns = array(
                'OpportunityID' => 'ID',
                'OpportunityName' => 'Opportunity Name',
                'productName' => 'Product',
                'Probability' => 'Probability',
                'milestoneStatus' => 'Stage',
                'OpportunityAmount' => 'Amount',
                'CreatedAt' => 'Created Date',
                'ExpectedCloseDate' => 'Close Date',
                'ModifiedAt' => 'Last Activity',
                'notes' => 'Notes',
                'AffiliateName' => 'Lead Source',
                'action' => 'Action'
                
            );
        }else{
            
            $columns = array(
                'OpportunityID' => 'ID',
                'OpportunityName' => 'Opportunity Name',
                'productName' => 'Product',
                'Probability' => 'Probability',
                'milestoneStatus' => 'Stage',
                'OpportunityAmount' => 'Amount',
                'CreatedAt' => 'Created Date',
                'ExpectedCloseDate' => 'Close Date',
                'ModifiedAt' => 'Last Activity',
                'notes' => 'Notes',
                'action' => 'Action'
                
            );
        }
        
        
        if(isset($_REQUEST['download_excel_custom_report'])&&!empty($_REQUEST['download_excel_custom_report'])){
            unset($columns['cb']);
            unset($columns['notes']);
            unset($columns['ModifiedAt']);
        }

        return $columns;
    }   

    /**
     * @var array
     *
     * Array contains slug columns that you want hidden
     *
     */

    private $hidden_columns = array();

    /**
     * edit a customer record.
     *
     * @param int $id customer ID
     */
    public static function edit_invoice($id) {
        $_SESSION['is_session'] = 0;
        //wp_redirect("admin.php?page=process-invoice.php&edit_invoice=" . $id);
        wp_redirect("admin.php?page=custom-invoice.php&edit_invoice=" . $id);
    }



    private function count_data($search)
    {
        global $wpdb;        
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id', $current_user_id);
        $user_roles = $user_data->roles;
        $opportunity_table = $wpdb->prefix.'opportunities';
        $start_date = $search['start_date'];
        $end_date = $search['end_date'];
        $start_date = date('Y-m-d', strtotime($start_date));
        $end_date = date('Y-m-d', strtotime($end_date));

        $search_date_range = array();
        $additional_table = $wpdb->prefix.'erc_iris_leads_additional_info';
        $business_info_table = $wpdb->prefix.'erc_business_info';
        $opportunity_table = $wpdb->prefix.'opportunities';
        $opportunity_product_table = $wpdb->prefix.'opportunity_products';
        $product_table = $wpdb->prefix.'crm_products';
        $milestone_table = $wpdb->prefix.'milestones';
        $milestone_status_table = $wpdb->prefix.'milestone_stages';
        $next_step_table = $wpdb->prefix.'next_step';
        $currency_table = $wpdb->prefix.'currencies';
        $assign_user_table = $wpdb->prefix.'erc_iris_leads_assign_users';

        $where = " WHERE 1=1 ";
        $where .= ' AND ( (lower(buss.business_legal_name) NOT LIKE "test%") AND (lower(addinfo.category) NOT LIKE "%test%") AND addinfo.lead_group != "ERC - Test Group") ';
        $where .= ' AND opp.DeletedAt IS NULL';
        $lead_assign_join = '';
        $where_assign = '';
        if (in_array("iris_sales_agent", $user_roles) ) {
            $lead_assign_join = " LEFT JOIN $assign_user_table ON addinfo.lead_id=$assign_user_table.lead_id";
            $where_assign .= " OR $assign_user_table.userid='".$current_user_id."'";
            $where .= " AND (addinfo.sales_user_id = '" . $current_user_id . "' || addinfo.sales_support_id = '" . $current_user_id . "' ".$where_assign.")";
        }
        if(isset($search['start_date']) && !empty($search['start_date']) && isset($search['end_date']) && !empty($search['end_date'])){
            $where .= " AND (STR_TO_DATE(opp.CreatedAt, '%Y-%m-%d') BETWEEN STR_TO_DATE('".$start_date."', '%Y-%m-%d') AND STR_TO_DATE('".$end_date."', '%Y-%m-%d')) ";
        }

        if(isset($search['milestonefilter']) && !empty($search['milestonefilter'])){
            $where .= " AND mile.milestone_id  = ".$search['milestonefilter']."";
        }

        if(isset($search['milestoneStatusfilter']) && !empty($search['milestoneStatusfilter'])){
            $where .= " AND mile_status.milestone_stage_id = ".$search['milestoneStatusfilter']."";
        }

        if(isset($search['productnamefilter']) && !empty($search['productnamefilter'])){
            $where .= " AND prod.ProductID = ".$search['productnamefilter']."";
        }

        if(isset($search['OpportunityName']) && !empty($search['OpportunityName'])){
            $where .= " AND opp.OpportunityID ='".$search['OpportunityName']."'";
        }
        if(isset($search['AffiliateUserName']) && !empty($search['AffiliateUserName'])){
            $where .= " AND addinfo.affiliate_user_id ='".$search['AffiliateUserName']."'";
        }
        if(isset($search['LeadGroupName']) && !empty($search['LeadGroupName'])){
            $where .= " AND addinfo.lead_group ='".$search['LeadGroupName']."'";
        }
        if(isset($search['OpportunityAmount']) && !empty($search['OpportunityAmount'])){
            $where .= " AND opp.OpportunityAmount='".$search['OpportunityAmount']."'";
        }
        if(isset($search['business_name']) && !empty($search['business_name'])){
            $where .= " AND opp.LeadID='".$search['business_name']."'";
        }

        if(isset($search['reportFilter']) && !empty($search['reportFilter'])){

            switch($search['reportFilter']) {
                
                /*case 'current':
                    // Get the current month and year
                    $currentMonth = date('m');
                    $currentYear = date('Y');
                    
                    // Calculate the first and last day of the current month
                    $firstDayOfMonth = date('Y-m-01');
                    $lastDayOfMonth = date('Y-m-t');
                    
                    // Append the condition to your SQL query
                    $where .= " AND (opp.expectedCloseDate >= '$firstDayOfMonth' AND opp.expectedCloseDate <= '$lastDayOfMonth')";
                    break;
                
                case 'next':
                    // Get the first day of the next month
                    $firstDayOfNextMonth = date('Y-m-01', strtotime('+1 month'));

                    // Get the last day of the next month
                    $lastDayOfNextMonth = date('Y-m-t', strtotime('+1 month'));

                    // Append the condition for the next month to your SQL query
                    $where .= " AND (opp.expectedCloseDate >= '$firstDayOfNextMonth' AND opp.expectedCloseDate <= '$lastDayOfNextMonth')";
                    break;*/
                
                case 'mine':
                    $where .= " AND opp.createdBy = " . get_current_user_id();
                    break;
                
                case 'new':
                    // Get current date and time
                    $current_datetime = date('Y-m-d H:i:s');
            
                    // Get the day of the week (1 for Monday, 2 for Tuesday, etc.)
                    $day_of_week = date('N');
            
                    // If today is Monday, use today's date, otherwise get last Monday's date
                    if ($day_of_week == 1) {
                        $date_monday = date('Y-m-d 00:00:00'); // Start of the day
                    } else {
                        $date_monday = date('Y-m-d H:i:s', strtotime('last Monday'));
                    }
            
                    // Add condition to retrieve rows where createdAt is equal to or greater than last Monday
                    $where .= " AND opp.createdAt >= '" . esc_sql($date_monday) . "'";
                    break;

                case 'pipeline':
                    // Add condition to exclude opportunity_won and opportunity_lose
                    $where .= " AND mile.milestone_id != 116 AND mile.milestone_id != 117";
                    break;

                case 'won':
                    $where .= " AND mile.milestone_id = 116";
                    break;

                case 'lost':
                    $where .= " AND mile.milestone_id = 117";
                    break;

            }

        }
        if(isset($search['masterSalesFilter']) && !empty($search['masterSalesFilter'])){
            $where .= " AND opp.createdBy = '".get_current_user_id()."'";
        }
        if(isset($search['owner']) && !empty($search['owner'])){
            $where .= " AND opp.CreatedBy='".$search['owner']."'";
        }
        if(in_array("iris_affiliate_users", $user_roles) ) {
            $where .= ' AND (
                addinfo.erc_reffrer_id = ' . get_current_user_id() . ' OR 
                addinfo.affiliate_user_id = ' . get_current_user_id() . ' OR 
                opp.createdBy = ' . get_current_user_id() . '
            )';
        }
        elseif(in_array("iris_employee", $user_roles) ) {
            $where .= ' AND addinfo.employee_id = '.get_current_user_id();
        }
        /*$sql = "SELECT opp.*, prod.Title as productName,prod.ProductID as product_ID, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus, ns.next_step  as NextStepName, buss.business_legal_name as AccountName, mile.milestone_id, mile_status.milestone_stage_id, ns.next_step, curr.currency_code as currencyName
FROM $opportunity_table opp 
    left join $opportunity_product_table oppPro on opp.OpportunityID = oppPro.opportunity_id 
    left join $product_table prod on oppPro.product_id = prod.ProductID 
    left join $milestone_table mile on oppPro.milestone_id = mile.milestone_id
    left join $milestone_status_table mile_status on oppPro.milestone_stage_id = mile_status.milestone_stage_id 
    left join $next_step_table ns on opp.NextStep = ns.next_step_id 
    left join $business_info_table buss on opp.LeadID = buss.lead_id   
    left join $currency_table curr on opp.OpportunityCurrency = curr.currency_id " . $where;*/

        $sql = "SELECT opp.*, prod.Title as productName,prod.ProductID as product_ID, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus, ns.next_step  as NextStepName, buss.business_legal_name as AccountName, mile.milestone_id, mile_status.milestone_stage_id, ns.next_step, curr.currency_code as currencyName,
                addinfo.affiliate_user_id,addinfo.erc_reffrer_id,addinfo.lead_group,addinfo.lead_group as LeadGroup
                FROM $opportunity_table opp
                LEFT JOIN $opportunity_product_table oppPro ON opp.OpportunityID = oppPro.opportunity_id
                LEFT JOIN $product_table prod ON oppPro.product_id = prod.ProductID
                LEFT JOIN $milestone_table mile ON oppPro.milestone_id = mile.milestone_id
                LEFT JOIN $milestone_status_table mile_status ON oppPro.milestone_stage_id = mile_status.milestone_stage_id
                LEFT JOIN $next_step_table ns ON opp.NextStep = ns.next_step_id
                LEFT JOIN $additional_table addinfo ON opp.LeadID = addinfo.lead_id
                LEFT JOIN $business_info_table buss ON opp.LeadID = buss.lead_id
                LEFT join $currency_table curr on opp.OpportunityCurrency = curr.currency_id
                LEFT JOIN eccom_op_contact_lead cl ON opp.LeadID = cl.lead_id
                LEFT JOIN eccom_op_email_contact ec ON cl.contact_id = ec.contact_id
                LEFT JOIN eccom_op_emails email_primary ON ec.email_id = email_primary.id
                LEFT JOIN eccom_op_phone_contact pc ON cl.contact_id = pc.contact_id
                LEFT JOIN eccom_op_phone phone_primary ON pc.phone_id = phone_primary.id
                $lead_assign_join
            $where
            GROUP BY opp.LeadID, oppPro.product_id
            ";

        $opportunity = $wpdb->get_results($sql);
        $Opportunity_data = array();
        if(!empty($opportunity)){
            $i=1;
            foreach($opportunity as $value){
                $is_record = 0;
                if(in_array('iris_sales_agent', $user_roles)){
                    if($value->CreatedBy > 0){
                        $user_data = get_user_by('id',$value->CreatedBy);
                        $userroles = $user_data->roles;
                    }
                    else{
                        $userroles = array();
                    }
                    if($value->CreatedBy == $current_user_id || (!in_array('iris_sales_agent', $userroles) && !in_array('master_sales', $userroles))){
                        $is_record = 1;
                    }
                }else{
                    $is_record = 1;
                }
                if($is_record == 1){
                    $Opportunity_data[] = $i;
                    $i++;
                }
            }
        }
        return $Opportunity_data;
    }
    
    /**
         * Method for name column
         *
         * @param array $item an array of DB data
         *
         * @return string
         */
    private function custom_table_data($search)
    {
        global $wpdb;
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id', $current_user_id);
        $user_roles = $user_data->roles;      
        if (isset($search['paged'])) {
            $paged = $search['paged'];
            $max_limit = $paged * $this->limitPerpage;
            $min_limit = $max_limit - $this->limitPerpage;
            $limit = $min_limit . ",$this->limitPerpage";
        } else {
            $limit = "0,$this->limitPerpage";
        }
        
        $currencies = $wpdb->get_results("SELECT * FROM {$wpdb->prefix}currencies WHERE {$wpdb->prefix}currencies.deleted_at IS NULL ORDER BY {$wpdb->prefix}currencies.currency_id DESC");


        //$where = " WHERE ( (lower({$wpdb->prefix}erc_business_info.business_legal_name) NOT LIKE 'test%') AND (lower({$wpdb->prefix}erc_iris_leads_additional_info.category) NOT LIKE '%test%') AND {$wpdb->prefix}erc_iris_leads_additional_info.lead_group != 'ERC - Test Group')";
        $lead_id = '';
        $bsns_name = '';
        $lead_status = '';
        $authorized_signatory = '';
        $business_phone = '';
        $business_email = '';
        $stage = '';
        $confidence_level = '';
        $empids = array();
        $i = 0;
            
        $start_date = $search['start_date'];
        $end_date = $search['end_date'];
        $start_date = date('Y-m-d', strtotime($start_date));
        $end_date = date('Y-m-d', strtotime($end_date));


        $search_date_range = array();
        $additional_table = $wpdb->prefix.'erc_iris_leads_additional_info';
        $business_info_table = $wpdb->prefix.'erc_business_info';
        $opportunity_table = $wpdb->prefix.'opportunities';
        $opportunity_product_table = $wpdb->prefix.'opportunity_products';
        $product_table = $wpdb->prefix.'crm_products';
        $milestone_table = $wpdb->prefix.'milestones';
        $milestone_status_table = $wpdb->prefix.'milestone_stages';
        $next_step_table = $wpdb->prefix.'next_step';
        $currency_table = $wpdb->prefix.'currencies';
        $user_table = $wpdb->prefix.'users';
        $assign_user_table = $wpdb->prefix.'erc_iris_leads_assign_users';
        
        $where = " WHERE 1=1 ";
        $where .= ' AND ( (lower(buss.business_legal_name) NOT LIKE "test%") AND (lower(addinfo.category) NOT LIKE "%test%") AND addinfo.lead_group != "ERC - Test Group") ';
        $where .= ' AND opp.DeletedAt IS NULL';
        $lead_assign_join = '';
        $where_assign = '';
        if (in_array("iris_sales_agent", $user_roles) ) {
            $lead_assign_join = " LEFT JOIN $assign_user_table ON addinfo.lead_id=$assign_user_table.lead_id";
            $where_assign .= " OR $assign_user_table.userid='".$current_user_id."'";
            $where .= " AND (addinfo.sales_user_id = '" . $current_user_id . "' || addinfo.sales_support_id = '" . $current_user_id . "' ".$where_assign.")";
        }
        if(isset($search['start_date']) && !empty($search['start_date']) && isset($search['end_date']) && !empty($search['end_date'])){
            $where .= " AND (STR_TO_DATE(opp.CreatedAt, '%Y-%m-%d') BETWEEN STR_TO_DATE('".$start_date."', '%Y-%m-%d') AND STR_TO_DATE('".$end_date."', '%Y-%m-%d')) ";
        }

        if(isset($search['milestonefilter']) && !empty($search['milestonefilter'])){
            $where .= " AND mile.milestone_id  = ".$search['milestonefilter']."";
        }

        if(isset($search['milestoneStatusfilter']) && !empty($search['milestoneStatusfilter'])){
            $where .= " AND mile_status.milestone_stage_id = ".$search['milestoneStatusfilter']."";
        }

        if(isset($search['productnamefilter']) && !empty($search['productnamefilter'])){
            $where .= " AND prod.ProductID = ".$search['productnamefilter']."";
        }

        if(isset($search['OpportunityName']) && !empty($search['OpportunityName'])){
            $where .= " AND opp.OpportunityID ='".$search['OpportunityName']."'";
        }
        if(isset($search['AffiliateUserName']) && !empty($search['AffiliateUserName'])){
            $where .= " AND addinfo.affiliate_user_id ='".$search['AffiliateUserName']."'";
        }
        if(isset($search['LeadGroupName']) && !empty($search['LeadGroupName'])){
            $where .= " AND addinfo.lead_group ='".$search['LeadGroupName']."'";
        }
        if(isset($search['OpportunityAmount']) && !empty($search['OpportunityAmount'])){
            $where .= " AND opp.OpportunityAmount='".$search['OpportunityAmount']."'";
        }
        if(isset($search['business_name']) && !empty($search['business_name'])){
            $where .= " AND opp.LeadID='".$search['business_name']."'";
        }
       if(in_array("iris_affiliate_users", $user_roles) ) {
            $where .= ' AND (
                addinfo.erc_reffrer_id = ' . get_current_user_id() . ' OR 
                addinfo.affiliate_user_id = ' . get_current_user_id() . ' OR 
                opp.createdBy = ' . get_current_user_id() . '
            )';
        }
        elseif(in_array("iris_employee", $user_roles) ) {
            $where .= ' AND addinfo.employee_id = '.get_current_user_id();
        }
        if(isset($search['reportFilter']) && !empty($search['reportFilter'])){

            switch($search['reportFilter']) {
                
                /*case 'current':
                    // Get the current month and year
                    $currentMonth = date('m');
                    $currentYear = date('Y');
                    
                    // Calculate the first and last day of the current month
                    $firstDayOfMonth = date('Y-m-01');
                    $lastDayOfMonth = date('Y-m-t');
                    
                    // Append the condition to your SQL query
                    $where .= " AND (opp.expectedCloseDate >= '$firstDayOfMonth' AND opp.expectedCloseDate <= '$lastDayOfMonth')";
                    break;
                
                case 'next':
                    // Get the first day of the next month
                    $firstDayOfNextMonth = date('Y-m-01', strtotime('+1 month'));

                    // Get the last day of the next month
                    $lastDayOfNextMonth = date('Y-m-t', strtotime('+1 month'));

                    // Append the condition for the next month to your SQL query
                    $where .= " AND (opp.expectedCloseDate >= '$firstDayOfNextMonth' AND opp.expectedCloseDate <= '$lastDayOfNextMonth')";
                    break;*/
                
                case 'mine':
                    $where .= " AND opp.createdBy = " . get_current_user_id();
                    break;
                
                case 'new':
                    // Get current date and time
                    $current_datetime = date('Y-m-d H:i:s');
            
                    // Get the day of the week (1 for Monday, 2 for Tuesday, etc.)
                    $day_of_week = date('N');
            
                    // If today is Monday, use today's date, otherwise get last Monday's date
                    if ($day_of_week == 1) {
                        $date_monday = date('Y-m-d 00:00:00'); // Start of the day
                    } else {
                        $date_monday = date('Y-m-d H:i:s', strtotime('last Monday'));
                    }
            
                    // Add condition to retrieve rows where createdAt is equal to or greater than last Monday
                    $where .= " AND opp.createdAt >= '" . esc_sql($date_monday) . "'";
                    break;

                case 'pipeline':
                    // Add condition to exclude opportunity_won and opportunity_lose
                    $where .= " AND mile.milestone_id != 116 AND mile.milestone_id != 117";
                    break;

                case 'won':
                    $where .= " AND mile.milestone_id = 116";
                    break;

                case 'lost':
                    $where .= " AND mile.milestone_id = 117";
                    break;

            }

        }
        if(isset($search['masterSalesFilter']) && !empty($search['masterSalesFilter'])){
            $where .= " AND opp.createdBy = '".get_current_user_id()."'";
        }
        if(isset($search['owner']) && !empty($search['owner'])){
            $where .= " AND opp.CreatedBy='".$search['owner']."'";
        }

        // $sql = "SELECT opp.*, oppPro.stage_deletedat, oppPro.milestone_deletedat, oppPro.deletedAt as product_deletedat, prod.Title as productName,prod.ProductID as product_ID, mile.milestone_name as milestoneName, mile_status.stage_name as milestoneStatus, ns.next_step  as NextStepName, buss.business_legal_name as AccountName, mile.milestone_id, mile_status.milestone_stage_id, ns.next_step, curr.currency_code as currencyName FROM $opportunity_table opp left join $opportunity_product_table oppPro on opp.OpportunityID = oppPro.opportunity_id left join $product_table prod on oppPro.product_id = prod.ProductID left join $milestone_table mile on oppPro.milestone_id = mile.milestone_id left join $milestone_status_table mile_status on oppPro.milestone_stage_id = mile_status.milestone_stage_id left join $next_step_table ns on opp.NextStep = ns.next_step_id left join $business_info_table buss on opp.LeadID = buss.lead_id   left join $currency_table curr on opp.OpportunityCurrency = curr.currency_id " . $where;


         $sql = "
            SELECT 
                opp.*,
                oppPro.stage_deletedat,
                oppPro.milestone_deletedat,
                oppPro.deletedAt as product_deletedat,
                prod.Title as productName,
                prod.ProductID as product_ID,
                mile.milestone_id,
                mile.milestone_name as milestoneName,
                mile.status as milestoneActiveStatus,
                mile.map as milestoneMap,
                mile_status.milestone_stage_id, 
                mile_status.stage_name as milestoneStatus,
                mile_status.deleted_at as StageDeleteStatus,
                mile_status.status as StageActiveStatus,
                ns.next_step  as NextStepName,
                buss.business_legal_name as AccountName,
                buss.lead_id as LeadId,
                ns.next_step,
                curr.currency_code as currencyName,
                email_primary.email AS primary_email,
                phone_primary.phone AS primary_phone,
                addinfo.affiliate_user_id, user_table.display_name as AffiliateName, addinfo.erc_reffrer_id,addinfo.lead_group, addinfo.lead_group as LeadGroup
            FROM 
                $opportunity_table opp
                LEFT JOIN $opportunity_product_table oppPro ON opp.OpportunityID = oppPro.opportunity_id
                LEFT JOIN $product_table prod ON oppPro.product_id = prod.ProductID
                LEFT JOIN $milestone_table mile ON oppPro.milestone_id = mile.milestone_id
                LEFT JOIN $milestone_status_table mile_status ON oppPro.milestone_stage_id = mile_status.milestone_stage_id
                LEFT JOIN $next_step_table ns ON opp.NextStep = ns.next_step_id
                LEFT JOIN $additional_table addinfo ON opp.LeadID = addinfo.lead_id 
                LEFT JOIN $user_table user_table ON addinfo.affiliate_user_id = user_table.ID 
                LEFT JOIN $business_info_table buss ON opp.LeadID = buss.lead_id
                LEFT join $currency_table curr on opp.OpportunityCurrency = curr.currency_id
                LEFT JOIN eccom_op_contact_lead cl ON opp.LeadID = cl.lead_id
                LEFT JOIN eccom_op_email_contact ec ON cl.contact_id = ec.contact_id
                LEFT JOIN eccom_op_emails email_primary ON ec.email_id = email_primary.id
                LEFT JOIN eccom_op_phone_contact pc ON cl.contact_id = pc.contact_id
                LEFT JOIN eccom_op_phone phone_primary ON pc.phone_id = phone_primary.id
                $lead_assign_join
            $where
            GROUP BY opp.LeadID, oppPro.product_id
            ";
        
        // If no specific order selected in front-end, reportFilter is pipeline, show oldest to newest post
        if( isset($search['reportFilter']) && $search['reportFilter'] == 'pipeline' ){
            $order_by = ' ORDER BY createdAt ASC ';
        } else {
            $order_by = ' ORDER BY OpportunityID DESC ';
        }

        
        if (isset($_REQUEST['orderby']) && !empty($_REQUEST['orderby']) && isset($_REQUEST['order']) && !empty($_REQUEST['order'])) {
            $order = $_REQUEST['order'];
            $order_by = $_REQUEST['orderby'];
            if(isset($_REQUEST['orderby'])&&$_REQUEST['orderby']=='milestoneStatus'){
               $order_by = 'Probability';
            }
            if (!empty($order)) {
                    $order_by = " ORDER BY $order_by $order ";
            } else {
                    $order_by = " ORDER BY $order_by desc ";
            }
        } //order by action

        if(!empty($search['download_excel_custom_report'])){
                 $sql .= $order_by;
                 // echo '<pre>';
                 //    print_r($_REQUEST);
                 //    echo '</pre>';
                 // echo $sql; exit;
        }else{
                $sql .= $order_by.' LIMIT '.$limit;
        }

        // $sql .= $order_by.' LIMIT '.$limit;

        // echo $sql;
        
        $opportunity = $wpdb->get_results($sql);

        // print_r($opportunity);
        

        $data = array();
        $c = 0;
        $i = 1;
        if (!empty($opportunity)) {
            $count = 0;
            foreach ($opportunity as $key =>$value ) {
                $opp_id = $value->OpportunityID;
                $url = admin_url('admin.php?page=add_edit_opportunity&action=edit&id='.$opp_id);
                $is_record = 0;
                if(in_array('iris_sales_agent', $user_roles)){
                    if($value->CreatedBy > 0){
                        $user_data = get_user_by('id',$value->CreatedBy);
                        $userroles = $user_data->roles;
                    }
                    else{
                        $userroles = array();
                    }
                    if($value->CreatedBy == $current_user_id || (!in_array('iris_sales_agent', $userroles) && !in_array('master_sales', $userroles))){
                        $is_record = 1;
                    }
                }else{
                    $is_record = 1;
                }
                if($is_record == 1){
                    $data[$c]['OpportunityIDs'] = $opp_id;
                    if(!empty($search['download_excel_custom_report'])){
                        $data[$c]['OpportunityID'] = $opp_id;
                    }else{
                        $data[$c]['OpportunityID'] = $opp_id;
                    }
                    $default_none = 'None';
                    if(!empty($search['download_excel_custom_report'])){
                        $data[$c]['OpportunityName'] = $value->OpportunityName ?: $default_none;
                    }else{
                         $data[$c]['OpportunityName'] = '<a target="_blank" href="admin.php?page=manage-opportunity&id='.$opp_id.'">'.($value->OpportunityName ?: $default_none).'</a>';
                    }

                    //$data[$c]['CreatedAt'] = date("m/d/Y H:i:s", strtotime($value->CreatedAt));
					$data[$c]['CreatedAt'] = !empty($value->CreatedAt) ? date("m/d/Y H:i:s", strtotime($value->CreatedAt)) : $default_none;
                    $data[$c]['productName'] = ($value->product_deletedat === null) ? $value->productName : $value->productName . ' (Deleted)';

                    $data[$c]['milestoneName'] = $value->milestoneName;
                    if($value->milestoneActiveStatus == 'inactive'){
                        $data[$c]['milestoneName'] = $value->milestoneName . ' (Inactive)';
                    }elseif($value->milestone_deletedat !== null){
                        $data[$c]['milestoneName'] = $value->milestoneName . ' (Deleted)';
                    }
                    if(empty(@unserialize($value->milestoneMap))){
                        $data[$c]['milestoneName'] = $value->milestoneName . ' (Unassigned)';
                    }elseif(!in_array("opportunity", unserialize($value->milestoneMap))){
                        $data[$c]['milestoneName'] = $value->milestoneName . ' (Unassigned)';
                    }
					
					// If milestoneName is empty after all conditions, set it to 'None'
					if (empty($data[$c]['milestoneName'])) {
						$data[$c]['milestoneName'] = $default_none;
					}					

                    //$data[$c]['milestoneName'] = ($value->milestone_deletedat === null) ? $value->milestoneName : $value->milestoneName . ' (Deleted)';

                    $data[$c]['milestoneStatus'] = $value->milestoneStatus;
                    if($value->StageActiveStatus == 'inactive'){
                        $data[$c]['milestoneStatus'] = $value->milestoneStatus . ' (Inactive)';
                    }elseif($value->stage_deletedat !== null){
                        $data[$c]['milestoneStatus'] = $value->milestoneStatus . ' (Deleted)';
                    }
                    //$data[$c]['milestoneStatus'] = ($value->stage_deletedat === null) ? $value->milestoneStatus : $value->milestoneStatus . ' (Deleted)';
					// If milestoneStatus is empty after all conditions, set it to $default_none
					if (empty($data[$c]['milestoneStatus'])) {
						$data[$c]['milestoneStatus'] = $default_none;
					}
					
					$data[$c]['OpportunityCurrency'] = !empty($value->currencyName) ? $value->currencyName : $default_one;
					
					if (!empty($search['download_excel_custom_report'])) {
						$data[$c]['OpportunityAmount'] = !empty($value->OpportunityAmount) 
							? $value->currencyName . '' . number_format($value->OpportunityAmount, 2, '.', ',') 
							: $default_one;
					} else {
						$data[$c]['OpportunityAmount'] = !empty($value->OpportunityAmount) 
							? $value->OpportunityAmount 
							: $default_one;
					}
                    

                    $user_details = get_user_by( 'id', $value->CreatedBy );
                    $data[$c]['CreatedBy'] = @$user_details->display_name;

					$data[$c]['Probability'] = !empty($value->Probability) ? $value->Probability . '%' : $default_one;
					$data[$c]['Stage'] = !empty($value->Stage) ? $value->Stage : $default_one;
					// $data[$c]['OpportunityCategory'] = !empty($value->OpportunityCategory) ? $value->OpportunityCategory : $default_one;
					$data[$c]['NextStep'] = !empty($value->NextStepName) ? $value->NextStepName : $default_one;
                    
					if (!empty($search['download_excel_custom_report'])) {
						$data[$c]['AccountName'] = !empty($value->AccountName) ? $value->AccountName : $default_none;
					} else {
						$data[$c]['AccountName'] = !empty($value->AccountName) 
							? "<a href='" . admin_url('admin.php?page=iris-fields-v1.php&lead_id=' . $value->LeadID) . "' target='_blank'>" . $value->AccountName . "</a>"
							: $default_none;
					}


                    if(!empty($value->ExpectedCloseDate) && $value->ExpectedCloseDate != '0000-00-00'){
                        $data[$c]['ExpectedCloseDate'] = date("m/d/Y", strtotime($value->ExpectedCloseDate));
                    }else{
                        $data[$c]['ExpectedCloseDate'] = $default_none;
                    }
					
                    if(empty($search['download_excel_custom_report'])){
                        $data[$c]['ModifiedAt'] = $value->ModifiedAt;
                    }

					$data[$c]['AccountEmail'] = !empty($value->primary_email) ? $value->primary_email : $default_none;
					$data[$c]['AccountPhone'] = !empty($value->primary_phone) ? $value->primary_phone : $default_none;

                    // $data[$c]['notes'] = '<span class="custom-notes-call" data-opportunityid="' . $value->OpportunityID . '" id=""><i class="fa-regular fa-comment" style="font-size: 16px;"></i></span>';
                    $opportunity_id = $value->OpportunityID;
                    $data[$c]['notes'] = '<div style="display: flex;"><span class="view_comment_notes" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-opp_id="'.$opportunity_id.'" title="View"><i class="fa-regular fa-comment"></i></span><span class="comment_btn custom-add-notes" data-toggle="modal" data-opp_id="'.$opportunity_id.'" title="Add"> <i class="fa-solid fa-comment-medical"></i></span></div>';

                        
                    //Below code do not work with sorting please add the below login in initial sql query
                    // $affiliate_user = '';
                    // $affiliate_user_id = $value->affiliate_user_id;
                    
                    // $erc_reffrer_id = $value->erc_reffrer_id;
                    
                    // if ($affiliate_user_id > 0) {
                    //     $AffiliateData = $wpdb->get_row("SELECT display_name FROM {$wpdb->prefix}users WHERE ID = '" . $affiliate_user_id . "'");
                    //     if (!empty($AffiliateData)) {
                    //         $affiliate_user = $AffiliateData->display_name;
                    //     }
                    // }
                    
                    // if ($erc_reffrer_id > 0) {
                    //     $AffiliateData = $wpdb->get_row("SELECT display_name FROM {$wpdb->prefix}users WHERE ID = '" . $erc_reffrer_id . "'");
                    //     if (!empty($AffiliateData)) {
                    //         $affiliate_user = $AffiliateData->display_name;
                    //     }
                    // }

                    // $data[$c]['AffiliateName'] = $affiliate_user;
					$data[$c]['AffiliateName'] = !empty($value->AffiliateName) ? $value->AffiliateName : $default_none;
					$data[$c]['LeadGroup'] = !empty($value->LeadGroup) ? $value->LeadGroup : $default_none;

                }

                $c++;
            }
        }
        return $data;
    }

    function get_bulk_actions() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
          if(in_array('administrator', $user_roles) || in_array('echeck_staff', $user_roles) || in_array('echeck_client', $user_roles) || in_array('master_sales', $user_roles)){ 
            // return array('delete' => __( 'Delete', ''),);
            return array();
          }else{
            return array();
          }      
    }

    function process_bulk_action() {
        global $wpdb;
            if (isset($_REQUEST['action2']) && $_REQUEST['action2']=='delete'){
                if(!empty($_REQUEST['deleteItem'])){
                    foreach($_REQUEST['deleteItem'] as $singleOpportunities){
                        $opportunity_table = $wpdb->prefix.'opportunities';
                        $sql = "Delete FROM $opportunity_table where OpportunityID  = ".$singleOpportunities;
                         $business_lead = $wpdb->get_results($sql);
                    }
                }
                
            }
    }


    /**
     * Allows you to sort the data by the variables set in the $_GET
     *
     * @return Mixed
     */
    private function sort_data($a, $b)
    {

        // Set defaults

        $orderby = 'id';

        $order = 'desc';

        // If orderby is set, use this as the sort column



        if (!empty($_GET['orderby'])) {

            $orderby = $_GET['orderby'];
        }



        // If order is set use this as the order

        if (!empty($_GET['order'])) {

            $order = $_GET['order'];
        }



        $result = strnatcmp($a[$orderby], $b[$orderby]);

        if ($order === 'asc') {

            return $result;
        }



        return -$result;
    }

    


        public function search_box_new()
    {
        $search_data = array();
        ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<?php  
global $wpdb;
$products_table = $wpdb->prefix.'crm_products';    
$all_products =  $wpdb->get_results("SELECT $products_table.productID,$products_table.title,$products_table.unitPrice,$products_table.deletedBy FROM $products_table WHERE $products_table.DeletedAt IS NULL AND $products_table.status = 'active' GROUP BY $products_table.productID");



$milestone_table = $wpdb->prefix.'milestones';    
$all_milestone =  $wpdb->get_results("SELECT $milestone_table.milestone_id,$milestone_table.milestone_name,$milestone_table.product_id FROM $milestone_table WHERE $milestone_table.deleted_at IS NULL AND $milestone_table.status = 'active' GROUP BY $milestone_table.milestone_id");

 $milestone_status_table = $wpdb->prefix.'milestone_stages';    
 $all_milestone_status =  $wpdb->get_results("SELECT $milestone_status_table.milestone_stage_id,$milestone_status_table.stage_name,$milestone_status_table.milestone_id,$milestone_status_table.status FROM $milestone_status_table WHERE $milestone_status_table.deleted_at IS NULL AND $milestone_status_table.Status = 'active' GROUP BY $milestone_status_table.milestone_stage_id");

$next_step_table = $wpdb->prefix.'next_step';    
$all_next_step =  $wpdb->get_results("SELECT $next_step_table.next_step_id,$next_step_table.next_step,$next_step_table.deleted_at FROM $next_step_table WHERE $next_step_table.deleted_at IS NULL  GROUP BY $next_step_table.next_step_id");

?>
                    <div id="overlay" onclick="overlay_off()"></div>
                    <form method="get" action="" id="search_all_form">
                        <input type="hidden" name="page" value="<?php echo $_GET['page'] ?>">

                        <div class="row align-items-center mb-3">
                            <div class="col-md-3 search_field">
                                <img src="https://design.echeck.money/portal/wp-content/plugins/echeck-theme111/assets/img/icon/Search.svg" alt="" class="icon">
                                <input type="text" placeholder="Search.." id="show_lead_search" autocomplete="off" class="open">
                                <input type="submit" style="visibility: hidden;position:absolute;">
                            </div>


                            <div class="col-md-2 report_filter">
                                <?php $report_filter = isset($search_data['reportFilter']) ? $search_data['reportFilter'] : ''; ?>
                                <select name="select_report_dropdown" id="report_filter" class="form-control">
                                    <option value="">Filter Report</option>
                                    <!--<option value="current" <?php if ($report_filter == 'current') { echo "selected"; } ?>>Closing This Month</option>
                                    <option value="next" <?php if ($report_filter == 'next') { echo "selected"; } ?>>Closing Next Month</option>-->
                                    <option value="mine" <?php if ($report_filter == 'mine') { echo "selected"; } ?>>My Opportunities</option>
                                    <option value="new" <?php if ($report_filter == 'new') { echo "selected"; } ?>>New This Week</option>
                                    <option value="pipeline" <?php if ($report_filter == 'pipeline') { echo "selected"; } ?>>Opportunity Pipeline</option>
                                    <option value="won" <?php if ($report_filter == 'won') { echo "selected"; } ?>>Won</option>
                                    <option value="lost" <?php if ($report_filter == 'lost') { echo "selected"; } ?>>Lost</option>                                     
                                </select>
                            </div>

                            <div class="col-md-7 invoice_date_range" style="margin-top: 10px;">

                                <p class="invoice_date_filter" style="margin-left: -20px;">
                                    <span>Date From : <input type="text" id="date_timepicker_start" class="date_from" value="" name="start_date" pattern="\d{2}/\d{2}/\d{4}" placeholder="MM/DD/YYYY" autocomplete="off" onkeydown="return false"></span>

                                    <span>Date To : <input type="text" id="date_timepicker_end" value="" name="end_date" class="date_to" pattern="\d{2}/\d{2}/\d{4}" placeholder="MM/DD/YYYY" autocomplete="off" onkeydown="return false"></span>

                                    <input type="button" class="button create_date_submit date_timepicker_submit" value="Submit">
                                    <input type="button" class="button create_date_submit" id="date_timepicker_reset" value="Reset">

                                </p>

                            </div>

                        </div>



                        <div class="popup-overlay search-popup">
                            <div class="popup-content">
                                <form method="post" class="container">
                                    <div class="search_header">
                                        <h4>Search</h4>
                                        <span class="close">
                                            <i class="fa-solid fa-xmark"></i>
                                        </span>
                                    </div>
                                    <div class="row ">
                                        <div class="col-md-6">
                                            <?php
                                            global $wpdb;
                                            $bsns_table = $wpdb->prefix . 'erc_business_info';

                                            $where = " WHERE ( (lower($bsns_table.business_legal_name) NOT LIKE 'test%') AND $bsns_table.business_legal_name!='')";
                                            $lead_sql = "SELECT lead_id,business_legal_name FROM $bsns_table $where";


                                            $business_lead = $wpdb->get_results($lead_sql);
                                            ?>
                                            <select id="business-name-select" class="search-popup-input-select" name="business_name" placeholder="Select Business Name" >
                                                <option value="">Select Business</option>
                                                <?php
                                                foreach ($business_lead as $key => $value) {
                                                    if (isset($search_data['business_name'])) {
                                                        $business_name = $search_data['business_name'];
                                                        if ($business_name == $value->lead_id) {
                                                            $sel = "selected";
                                                        } else {
                                                            $sel = '';
                                                        }
                                                    } else {
                                                        $sel = '';
                                                    }
                                                    ?>
                                                        <option value="<?php echo htmlentities($value->lead_id); ?>" <?php echo $sel; ?>><?= $value->business_legal_name; ?></option>
                                                <?php } ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <select name="productnamefilter" id="productnamefilter" class="search-popup-input-select" data-no="<?php echo $i; ?>">
                                                        <option value="">Select Product</option>
                                                        <?php 
                                                        foreach ($all_products as $pro_key => $pro_value) { 
                                                            $productid = $pro_value->productID;     
                                                            
                                                           $product_title = $pro_value->title;     
                                                           $product_amount = $pro_value->unitPrice;     
                                                           $product_status = $pro_value->status;     
                                                            echo '<option  value="'.$productid.'" >'.$product_title.'</option>';
                                                                 }
                                                             ?>         
                                            </select>
                                        </div>
                                         
                                <?php if(current_user_can('master_sales') || current_user_can('master_ops')){  ?>
                                        
                                        <div class="col-md-6 mt-2" id="ownerView" >
                                            <?php
                                            global $wpdb;
                                            $sales_table = $wpdb->prefix . 'erc_sales_team';
                                            $salesUsers = "SELECT userid, full_name as name
                                                                FROM $sales_table
                                                                WHERE active_sales_agent = 1 AND userid NOT IN (41599,43341,43342,44022,44064,111318,111321,111317,111320) ORDER BY full_name ASC ";

                                            $sales_users = $wpdb->get_results($salesUsers,ARRAY_A);
                                            ?>
                                            <select id="owner-select" class="search-popup-input-select" name="owner" placeholder="Select Owner" >
                                                <option value="">Select Owner</option>
                                                <?php
                                                foreach ($sales_users as $key => $value) {
                                                    if (isset($search_data['owner'])) {
                                                        $search_owner = $search_data['owner'];
                                                        if ($search_owner == $value['userid']) {
                                                            $sel = "selected";
                                                        } else {
                                                            $sel = '';
                                                        }
                                                    } else {
                                                        $sel = '';
                                                    }
                                                    ?>
                                                        <option value="<?php echo htmlentities($value['userid']); ?>" <?php echo $sel; ?>><?= $value['name']; ?></option>
                                                <?php } ?>
                                            </select>
                                        </div>
                                <?php } ?>
                                        <div class="col-md-6 mt-2" style="display:none;">
                                            <select name="milestonefilter" id="milestonefilter" class="search-popup-input-select" data-no="<?php echo $i; ?>">
                                                        <option value="">Select Milestone</option>
                                                        <?php 
                                                        foreach ($all_milestone as $pro_key => $pro_value) {

                                                            $milestone_id = $pro_value->milestone_id;     
                                                           $milestone_title = $pro_value->milestone_name;
                                                            echo '<option  value="'.$milestone_id.'" >'.$milestone_title.'</option>';
                                                                 }
                                                             ?>         
                                            </select>
                                        </div>
                                        <div class="col-md-6 mt-2">
                                            <select name="milestoneStatusfilter" id="milestoneStatusfilter" class="search-popup-input-select" data-no="<?php echo $i; ?>">
                                                        <option value="">Select Stage</option>
                                                        <?php 
                                                        foreach ($all_milestone_status as $pro_key => $pro_value) {

                                                            $milestone_stage_id = $pro_value->milestone_stage_id;     
                                                           $milestone_status_title = $pro_value->stage_name;         
                                                            echo '<option  value="'.$milestone_stage_id.'" >'.$milestone_status_title.'</option>';
                                                                 }
                                                             ?>         
                                            </select>
                                        </div>
                                        <div class="col-md-6 mt-2">
                                            <?php
                                            global $wpdb;
                                            $bsns_table = $wpdb->prefix . 'erc_business_info';
                                            $opportunity_table = $wpdb->prefix.'opportunities';

                                            $where = " WHERE ( (lower($bsns_table.business_legal_name) NOT LIKE 'test%') AND $bsns_table.business_legal_name!='')";
                                            $lead_sql = "SELECT lead_id,business_legal_name FROM $bsns_table $where";


                                            $business_lead = $wpdb->get_results($lead_sql);
                                            $op_sql = "SELECT OpportunityID, OpportunityName FROM $opportunity_table ";
                                            $opportunity = $wpdb->get_results($op_sql);

                                            ?> 
                                            <select id="OpportunityName" class="search-popup-input-selectt" name="OpportunityName" placeholder="Opportunity Name" >
                                                <option value="">Opportunity Name</option>
                                                <?php
                                                foreach ($opportunity as $key => $value) {
                                                    ?>
                                                        <option value="<?php echo $value->OpportunityID; ?>"><?= $value->OpportunityName; ?></option>
                                                <?php } ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mt-2">
                                            <?php
                                            global $wpdb;
                                            $additional_table = $wpdb->prefix . 'erc_iris_leads_additional_info';
                                            $user_table = $wpdb->prefix.'users';

                                            
                                            $Affiliate_sql = "SELECT $user_table.ID, $user_table.display_name FROM $additional_table LEFT join $user_table on $additional_table.affiliate_user_id = $user_table.ID where $user_table.display_name is not null GROUP by $user_table.ID";


                                            $Affiliate_user = $wpdb->get_results($Affiliate_sql);

                                            ?> 
                                            <select id="AffiliateUserName" class="search-affiliate-user" name="AffiliateUserName" placeholder="Select Affiliate User" >
                                                <option value="">Select Affiliate Name</option>
                                                <?php
                                                foreach ($Affiliate_user as $key => $value) {
                                                    ?>
                                                        <option value="<?php echo $value->ID; ?>"><?= $value->display_name; ?></option>
                                                <?php } ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mt-2">
                                            <?php
                                            global $wpdb;
                                            $additional_table = $wpdb->prefix . 'erc_iris_leads_additional_info';

                                            
                                            $lead_group_sql = "SELECT $additional_table.lead_group FROM $additional_table  where $additional_table.lead_group is not null GROUP by $additional_table.lead_group";


                                            $lead_group_data = $wpdb->get_results($lead_group_sql);

                                            ?> 
                                            <select id="LeadGroupName" class="user-group-user" name="LeadGroupName" placeholder="Select Lead Group" >
                                                <option value="">Select Lead Group</option>
                                                <?php
                                                foreach ($lead_group_data as $key => $value) {
                                                    ?>
                                                        <option value="<?php echo $value->lead_group; ?>"><?= $value->lead_group; ?></option>
                                                <?php } ?>
                                            </select>
                                        </div>
                                        <div class="col-md-6 mt-2">
                                            <input step="0.01" id="OpportunityAmount" name="OpportunityAmount" placeholder="Amount" value="<?php
                                            if (isset($search_data['OpportunityAmount'])) {
                                                echo $search_data['OpportunityAmount'];
                                            }
                                            ?>" class="search-popup-input-select" min="0" oninput="javascript: if (this.value.length > this.maxLength) this.value = this.value.slice(0, this.maxLength);" type = "number" maxlength = "15">
                                        </div>    
                                    </div>
                    
                                    <div class="row mt-3">
                                        <div class="col-md-12 text-center">
                                            <input type="button" id="btn_search_submit" class="search-popup-submit-btn" value="Search" />
                                            <input type="button" class="search-popup-reset-btn" value="Clear" id="reset_Form_button" onclick="resetAffForm()"/>
                                        </div>
                                    </div>
                            </div>
                        </div>
                    </form>

                    <div class="send_decl_confirm_box_wraper send_decl_box" id="send_decl_box" style="display:none;"><a href="javascript:void(0);" class="close_send_decl_popup" onclick="close_send_decl_popup()">x</a>
                        <div class="confirm_box_inner send_decl_box_inner" style="padding-bottom:36px;"> <h5>Send Declaration</h5><hr class="popup-hr">
                            <div class="decl_btn">
                                <div class="decl_btn_yes">
                                    <a href="javascript:void(0);" class="send_withsdgr btn btn-primary" data-lead_id="">Declaration With SDGR</a>
                                </div>
                                <div class="decl_btn_no">
                                    <a href="javascript:void(0);" class="send_nosdgr btn btn-warning" data-lead_id="">Declaration W/O SDGR</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sent_decl_confirm_box_wraper sent_decl_box" id="sent_decl_box" style="display:none;"><a href="javascript:void(0);" class="close_sent_decl_popup" onclick="close_sent_decl_popup()">x</a>
                        <div class="confirm_box_inner sent_decl_box_inner" style="padding-bottom:36px;"><hr class="popup-hr">
                            <div class="sent_decl_btn">
                            </div>
                        </div>
                    </div>


    
                    <script type="text/javascript">
                        function resetAffForm() {
                            jQuery("#reset_Form_button").val('Clearing...');
                            var site_url = '<?php echo get_site_url() ?>';
                            window.location.href = site_url + '/wp-admin/admin.php?page=<?php  echo $_GET['page']  ?>';
                        }
                        jQuery(".open").on("click", function () {
                            jQuery('.status_box').hide();
                            jQuery(".popup-overlay, .popup-content").addClass("active");
                            jQuery(".search_lead_id").focus();
                            jQuery('#overlay').show();
                        });

                        jQuery(".close").on("click", function () {
                            jQuery(".popup-overlay, .popup-content").removeClass("active");
                            jQuery('#overlay').hide();
                        });

                        function overlay_off() {
                            jQuery(".close").trigger("click");
                            jQuery('#overlay').hide();
                        }
                        
                    </script>

                    <?php
    }

    // check confidence 
    public function check_confidence_user(){
        global $wpdb;
     $confidence_user = 0;
     $option_table = $wpdb->prefix.'onedrive_options';
      $selected_user = $wpdb->get_var("SELECT meta_value FROM $option_table WHERE meta_key='notes_confidence_users' ");
      if(!empty($selected_user)){
        $selected_users = explode(",",$selected_user);
        $current_user_id = get_current_user_id();
        if(in_array($current_user_id,$selected_users)){
            $confidence_user = 1;
        }
      }
      return $confidence_user;
    }

    public function export_leads_button($search_data)
    {
        ?>

<script type="text/javascript">
    jQuery(document).ready(function () {
        jQuery("#business-name-select").select2();
        jQuery("#OpportunityName").select2();
        jQuery("#AffiliateUserName").select2();
        jQuery("#LeadGroupName").select2();
        jQuery("#productnamefilter").select2();
        jQuery("#search_milestonefilter").select2();
        jQuery("#search_milestoneStatusfilter").select2();
        jQuery("#milestonefilter").select2();
        jQuery("#milestoneStatusfilter").select2();
        jQuery("#report_filter").select2();
        jQuery("#owner-select").select2();
        
        jQuery(document).on("click", ".export_leads", function () {
            //jQuery(this).text('Please wait..');
            var reportFilter = jQuery('#report_filter').val();
            var lead_id = jQuery("#lead_id").val();
            var bsns_name = jQuery("#bsns_name").val();
            var group_filter = jQuery("#group-select").val();
            var lead_status = jQuery("#lead_status").val();
            var authorized_signatory = jQuery("#authorized_signatory").val();
            ;
            var business_phone = jQuery("#business_phone").val();
            var business_email = jQuery("#business_email").val();
            var stage = jQuery("#stage").val();
            var affiliate_user = jQuery("select[name=affiliate_user]").val();
            var account_iris_user = jQuery("select[name=account_iris_user]").val();

            var from = jQuery("input[name=start_date]").val();
            var to = jQuery("input[name=end_date]").val();

            console.log('from:' + from);
            console.log('to:' + to);

            var confidence_level = "<?php echo $confidence_level; ?>";
            jQuery(this).css('pointer-events', 'none');
            jQuery.ajax({
                url: '<?php echo admin_url('admin-ajax.php'); ?>',
                method: 'post',
                data: {action: 'export_lead_detailed_data', reportFilter: reportFilter, lead_id: lead_id, business_name: business_name, lead_status: lead_status, group_filter: group_filter, authorized_signatory: authorized_signatory, business_phone: business_phone, business_email: business_email, stage: stage, confidence_level: confidence_level, affiliate_user: affiliate_user, account_iris_user: account_iris_user, from: from, to: to},
                success(response) {
                    jQuery(".export_leads").text('Export');
                    jQuery(".export_leads").css('pointer-events', '');
                    var downloadLink = document.createElement("a");
                    var responseData = jQuery.trim(response);
                    var fileData = ['\ufeff' + responseData];

                    var blobObject = new Blob(fileData, {
                        type: "text/csv;charset=utf-8;"
                    });

                    var url = URL.createObjectURL(blobObject);
                    downloadLink.href = url;
                    var currentDate = new Date();

                    //var dateString = currentDate.toISOString().slice(0, 10); // YYYY-MM-DD

                    // Extract month, day, and year
                    var month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Month is zero-based
                        var day = String(currentDate.getDate()).padStart(2, '0');
                        var year = currentDate.getFullYear();

                        // Combine into MM/DD/YYYY format
                        var dateString = month + '/' + day + '/' + year;

                    var timeString = currentDate

                            .toLocaleTimeString("en-US", {

                                hour12: false,

                                hour: "2-digit",

                                minute: "2-digit",

                                second: "2-digit",

                            })

                            .replace(/:/g, "_");

                    var filename = "Opportunity_Report_" + dateString + "_" + timeString + ".csv";
                    downloadLink.download = filename;

                    /*
                     * Actually download CSV
                     */
                    document.body.appendChild(downloadLink);
                    downloadLink.click();
                    document.body.removeChild(downloadLink);
                }
            });
        });

    });


    jQuery(document).ready(function () {

        jQuery('.date_from').datetimepicker({
            format: 'm/d/Y',
            autoclose: true,
            orientation: 'bottom',
            timepicker: false,
            autocomplete: 'off',
            maxDate: 'now',
            onChangeDateTime: function (dp, $input) {
                console.log(new Date($('.date_to').val()));
                if (new Date($('.date_to').val()) < new Date($input.val())) {
                    alert("Date To can not be less than Date from");
                    $('.date_to').val('');
                    $('.date_from').val('');
                }
            }
        });


        jQuery('.date_to').datetimepicker({
            format: 'm/d/Y',
            autoclose: true,
            orientation: 'bottom',
            timepicker: false,
            autocomplete: 'off',
            maxDate: 'now',
            onChangeDateTime: function (dp, $input) {
                console.log(new Date($('.date_from').val()));
                if (new Date($input.val()) < new Date($('.date_from').val())) {
                    alert("Date To can not be less than Date from");
                    $('.date_to').val('');
                    $('.date_from').val('');
                }
            }
        });


        jQuery('#date_timepicker_reset').on('click', function () {
            jQuery('.date_to')
                    .datetimepicker('reset');

            jQuery('.date_from')
                    .datetimepicker('reset');

        });

//set date by JS
<?php $start_date_js = !empty($_REQUEST['start_date']) ? $_REQUEST['start_date'] : '' ?>
        var start_date = '<?php echo $start_date_js; ?>';
        jQuery('#date_timepicker_start').val(start_date);

<?php $end_date_js = !empty($_REQUEST['end_date']) ? $_REQUEST['end_date'] : '' ?>
        var end_date = '<?php echo $end_date_js; ?>';
        jQuery('#date_timepicker_end').val(end_date);

    });


    jQuery(document).ready(function ($) {
        $('#search_all_form').on('submit', function (e) {
            if ($('.date_from').val() && !$('.date_to').val()) {
                e.preventDefault();
                alert('Please select a Date To.');
            }

            if ($('.date_to').val() && !$('.date_from').val()) {
                e.preventDefault();
                alert('Please select a Date From.');
            }
        });
    });

        jQuery(document).on('click','.delete_opportunity', function () {
            var opportunity_id = jQuery(this).data('opportunity_id');
            // console.log(opportunity_id);
            swal({
                            title: "Are you sure?",
                            text: "Yes, I want to delete this Opportunity.",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                                confirm: "Delete",
                            },
                            content: {
                                element: "input",
                                attributes: {
                                    type: "checkbox",
                                    id: "agreeCheckbox"
                                },
                            },
                            dangerMode: true,
            })
            .then((willDelete) => {
                if (willDelete && $('#agreeCheckbox').prop('checked')) {
                    $(this).attr('disabled',true); 
                    $("#loader_box").show();
                        jQuery.ajax({
                            url:'<?php echo admin_url('admin-ajax.php'); ?>',
                            method:'post',
                            data:{action: 'delete_opportunity', opportunity_id:opportunity_id },
                            success(response){
                                window.location.href = '?page=opportunities';
                            },
                            error: function(jqXHR, textStatus, errorThrown) {        
                                window.location.href = '?page=opportunities';
                                jQuery(".popup-overlay, .popup-content").removeClass("active");
                                jQuery('#overlay').hide();
                                jQuery('.pleasewait').hide();    
                                },
                        });
            }else{

            } 
            //$('.swal-modal').removeClass('crm-erp-opportunities-delete-swal');
        });   
        $('.swal-modal').addClass('crm-erp-opportunities-delete-swal'); 
    });        
</script>
<?php
    }
    function get_sortable_columns() {
        $sortable_columns = array();
        $columns  = $this->get_columns();
        foreach ($columns as $key => $value) {
            if($key != 'cb' && $key != 'action'){
                $sortable_columns[$key] = array($key, true);
            }
        }
        return $sortable_columns;
    }

    /**
     * @Override of prepare_items method
     *
     */

    function prepare_items() {

        /**
         * How many records for page do you want to show?
         */
        $per_page = $this->limitPerpage;

        /**
         * Define of column_headers. It's an array that contains:
         * columns of List Table
         * hiddens columns of table
         * sortable columns of table
         * optionally primary column of table
         */
   
        // $this->process_bulk_action();
        $columns  = $this->get_columns();
        $hidden   = $this->hidden_columns;
        $sortable = $this->get_sortable_columns();
        $this->_column_headers = array($columns, $hidden, $sortable);
        
         
        
        
        /**
         * Following lines are only a sample with a static array
         * in a real situation you can get data
         * from a REST architecture or from database (using $wpdb)
         */
        $data = $this->custom_table_data($_REQUEST);
        
        function usort_reorder( $a, $b ) {

            $orderby = ( ! empty( $_REQUEST['orderby'] ) ) ? $_REQUEST['orderby'] : 'OpportunityID';
            $order = ( ! empty( $_REQUEST['order'] ) ) ? $_REQUEST['order'] : 'asc';

            $result = strcmp( $a[ $orderby ], $b[ $orderby ] );
            return ( 'asc' === $order ) ? $result : -$result;
        }
        // usort( $data, 'usort_reorder' );

        /**
         * Get current page calling get_pagenum method
         */

        $total_items = count($data);
        $this->items = $data;

        /**
         * Call to _set_pagination_args method for informations about
         * total items, items for page, total pages and ordering
         */
         $search = $_REQUEST;
         $total_items = $this->count_data($search);
         $total_items = count($total_items);

         
        $this->set_pagination_args(
            array(

                'total_items'   => $total_items,
                'per_page'      => $per_page,
                'total_pages'   => ceil( $total_items / $per_page ),
                'orderby'       => ! empty( $_REQUEST['orderby'] ) && '' != $_REQUEST['orderby'] ? $_REQUEST['orderby'] : 'OpportunityID',
                'order'         => ! empty( $_REQUEST['order'] ) && '' != $_REQUEST['order'] ? $_REQUEST['order'] : 'asc'
            )
        );


    }

    protected function pagination($which)
    {

        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id', $current_user_id);
        $user_roles = $user_data->roles;

        $filter_invoice_status = "All";

        if (empty($this->_pagination_args)) {

            return;
        }

        if (!empty($_REQUEST['filter_invoice_status'])) {

            $filter_invoice_status = $_REQUEST['filter_invoice_status'];
        }

        $total_items = $this->_pagination_args['total_items'];

        $total_pages = $this->_pagination_args['total_pages'];

        $infinite_scroll = false;

        if (isset($this->_pagination_args['infinite_scroll'])) {

            $infinite_scroll = $this->_pagination_args['infinite_scroll'];
        }



        if ('top' === $which && $total_pages > 1) {

            $this->screen->render_screen_reader_content('heading_pagination');
        }



        $output = '<span class="displaying-num">' . sprintf(
            /* translators: %s: Number of items. */

            _n('%s item', '%s items', $total_items),
            number_format_i18n($total_items)
        ) . '</span>';

        $current = $this->get_pagenum();

        $removable_query_args = wp_removable_query_args();

        $current_url = set_url_scheme('http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']);

        $current_url = remove_query_arg($removable_query_args, $current_url);

        if (!empty($_GET['business_name'])) {
            $current_url = add_query_arg('business_name', $_GET['business_name'], $current_url);
        }

        if (!empty($_GET['lead_id'])) {
            $current_url = add_query_arg('lead_id', $_GET['lead_id'], $current_url);
        }



        if (!empty($_GET['authorized_signatory'])) {
            $current_url = add_query_arg('authorized_signatory', $_GET['authorized_signatory'], $current_url);
        }

        if (!empty($_GET['productnamefilter'])) {
            $current_url = add_query_arg('productnamefilter', $_GET['productnamefilter'], $current_url);
        }


        if (!empty($_GET['business_phone'])) {
            $current_url = add_query_arg('business_phone', $_GET['business_phone'], $current_url);
        }

        if (!empty($_GET['business_email'])) {
            $current_url = add_query_arg('business_email', $_GET['business_email'], $current_url);
        }

        if (!empty($_GET['stage'])) {
            $current_url = add_query_arg('stage', $_GET['stage'], $current_url);
        }

        if (!empty($_GET['lead_status'])) {
            $current_url = add_query_arg('lead_status', $_GET['lead_status'], $current_url);
        }

        if (!empty($_GET['start_date'])) {
            $current_url = add_query_arg('start_date', $_GET['start_date'], $current_url);
        }

        if (!empty($_GET['end_date'])) {
            $current_url = add_query_arg('end_date', $_GET['end_date'], $current_url);
        }

        if (!empty($_GET['select_report_dropdown'])) {
            $current_url = add_query_arg('select_report_dropdown', $_GET['select_report_dropdown'], $current_url);
        }


        $page_links = array();

        $total_pages_before = '<span class="paging-input">';

        $total_pages_after = '</span></span>';

        $disable_first = false;

        $disable_last = false;

        $disable_prev = false;

        $disable_next = false;

        if (1 == $current) {

            $disable_first = true;

            $disable_prev = true;
        }

        if (2 == $current) {

            $disable_first = true;
        }

        if ($total_pages == $current) {

            $disable_last = true;

            $disable_next = true;
        }

        if ($total_pages - 1 == $current) {

            $disable_last = true;
        }



        if ($disable_first) {

            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&laquo;</span>';
        } else {

            $page_links[] = sprintf(
                "<a class='first-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url(remove_query_arg('paged', $current_url)),
                __('First page'),
                '&laquo;'
            );
        }



        if ($disable_prev) {

            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&lsaquo;</span>';
        } else {

            $page_links[] = sprintf(
                "<a class='prev-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url(add_query_arg('paged', max(1, $current - 1), $current_url)),
                __('Previous page'),
                '&lsaquo;'
            );
        }



        if ('bottom' === $which) {

            $html_current_page = $current;

            $total_pages_before = '<span class="screen-reader-text">' . __('Current Page') . '</span><span id="table-paging" class="paging-input"><span class="tablenav-paging-text">';
        } else {

            $html_current_page = sprintf(
                "%s<input class='current-page' id='current-page-selector' type='text' name='paged' value='%s' size='%d' aria-describedby='table-paging' /><span class='tablenav-paging-text'>",
                '<label for="current-page-selector" class="screen-reader-text">' . __('Current Page') . '</label>',
                $current,
                strlen($total_pages)
            );
        }

        $html_total_pages = sprintf("<span class='total-pages'>%s</span>", number_format_i18n($total_pages));

        $page_links[] = $total_pages_before . sprintf(
            /* translators: 1: Current page, 2: Total pages. */

            _x('%1$s of %2$s', 'paging'),
            $html_current_page,
            $html_total_pages
        ) . $total_pages_after;

        if ($disable_next) {

            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&rsaquo;</span>';
        } else {

            $page_links[] = sprintf(
                "<a class='next-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url(add_query_arg(array('paged' => min($total_pages, $current + 1), 'filter_invoice_status' => $filter_invoice_status), $current_url)),
                __('Next page'),
                '&rsaquo;'
            );
        }



        if ($disable_last) {

            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&raquo;</span>';
        } else {

            $page_links[] = sprintf(
                "<a class='last-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url(add_query_arg(array('paged' => $total_pages, 'filter_invoice_status' => $filter_invoice_status), $current_url)),
                __('Last page'),
                '&raquo;'
            );
        }



        $pagination_links_class = 'pagination-links';

        if (!empty($infinite_scroll)) {

            $pagination_links_class .= ' hide-if-js';
        }

        $output .= "\n<span class='$pagination_links_class'>" . implode("\n", $page_links) . '</span>';

        if ($total_pages) {

            $page_class = $total_pages < 2 ? ' one-page' : '';
        } else {

            $page_class = ' no-pages';
        }

        $this->_pagination = "<div class='tablenav-pages{$page_class}'>$output</div>";

        echo $this->_pagination;
    }



    /**
     * @Override of display method
     */

    function display() {

        /**
         * Adds a nonce field
         */
        wp_nonce_field( 'ajax-custom-list-nonce', '_ajax_custom_list_nonce' );

        /**
         * Adds field order and orderby
         */
        echo '<input type="hidden" id="order" name="order" value="' . $this->_pagination_args['order'] . '" />';
        echo '<input type="hidden" id="orderby" name="orderby" value="' . $this->_pagination_args['orderby'] . '" />';

        parent::display();
    }

    /**
     * @Override ajax_response method
     */

    function ajax_response() {

        $this->prepare_items();

        extract( $this->_args );
        extract( $this->_pagination_args, EXTR_SKIP );

        ob_start();
        if ( ! empty( $_REQUEST['no_placeholder'] ) )
            $this->display_rows();
        else
            $this->display_rows_or_placeholder();
        $rows = ob_get_clean();

        ob_start();
        $this->print_column_headers();
        $headers = ob_get_clean();

        ob_start();
        $this->pagination('top');
        $pagination_top = ob_get_clean();

        ob_start();
        $this->pagination('bottom');
        $pagination_bottom = ob_get_clean();

        $response = array( 'rows' => $rows );
        $response['pagination']['top'] = $pagination_top;
        $response['pagination']['bottom'] = $pagination_bottom;
        $response['column_headers'] = $headers;

        if ( isset( $total_items ) )
            $response['total_items_i18n'] = sprintf( _n( '1 item', '%s items', $total_items ), number_format_i18n( $total_items ) );

        if ( isset( $total_pages ) ) {
            $response['total_pages'] = $total_pages;
            $response['total_pages_i18n'] = number_format_i18n( $total_pages );
        }

        die( json_encode( $response ) );
    }

    function export_leads_to_excel($filter_invoice_status)
    {
        $search = $_REQUEST;
        $data = $this->custom_table_data($_REQUEST);
        $data = $this->format_date_for_exl_csv($data);
        $column_headers = $this->get_columns();
        $column_index = 'A';
        ob_end_clean();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        foreach ($column_headers as $column_key => $column_name) {
            if ($column_name && !in_array($column_name, ['Book A Call', 'Agreement', 'Call / Text', 'Action'])) {
                $sheet->setCellValue($column_index . '1', $column_name);
                $column_index++;
            }
        }
        $row_index = 2;
        foreach ($data as $row_data) {
            $column_index = 'A';
            foreach ($column_headers as $column_key => $column_name) {
                ob_end_clean();
                $accounting_columns = ['OpportunityAmount']; 

                $display_data = $row_data[$column_key];
                $display_data = htmlspecialchars_decode($display_data);
                $display_data = str_replace('&#039;', "'", $display_data);
                $display_data = str_replace('&amp;', "&", $display_data);

                // If the column is one of the accounting columns, format it accordingly
                if (in_array($column_key, $accounting_columns)) { // Replace 'amount' with the actual key for your amount column
                    //$formatted_amount = formatCurrency($display_data, true); // Format the amount
                    //echo $display_data;
                    $normalized_amount = str_replace(['$', ','], '', $display_data); // Normalize the amount for numeric formatting
                    $sheet->setCellValue($column_index . $row_index, (float)$normalized_amount); // Set numeric value
                    // //Set the number format to accounting format
                    $sheet->getStyle($column_index . $row_index)
                    ->getNumberFormat()
                    ->setFormatCode('_("$"* #,##0.00_);_("$"* \(#,##0.00\);_("$"* "-"??_);_(@_)');

					setlocale(LC_NUMERIC, 'en_US');
					setlocale(LC_MONETARY, 'en_US');
            
                }else{
                    $sheet->setCellValue($column_index . $row_index, $display_data);
                    
                }
                $column_index++;
            }
            $row_index++;
        }


        date_default_timezone_set('America/New_York');

        // Get the current date and time in the desired format
        //$currentDate = date('Y-m-d');
        $currentDate = date('m/d/Y');
        $currentTime = (new DateTime())->format('H-i-s');
        $fileName = "Opportunity_Report_$currentDate" . "_" . "$currentTime.xlsx";

        ob_end_clean();
        $writer = new Xlsx($spreadsheet);

        ob_end_clean();
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . urlencode($fileName) . '"');

        ob_end_clean();
        $writer->save('php://output');
        exit;

    }

    function format_date_for_exl_csv($data)
    {
        foreach ($data as &$item) {
            if (!empty($item['lead_id'])) {
                $item['lead_id'] = $item['id'];
                $item['business_legal_name'] = $item['business_legal_name_unf'];
                $item['claim_filed_amount'] = $item['claim_filed_amount_export'];
                $item['action'] = '';
                $item['call'] = '';
                $item['view_agreement'] = '';
                $item['sales_user'] = $item['sales_user_export'];
                $item['sales_support'] = $item['sales_support_export'];
            }
        }
        return $data;
    }

}



function wp_list_page() {

 //include(WP_CONTENT_DIR . '/plugins/affiliate_portal/page-templates/header.php');
    $wp_list_table = new Opportunity_Report_Table();
    $wp_list_table->search_box_new();
    $wp_list_table->export_leads_button($_GET);
    $confidence_user = $wp_list_table->check_confidence_user();
    global $wpdb;

    ?>
            <div class="wrap woocommerce">
            

            <div class="loader_box pleasewait" style="display: none;">
                <div class="loading">
                    <p class="loading__text">Please Wait. We are fetching the data.</p>
                    <div class="loading__bar"></div>
                </div>
            </div>
            <!-- <div class="d-flex align-items-center">
                <div class="invoice_exports mr-2">
                    <a href="admin.php?page=add_edit_opportunity&action=add" class="add-opp-custom-icon export_invoice_excel export_leads"><i class="fa-solid fa-plus"></i> Create Opportunity</a>
                </div>
                <div class="invoice_exports">
                    <form method="post" onsubmit="return onSubmitDownloadExcelForm();">
                        <input type="hidden" name="page" value="detailed_leads_report.php">
                        <i class="fa-solid fa-file-export export_icon_btn"></i>
                        <input type="button" name="download_excel_custom_report"
                            class="common_export_btn" id="excelButton" value="Export">
                    </form>
                </div>
            </div> 
            <a href="javascript:void(0);" class="status_btn_export export_invoice_excel export_products">Export</a>

        -->

            <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
            <script type="text/javascript">
                jQuery(document).ready(function() {
                    jQuery(document).on("click",'.custom-notes-call', function() {
                        var opportunityid = $(this).data('opportunityid');
                        $("#add-new-custom-notes #opportunity_id").val(opportunityid);
                        $('.note-response').html("");
                        $('.note-response').css('display', 'none');
                        $('.error-response').css('display', 'none');
                        $("#add-new-custom-notes").modal('show');

                    });
                    
                    // Remove class when user inputs something
                    $("#add-new-custom-notes #notes-input").on("input", function() {
                        if ($(this).val().trim() !== "") {
                            $(this).removeClass("red-border");
                        } else {
                            $(this).addClass("red-border");
                        }
                    });
                });
                
                //--------- notes script -----
                jQuery(document).ready(function() {

                            $(document).on("click",".custom-add-notes",function() {
                                var opp_id = $(this).data('opp_id');
                                $("#add-new-custom-notes #opportunity_id").val(opp_id);
                                $('.note-response').html("");
                                $('.note-response').css('display', 'none');
                                $('.error-response').css('display', 'none');
                                $("#add-new-custom-notes").modal('show');

                            });

                            $(document).on('click','.close-popup-notes',function() {
                                $("#add-new-custom-notes #opportunity_id").val('');
                                $("#add-new-custom-notes #notes-input").val('');
                                $('.note-response').css('display', 'none');
                                $('.error-response').css('display', 'none');
                                $("#add-new-custom-notes").modal('hide');
                            });
                        });

                        $(document).on('click', '#create-note', function() {
                        $(this).text('Please wait...').prop('disabled', true);
                        var notes = $("#notes-input").val().trim();

                        if (notes == '' || notes == ' ' || notes == null) {
                            jQuery("#add-new-custom-notes #notes-input").css("border", "1px solid red !important");
                            jQuery("#notes-input").focus();
                            jQuery("#notes-input").attr("placeholder", "Please enter notes");
                            // jQuery("#notes-input").next().remove();
                            jQuery("#notes-error").show();
                            $(this).text('Submit').prop('disabled', false);
                            return false;
                        }else{
                            jQuery("#notes-error").hide();
                        }

                        var opportunity_id = $("#add-new-custom-notes #opportunity_id").val();
                        var created_by = $("#created_by").val();
                
                        var confidence_notes_access = jQuery("#confidence_user_check:checked").val();
                        if(typeof confidence_notes_access === "undefined"){
                            confidence_notes_access = 0;
                        }else{
                            confidence_notes_access = 1;
                        }

                        $.ajax({
                            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/add_edit_notes',
                            type: 'POST',
                            dataType: 'Json',
                            data: { action:'add_custom_note_data',note: notes, opp_product_id: opportunity_id,user_id: created_by,confidence_notes_access:confidence_notes_access,note_type:'opportunity'},
                            success: function(response) {
                                let parsedResponse = JSON.parse(response);
                                $('#create-note').text('Submit').prop('disabled', false);
                                if (parsedResponse.add === 1) {
                                    
                                    $("#notes-input").val('');
                                    $("#add-new-custom-notes #opportunity_id").val('');
                                    jQuery("#confidence_user_check").prop('checked',false);
                                    $("#add-new-custom-notes").modal('hide');
                                    swal("Success", "Note added successfully.", "success");
                                    // location.reload();
                                } else {
                                    swal("Error", "Failed to add note.", "error");
                                    // location.reload();
                                }
                            }
                        });
                    });
                // ----- Notes create js end ------        
                
                // ----- Notes listing js start ------
                jQuery(document).on('click', '.view_comment_notes', function () {
                    var opp_id = jQuery(this).data('opp_id');
                    
                    jQuery('#exampleModal_ercdoc_view .modal-body').html('<div style="text-align:center;"><i aria-hidden="true" class="fa fa-spinner fa-spin"></i></div>');
                    $.ajax({
                        url: doc_upload.ajaxurl,
                        type: 'POST',
                        dataType:'json',
                        data: {
                            action: 'view_opp_project_notes',
                            opp_project_id: opp_id,
                            note_type:'opportunity'
                        },
                        success: function (response) {
                            // var json_response = JSON.parse(response);
                            jQuery('#exampleModal_ercdoc_view .modal-body').html('');
                            var inc = 0;

                            jQuery.each(response, function (index, value) {
                                    
                                    jQuery('#exampleModal_ercdoc_view .modal-body').append(value);

                                // if (index != 0) {
                                //     var prepare_html_op = "<div style='display:none;' class='iris_comment_view_all iris_comment_" + index + "'>" + value + "</div>";
                                //     jQuery('#exampleModal_ercdoc_view .modal-body').append(prepare_html_op);
                                // } else {
                                //     var prepare_html_op = "<div class='iris_comment_view_all iris_comment_" + index + "'>" + value + "</div>";
                                //     jQuery('#exampleModal_ercdoc_view .modal-body').append(prepare_html_op);
                                // }
                                 inc++;
                            });

                            if (inc == 1) {
                                jQuery('#exampleModal_ercdoc_view .modal-footer .btn').hide();
                            } else {
                                jQuery('#exampleModal_ercdoc_view .modal-footer .btn').show();
                            }

                        }
                    });
                    jQuery(document).on('click', '#exampleModal_ercdoc_view .modal-footer .btn', function () {
                        jQuery('.iris_comment_view_all').show();
                        jQuery('#exampleModal_ercdoc_view .modal-footer .btn').hide();
                    });
            });   
            // ----- Notes listing end js ------

                        
                        jQuery(document).ready(function(){
                            jQuery('#notes-input').keypress(function (event) {
                                var Length = jQuery("#notes-input").val().length;
                                  maxLen = 1000;
                                if (Length >= maxLen) {
                                    if (event.which != 8) {
                                        //swal("Error", "Only 70 characters are allowed", "error");
                                        return false;
                                    }
                                }
                            });

                            const maxLength = 1000;
                            jQuery('#notes-input').on('input', function() {
                                const remainingChars = maxLength - $(this).val().length;
                                jQuery('#remaining-msg').text(remainingChars + '/1000 characters remaining.');
                            });
                        }); 


                </script>
            <style type="text/css">
            .custom-notes-call {
                color: #fff !important;
                padding: 0.3rem !important;
                background: transparent linear-gradient(89deg, #ff5c00 0%, #ff5c00 100%) 0% 0% no-repeat padding-box;
                text-align: center;
                margin: 0 auto;
                width: 26px;
                height: 26px !important;
                cursor: pointer;
                border-radius: 50% !important;
                line-height: 17px;
                display: inline-block;
            }
            .custom-crm-erp-opp-report #notes {
              width: 7%;
            }
            .red-border {
                border: 1px solid red !important;
            }

            /* comment notes css */
        .column-notes{
                width: 7%;
            }
            #remaining-msg{
                float: right;
            }
            #notes-error{
                display: none;
                color: #ff0000;
            }
            #notes-input{
                margin-bottom:6px;
            }
            .view_comment_notes {
                color: #fff;
                padding: 0.5rem !important;
                /* background: transparent linear-gradient( 89deg , #1658A5 0%, #0089C2 100%) 0% 0% no-repeat padding-box; */
                background: transparent linear-gradient( 89deg , #ff5c00 0%, #ff5c00 100%) 0% 0% no-repeat padding-box;
                text-align: center;
                margin: 0 auto;
                width: 32px;
                height: 32px;
                cursor: pointer;
                border-radius: 50% !important;
                line-height: 17px;
                display: inline-block;
                font-size: 17px;
                margin: 2%;
            }
            .comment_btn {
                color: #fff;
                padding: 0.5rem !important;
                background: #ff5c00;
                text-align: center;
                margin: 0 auto;
                width: 32px;
                display: block;
                height: 32px;
                cursor: pointer;
                border-radius: 50% !important;
                line-height: 17px;
                display: inline-block;
                font-size: 17px;
                margin: 2%;
            }
            #exampleModal_otherdoc_adding_comment #submit-comment-btn,
            #exampleModal_otherdoc_adding_comment .add_approved_rejected_comment_btn,
            #exampleModal_ercdoc_view .btn-warning {
                background: #ff5c00;
                border-color: #ff5c00;
                color: #fff;
            }

            #exampleModal_otherdoc_adding_comment #submit-comment-btn:focus,
            #exampleModal_otherdoc_adding_comment .add_approved_rejected_comment_btn:focus {
                box-shadow:none !important
            }

            .add_approved_project_rejected_comment_btn {
                background: #ff5c00;
                border-color: #ff5c00;
            }
             
            .add_approved_project_rejected_comment_btn:hover {
                background: #ff5c00;
                border-color: #ff5c00;
            }

            .add_approved_project_rejected_comment_btn:active {
                box-shadow: none !important;
            }

            #stc-submit-comment-btn {
                background: #ff5c00;
                border-color: #ff5c00;
            }
             
             
            #stc-submit-comment-btn:hover {
                background: #ff5c00;
                border-color: #ff5c00;
            }
             
             
            #stc-submit-comment-btn:active {
                box-shadow: none !important;
            }

            #exampleModal_ercdoc_view .modal-title {
                margin-top: 5px;
                font-weight: 700;
                color: #1261ab;
                font-size: 17px;
            }


        #exampleModal_ercdoc_view .close {
          position: absolute;
          right: -15px;
          top: -15px;
          background: red;
          text-align: center;
          width: 28px;
          height: 28px !important;
          cursor: pointer;
          border-radius: 50% !important;
          line-height: 10px !important;
          color: #fff;
          opacity: 1 !important;
          padding: 0 !important;
          margin: 0 !important;
          text-shadow: none !important;
        }


        #exampleModal_ercdoc_view .close:not(:disabled):not(.disabled):focus,
        #exampleModal_ercdoc_view .close:not(:disabled):not(.disabled):hover {
          opacity: 1;
        }
        
        .iris_comment_view_all p{
            margin-bottom: 1rem !important;
        }

        .confidential-notes-div{
            background: #FFA500;
            height: 80px;
            padding-left: 5px;
            padding-bottom: 11%;
        }
        .confidential-notes-div span ,.confidential-notes-div p{
            color: #000 !important; 
            font-size: 14px!important;
            font-weight: 400!important;  
        }
        #exampleModal_ercdoc_view .modal-dialog{
            min-width:55%
        }

            </style>

            <div class="modal opportunity-add-new-notes" id="add-new-custom-notes" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-comment"></i> Add Note</h5>
                            <button type="button" class="close-popup-notes close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="show_message_popup">
                                <p class="note-response" style="display: none;"></p>
                                <p class="error-response" style="display: none;">Notes is required.</p>
                            </div>

                            <div class="row">
                                <div class="floating col-md-12">
                                    <input type="hidden" name="opportunity_id" id="opportunity_id">
                                    <input type="hidden" name="created_by" id="created_by" value="<?php echo get_current_user_id(); ?>">

                                    <textarea id="notes-input" class="form-control" rows="5" maxlength="1000" placeholder="Only 1000 characters are allowed" style="resize:none;"></textarea>
                                    <?php if($confidence_user==1){
                                             $confidence_check = '';
                                            if(get_current_user_id() == 44019){ // nedeen id 
                                                  $confidence_check = 'checked';
                                            }
                                    ?>
                                        Mark As Confidential  <input type="checkbox" name="confidence_user_check" id="confidence_user_check" value="1" style="margin-top:0px;" <?php echo $confidence_check; ?>>
                                    <?php } ?>
                                    <p id="remaining-msg">1000/1000 characters remaining.</p>
                                </div>
                            </div>
                            <div class="buttion_next_prev">
                                <button type="button" class="nxt_btn" id="create-note">Submit</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- View comment Modal -->
                <div class="modal fade" id="exampleModal_ercdoc_view" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
                     aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="exampleModalLabel">Notes</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body" style="max-height:200px; overflow-y:auto;">
                                <input type="hidden" name="view_comment_id" class="view_comment_id" id="view_comment_id">
                                <span class="comment_username" style="font-size:14px">User Name</span>
                                <p class="mt-2"></p>
                                <span class="comment_date" style="float: right; font-size:12px">Comment Date:- 05-12-2023</span>
                                <br>
                            </div>
                            <div class="modal-footer justify-content-center">
                                <button type="button" class="btn btn-warning">More Notes</button>
                            </div>
                        </div>
                    </div>
                </div>

                <?php
                echo '<div class="white_card card_height_100 mb_30">';
                echo '<div class="white_card_header ">
            <div class="box_header m-0 new_report_header">
            <div class="title_img">
                <img src="'.get_site_url().'/wp-content/plugins/oc-crm/assets/img/opportunity-icon.png" class="page-title-img" alt="">
                <h4>Opportunity Reports</h4>
            </div>
            <div class="invoice_exports">';
            
                if(current_user_can('master_sales') ){
                    echo '<input type="radio" value="1" name="master-view" id="master-view" style="display: none;">
                    <a href="javascript:void(0);" class="add-opp-custom-icon master-view masterView"> Master View</a>
    
                    <input type="radio" value="0" name="master-view" id="sales-view" style="display: none;">
                    <a href="javascript:void(0);" style="display:none;" class="add-opp-custom-icon masterView sales-view"> Self View</a>';
                }

                echo '<a href="admin.php?page=add_edit_opportunity&action=add" class="add-opp-custom-icon export_invoice_excel export_leads"><i class="fa-solid fa-plus"></i> New Opportunity</a>
                
                <a href="javascript:void(0);" class="status_btn_export export_invoice_excel export_products export_opportunity_list">Export</a> 
                <form method="post" id="export_opportunity_form">
                <input type="hidden" name="page" value="opportunities.php">
                <input type="hidden" name="order" value="desc">
                <input type="hidden" name="orderby" value="OpportunityID">
                <input type="hidden" name="reportFilter" value="">
                <input type="hidden" name="masterSalesFilter" value="">
                <input type="hidden" name="OpportunityName" value="">
                <input type="hidden" name="AffiliateUserName" value="">
                <input type="hidden" name="LeadGroupName" value="">
                <input type="hidden" name="OpportunityAmount" value="">
                <input type="hidden" name="business_name" value="">
                <input type="hidden" name="owner" value="">
                <input type="hidden" name="start_date" value="">
                <input type="hidden" name="end_date" value="">
                <input type="hidden" name="milestonefilter" value="">
                <input type="hidden" name="milestoneStatusfilter" value="">
                <input type="hidden" name="productnamefilter" value="">
                <input type="hidden" name="oppsidfilter" value="">
                <input type="hidden" name="download_excel_custom_report"
                            class="common_export_btn" id="excelButton" value="Export">
                </form>
            </div>
                
        </div>
    </div>';
                echo '<div class="loader_box" id="loader_box" style="display: none;">';
                echo    '<div class="loading">';
                echo        '<p class="loading__text">Please Wait. Deleting Opportunity.</p>';
                echo        '<div class="loading__bar"></div>';
                echo    '</div>';
                echo '</div>';
                echo '<div class="white_card_body custom-crm-erp-opp-report p-15" id="echeck_report_table_wrap">';
                echo '';

                echo '<form method="get" action="" id="opportunity_table_form">';
                echo '<input type="hidden" name="page" value="'.$_GET['page'].'">';
                echo '<div class="custom_lead_details">';

                


                echo '</div>';

                echo '</form>';

                echo '</div>';

                echo '</div></div></div>';

                include(WP_CONTENT_DIR . '/plugins/affiliate_portal/page-templates/footer.php');
                ?>
               
                <?php 
}


/**
 * fetch_ts_script function based from Charlie's original function
 */

function fetch_ts_script() {
    $screen = get_current_screen();

    /**
     * For testing purpose, finding Screen ID
     */

    ?>

    <script type="text/javascript">console.log("<?php echo $screen->id; ?>")</script>
    <script src='https://play.occamsadvisory.com/portal/wp-content/plugins/invoice-create/js/invoice-script.js?ver=5.4'></script>

    <?php if(current_user_can('master_sales') ){ ?>
    <script type="text/javascript">
        // Retrieve master-view state from session when the page loads
            $(document).ready(function() {
                var masterViewState = sessionStorage.getItem('masterViewState');
                var $radios = $('input:radio[name=master-view]');
                $("#ownerView").hide();
                if (masterViewState === 'true') {
                    $('.master-view').click();
ownerView
                    if($radios.is(':checked') === false) {
                        $radios.filter('[value=1]').prop('checked', true);
                    }
                    $("#ownerView").hide();
                    $(".add-opp-custom-icon.master-view").hide();
                    $(".add-opp-custom-icon.sales-view").show();
                }else{
                    $('.sales-view').click();

                    if($radios.is(':checked') === false) {
                        $radios.filter('[value=0]').prop('checked', true);
                    }
                    $("#ownerView").show();
                    $(".add-opp-custom-icon.master-view").show();
                    $(".add-opp-custom-icon.sales-view").hide();
                }
            });
    </script>
    <?php } ?>
    <script type="text/javascript">
        $(document).ready(function() {
            // Handler for the change event of #agreeCheckbox
            $(document).on('change', '#agreeCheckbox', function() {
                // Check if #agreeCheckbox is checked
                if ($(this).prop('checked')) {
                    $(".swal-button--confirm").css("pointer-events", "auto");
                } else {
                    $(".swal-button--confirm").css("pointer-events", "none");
                }
            });

            $(document).on('click', '.swal-button-container:has(.swal-button--confirm)', function() {
                if ($('#agreeCheckbox').prop('checked')) {
                    return true;
                } else {
                    // Check if error message already exists
                    if (!$('.swal-content + p.error-message').length) {
                        // Append the error message if it doesn't exist
                        $('.swal-content').after('<p class="error-message" style="color: red; margin: 20px;">Please agree to Delete the Opportunity!</p>');
                    }
                }
            });
        });
        (function ($) {



            list = {

                /** added method display
                 * for getting first sets of data
                 **/

                display: function() {
                    var deleteItemID = [];
                    $(".deleteItemID:checked").each(function() {
                        deleteItemID.push($(this).val());
                    });
                    $.ajax({

                        url: ajaxurl,
                        dataType: 'json',
                        data: {
                            page: '<?php echo $_GET['page']  ?>',
                            _ajax_custom_list_nonce: $('#_ajax_custom_list_nonce').val(),
                            action: 'opportunity_listing_ajax',
                            deleteItem: deleteItemID,
                            action2: $('#bulk-action-selector-bottom').val(),
                        },
                        success: function (response) {

                            $(".custom_lead_details").html(response.display);
                            $("tbody").on("click", ".toggle-row", function(e) {
                                e.preventDefault();
                                $(this).closest("tr").toggleClass("is-expanded")
                            });

                            list.init();
                        }
                    });

                },

                init: function () {

                    var timer;
                    var delay = 500;

                    $('.tablenav-pages a, .manage-column.sortable a, .manage-column.sorted a').on('click', function (e) {
                        jQuery('.pleasewait').show();
                        e.preventDefault();
                        //alert(1);
                        var query = this.search.substring(1);
                        var data = {
                            page: '<?php echo $_GET['page']  ?>',
                            paged: list.__query( query, 'paged' ) || '1',
                            order: list.__query( query, 'order' ) || 'asc',
                            orderby: list.__query( query, 'orderby' ) || 'OpportunityID',
                            reportFilter: $('#report_filter').val() || '',
                            masterSalesFilter: $('input[name="master-view"]:checked').val(),
                            OpportunityName: $('#OpportunityName').val() || '',
                            AffiliateUserName: $('#AffiliateUserName').val() || '',
                            LeadGroupName: $('#LeadGroupName').val() || '',
                            OpportunityAmount: $('#OpportunityAmount').val() || '',
                            business_name: $('#business-name-select').val() || '',
                            owner: $('#owner-select').val() || '',
                            start_date: $('#date_timepicker_start').val() || '',
                            end_date: $('#date_timepicker_end').val() || '',
                            milestonefilter: $('#milestonefilter').val() || '',
                            milestoneStatusfilter: $('#milestoneStatusfilter').val() || '',
                            productnamefilter: $('#productnamefilter').val() || '',
                            oppsidfilter: $('#oppsidfilter').val() || '',
                        };
                        list.update(data);
                    });

                    $('input[name=paged]').on('keyup', function (e) {
                        jQuery('.pleasewait').show();
                        if (13 == e.which)
                            e.preventDefault();

                        var data = {
                            page: '<?php echo $_GET['page']  ?>',
                            paged: parseInt($('input[name=paged]').val()) || '1',
                            order: $('input[name=order]').val() || 'asc',
                            orderby: $('input[name=orderby]').val() || 'OpportunityID',
                            reportFilter: $('#report_filter').val() || '',
                            masterSalesFilter: $('input[name="master-view"]:checked').val(),
                            OpportunityName: $('#OpportunityName').val() || '',
                            AffiliateUserName: $('#AffiliateUserName').val() || '',
                            LeadGroupName: $('#LeadGroupName').val() || '',
                            OpportunityAmount: $('#OpportunityAmount').val() || '',
                            business_name: $('#business-name-select').val() || '',
                            owner: $('#owner-select').val() || '',
                            start_date: $('#date_timepicker_start').val() || '',
                            end_date: $('#date_timepicker_end').val() || '',
                            milestonefilter: $('#milestonefilter').val() || '',
                            milestoneStatusfilter: $('#milestoneStatusfilter').val() || '',
                            productnamefilter: $('#productnamefilter').val() || '',
                            oppsidfilter: $('#oppsidfilter').val() || '', 
                        };
                        window.clearTimeout(timer);
                        timer = window.setTimeout(function () {
                            list.update(data);
                        }, delay);
                    });



                    $('#email-sent-list').on('submit', function(e){

                        e.preventDefault();

                    });

                },

                /** AJAX call
                 *
                 * Send the call and replace table parts with updated version!
                 *
                 * @param    object    data The data to pass through AJAX
                 */
                update: function (data) {
                    $.ajax({

                        url: ajaxurl,
                        data: $.extend(
                            {
                                page: '<?php echo $_GET['page']  ?>',
                                _ajax_custom_list_nonce: $('#_ajax_custom_list_nonce').val(),
                                action: 'fetch_opportunity_filter',
                            },
                            data
                        ),
                        success: function (response) {
                            var response = $.parseJSON(response);

                            if (response.rows.length)
                                $('#the-list').html(response.rows);
                            if (response.column_headers.length)
                                $('thead tr, tfoot tr').html(response.column_headers);
                            if (response.pagination.bottom.length)
                                $('.tablenav.top .tablenav-pages').html($(response.pagination.top).html());
                            if (response.pagination.top.length)
                                $('.tablenav.bottom .tablenav-pages').html($(response.pagination.bottom).html());

                            list.init();
                            $('#btn_search_submit').val('Search');
                            $('.date_timepicker_submit').val('Search');
                            $('#export_opportunity_form input[name="reportFilter"]').val( $('#report_filter').val() );
                            $('#export_opportunity_form input[name="masterSalesFilter"]').val( $('input[name="master-view"]:checked').val() );
                            $('#export_opportunity_form input[name="OpportunityName"]').val( $('#OpportunityName').val() );
                            $('#export_opportunity_form input[name="order"]').val( data.order );
                            $('#export_opportunity_form input[name="orderby"]').val( data.orderby );
                            $('#export_opportunity_form input[name="AffiliateUserName"]').val( $('#AffiliateUserName').val() );
                            $('#export_opportunity_form input[name="LeadGroupName"]').val( $('#LeadGroupName').val() );
                            $('#export_opportunity_form input[name="OpportunityAmount"]').val( $('#OpportunityAmount').val() );
                            $('#export_opportunity_form input[name="business_name"]').val( $('#business-name-select').val() );
                            $('#export_opportunity_form input[name="owner"]').val( $('#owner-select').val() );
                            $('#export_opportunity_form input[name="start_date"]').val( $('#date_timepicker_start').val() );
                            $('#export_opportunity_form input[name="end_date"]').val( $('#date_timepicker_end').val() );
                            $('#export_opportunity_form input[name="milestonefilter"]').val( $('#milestonefilter').val() );
                            $('#export_opportunity_form input[name="milestoneStatusfilter"]').val( $('#milestoneStatusfilter').val() );
                            $('#export_opportunity_form input[name="productnamefilter"]').val( $('#productnamefilter').val() );
                            $('#export_opportunity_form input[name="oppsidfilter"]').val( $('#oppsidfilter').val() );
                            jQuery(".popup-overlay, .popup-content").removeClass("active");
                            jQuery('#overlay').hide();
                            jQuery('.pleasewait').hide();
                        },
                        error: function(jqXHR, textStatus, errorThrown) {        $('#btn_search_submit').val('Submit');
                                $('.date_timepicker_submit').val('Submit');
                                jQuery(".popup-overlay, .popup-content").removeClass("active");
                                jQuery('#overlay').hide();
                                jQuery('.pleasewait').hide();    

                             },
                    });
                },

                /**
                 * Filter the URL Query to extract variables
                 *
                 * @see http://css-tricks.com/snippets/javascript/get-url-variables/
                 *
                 * @param    string    query The URL query part containing the variables
                 * @param    string    variable Name of the variable we want to get
                 *
                 * @return   string|boolean The variable value if available, false else.
                 */
                __query: function (query, variable) {

                    var vars = query.split("&");
                    for (var i = 0; i < vars.length; i++) {
                        var pair = vars[i].split("=");
                        if (pair[0] == variable)
                            return pair[1];
                    }
                    return false;
                },
            }

            list.display();

            // Update Listing on report change
            $('#report_filter').on('change', function (e) {
                jQuery('.pleasewait').show();
                var buttonValue = $(this).val();
                var data = {
                    buttonValue: buttonValue,
                    page: '<?php echo $_GET['page']  ?>',
                    paged: '1',
                    reportFilter: $('#report_filter').val() || '',
                    masterSalesFilter: $('input[name="master-view"]:checked').val(),
                    OpportunityName: $('#OpportunityName').val() || '',
                    AffiliateUserName: $('#AffiliateUserName').val() || '',
                    LeadGroupName: $('#LeadGroupName').val() || '',
                    OpportunityAmount: $('#OpportunityAmount').val() || '',
                    business_name: $('#business-name-select').val() || '',
                    owner: $('#owner-select').val() || '',
                    start_date: $('#date_timepicker_start').val() || '',
                    end_date: $('#date_timepicker_end').val() || '',
                    milestonefilter: $('#milestonefilter').val() || '',
                    milestoneStatusfilter: $('#milestoneStatusfilter').val() || '',
                    productnamefilter: $('#productnamefilter').val() || '',
                    oppsidfilter: $('#oppsidfilter').val() || '',
                };
                list.update(data);
            });

            
            // Update Listing on master view report change
            $('.master-view').on('click', function (e) {
                jQuery('.pleasewait').show();

                // Save master-view state in session
                sessionStorage.setItem('masterViewState', 'true');
                var $radios = $('input:radio[name=master-view]');
                $radios.filter('[value=1]').prop('checked', true);
                var buttonValue = $(this).val();
                var data = {
                    buttonValue: buttonValue,
                    page: '<?php echo $_GET['page']  ?>',
                    paged: '1',
                    reportFilter: $('#report_filter').val() || '',
                    masterSalesFilter: $('#master-view').val() || '',
                    OpportunityName: $('#OpportunityName').val() || '',
                    AffiliateUserName: $('#AffiliateUserName').val() || '',
                    LeadGroupName: $('#LeadGroupName').val() || '',
                    OpportunityAmount: $('#OpportunityAmount').val() || '',
                    business_name: $('#business-name-select').val() || '',
                    owner: '',
                    start_date: $('#date_timepicker_start').val() || '',
                    end_date: $('#date_timepicker_end').val() || '',
                    milestonefilter: $('#milestonefilter').val() || '',
                    milestoneStatusfilter: $('#milestoneStatusfilter').val() || '',
                    productnamefilter: $('#productnamefilter').val() || '',
                    oppsidfilter: $('#oppsidfilter').val() || '',
                };
                $("#ownerView").hide();
                $(".add-opp-custom-icon.master-view").hide();
                $(".add-opp-custom-icon.sales-view").show();
                list.update(data);
            });

            // Update Listing on sales view report change
            $('.sales-view').on('click', function (e) {
                jQuery('.pleasewait').show();

                // Save master-view state in session
                sessionStorage.setItem('masterViewState', 'false');
                var $radios = $('input:radio[name=master-view]');
                $radios.filter('[value=0]').prop('checked', true);
                var buttonValue = $(this).val();
                var data = {
                    buttonValue: buttonValue,
                    page: '<?php echo $_GET['page']  ?>',
                    paged: '1',
                    reportFilter: $('#report_filter').val() || '',
                    masterSalesFilter: $('#sales-view').val() || '',
                    OpportunityName: $('#OpportunityName').val() || '',
                    AffiliateUserName: $('#AffiliateUserName').val() || '',
                    LeadGroupName: $('#LeadGroupName').val() || '',
                    OpportunityAmount: $('#OpportunityAmount').val() || '',
                    business_name: $('#business-name-select').val() || '',
                    owner: $('#owner-select').val() || '',
                    start_date: $('#date_timepicker_start').val() || '',
                    end_date: $('#date_timepicker_end').val() || '',
                    milestonefilter: $('#milestonefilter').val() || '',
                    milestoneStatusfilter: $('#milestoneStatusfilter').val() || '',
                    productnamefilter: $('#productnamefilter').val() || '',
                    oppsidfilter: $('#oppsidfilter').val() || '',
                };
                $("#ownerView").show();
                $(".add-opp-custom-icon.sales-view").hide();
                $(".add-opp-custom-icon.master-view").show();
                list.update(data);
            });

            //event for filters
            $('.date_timepicker_submit, #btn_search_submit').on('click', function (e) {
                jQuery('.pleasewait').show();
                var buttonValue = $(this).val();
                $(this).val('Wait..');
                 var data = {
                    buttonValue: buttonValue,
                    page: '<?php echo $_GET['page']  ?>',
                    paged: '1',
                    reportFilter: $('#report_filter').val() || '',
                    masterSalesFilter: $('input[name="master-view"]:checked').val(),
                    OpportunityName: $('#OpportunityName').val() || '',
                    AffiliateUserName: $('#AffiliateUserName').val() || '',
                    LeadGroupName: $('#LeadGroupName').val() || '',
                    OpportunityAmount: $('#OpportunityAmount').val() || '',
                    business_name: $('#business-name-select').val() || '',
                    owner: $('#owner-select').val() || '',
                    start_date: $('#date_timepicker_start').val() || '',
                    end_date: $('#date_timepicker_end').val() || '',
                    milestonefilter: $('#milestonefilter').val() || '',
                    milestoneStatusfilter: $('#milestoneStatusfilter').val() || '',
                    productnamefilter: $('#productnamefilter').val() || '',
                    oppsidfilter: $('#oppsidfilter').val() || '',
                };
                list.update(data);
            });

            $('.common_export_btn').on('click', function (e) {
                e.preventDefault();
                //alert(1);
                $(this).val('Wait...');

                var data = {
                    page: '<?php echo $_GET['page']  ?>',
                    download_excel_custom_report: 'yes',
                    reportFilter: $('#report_filter').val() || '',
                    masterSalesFilter: $('input[name="master-view"]:checked').val(),
                    OpportunityName: $('#OpportunityName').val() || '',
                    LeadGroupName: $('#LeadGroupName').val() || '',
                    OpportunityAmount: $('#OpportunityAmount').val() || '',
                    OpportunityAmount: $('#OpportunityAmount').val() || '',
                    business_name: $('#business-name-select').val() || '',
                    owner: $('#owner-select').val() || '',
                    start_date: $('#date_timepicker_start').val() || '',
                    end_date: $('#date_timepicker_end').val() || '',
                    milestonefilter: $('#milestonefilter').val() || '',
                    milestoneStatusfilter: $('#milestoneStatusfilter').val() || '',
                    productnamefilter: $('#productnamefilter').val() || '',
                    oppsidfilter: $('#oppsidfilter').val() || '',
                    
                };
                
                var siteurl = '<?php echo get_site_url(); ?>/wp-admin/admin.php?';
                $.each(data, function(key,val) {
                    siteurl = siteurl+key+'='+val+'&'; 
                });
                window.location.replace(siteurl);
                $(this).val('Export');

            });

            jQuery(document).on("click",'#doaction2', function(e) {
                e.preventDefault();
                if($('#bulk-action-selector-bottom').val()=='delete'){
                    var checkedCount = $('.wp-list-table').find(".deleteItemID:checked").length;
                    if(checkedCount>0){
                        swal({
                            title: "Are you sure?",
                            text: "Yes, I want to delete selected Opportunities.",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                                confirm: "Delete",
                            },
                            content: {
                                element: "input",
                                attributes: {
                                    type: "checkbox",
                                    id: "agreeCheckbox"
                                },
                            },
                            dangerMode: true,
                        })
                        
                        .then((willDelete) => {
                            if (willDelete && $('#agreeCheckbox').prop('checked')) {
                                $(this).attr('disabled',true);
                                $("#loader_box").show();
                                var deleteItemID = [];
                                $(".deleteItemID:checked").each(function() {
                                    deleteItemID.push($(this).val());
                                });
                                
                                jQuery.ajax({
                                    url:'<?php echo admin_url('admin-ajax.php'); ?>',
                                    method:'post',
                                    data:{action: 'delete_opportunity', opportunity_id: deleteItemID},
                                    success(response){
                                        window.location.href = '?page=opportunities';
                                    },
                                    error: function(jqXHR, textStatus, errorThrown) {        $('#btn_search_submit').val('Submit');
                                        $('.date_timepicker_submit').val('Submit');
                                        jQuery(".popup-overlay, .popup-content").removeClass("active");
                                        jQuery('#overlay').hide();
                                        jQuery('.pleasewait').hide();    

                                     },
                                }); 
                            }
                            $('.swal-modal').removeClass('crm-erp-opportunities-delete-swal');
                        });
                        $('.swal-modal').addClass('crm-erp-opportunities-delete-swal');
                        
                    }else{
                        swal({
                            title: "Please select atleast one item",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                    }
                }else{ 
                    swal({
                            title: "Please select some action.",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                }
            });

        })(jQuery);

    </script>
    <script type="text/javascript">
        $(document).ready(function(){
            $('.export_opportunity_list').click(function(){
                $('#export_opportunity_form').submit();
            });
        });
    </script>
    <?php
}

add_action('admin_footer', 'fetch_ts_script');
function fetch_ts_style()
{
    ?>
        <link rel='stylesheet' id='style-css'  href=<?php echo get_site_url() . '/wp-content/plugins/invoice-create/css/invoice-style.css?ver=5.4'; ?> media='all' />
    <?php
}
add_action('admin_header', 'fetch_ts_style');