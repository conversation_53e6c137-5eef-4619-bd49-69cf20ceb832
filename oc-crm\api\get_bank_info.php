<?php
global $wpdb;
$table_name = $wpdb->prefix.'projects';
$data = $request->get_json_params();
$project_id = $data['project_id'];

$project = $wpdb->get_row("SELECT {$wpdb->prefix}projects.lead_id
                                                                    FROM {$wpdb->prefix}projects 
                                                                    WHERE {$wpdb->prefix}projects.deleted_at IS NULL 
                                                                    AND {$wpdb->prefix}projects.project_id = ".$project_id." ");
if(!empty($project)){
	$lead_id = $project->lead_id;
	$ercbi_bank_name = "";
	$ercbi_bank_mailing_address = "";
	$ercbi_city = "";
	$ercbi_state = "";
	$ercbi_zip = "";
	$ercbi_country = "";
	$ercbi_bank_phone = "";
	$ercbi_account_holder_name = "";
	$ercbi_account_type = "";
	$ercbi_other = "";
	$ercbi_aba_routing_number = "";
	$ercbi_account_number = "";
	$ercbi_swift = "";
	$ercbi_iban = "";

	// ----------------- Get data from db ------
	global $wpdb;

	// Bank Info Code here
	$bank_data_arr = [
	    "ercbi_bank_name" => "bank_name",
	    "ercbi_bank_mailing_address" => "bank_mailing_address",
	    "ercbi_city" => "city",
	    "ercbi_state" => "state",
	    "ercbi_zip" => "zip",
	    "ercbi_country" => "country",
	    "ercbi_bank_phone" => "bank_phone",
	    "ercbi_account_holder_name" => "account_holder_name",
	    "ercbi_account_type" => "account_type",
	    "ercbi_other" => "other",
	    "ercbi_aba_routing_number" => "aba_routing_no",
	    "ercbi_account_number" => "account_number",
	    "ercbi_swift" => "swift",
	    "ercbi_iban" => "iban",
	];
	foreach ($bank_data_arr as $var_name => $column_name) {
	    $leadData = $wpdb->get_row(
	        "SELECT $column_name FROM {$wpdb->prefix}erc_bank_info WHERE lead_id = $lead_id"
	    );
	    if (!empty($leadData)) {
	        $column_val = $leadData->$column_name;
	    } else {
	        $column_val = "";
	    }
	    $$var_name = $column_val;
	} // bank loop
	$results['status'] = 1;
	$results['message'] = 'Bank Detail';
	$results['result'][0]['bank_name'] = $ercbi_bank_name;
	$results['result'][0]['bank_mailing_addres'] = $ercbi_bank_mailing_address;
	$results['result'][0]['city'] = $ercbi_city;
	$results['result'][0]['state'] = $ercbi_state;
	$results['result'][0]['zip'] = $ercbi_zip;
	$results['result'][0]['country'] = $ercbi_country;
	$results['result'][0]['bank_phone'] = $ercbi_bank_phone;
	$results['result'][0]['account_holder_name'] = $ercbi_account_holder_name;
	$results['result'][0]['account_type'] = $ercbi_account_type;
	$results['result'][0]['other'] = $ercbi_other;
	$results['result'][0]['aba_routing_number'] = $ercbi_aba_routing_number;
	$results['result'][0]['account_number'] = $ercbi_account_number;
	$results['result'][0]['swift'] = $ercbi_swift;
	$results['result'][0]['iban'] = $ercbi_iban;
}else{
	$results['status'] = 0;
    $results['message'] = 'No project found.';
}
echo json_encode($results);die;