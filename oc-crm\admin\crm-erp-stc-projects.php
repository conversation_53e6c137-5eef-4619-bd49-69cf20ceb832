<?php
/**
 *
 * WP_List_Table class inclusion
 *
 */
if ( ! class_exists( 'WP_List_Table' ) )
    require_once( ABSPATH . 'wp-admin/includes/class-wp-list-table.php' );
/**
 *
 * As <PERSON> suggested in its plugin https://github.com/Askelon/Custom-AJAX-List-Table-Example
 * it's better to set the error_reporting in order to hiding notices to avoid AJAX errors
 *
 */
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
error_reporting( ~E_NOTICE );
class STC_Project_Report_Table extends WP_List_Table {
    /**
     *
     * For this sample we'll use a dataset in a static array
     *
     */
    private $userRole;
    private $limitPerpage;
    private $master_sales_user_id;
    private $master_ops_user_id;

    /**
     *
     * @Override of constructor
     * Constructor take 3 parameters:
     * singular : name of an element in the List Table
     * plural : name of all of the elements in the List Table
     * ajax : if List Table supports AJAX set to true
     *
     */

    function __construct() {
        global $status, $page;

        // Instantiate the OC_Crm_Settings class
        $crmSettings = new OC_Crm_Settings();
        // Retrieve the number of records setting
        $saved_no_of_records = $crmSettings->get_crm_option('opt_num_set_one');

        // Check if the retrieved setting is not empty and is numeric, then set it, else default to 15
        if (!empty($saved_no_of_records) && is_numeric($saved_no_of_records)) {
            $this->limitPerpage = (int) $saved_no_of_records;
        } else {
            $this->limitPerpage = 15; // Default value
        }
        
        $this->master_sales_user_id = array('43456', '43613');
        $this->master_ops_user_id = array('44025');
        parent::__construct(
            array(
                'singular' => 'id',
                //singular name of the listed records
                'plural' => 'ids',
                //plural name of the listed records
                'ajax' => false
                //does this table support ajax?
            )
        );

    }
    /**
     * Define what data to show on each column of the table
     *
     * @param  Array $item        Data
     * @param  String $column_name - Current column name
     *
     * @return Mixed
     */
    public function column_default($item, $column_name)
    {
        switch ($column_name) {
            case 'project_id':
                return $item[$column_name];
            case 'business_name':
                return '<a href="'.get_site_url().'/wp-admin/admin.php?page=iris-fields-v1.php&lead_id='.$item["lead_id"].'">'.ucfirst($item[$column_name]).'</a>';
            case 'contact_card':
                return $item[$column_name];
            case 'product_name':
                return ucfirst($item[$column_name]);
            case 'project_name':
                return ucfirst($item[$column_name]);
            case 'milestone':
                return ucfirst($item[$column_name]);
            case 'milestone_stage':
                return ucfirst($item[$column_name]);
            case 'sales_agent_id':
                return $item[$column_name];
            case 'taxnow_signup_status':
                return $item[$column_name];
            case 'sales_support_id':
                return $item[$column_name];
            case 'created_at':
                return $item[$column_name];
            case 'notes':
                return $item[$column_name];
            case 'collaborator':
                return $item[$column_name];
            case 'action':
                return $item[$column_name];
            default:
                return print_r($item, true);
        }
    } 

    function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" class="deleteItemID" name="deleteItem[]" value="'.$item['project_id'].'"/>',
            $this->_args['singular'], //Let's simply repurpose the table's singular label ("plugin")
            /* $2%s */
            $item['project_id'] //The value of the checkbox should be the record's id
        );
    }

    function column_project_id($item){
        return '<a target="_blank" href="admin.php?page=manage-stc-project&id='.$item['project_id'].'">'.$item['project_id'].'</a>';
    }

    
    function column_action($item){
        $current_user = wp_get_current_user();
        $user_roles = $current_user->roles;
        if(in_array('administrator', $user_roles)){ 
            return '<button data-project_id="'.$item['project_id'].'" class="delete_project" type="button" title="Remove"> <i class="fa-solid fa-trash"></i></button>';
        }
    }

    /**
     * Override the parent columns method. Defines the columns to use in your listing table
     *
     * @return Array
     */
    public function get_columns()
    {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id', $current_user_id);
        $user_roles = $user_data->roles;
        if(in_array('administrator', $user_roles)){
            $columns = array(
                'project_id' => 'ID',
                'created_at' => 'Date',
                'business_name' => 'Business Name',
                'contact_card' => 'Contact Card',
                'product_name' => 'Product',
                'project_name' => 'Project',
                'collaborator' => 'Collaborators',
                'milestone' => 'Milestone',
                'milestone_stage' => 'Stage',
                'taxnow_signup_status' => 'TaxNow Signup Status',
                'notes' => 'Notes',
                /*'sales_agent_id' => 'Internal Sales Agent',
                'sales_support_id' => 'Internal Sales Support',*/
                'action' => 'Action'
            );
        }else{
            $columns = array(
                'project_id' => 'ID',
                'created_at' => 'Date',
                'business_name' => 'Business Name',
                'contact_card' => 'Contact Card',
                'product_name' => 'Product',
                'project_name' => 'Project',
                 'collaborator' => 'Collaborators',
                'milestone' => 'Milestone',
                'milestone_stage' => 'Stage',
                'taxnow_signup_status' => 'TaxNow Signup Status',
                'notes' => 'Notes'
            );
        }
        if(isset($_REQUEST['download_excel_custom_report'])&&!empty($_REQUEST['download_excel_custom_report'])){
            unset($columns['cb']);
        }
        return $columns;
    }   

    /**
     * Override the parent columns method. Defines the columns to use in your listing table
     *
     * @return Array
     */
    public function get_export_columns()
    {
        $columns = array(
            'project_id' => 'ID',
            'created_at' => 'Date',
            'business_name' => 'Business Name',
            'authorized_signatory_name' => 'Authorized Signatory Name',
            'business_email' => 'Business Email',
            'business_phone' => 'Business Phone',
            'product_name' => 'Product',
            'project_name' => 'Project',
            'collaborator' => 'Collaborators',
            'milestone' => 'Milestone',
            'milestone_stage' => 'Stage',
            'taxnow_signup_status' => 'TaxNow Signup Status',
            // 'sales_agent_user' => 'Internal Sales Agent',
            // 'sales_support_user' => 'Internal Sales Support',
        );
        if(isset($_REQUEST['download_excel_custom_report'])&&!empty($_REQUEST['download_excel_custom_report'])){
            unset($columns['cb']);

            // Add extra columns
            $columns['bank_name'] = 'Bank Name';
            $columns['account_holder_name'] = 'Account Holder Name';
            $columns['account_number'] = 'Account Number';
            $columns['aba_routing_no'] = 'ABA Routing Number';

   

            // Add new columns for the report
            $columns['income_2019'] = 'Income 2019';
            $columns['income_2020'] = 'Income 2020';
            $columns['income_2021'] = 'Income 2021';
            $columns['stc_amount_2020'] = 'STC Amount 2020';
            $columns['stc_amount_2021'] = 'STC Amount 2021';
            $columns['maximum_credit'] = 'Maximum Credit';
            $columns['actual_credit'] = 'Actual Credit';
            $columns['estimated_fee'] = 'Estimated Fee';
            $columns['actual_fee'] = 'Actual Fee';
            $columns['years'] = 'Years';

            $columns['sales_agent'] = 'Sales User';

        }
        return $columns;
    }   

    /**
     * @var array
     *
     * Array contains slug columns that you want hidden
     *
     */

    private $hidden_columns = array();

    /**
     * edit a project record.
     *
     * @param int $id project ID
     */
    public static function edit_project($id) {
        $_SESSION['is_session'] = 0;
        wp_redirect("admin.php?page=projects&edit_project=" . $id);
    }



    private function count_data($search)
    {
        global $wpdb;    
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id', $current_user_id);
        $user_roles = $user_data->roles;     
        $projects_table = $wpdb->prefix.'projects';
        $business_info_table = $wpdb->prefix.'erc_business_info';
        $product_table = $wpdb->prefix.'crm_products';
        $milestone_table = $wpdb->prefix.'milestones';
        $milestone_status_table = $wpdb->prefix.'milestone_stages';
        $sales_team_table = $wpdb->prefix.'erc_sales_team';
        $additional_table = $wpdb->prefix.'erc_iris_leads_additional_info';
        
        $search_date_range = array();
        $start_date = $search['start_date'];
        $end_date = $search['end_date'];
        $start_date = date('Y-m-d', strtotime($start_date));
        $end_date = date('Y-m-d', strtotime($end_date));
        $where = ' WHERE projects.deleted_at IS NULL AND ((lower(bi.business_legal_name) NOT LIKE "test%") AND addinfo.lead_group != "ERC - Test Group") ';

        if(isset($search['start_date']) && !empty($search['start_date']) && isset($search['end_date']) && !empty($search['end_date'])){
            $where .= " AND (STR_TO_DATE(projects.created_at, '%Y-%m-%d') BETWEEN STR_TO_DATE('".$start_date."', '%Y-%m-%d') AND STR_TO_DATE('".$end_date."', '%Y-%m-%d')) ";
        }

        if(isset($search['project_id']) && !empty($search['project_id'])){
            $where .= " AND projects.project_id = ".$search['project_id']."";
        }

        if(isset($search['authorized_signatory_name']) && !empty($search['authorized_signatory_name'])){
            $where .= " AND bi.authorized_signatory_name LIKE '%".$search['authorized_signatory_name']."%'";
        }

        if(isset($search['business_email']) && !empty($search['business_email'])){
            $where .= " AND bi.business_email LIKE '%".$search['business_email']."%'";
        }

        if(isset($search['business_phone']) && !empty($search['business_phone'])){
            $where .= " AND bi.business_phone LIKE '%".$search['business_phone']."%'";
        }

        if(isset($search['milestone_id']) && !empty($search['milestone_id'])){
            $where .= " AND projects.milestone_id  = ".$search['milestone_id']."";
        }

        if(isset($search['milestone_stage_id']) && !empty($search['milestone_stage_id'])){
            $where .= " AND projects.milestone_stage_id = ".$search['milestone_stage_id']."";
        }

        //if(isset($search['product_id']) && !empty($search['product_id'])){
            $where .= " AND projects.product_id = 937";
        //}

        if(isset($search['project_name']) && !empty($search['project_name'])){
            $where .= " AND projects.project_id ='".$search['project_name']."'";
        }

        if(isset($search['business_name']) && !empty($search['business_name'])){
            $where .= " AND projects.lead_id='".$search['business_name']."'";
        }
        if(isset($search['taxnow_signup_status']) && !empty($search['taxnow_signup_status'])){
            $where .= " AND bi.taxnow_signup_status='".$search['taxnow_signup_status']."'";
        }
        if(in_array("iris_affiliate_users", $user_roles) ) {
            $where .= ' AND (addinfo.erc_reffrer_id = '.get_current_user_id().' OR addinfo.affiliate_user_id = '.get_current_user_id().')';
        }
        elseif(in_array("iris_employee", $user_roles) ) {
            $where .= ' AND addinfo.employee_id = '.get_current_user_id();
        }

        $sql = "SELECT projects.* 
            FROM $projects_table projects
            left join $product_table prod on projects.product_id = prod.ProductID 
            left join $business_info_table bi on projects.lead_id = bi.lead_id 
            left join $milestone_table mile on projects.milestone_id = mile.milestone_id 
            left join $milestone_status_table mile_status on projects.milestone_stage_id = mile_status.milestone_stage_id 
            LEFT JOIN $additional_table addinfo ON projects.lead_id = addinfo.lead_id  
            " . $where . " GROUP BY projects.project_id";
        $projects = $wpdb->get_results($sql);
        return $projects;
    }
    
    /**
         * Method for name column
         *
         * @param array $item an array of DB data
         *
         * @return string
         */
    private function custom_table_data($search)
    {
        global $wpdb;
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id', $current_user_id);
        $user_roles = $user_data->roles; 
        if (isset($search['paged'])) {
            $paged = $search['paged'];
            $max_limit = $paged * $this->limitPerpage;
            $min_limit = $max_limit - $this->limitPerpage;
            $limit = $min_limit . ",$this->limitPerpage";
        } else {
            $limit = "0,$this->limitPerpage";
        }
        $projects_table = $wpdb->prefix.'projects';
        $business_info_table = $wpdb->prefix.'erc_business_info';
        $product_table = $wpdb->prefix.'crm_products';
        $milestone_table = $wpdb->prefix.'milestones';
        $milestone_status_table = $wpdb->prefix.'milestone_stages';
        $sales_team_table = $wpdb->prefix.'erc_sales_team';
        $additional_table = $wpdb->prefix.'erc_iris_leads_additional_info';
        
        $search_date_range = array();
        $start_date = $search['start_date'];
        $end_date = $search['end_date'];
        $start_date = date('Y-m-d', strtotime($start_date));
        $end_date = date('Y-m-d', strtotime($end_date));
            
        $start_date = $search['start_date'];
        $end_date = $search['end_date'];
        $start_date = date('Y-m-d', strtotime($start_date));
        $end_date = date('Y-m-d', strtotime($end_date));


        $search_date_range = array();
        $where = ' WHERE projects.deleted_at IS NULL AND ((lower(bi.business_legal_name) NOT LIKE "test%") AND addinfo.lead_group != "ERC - Test Group") ';

        if(isset($search['start_date']) && !empty($search['start_date']) && isset($search['end_date']) && !empty($search['end_date'])){
            $where .= " AND (STR_TO_DATE(projects.created_at, '%Y-%m-%d') BETWEEN STR_TO_DATE('".$start_date."', '%Y-%m-%d') AND STR_TO_DATE('".$end_date."', '%Y-%m-%d')) ";
        }
        if(isset($search['project_id']) && !empty($search['project_id'])){
            $where .= " AND projects.project_id = ".$search['project_id']."";
        }

        if(isset($search['authorized_signatory_name']) && !empty($search['authorized_signatory_name'])){
            $where .= " AND bi.authorized_signatory_name LIKE '%".$search['authorized_signatory_name']."%'";
        }

        if(isset($search['business_email']) && !empty($search['business_email'])){
            $where .= " AND bi.business_email LIKE '%".$search['business_email']."%'";
        }

        if(isset($search['business_phone']) && !empty($search['business_phone'])){
            $where .= " AND bi.business_phone LIKE '%".$search['business_phone']."%'";
        }

        if(isset($search['milestone_id']) && !empty($search['milestone_id'])){
            $where .= " AND projects.milestone_id  = ".$search['milestone_id']."";
        }

        if(isset($search['milestone_stage_id']) && !empty($search['milestone_stage_id'])){
            $where .= " AND projects.milestone_stage_id = ".$search['milestone_stage_id']."";
        }

        //if(isset($search['product_id']) && !empty($search['product_id'])){
            $where .= " AND projects.product_id = 937";
        //}

        if(isset($search['project_name']) && !empty($search['project_name'])){
            $where .= " AND projects.project_id ='".$search['project_name']."'";
        }

        if(isset($search['business_name']) && !empty($search['business_name'])){
            $where .= " AND projects.lead_id='".$search['business_name']."'";
        }
        if(isset($search['taxnow_signup_status']) && !empty($search['taxnow_signup_status'])){
            $where .= " AND bi.taxnow_signup_status='".$search['taxnow_signup_status']."'";
        }
        if(in_array("iris_affiliate_users", $user_roles) ) {
            $where .= ' AND (addinfo.erc_reffrer_id = '.get_current_user_id().' OR addinfo.affiliate_user_id = '.get_current_user_id().')';
        }
        elseif(in_array("iris_employee", $user_roles) ) {
            $where .= ' AND addinfo.employee_id = '.get_current_user_id();
        }

        $sql = "SELECT projects.*, bi.business_legal_name, bi.authorized_signatory_name, bi.business_phone, bi.business_email,bi.taxnow_signup_status,prod.Title as product_name, mile.milestone_name as milestone, mile_status.stage_name ,
        {$wpdb->prefix}erc_bank_info.bank_name,{$wpdb->prefix}erc_bank_info.account_holder_name,{$wpdb->prefix}erc_bank_info.account_number,{$wpdb->prefix}erc_bank_info.aba_routing_no, addinfo.internal_sales_agent
            FROM $projects_table projects
            left join $product_table prod on projects.product_id = prod.ProductID 
            left join $business_info_table bi on projects.lead_id = bi.lead_id 
            left join $milestone_table mile on projects.milestone_id = mile.milestone_id 
            left join $milestone_status_table mile_status on projects.milestone_stage_id = mile_status.milestone_stage_id  
            LEFT JOIN $additional_table addinfo ON projects.lead_id = addinfo.lead_id  
            LEFT JOIN {$wpdb->prefix}erc_bank_info
            ON projects.lead_id = {$wpdb->prefix}erc_bank_info.lead_id
            " . $where . " GROUP BY projects.project_id";
            // If no specific order selected in front-end, reportFilter is pipeline, show oldest to newest post
            if( isset($search['reportFilter']) && $search['reportFilter'] == 'pipeline' ){
                $order_by = ' ORDER BY projects.created_at ASC ';
            } else {
                $order_by = ' ORDER BY projects.created_at DESC ';
            }
        
            if (isset($_GET['orderby']) && !empty($_GET['orderby']) && isset($_GET['order']) && !empty($_GET['order'])) {
                $order = $_GET['order'];
                $order_by = $_GET['orderby'];
                if($order_by == 'business_name'){
                    $order_by = 'business_legal_name';
                }
                elseif($order_by == 'milestone_stage'){
                    $order_by = 'stage_name';
                }
                if ($order == 'asc') {
                    $order_by = " ORDER BY $order_by DESC ";
                } else {
                    $order_by = " ORDER BY $order_by ASC ";
                }
            } //order by action

            if(!empty($search['download_excel_custom_report'])){
                     $sql .= $order_by;
                }else{
                    $sql .= $order_by.' LIMIT '.$limit;
            }
            $projects = $wpdb->get_results($sql);
            $data = array();
            $c = 0;
            $i = 1;
            if (!empty($projects)) {
                $count = 0;
				$default_none = 'None';
                foreach ($projects as $key =>$value ) {
                    $project_id = $value->project_id;
                    $data[$c]['product_id'] = $value->product_id;
                    $url = admin_url('admin.php?page=add_edit_project&action=edit&id='.$project_id);
                    $data[$c]['project_ids'] = $project_id;
                    if(!empty($search['download_excel_custom_report'])){
                        $data[$c]['project_id'] = $project_id;
                    }else{
                        $data[$c]['project_id'] = $project_id;
                    }
                    if(!empty($search['download_excel_custom_report'])){
                        $data[$c]['project_name'] = !empty($value->project_name) ? $value->project_name : $default_none;
                    }else{
                        if($value->product_id == 937){
                            $data[$c]['project_name'] = '<a target="_blank" href="admin.php?page=manage-stc-project&id='.$project_id.'">'.(!empty($value->project_name) ? $value->project_name : $default_none).'</a>';
                        }else{
                            $data[$c]['project_name'] = !empty($value->project_name) ? $value->project_name : $default_none;
                        }
                    }
                    //Get Project Collaborators
                    $collaborators = $wpdb->get_results("SELECT user_id FROM {$wpdb->prefix}collaborators WHERE project_id = ".$project_id."");

					$data[$c]['lead_id'] = !empty($value->lead_id) ? $value->lead_id : $default_none;
					$data[$c]['business_name'] = !empty($value->business_legal_name) ? $value->business_legal_name : $default_none;
                    $contact_data = $wpdb->get_row("SELECT {$wpdb->prefix}op_contacts.first_name,{$wpdb->prefix}op_contacts.last_name,{$wpdb->prefix}op_emails.email,{$wpdb->prefix}op_phone.phone FROM {$wpdb->prefix}op_contacts
                                                                                LEFT JOIN {$wpdb->prefix}op_emails ON {$wpdb->prefix}op_contacts.id = {$wpdb->prefix}op_emails.id
                                                                                LEFT JOIN {$wpdb->prefix}op_phone ON {$wpdb->prefix}op_contacts.id = {$wpdb->prefix}op_phone.id
                                                                                WHERE {$wpdb->prefix}op_contacts.report_to_id = ".$value->lead_id."
                                                                                ");
                    if(!empty($contact_data)){
                        $lead_name = $contact_data->first_name.' '.$contact_data->last_name;
                        $lead_email = $contact_data->email;
                        $lead_phone = $contact_data->phone;
                    }else{
                        $lead_name = $value->authorized_signatory_name;
                        $lead_email = $value->business_email;
                        $lead_phone = $value->business_phone;
                    }
                    $data[$c]['contact_card'] = '<a href="#" class="card-link " data-row="' . $value->project_id . '">View Card</a>
                    <div class="modal contact-card-popup" id="card_box-' . $value->project_id . '" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="exampleModalLabel"><i class="fa-solid fa-id-badge"></i> ' . $value->project_name . '</h5>
                                    <button type="button" class="close" onclick="close_card_popup(' . $value->project_id . ')">
                                    <span aria-hidden="true">×</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    <div class="contact-card">
                                        <div class="card-exam">
                                            <div class="d-flex w-100 align-items-center">
                                                <div class="circle"><i class="fa-solid fa-id-badge" style="font-size:26px"></i></div>
                                                <div class="card-exam-title">
                                                    <p>' . $lead_name . '</p>
                                                    <p>' . $lead_phone . '</p>
                                                    <p>' . $lead_email . '</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>';
					$data[$c]['product_name'] = !empty($value->product_name) ? $value->product_name : $default_none;
					$data[$c]['project_name'] = !empty($value->project_name) ? $value->project_name : $default_none;
                    $data[$c]['authorized_signatory_name'] = !empty($lead_name) ? $lead_name : $default_none;
                    $data[$c]['business_phone'] = !empty($lead_phone) ? $lead_phone : $default_none;
                    $data[$c]['business_email'] = !empty($lead_email) ? $lead_email : $default_none;
                    //$data[$c]['milestone'] = ($value->milestone_deletedat === null) ? $value->milestoneName : $value->milestoneName . ' (Deleted)';
                    //$data[$c]['milestone_stage'] = ($value->stage_deletedat === null) ? $value->milestoneStatus : $value->milestoneStatus . ' (Deleted)';
					$data[$c]['milestone'] = !empty($value->milestone) ? $value->milestone : $default_none;
					$data[$c]['milestone_stage'] = !empty($value->stage_name) ? $value->stage_name : $default_none;
					$data[$c]['taxnow_signup_status'] = !empty($value->taxnow_signup_status) ? $value->taxnow_signup_status : $default_none;


                    $data[$c]['bank_name'] = !empty($value->bank_name) ? $value->bank_name : $default_none;
					$data[$c]['account_holder_name'] = !empty($value->account_holder_name) ? $value->account_holder_name : $default_none;
					$data[$c]['account_number'] = !empty($value->account_number) ? $value->account_number : $default_none;
                    $data[$c]['aba_routing_no'] = !empty($value->aba_routing_no) ? $value->aba_routing_no : $default_none;

                    $data[$c]['income_2019'] = !empty($value->income_2019) ? $value->income_2019 : $default_none;
					$data[$c]['income_2020'] = !empty($value->income_2020) ? $value->income_2020 : $default_none;
					$data[$c]['income_2021'] = !empty($value->income_2021) ? $value->income_2021 : $default_none;
             
                    $data[$c]['stc_amount_2020'] = !empty($value->stc_amount_2020) ? $value->stc_amount_2020 : $default_none;
					$data[$c]['stc_amount_2021'] = !empty($value->stc_amount_2021) ? $value->stc_amount_2021 : $default_none;
					
                    $data[$c]['maximum_credit'] = !empty($value->maximum_credit) ? $value->maximum_credit : $default_none;
					$data[$c]['actual_credit'] = !empty($value->actual_credit) ? $value->actual_credit : $default_none;
					$data[$c]['estimated_fee'] = !empty($value->estimated_fee) ? $value->estimated_fee : $default_none;
                    $data[$c]['actual_fee'] = !empty($value->actual_fee) ? $value->actual_fee : $default_none;
					$data[$c]['years'] = !empty($value->years) ? $value->years : $default_none;
                    
                    $data[$c]['sales_agent'] = !empty($value->internal_sales_agent) ? $value->internal_sales_agent : $default_none;

                    // if (!empty($value->sales_user_id)) {
                    //     $sales_users = get_userdata($value->sales_user_id);
                    //     $sales_user_display_name = $sales_users->data->display_name;
                    //     $data[$c]['sales_agent'] = $sales_user_display_name;
                    // }else{
                    //     $data[$c]['sales_agent'] = $default_none;
                    // }

                    $collaborator_names = '';
                    if(!empty($collaborators)){
                        foreach($collaborators as $collaborator){
                            $user_data = get_user_by('id',$collaborator->user_id);
                            $collaborator_names .= $user_data->data->display_name.', ';
                        }
                        $collaborator_names = rtrim($collaborator_names,', ');
					} else {
						$collaborator_names = $default_none;
					}

					$data[$c]['created_at'] = !empty($value->created_at) ? date('m/d/Y H:i:s', strtotime($value->created_at)) : $default_none;
					$data[$c]['collaborator'] = $collaborator_names;
                    /*$data[$c]['notes'] = '<span class="custom-notes-call" data-projectid="' . $project_id . '" id=""><i class="fa-regular fa-comment" style="font-size: 16px;"></i></span>';*/
                    // step - 1
                    $data[$c]['notes'] = '<div style="display: flex;"><span class="view_comment_notes" data-toggle="modal" data-project_id="'.$project_id.'" title="View"><i class="fa-regular fa-comment"></i></span><span class="comment_btn custom-add-notes" data-toggle="modal" data-project_Id="'.$project_id.'" title="Add"> <i class="fa-solid fa-comment-medical"></i></span></div>';
					
                    $c++;
                }
        }
        return $data;
    }

    function get_bulk_actions() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
          if(in_array('administrator', $user_roles) || in_array('echeck_staff', $user_roles) || in_array('echeck_client', $user_roles) || in_array('master_sales', $user_roles)){ 
            // return array('delete' => __( 'Delete', ''),);
            return array();
          }else{
            return array();
          }      
    }

    function process_bulk_action() {
        global $wpdb;
        if (isset($_REQUEST['action2']) && $_REQUEST['action2']=='delete'){
            if(!empty($_REQUEST['deleteItem'])){
                foreach($_REQUEST['deleteItem'] as $singleProject){
                    $projects_table = $wpdb->prefix.'projects';
                    $sql = "Delete FROM $projects_table where project_id  = ".$singleProject;
                     $business_lead = $wpdb->get_results($sql);
                }
            }
        }
    }


    /**
     * Allows you to sort the data by the variables set in the $_GET
     *
     * @return Mixed
     */
    private function sort_data($a, $b)
    {
        // Set defaults
        $orderby = 'id';
        $order = 'desc';
        // If orderby is set, use this as the sort column
        if (!empty($_GET['orderby'])) {
            $orderby = $_GET['orderby'];
        }
        // If order is set use this as the order
        if (!empty($_GET['order'])) {
            $order = $_GET['order'];
        }
        $result = strnatcmp($a[$orderby], $b[$orderby]);
        if ($order === 'asc') {
            return $result;
        }
        return -$result;
    }
    public function search_box_new()
    {
        $search_data = array();
    ?>
        <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
        <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
        <?php  
        global $wpdb;
        $products_table = $wpdb->prefix.'crm_products';    
        $all_products =  $wpdb->get_results("SELECT $products_table.productID,$products_table.title,$products_table.unitPrice,$products_table.deletedBy FROM $products_table WHERE $products_table.DeletedAt IS NULL AND $products_table.status = 'active' GROUP BY $products_table.productID");

        $milestone_table = $wpdb->prefix.'milestones';    
        $all_milestone =  $wpdb->get_results("SELECT $milestone_table.milestone_id,$milestone_table.milestone_name,$milestone_table.product_id FROM $milestone_table WHERE $milestone_table.deleted_at IS NULL AND $milestone_table.status = 'active' AND FIND_IN_SET(937,$milestone_table.product_id) AND $milestone_table.map LIKE '%\"project\"%' GROUP BY $milestone_table.milestone_id");

        $milestone_status_table = $wpdb->prefix.'milestone_stages';    
        $all_milestone_status =  $wpdb->get_results("SELECT $milestone_status_table.milestone_stage_id,$milestone_status_table.stage_name,$milestone_status_table.milestone_id,$milestone_status_table.status FROM $milestone_status_table JOIN $milestone_table ON $milestone_status_table.milestone_id = $milestone_table.milestone_id WHERE $milestone_status_table.deleted_at IS NULL AND $milestone_status_table.Status = 'active' AND FIND_IN_SET(937,$milestone_table.product_id) AND $milestone_table.map LIKE '%\"project\"%' GROUP BY $milestone_status_table.milestone_stage_id");
        ?>
        <div id="overlay" onclick="overlay_off()"></div>
            <form method="get" action="" id="search_all_form">
                <input type="hidden" name="page" value="<?php echo $_GET['page'] ?>">
                <div class="row align-items-center mb-3">
                    <div class="col-md-3 search_field">
                        <img src="https://design.echeck.money/portal/wp-content/plugins/echeck-theme111/assets/img/icon/Search.svg" alt="" class="icon">
                        <input type="text" placeholder="Search.." id="show_lead_search" autocomplete="off" class="open">
                        <input type="submit" style="visibility: hidden;position:absolute;">
                    </div>
                    <div class="col-md-9 invoice_date_range" style="margin-top: 10px;">
                        <p class="invoice_date_filter">
                            <span>Date From : <input type="text" id="date_timepicker_start" class="date_from" value="" name="start_date" pattern="\d{2}/\d{2}/\d{4}" placeholder="MM/DD/YYYY" autocomplete="off" onkeydown="return false"></span>
                            <span>Date To : <input type="text" id="date_timepicker_end" value="" name="end_date" class="date_to" pattern="\d{2}/\d{2}/\d{4}" placeholder="MM/DD/YYYY" autocomplete="off" onkeydown="return false"></span>
                            <input type="button" class="button create_date_submit date_timepicker_submit" value="Submit">
                            <input type="button" class="button create_date_submit" id="date_timepicker_reset" value="Reset">
                        </p>
                    </div>
                </div>
                <div class="popup-overlay search-popup">
                    <div class="popup-content">
                        <form method="post" class="container">
                            <div class="search_header">
                                <h4>Search</h4>
                                <span class="close">
                                    <i class="fa-solid fa-xmark"></i>
                                </span>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <input type="text" name="project_id" id="project_id" placeholder="Project ID" value="<?php
                                    if (isset($search_data['project_id'])) {
                                        echo $search_data['project_id'];
                                    }
                                    ?>" class="search-popup-input-select" maxlength="10">
                                </div>
                                <div class="col-md-6">
                                    <input type="text" id="authorized_signatory_name" name="authorized_signatory_name" placeholder="Authorized Signatory Name" value="<?php
                                    if (isset($search_data['authorized_signatory_name'])) {
                                        echo $search_data['authorized_signatory_name'];
                                    }
                                    ?>" class="search-popup-input-select">
                                </div>    
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <input type="text" name="business_email" id="business_email" placeholder="Business Email" value="<?php
                                    if (isset($search_data['business_email'])) {
                                        echo $search_data['business_email'];
                                    }
                                    ?>" class="search-popup-input-select">
                                </div>
                                <div class="col-md-6">
                                    <input type="text" id="business_phone" name="business_phone" placeholder="Business Phone" value="<?php
                                    if (isset($search_data['business_phone'])) {
                                        echo $search_data['business_phone'];
                                    }
                                    ?>" class="search-popup-input-select">
                                </div>    
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <select name="milestone_id" id="milestone_id" class="search-popup-input-select" data-no="<?php echo $i; ?>">
                                        <option value="">Select Milestone</option>
                                        <?php 
                                            foreach ($all_milestone as $pro_key => $pro_value) {
                                                $milestone_id = $pro_value->milestone_id;     
                                                $milestone_title = $pro_value->milestone_name;
                                                echo '<option  value="'.$milestone_id.'" >'.$milestone_title.'</option>';
                                            }
                                        ?>         
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <select name="milestone_stage_id" id="milestone_stage_id" class="search-popup-input-select" data-no="<?php echo $i; ?>">
                                        <option value="">Select Stage</option>
                                            <?php 
                                                foreach ($all_milestone_status as $pro_key => $pro_value) {
                                                    $milestone_stage_id = $pro_value->milestone_stage_id;     
                                                    $milestone_status_title = $pro_value->stage_name;         
                                                    echo '<option  value="'.$milestone_stage_id.'" >'.$milestone_status_title.'</option>';
                                                }
                                        ?>         
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <?php
                                    global $wpdb;
                                    $projects_table = $wpdb->prefix.'projects';
                                    $lead_info_table_add = $wpdb->prefix . 'erc_iris_leads_additional_info';
                                    $bsns_table = $wpdb->prefix . 'erc_business_info';
                                    $project_sql = "SELECT $projects_table.project_id, $projects_table.project_name 
                                                                        FROM $projects_table 
                                                                        LEFT JOIN $lead_info_table_add ON $lead_info_table_add.lead_id = $projects_table.lead_id
                                                                        LEFT JOIN $bsns_table ON $bsns_table.lead_id = $projects_table.lead_id
                                                                        WHERE $projects_table.product_id = 937 AND $projects_table.deleted_at IS NULL AND ((lower($bsns_table.business_legal_name) NOT LIKE 'test%') AND $lead_info_table_add.lead_group != 'ERC - Test Group')
                                                                        GROUP BY $projects_table.project_name";
                                    $projects = $wpdb->get_results($project_sql);
                                    ?>
                                    <select id="project_name" class="search-popup-input-select" name="project_name" placeholder="Select Project Name" >
                                        <option value="">Select Project Name</option>
                                        <?php foreach ($projects as $key => $value) {?>
                                                <option value="<?php echo $value->project_id; ?>"><?= $value->project_name; ?></option>
                                        <?php } ?>
                                    </select>

                                </div> 
                                <div class="col-md-6">
                                    <?php
                                    global $wpdb;
                                    $bsns_table = $wpdb->prefix . 'erc_business_info';
                                    $lead_info_table_add = $wpdb->prefix . 'erc_iris_leads_additional_info';
                                    $opportunity_table = $wpdb->prefix . 'opportunities';
                                    $opportunity_product_table = $wpdb->prefix . 'opportunity_products';
                                    $whereNoTest = " AND ( (lower({$wpdb->prefix}erc_iris_leads_additional_info.category) NOT LIKE '%test%') AND {$wpdb->prefix}erc_iris_leads_additional_info.lead_group != 'ERC - Test Group')";
                                    $lead_sql = "SELECT $bsns_table.lead_id,$bsns_table.business_legal_name 
                                                            FROM 
                                                            $bsns_table 
                                                            LEFT JOIN $opportunity_table ON $opportunity_table.LeadID = $bsns_table.lead_id
                                                            LEFT JOIN $opportunity_product_table ON $opportunity_product_table.opportunity_id = $opportunity_table.OpportunityID
                                                            LEFT JOIN $lead_info_table_add ON $lead_info_table_add.lead_id = $bsns_table.lead_id
                                                            WHERE (lower($bsns_table.business_legal_name) NOT LIKE 'test%') ".$whereNoTest." AND $opportunity_product_table.product_id = 937
                                                            GROUP BY $bsns_table.business_legal_name";
                                    $business_lead = $wpdb->get_results($lead_sql);
                                    ?>
                                    <select id="business-name-select" class="search-popup-input-select" name="business_name" placeholder="Select Business Name" >
                                        <option value="">Select Business</option>
                                        <?php
                                        foreach ($business_lead as $key => $value) {
                                            if (isset($search_data['business_name'])) {
                                                $business_name = $search_data['business_name'];
                                                if ($business_name == $value->lead_id) {
                                                    $sel = "selected";
                                                } else {
                                                    $sel = '';
                                                }
                                            } else {
                                                $sel = '';
                                            }
                                            ?>
                                                <option value="<?php echo htmlentities($value->lead_id); ?>" <?php echo $sel; ?>><?= $value->business_legal_name; ?></option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div> 
                            <div class="row mt-2">
                                <div class="col-md-6">
                                    <select class="form-control" name="taxnow_signup_status" id="taxnow_signup_status" style="height:30px">
                                        <option value="">Select TaxNow Signup Status</option>
                                        <option value="Complete" <?php if($search_data['taxnow_signup_status'] == 'Complete'){echo "selected";}?>>Complete</option>
                                        <option value="Incomplete" <?php if($search_data['taxnow_signup_status'] == 'Incomplete'){echo "selected";}?>>Incomplete</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12 text-center">
                                    <input type="button" id="btn_search_submit" class="search-popup-submit-btn" value="Search" />
                                    <input type="button" class="search-popup-reset-btn" value="Clear" id="reset_Form_button" onclick="resetAffForm()"/>
                                </div>
                            </div>
                    </div>
                </div>
            </form>

            <div class="send_decl_confirm_box_wraper send_decl_box" id="send_decl_box" style="display:none;"><a href="javascript:void(0);" class="close_send_decl_popup" onclick="close_send_decl_popup()">x</a>
                <div class="confirm_box_inner send_decl_box_inner" style="padding-bottom:36px;"> <h5>Send Declaration</h5><hr class="popup-hr">
                    <div class="decl_btn">
                        <div class="decl_btn_yes">
                            <a href="javascript:void(0);" class="send_withsdgr btn btn-primary" data-lead_id="">Declaration With SDGR</a>
                        </div>
                        <div class="decl_btn_no">
                            <a href="javascript:void(0);" class="send_nosdgr btn btn-warning" data-lead_id="">Declaration W/O SDGR</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="sent_decl_confirm_box_wraper sent_decl_box" id="sent_decl_box" style="display:none;"><a href="javascript:void(0);" class="close_sent_decl_popup" onclick="close_sent_decl_popup()">x</a>
                <div class="confirm_box_inner sent_decl_box_inner" style="padding-bottom:36px;"><hr class="popup-hr">
                    <div class="sent_decl_btn">
                    </div>
                </div>
            </div>
            <script type="text/javascript">
                jQuery("#milestone_id").select2();
                jQuery("#milestone_stage_id").select2();
                jQuery("#product_id").select2();
                jQuery("#project_name").select2();
                jQuery("#business-name-select").select2();
                function close_card_popup(rowId) {
                    // Close the card popup
                    jQuery('#card_box-' + rowId).hide();
                }
                function resetAffForm() {
                    jQuery("#reset_Form_button").val('Clearing...');
                    var site_url = '<?php echo get_site_url() ?>';
                    window.location.href = site_url + '/wp-admin/admin.php?page=<?php  echo $_GET['page']  ?>';
                }
                jQuery(".open").on("click", function () {
                    jQuery('.status_box').hide();
                    jQuery(".popup-overlay, .popup-content").addClass("active");
                    jQuery(".search_lead_id").focus();
                    jQuery('#overlay').show();
                });
				
                // step-2
                jQuery(document).on("click",".close", function () {
                    jQuery(".popup-overlay, .popup-content").removeClass("active");
                    jQuery('#overlay').hide();
                    jQuery("#add-new-custom-notes").modal('hide');
                    jQuery("#exampleModal_ercdoc_view").modal('hide');
                });				

                function overlay_off() {
                    jQuery(".close").trigger("click");
                    jQuery('#overlay').hide();
                }
            </script>
        <?php
    }
     //step-3
     // check confidence 
    public function check_confidence_user(){
        global $wpdb;
     $confidence_user = 0;
     $option_table = $wpdb->prefix.'onedrive_options';
      $selected_user = $wpdb->get_var("SELECT meta_value FROM $option_table WHERE meta_key='notes_confidence_users' ");
      if(!empty($selected_user)){
        $selected_users = explode(",",$selected_user);
        $current_user_id = get_current_user_id();
        if(in_array($current_user_id,$selected_users)){
            $confidence_user = 1;
        }
      }
      return $confidence_user;
    }
	
    public function export_leads_button($search_data)
    {
    ?>
        <script type="text/javascript">
        jQuery(document).ready(function () {
            jQuery("#business-name-select").select2();
            jQuery("#OpportunityName").select2();
            jQuery("#productnamefilter").select2();
            jQuery("#search_milestonefilter").select2();
            jQuery("#search_milestoneStatusfilter").select2();
            jQuery("#milestonefilter").select2();
            jQuery("#milestoneStatusfilter").select2();
            jQuery("#report_filter").select2();
            jQuery(document).on("click", ".export_leads", function () {
                var reportFilter = jQuery('#report_filter').val();
                var lead_id = jQuery("#lead_id").val();
                var bsns_name = jQuery("#bsns_name").val();
                var group_filter = jQuery("#group-select").val();
                var lead_status = jQuery("#lead_status").val();
                var authorized_signatory = jQuery("#authorized_signatory").val();
                ;
                var business_phone = jQuery("#business_phone").val();
                var business_email = jQuery("#business_email").val();
                var stage = jQuery("#stage").val();
                var affiliate_user = jQuery("select[name=affiliate_user]").val();
                var account_iris_user = jQuery("select[name=account_iris_user]").val();

                var from = jQuery("input[name=start_date]").val();
                var to = jQuery("input[name=end_date]").val();

                console.log('from:' + from);
                console.log('to:' + to);

                var confidence_level = "<?php echo $confidence_level; ?>";
                jQuery(this).css('pointer-events', 'none');
                jQuery.ajax({
                    url: '<?php echo admin_url('admin-ajax.php'); ?>',
                    method: 'post',
                    data: {action: 'export_lead_detailed_data', reportFilter: reportFilter, lead_id: lead_id, business_name: business_name, lead_status: lead_status, group_filter: group_filter, authorized_signatory: authorized_signatory, business_phone: business_phone, business_email: business_email, stage: stage, confidence_level: confidence_level, affiliate_user: affiliate_user, account_iris_user: account_iris_user, from: from, to: to},
                    success(response) {
                        jQuery(".export_leads").text('Export');
                        jQuery(".export_leads").css('pointer-events', '');
                        var downloadLink = document.createElement("a");
                        var responseData = jQuery.trim(response);
                        var fileData = ['\ufeff' + responseData];

                        var blobObject = new Blob(fileData, {
                            type: "text/csv;charset=utf-8;"
                        });

                        var url = URL.createObjectURL(blobObject);
                        downloadLink.href = url;
                        var currentDate = new Date();

                        var dateString = currentDate.toISOString().slice(0, 10); // YYYY-MM-DD

                        var timeString = currentDate
                                .toLocaleTimeString("en-US", {
                                    hour12: false,
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    second: "2-digit",
                                })

                                .replace(/:/g, "-");
                        var filename = "Opportunity_Report_" + dateString + "_" + timeString + ".csv";
                        downloadLink.download = filename;
                        document.body.appendChild(downloadLink);
                        downloadLink.click();
                        document.body.removeChild(downloadLink);
                    }
                });
            });
        });
        jQuery(document).ready(function () {
            jQuery('.date_from').datetimepicker({
                format: 'm/d/Y',
                autoclose: true,
                orientation: 'bottom',
                timepicker: false,
                autocomplete: 'off',
                maxDate: 'now',
                onChangeDateTime: function (dp, $input) {
                    console.log(new Date($('.date_to').val()));
                    if (new Date($('.date_to').val()) < new Date($input.val())) {
                        alert("Date To can not be less than Date from");
                        $('.date_to').val('');
                        $('.date_from').val('');
                    }
                }
            });
            jQuery('.date_to').datetimepicker({
                format: 'm/d/Y',
                autoclose: true,
                orientation: 'bottom',
                timepicker: false,
                autocomplete: 'off',
                maxDate: 'now',
                onChangeDateTime: function (dp, $input) {
                    console.log(new Date($('.date_from').val()));
                    if (new Date($input.val()) < new Date($('.date_from').val())) {
                        alert("Date To can not be less than Date from");
                        $('.date_to').val('');
                        $('.date_from').val('');
                    }
                }
            });
            jQuery('#date_timepicker_reset').on('click', function () {
                jQuery('.date_to').datetimepicker('reset');
                jQuery('.date_from').datetimepicker('reset');
                location.reload();
            });
            //set date by JS
            <?php $start_date_js = !empty($_REQUEST['start_date']) ? $_REQUEST['start_date'] : '' ?>
            var start_date = '<?php echo $start_date_js; ?>';
            jQuery('#date_timepicker_start').val(start_date);
            <?php $end_date_js = !empty($_REQUEST['end_date']) ? $_REQUEST['end_date'] : '' ?>
            var end_date = '<?php echo $end_date_js; ?>';
            jQuery('#date_timepicker_end').val(end_date);
        });
        jQuery(document).ready(function ($) {
            $('#search_all_form').on('submit', function (e) {
                if ($('.date_from').val() && !$('.date_to').val()) {
                    e.preventDefault();
                    alert('Please select a Date To.');
                }

                if ($('.date_to').val() && !$('.date_from').val()) {
                    e.preventDefault();
                    alert('Please select a Date From.');
                }
            });
        });

        jQuery(document).on('click','.delete_project', function () {
            var project_id = jQuery(this).data('project_id');
            swal({
                title: "Are you sure?",
                text: "Yes, I want to delete this Project.",
                icon: "warning",
                buttons: {
                    cancel: "Cancel",
                    confirm: "Delete",
                },
                content: {
                    element: "input",
                    attributes: {
                        type: "checkbox",
                        id: "agreeCheckbox"
                    },
                },
                dangerMode: true,
            })
            .then((willDelete) => {
                if (willDelete && $('#agreeCheckbox').prop('checked')) {
                    $(this).attr('disabled',true); 
                    $("#loader_box").show();
                        jQuery.ajax({
                            url:'<?php echo admin_url('admin-ajax.php'); ?>',
                            method:'post',
                            data:{action: 'delete_project', project_id:project_id },
                            success(response){
                                window.location.href = '?page=projects';
                            },
                            error: function(jqXHR, textStatus, errorThrown) {        
                                window.location.href = '?page=projects';
                                jQuery(".popup-overlay, .popup-content").removeClass("active");
                                jQuery('#overlay').hide();
                                jQuery('.pleasewait').hide();    
                                },
                        });
                }else{
                } 
            });   
            $('.swal-modal').addClass('crm-erp-project-delete-swal'); 
        });        
        </script>
    <?php
    }
    function get_sortable_columns() {
        $sortable_columns = array();
        $columns  = $this->get_columns();
        foreach ($columns as $key => $value) {
            if($key != 'cb' && $key != 'action' && $key != 'contact_card' && $key != 'collaborator'){
                $sortable_columns[$key] = array($key, true);
            }
        }
        return $sortable_columns;
    }

    /**
     * @Override of prepare_items method
     *
     */

    function prepare_items() {
        /**
         * How many records for page do you want to show?
         */
        $per_page = $this->limitPerpage;
        // $this->process_bulk_action();
        $columns  = $this->get_columns();
        $hidden   = $this->hidden_columns;
        $sortable = $this->get_sortable_columns();
        $this->_column_headers = array($columns, $hidden, $sortable);
        $data = $this->custom_table_data($_REQUEST);
        function usort_reorder( $a, $b ) {
            $orderby = ( ! empty( $_REQUEST['orderby'] ) ) ? $_REQUEST['orderby'] : 'project_id';
            $order = ( ! empty( $_REQUEST['order'] ) ) ? $_REQUEST['order'] : 'asc';
            $result = strcmp( $a[ $orderby ], $b[ $orderby ] );
            return ( 'asc' === $order ) ? $result : -$result;
        }
        // usort( $data, 'usort_reorder' );
        $total_items = count($data);
        $this->items = $data;

        /**
         * Call to _set_pagination_args method for informations about
         * total items, items for page, total pages and ordering
         */
         $search = $_REQUEST;
         $total_items = $this->count_data($search);
         $total_items = count($total_items);

        $this->set_pagination_args(
            array(

                'total_items'   => $total_items,
                'per_page'      => $per_page,
                'total_pages'   => ceil( $total_items / $per_page ),
                'orderby'       => ! empty( $_REQUEST['orderby'] ) && '' != $_REQUEST['orderby'] ? $_REQUEST['orderby'] : 'project_id',
                'order'         => ! empty( $_REQUEST['order'] ) && '' != $_REQUEST['order'] ? $_REQUEST['order'] : 'asc'
            )
        );

    }
        

    protected function pagination($which)
    {

        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id', $current_user_id);
        $user_roles = $user_data->roles;

        $filter_invoice_status = "All";

        if (empty($this->_pagination_args)) {

            return;
        }

        if (!empty($_REQUEST['filter_invoice_status'])) {

            $filter_invoice_status = $_REQUEST['filter_invoice_status'];
        }

        $total_items = $this->_pagination_args['total_items'];

        $total_pages = $this->_pagination_args['total_pages'];

        $infinite_scroll = false;

        if (isset($this->_pagination_args['infinite_scroll'])) {

            $infinite_scroll = $this->_pagination_args['infinite_scroll'];
        }



        if ('top' === $which && $total_pages > 1) {

            $this->screen->render_screen_reader_content('heading_pagination');
        }



        $output = '<span class="displaying-num">' . sprintf(
            /* translators: %s: Number of items. */

            _n('%s item', '%s items', $total_items),
            number_format_i18n($total_items)
        ) . '</span>';

        $current = $this->get_pagenum();

        $removable_query_args = wp_removable_query_args();

        $current_url = set_url_scheme('http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']);

        $current_url = remove_query_arg($removable_query_args, $current_url);

        if (!empty($_GET['business_name'])) {
            $current_url = add_query_arg('business_name', $_GET['business_name'], $current_url);
        }

        if (!empty($_GET['lead_id'])) {
            $current_url = add_query_arg('lead_id', $_GET['lead_id'], $current_url);
        }



        if (!empty($_GET['authorized_signatory'])) {
            $current_url = add_query_arg('authorized_signatory', $_GET['authorized_signatory'], $current_url);
        }


        if (!empty($_GET['business_phone'])) {
            $current_url = add_query_arg('business_phone', $_GET['business_phone'], $current_url);
        }

        if (!empty($_GET['business_email'])) {
            $current_url = add_query_arg('business_email', $_GET['business_email'], $current_url);
        }

        if (!empty($_GET['stage'])) {
            $current_url = add_query_arg('stage', $_GET['stage'], $current_url);
        }

        if (!empty($_GET['lead_status'])) {
            $current_url = add_query_arg('lead_status', $_GET['lead_status'], $current_url);
        }

        if (!empty($_GET['start_date'])) {
            $current_url = add_query_arg('start_date', $_GET['start_date'], $current_url);
        }

        if (!empty($_GET['end_date'])) {
            $current_url = add_query_arg('end_date', $_GET['end_date'], $current_url);
        }

        if (!empty($_GET['select_report_dropdown'])) {
            $current_url = add_query_arg('select_report_dropdown', $_GET['select_report_dropdown'], $current_url);
        }


        $page_links = array();

        $total_pages_before = '<span class="paging-input">';

        $total_pages_after = '</span></span>';

        $disable_first = false;

        $disable_last = false;

        $disable_prev = false;

        $disable_next = false;

        if (1 == $current) {

            $disable_first = true;

            $disable_prev = true;
        }

        if (2 == $current) {

            $disable_first = true;
        }

        if ($total_pages == $current) {

            $disable_last = true;

            $disable_next = true;
        }

        if ($total_pages - 1 == $current) {

            $disable_last = true;
        }



        if ($disable_first) {

            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&laquo;</span>';
        } else {

            $page_links[] = sprintf(
                "<a class='first-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url(remove_query_arg('paged', $current_url)),
                __('First page'),
                '&laquo;'
            );
        }



        if ($disable_prev) {

            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&lsaquo;</span>';
        } else {

            $page_links[] = sprintf(
                "<a class='prev-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url(add_query_arg('paged', max(1, $current - 1), $current_url)),
                __('Previous page'),
                '&lsaquo;'
            );
        }



        if ('bottom' === $which) {

            $html_current_page = $current;

            $total_pages_before = '<span class="screen-reader-text">' . __('Current Page') . '</span><span id="table-paging" class="paging-input"><span class="tablenav-paging-text">';
        } else {

            $html_current_page = sprintf(
                "%s<input class='current-page' id='current-page-selector' type='text' name='paged' value='%s' size='%d' aria-describedby='table-paging' /><span class='tablenav-paging-text'>",
                '<label for="current-page-selector" class="screen-reader-text">' . __('Current Page') . '</label>',
                $current,
                strlen($total_pages)
            );
        }

        $html_total_pages = sprintf("<span class='total-pages'>%s</span>", number_format_i18n($total_pages));

        $page_links[] = $total_pages_before . sprintf(
            /* translators: 1: Current page, 2: Total pages. */

            _x('%1$s of %2$s', 'paging'),
            $html_current_page,
            $html_total_pages
        ) . $total_pages_after;

        if ($disable_next) {

            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&rsaquo;</span>';
        } else {

            $page_links[] = sprintf(
                "<a class='next-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url(add_query_arg(array('paged' => min($total_pages, $current + 1), 'filter_invoice_status' => $filter_invoice_status), $current_url)),
                __('Next page'),
                '&rsaquo;'
            );
        }



        if ($disable_last) {

            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&raquo;</span>';
        } else {

            $page_links[] = sprintf(
                "<a class='last-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url(add_query_arg(array('paged' => $total_pages, 'filter_invoice_status' => $filter_invoice_status), $current_url)),
                __('Last page'),
                '&raquo;'
            );
        }



        $pagination_links_class = 'pagination-links';

        if (!empty($infinite_scroll)) {

            $pagination_links_class .= ' hide-if-js';
        }

        $output .= "\n<span class='$pagination_links_class'>" . implode("\n", $page_links) . '</span>';

        if ($total_pages) {

            $page_class = $total_pages < 2 ? ' one-page' : '';
        } else {

            $page_class = ' no-pages';
        }

        $this->_pagination = "<div class='tablenav-pages{$page_class}'>$output</div>";

        echo $this->_pagination;
    }



    /**
     * @Override of display method
     */

    function display() {

        /**
         * Adds a nonce field
         */
        wp_nonce_field( 'ajax-custom-list-nonce', '_ajax_custom_list_nonce' );

        /**
         * Adds field order and orderby
         */
        echo '<input type="hidden" id="order" name="order" value="' . $this->_pagination_args['order'] . '" />';
        echo '<input type="hidden" id="orderby" name="orderby" value="' . $this->_pagination_args['orderby'] . '" />';

        parent::display();
    }

    /**
     * @Override ajax_response method
     */

    function ajax_response() {

        $this->prepare_items();

        extract( $this->_args );
        extract( $this->_pagination_args, EXTR_SKIP );

        ob_start();
        if ( ! empty( $_REQUEST['no_placeholder'] ) )
            $this->display_rows();
        else
            $this->display_rows_or_placeholder();
        $rows = ob_get_clean();

        ob_start();
        $this->print_column_headers();
        $headers = ob_get_clean();

        ob_start();
        $this->pagination('top');
        $pagination_top = ob_get_clean();

        ob_start();
        $this->pagination('bottom');
        $pagination_bottom = ob_get_clean();

        $response = array( 'rows' => $rows );
        $response['pagination']['top'] = $pagination_top;
        $response['pagination']['bottom'] = $pagination_bottom;
        $response['column_headers'] = $headers;

        if ( isset( $total_items ) )
            $response['total_items_i18n'] = sprintf( _n( '1 item', '%s items', $total_items ), number_format_i18n( $total_items ) );

        if ( isset( $total_pages ) ) {
            $response['total_pages'] = $total_pages;
            $response['total_pages_i18n'] = number_format_i18n( $total_pages );
        }

        die( json_encode( $response ) );
    }

    function export_leads_to_excel()
    {
        $search = $_REQUEST;
        $data = $this->custom_table_data($_REQUEST);
/*      echo'<pre>';
        print_r($data);
        echo '</pre><br>'; */
        $data = $this->format_date_for_exl_csv($data);
        //print_r($exceltocsv);
        //die;
        $column_headers = $this->get_export_columns();
        $column_index = 'A';
        ob_end_clean();
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        foreach ($column_headers as $column_key => $column_name) {
            if ($column_name && !in_array($column_name, ['Book A Call', 'Agreement', 'Call / Text', 'Action'])) {
                $sheet->setCellValue($column_index . '1', $column_name);
                $column_index++;
            }
        }
        $row_index = 2;
        foreach ($data as $row_data) {
            $column_index = 'A';
            foreach ($column_headers as $column_key => $column_name) {
                if (1) {
                    $display_data = $row_data[$column_key];
                    $display_data = htmlspecialchars_decode($display_data);
                    $display_data = str_replace('&#039;', "'", $display_data);
                    $display_data = str_replace('&amp;', "&", $display_data);
                    $sheet->setCellValue($column_index . $row_index, $display_data);
                    $column_index++;
                }
            }
            $row_index++;
        }


        date_default_timezone_set('America/New_York');

        // Get the current date and time in the desired format
        //$currentDate = date('Y-m-d');
        $currentDate = date('m/d/Y');
        $currentTime = (new DateTime())->format('H-i-s');
        $fileName = "STC_Project_Report_$currentDate" . "_" . "$currentTime.xlsx";

        ob_end_clean();
        $writer = new Xlsx($spreadsheet);

        ob_end_clean();
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . urlencode($fileName) . '"');

        ob_end_clean();
        $writer->save('php://output');
        exit;

    }

    function format_date_for_exl_csv($data)
    {
        $items = array();
        $i = 0; // Initialize $i outside of the foreach loop
        foreach ($data as &$item) {
            if (!empty($item['project_id'])) {
                $items[$i]['project_id'] = $item['project_id'];
                $items[$i]['business_name'] = $item['business_name'];
                $items[$i]['authorized_signatory_name'] = $item['authorized_signatory_name'];
                $items[$i]['business_email'] = $item['business_email'];
                $items[$i]['business_phone'] = $item['business_phone'];
                $items[$i]['project_name'] = $item['project_name'];
                $items[$i]['product_name'] = $item['product_name'];
                $items[$i]['milestone'] = $item['milestone'];
                $items[$i]['milestone_stage'] = $item['milestone_stage'];
                $items[$i]['taxnow_signup_status'] = $item['taxnow_signup_status'];
                $items[$i]['created_at'] = $item['created_at'];
                $items[$i]['collaborator'] = $item['collaborator'];

                $items[$i]['income_2019'] = $item['income_2019'];
                $items[$i]['income_2020'] = $item['income_2020'];
                $items[$i]['income_2021'] = $item['income_2021'];

                $items[$i]['bank_name'] = $item['bank_name'];
                $items[$i]['account_holder_name'] = $item['account_holder_name'];
                $items[$i]['account_number'] = $item['account_number'];
                $items[$i]['aba_routing_no'] = $item['aba_routing_no'];

                $items[$i]['stc_amount_2020'] = $item['stc_amount_2020'];
                $items[$i]['stc_amount_2021'] = $item['stc_amount_2021'];

                $items[$i]['maximum_credit'] = $item['maximum_credit'];
                $items[$i]['actual_credit'] = $item['actual_credit'];
                $items[$i]['estimated_fee'] = $item['estimated_fee'];
                $items[$i]['actual_fee'] = $item['actual_fee'];
                $items[$i]['years'] = $item['years'];
                $items[$i]['sales_agent'] = $item['sales_agent'];

                // If there are additional fields you want to include, add them here.
                $i++; // Increment $i at the end of each loop iteration
            }
        }
        return $items;
        //print_r($items);
    }

}
function wp_list_page() {
    /*<a href="admin.php?page=add_edit_project&action=add" class="add-opp-custom-icon export_invoice_excel"><i class="fa-solid fa-plus"></i> New Project</a>*/
    $wp_list_table = new STC_Project_Report_Table();
    $wp_list_table->search_box_new();
	// step4
    $confidence_user = $wp_list_table->check_confidence_user(); //step-4
    global $wpdb;
    ?>
    <div class="wrap woocommerce">
        <div class="loader_box pleasewait" style="display: none;">
            <div class="loading">
                <p class="loading__text">Please Wait. We are fetching the data.</p>
                <div class="loading__bar"></div>
            </div>
        </div>
        <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
        <script type="text/javascript">
            jQuery(document).ready(function() {
                //step-5
                /*
                jQuery(document).on("click",'.custom-notes-call', function() {
                    var projectid = $(this).data('projectid');
                    $("#add-new-custom-notes #project_id").val(projectid);
                    $('.note-response').html("");
                    $('.note-response').css('display', 'none');
                    $('.error-response').css('display', 'none');
                    $("#add-new-custom-notes").modal('show');

                });
                */
                jQuery(document).on("click",".custom-add-notes", function() {
                        var project_id = $(this).data('project_id');
                        $("#add-new-custom-notes #project_id").val(project_id);
                        $('.note-response').html("");
                        $('.note-response').css('display', 'none');
                        $('.error-response').css('display', 'none');
                        $("#add-new-custom-notes").modal('show');

                    });
                    jQuery(document).on("click",'.close-popup-notes', function() {
                        $("#add-new-custom-notes #project_id").val('');
                        $("#add-new-custom-notes #notes-input").val('');
                        $('.note-response').css('display', 'none');
                        $('.error-response').css('display', 'none');
                        $("#add-new-custom-notes").modal('hide');
                    });

                // Remove class when user inputs something
                $("#add-new-custom-notes #notes-input").on("input", function() {
                    if ($(this).val().trim() !== "") {
                        $(this).removeClass("red-border");
                    } else {
                        $(this).addClass("red-border");
                    }
                });
            });
// step6           
jQuery(document).on("click",'#create-note', function() {
                    $(this).text('Please wait...').prop('disabled', true);
                    var notes = $("#notes-input").val().trim();

                    if (notes == '' || notes == ' ' || notes == null) {
                        jQuery("#add-new-custom-notes #notes-input").addClass("red-border");
                        jQuery("#notes-input").focus();
                        jQuery("#notes-input").attr("placeholder", "Please enter notes");
                        jQuery("#notes-input").next().remove();
                           jQuery("#notes-error").show();
                        jQuery("#notes-input").after('<span class="error">Please enter notes</span>');
                        $(this).text('Submit').prop('disabled', false);
                        return false;
                    }else{
                            jQuery("#notes-error").hide();
                        }

                    var project_id = $("#add-new-custom-notes #project_id").val();
                    var created_by = $("#created_by").val();

                    var confidence_notes_access = jQuery("#confidence_user_check:checked").val();
                        if(typeof confidence_notes_access === "undefined"){
                            confidence_notes_access = 0;
                        }else{
                            confidence_notes_access = 1;
                        }

                        $.ajax({
                            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/add_edit_notes',
                            type: 'POST',
                            dataType: 'Json',
                            data: { action:'add_custom_note_data',note: notes, opp_product_id: project_id,user_id: created_by,confidence_notes_access:confidence_notes_access,note_type:'project'},
                        success: function(response) {
                            let parsedResponse = JSON.parse(response);
                                $('#create-note').text('Submit').prop('disabled', false);
                                if (parsedResponse.add === 1) {
                                    
                                    $("#notes-input").val('');
                                    $("#add-new-custom-notes #project_id").val('');
                                    jQuery("#confidence_user_check").prop('checked',false);
                                    $("#add-new-custom-notes").modal('hide');
                                    swal("Success", "Note added successfully.", "success");
                                    // location.reload();
                                } else {
                                    swal("Error", "Failed to add note.", "error");
                                    // location.reload();
                                }
                        }
                    });
                 });//-------- Notes create js end

                //step-6
// ----- Notes listing js start ------ 
                jQuery(document).on('click', '.view_comment_notes', function () {
                    var project_id = jQuery(this).data('project_id');
                    $("#exampleModal_ercdoc_view").modal('show');
                    jQuery('#exampleModal_ercdoc_view .modal-body').html('<div style="text-align:center;"><i aria-hidden="true" class="fa fa-spinner fa-spin"></i></div>');
                    $.ajax({
                        url: doc_upload.ajaxurl,
                        type: 'POST',
                        dataType:'json',
                        data: {
                            action: 'view_opp_project_notes',
                            opp_project_id: project_id,
                            note_type:'project'
                        },
                        success: function (response) {
                            // var json_response = JSON.parse(response);
                            jQuery('#exampleModal_ercdoc_view .modal-body').html('');
                            var inc = 0;

                            jQuery.each(response, function (index, value) {
                                    
                                    jQuery('#exampleModal_ercdoc_view .modal-body').append(value);

                                // if (index != 0) {
                                //     var prepare_html_op = "<div style='display:none;' class='iris_comment_view_all iris_comment_" + index + "'>" + value + "</div>";
                                //     jQuery('#exampleModal_ercdoc_view .modal-body').append(prepare_html_op);
                                // } else {
                                //     var prepare_html_op = "<div class='iris_comment_view_all iris_comment_" + index + "'>" + value + "</div>";
                                //     jQuery('#exampleModal_ercdoc_view .modal-body').append(prepare_html_op);
                                // }
                                 inc++;
                            });
                            
                            if(inc == 0){
                                jQuery('#exampleModal_ercdoc_view .modal-body').append('No any notes available.');
                            }

                            if (inc == 1) {
                                jQuery('#exampleModal_ercdoc_view .modal-footer .btn').hide();
                            } else if(inc !=0){
                                jQuery('#exampleModal_ercdoc_view .modal-footer .btn').show();
                            }

                        }
                    });
                    jQuery(document).on('click', '#exampleModal_ercdoc_view .modal-footer .btn', function () {
                        jQuery('.iris_comment_view_all').show();
                        jQuery('#exampleModal_ercdoc_view .modal-footer .btn').hide();
                    });
                });   
            // ----- Notes listing end js ------

                //step-7
                jQuery(document).ready(function(){
                            jQuery('#notes-input').keypress(function (event) {
                                var Length = jQuery("#notes-input").val().length;
                                  maxLen = 1000;
                                if (Length >= maxLen) {
                                    if (event.which != 8) {
                                        //swal("Error", "Only 70 characters are allowed", "error");
                                        return false;
                                    }
                                }
                            });

                            const maxLength = 1000;
                            jQuery('#notes-input').on('input', function() {
                                const remainingChars = maxLength - $(this).val().length;
                                jQuery('#remaining-msg').text(remainingChars + '/1000 characters remaining.');
                            });
                        });
            </script>
        <style type="text/css">
            .custom-notes-call {
                color: #fff !important;
                padding: 0.3rem !important;
                background: transparent linear-gradient(89deg, #ff5c00 0%, #ff5c00 100%) 0% 0% no-repeat padding-box;
                text-align: center;
                margin: 0 auto;
                width: 26px;
                height: 26px !important;
                cursor: pointer;
                border-radius: 50% !important;
                line-height: 17px;
                display: inline-block;
            }
            .custom-crm-erp-opp-report #notes {
              width: 7%;
            }
            .red-border {
                border: 1px solid red !important;
            }
/*            .custom-crm-erp-project-report #notes{width: 5%;}*/
			#echeck_report_table_wrap .column-taxnow_signup_status {width: 6%;}
        </style>
        <!-- step-8 -->
        <link rel='stylesheet' id='style-css'  href=<?php echo get_site_url() . '/wp-content/plugins/wp-iris/css/notes.css'; ?> media='all' />

            <div class="modal opportunity-add-new-notes" id="add-new-custom-notes" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-comment"></i> Add Note</h5>
                            <button type="button" class="close-popup-notes close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="show_message_popup">
                                <p class="note-response" style="display: none;"></p>
                                <p class="error-response" style="display: none;">Notes is required.</p>
                            </div>

                            <div class="row">
                                <div class="floating col-md-12">


                                    <input type="hidden" name="project_id" id="project_id">
                                    <input type="hidden" name="created_by" id="created_by" value="<?php echo get_current_user_id(); ?>">

                                    <textarea id="notes-input" class="form-control" rows="5" maxlength="1000" placeholder="Only 1000 characters are allowed"></textarea>
                                    <p id="notes-error">Please enter notes.</p>
                                    <?php if($confidence_user==1){
                                             $confidence_check = '';
                                            if(get_current_user_id() == 44019){ // nedeen id 
                                                  $confidence_check = 'checked';
                                            }
                                    ?>
                                        Mark As Confidential  <input type="checkbox" name="confidence_user_check" id="confidence_user_check" value="1" style="margin-top:0px;" <?php echo $confidence_check; ?>>
                                    <?php } ?>
                                    <p id="remaining-msg">1000/1000 characters remaining.</p>
                                </div>
                            </div>
                            <div class="buttion_next_prev">
                                <button type="button" class="nxt_btn" id="create-note">Submit</button>
                            </div>
                        </div>
                    </div>
                    </div>
                </div>

            <!-- View comment Modal -->
                <div class="modal" id="exampleModal_ercdoc_view" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="exampleModalLabel">Notes</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body" style="max-height:200px; overflow-y:auto;">
                                <input type="hidden" name="view_comment_id" class="view_comment_id" id="view_comment_id">
                                <span class="comment_username" style="font-size:14px">User Name</span>
                                <p class="mt-2"></p>
                                <span class="comment_date" style="float: right; font-size:12px">Comment Date:- 05-12-2023</span>
                                <br>
                            </div>
                            <div class="modal-footer justify-content-center">
                                <button type="button" class="btn btn-warning">More Notes</button>
                            </div>
                        </div>
                    </div>
                </div>
         <!-- step-8 end-->   
        <?php
    //$wp_list_table->prepare_items();  
        
            echo '<div class="white_card card_height_100 mb_30">';
            echo '<div class="white_card_header ">
            <div class="box_header m-0 new_report_header">
                    <div class="title_img">
                        <img src="'.get_site_url().'/wp-content/plugins/oc-crm/assets/img/project-icon.png" class="page-title-img" alt="">
                        <h4>STC Projects</h4>
                    </div>
                    <div class="invoice_exports">
                        
                        <a href="javascript:void(0);" class="status_btn_export export_invoice_excel export_products export_project_list">Export</a> 
                        <form method="post" id="export_project_form">
                        <input type="hidden" name="page" value="export_projects.php">
                        <input type="hidden" name="project_name" value="">
                        <input type="hidden" name="product_name" value="">
                        <input type="hidden" name="business_name" value="">
                        <input type="hidden" name="start_date" value="">
                        <input type="hidden" name="end_date" value="">
                        <input type="hidden" name="authorized_signatory_name" value="">
                        <input type="hidden" name="business_email" value="">
                        <input type="hidden" name="business_phone" value="">
                        <input type="hidden" name="milestone_id" value="">
                        <input type="hidden" name="milestone_stage_id" value="">
                        <input type="hidden" name="taxnow_signup_status" value="">
                        <input type="hidden" name="project_id" value="">
                        <input type="hidden" name="download_excel_custom_report" class="common_export_btn" id="excelButton" value="Export">
                        </form>
                    </div>
                </div>
            </div>';
            echo '<div class="loader_box" id="loader_box" style="display: none;">';
            echo    '<div class="loading">';
            echo        '<p class="loading__text">Please Wait. Deleting Project.</p>';
            echo        '<div class="loading__bar"></div>';
            echo    '</div>';
            echo '</div>';
            echo '<div class="white_card_body custom-crm-erp-test-stc-project-report p-15" id="echeck_report_table_wrap">';
            echo '';

            echo '<form method="get" action="" id="project_table_form">';
            echo '<input type="hidden" name="page" value="'.$_GET['page'].'">';
            echo '<div class="custom_lead_details">';
            echo '</div>';
            echo '</form>';
            echo '</div>';
            echo '</div></div></div>';
            include(WP_CONTENT_DIR . '/plugins/affiliate_portal/page-templates/footer.php');
            ?>
    <?php 
}

/**
 * fetch_ts_script function based from Charlie's original function
 */

function fetch_ts_script() {
    ?>
    <script type="text/javascript">console.log("<?php echo $screen->id; ?>")</script>
    <script src='https://play.occamsadvisory.com/portal/wp-content/plugins/invoice-create/js/invoice-script.js?ver=5.4'></script>
    <script type="text/javascript">
        $(document).ready(function() {
            jQuery(document).on("change",".sales_agent_id",function(){
                var sales_agent_id = jQuery(this).val();;
                var project_id = jQuery(this).find(':selected').attr('data-id');
                var user_name = jQuery(this).find(':selected').attr('user_name');
                if(sales_agent_id > 0){
                    var conf_msg = "Are you sure, Want to assign project #"+project_id+" to "+user_name+"?";
                }else{
                    var conf_msg = "Are you sure, Want to remove the assigned sales agent!";
                }
                var ajaxurl = 'admin-ajax.php';
                jQuery.ajax({
                    url: ajaxurl,
                    data: {
                        action: 'update_sales_agent_of_project',
                        project_id: project_id,
                        sales_agent_id:sales_agent_id,
                        user_name:user_name,
                    },
                    type: 'POST',
                    beforeSend:function(){
                         return confirm(conf_msg);
                    },
                    success: function(data)
                    {
                        alert("Sales agent "+user_name+" assigned successfully on project #"+project_id+"");
                        return false;
                    }
                });
            });
            jQuery(document).on("change",".assigned_sales_support",function(){
                var sales_support_id = jQuery(this).val();
                var project_id = jQuery(this).find(':selected').attr('data-id');
                var user_name = jQuery(this).find(':selected').attr('user_name');
                if(sales_support_id > 0){
                    var conf_msg = "Are you sure, Want to assign project #"+project_id+" to "+user_name+"?";
                }else{
                    var conf_msg = "Are you sure, Want to remove the assigned sales agent!";
                }
                var ajaxurl = 'admin-ajax.php';
                jQuery.ajax({
                    url: ajaxurl,
                    data: {
                        action: 'update_sales_support_of_project',
                        project_id: project_id,
                        sales_support_id:sales_support_id,
                        user_name:user_name,
                    },
                    type: 'POST',
                    beforeSend:function(){
                         return confirm(conf_msg);
                    },
                    success: function(data)
                    {
                        alert("Sales support "+user_name+" assigned successfully on project #"+project_id+"");
                        return false;
                    }
                });
            });
            jQuery(document).on('click', '.card-link', function(e) {
                e.preventDefault();
                // Get the row ID from the data attribute
                var rowId = jQuery(this).data('row');
                // Show the corresponding card popup
                jQuery('#card_box-' + rowId).show();
                });  
            // Handler for the change event of #agreeCheckbox
            $(document).on('change', '#agreeCheckbox', function() {
                // Check if #agreeCheckbox is checked
                if ($(this).prop('checked')) {
                    $(".swal-button--confirm").css("pointer-events", "auto");
                } else {
                    $(".swal-button--confirm").css("pointer-events", "none");
                }
            });

            $(document).on('click', '.swal-button-container:has(.swal-button--confirm)', function() {
                if ($('#agreeCheckbox').prop('checked')) {
                    return true;
                } else {
                    // Check if error message already exists
                    if (!$('.swal-content + p.error-message').length) {
                        // Append the error message if it doesn't exist
                        $('.swal-content').after('<p class="error-message" style="color: red; margin: 20px;">Please agree to Delete the Project!</p>');
                    }
                }
            });
        });
        (function ($) {
            list = {
                display: function() {
                    var deleteItemID = [];
                    $(".deleteItemID:checked").each(function() {
                        deleteItemID.push($(this).val());
                    });
                    $.ajax({
                        url: ajaxurl,
                        dataType: 'json',
                        data: {
                            page: '<?php echo $_GET['page']  ?>',
                            _ajax_custom_list_nonce: $('#_ajax_custom_list_nonce').val(),
                            action: 'stc_project_listing_ajax',
                            deleteItem: deleteItemID,
                            action2: $('#bulk-action-selector-bottom').val(),
                        },
                        success: function (response) {
                            $(".custom_lead_details").html(response.display);
                            $("tbody").on("click", ".toggle-row", function(e) {
                                e.preventDefault();
                                $(this).closest("tr").toggleClass("is-expanded")
                            });

                            list.init();
                        }
                    });
                },
                init: function () {
                    var timer;
                    var delay = 500;
                    $('.tablenav-pages a, .manage-column.sortable a, .manage-column.sorted a').on('click', function (e) {
                        jQuery('.pleasewait').show();
                        e.preventDefault();
                        //alert(1);
                        var query = this.search.substring(1);
                        var data = {
                            page: '<?php echo $_GET['page']  ?>',
                            paged: list.__query( query, 'paged' ) || '1',
                            order: list.__query( query, 'order' ) || 'asc',
                            orderby: list.__query( query, 'orderby' ) || 'project_id',
                            project_id: $('#project_id').val() || '',
                            authorized_signatory_name: $('#authorized_signatory_name').val() || '',
                            business_email: $('#business_email').val() || '',
                            business_phone: $('#business_phone').val() || '',
                            milestone_id: $('#milestone_id').val() || '',
                            milestone_stage_id: $('#milestone_stage_id').val() || '',
                            taxnow_signup_status: $('#taxnow_signup_status').val() || '',
                            product_id: $('#product_id').val() || '',
                            project_name: $('#project_name').val() || '',
                            start_date: $('#date_timepicker_start').val() || '',
                            end_date: $('#date_timepicker_end').val() || '',
                            business_name: $('#business-name-select').val() || '',
                        };
                        list.update(data);
                    });

                    $('input[name=paged]').on('keyup', function (e) {
                        jQuery('.pleasewait').show();
                        if (13 == e.which)
                            e.preventDefault();
                            var data = {
                                page: '<?php echo $_GET['page']  ?>',
                                paged: parseInt($('input[name=paged]').val()) || '1',
                                order: $('input[name=order]').val() || 'asc',
                                orderby: $('input[name=orderby]').val() || 'project_id',
                                project_id: $('#project_id').val() || '',
                                authorized_signatory_name: $('#authorized_signatory_name').val() || '',
                                business_email: $('#business_email').val() || '',
                                business_phone: $('#business_phone').val() || '',
                                milestone_id: $('#milestone_id').val() || '',
                                milestone_stage_id: $('#milestone_stage_id').val() || '',
                                taxnow_signup_status: $('#taxnow_signup_status').val() || '',
                                product_id: $('#product_id').val() || '',
                                project_name: $('#project_name').val() || '',
                                start_date: $('#date_timepicker_start').val() || '',
                                end_date: $('#date_timepicker_end').val() || '',
                                business_name: $('#business-name-select').val() || '',
                            };
                            window.clearTimeout(timer);
                            timer = window.setTimeout(function () {
                                list.update(data);
                            }, delay);
                    });
                },

                /** AJAX call
                 *
                 * Send the call and replace table parts with updated version!
                 *
                 * @param    object    data The data to pass through AJAX
                 */
                update: function (data) {
                    $.ajax({
                        url: ajaxurl,
                        data: $.extend(
                            {
                                page: '<?php echo $_GET['page']  ?>',
                                _ajax_custom_list_nonce: $('#_ajax_custom_list_nonce').val(),
                                action: 'stc_fetch_project_filter',
                            },
                            data
                        ),
                        success: function (response) {
                            var response = $.parseJSON(response);
                            if (response.rows.length)
                                $('#the-list').html(response.rows);
                            if (response.column_headers.length)
                                $('thead tr, tfoot tr').html(response.column_headers);
                            if (response.pagination.bottom.length)
                                $('.tablenav.top .tablenav-pages').html($(response.pagination.top).html());
                            if (response.pagination.top.length)
                                $('.tablenav.bottom .tablenav-pages').html($(response.pagination.bottom).html());

                            list.init();
                            $('#btn_search_submit').val('Submit');
                            $('.date_timepicker_submit').val('Submit');
                            $('#export_project_form input[name="authorized_signatory_name"]').val( $('#authorized_signatory_name').val() );
                            $('#export_project_form input[name="business_email"]').val( $('#business_email').val() );
                            $('#export_project_form input[name="business_phone"]').val( $('#business_phone').val() );
                            $('#export_project_form input[name="business_name"]').val( $('#business-name-select').val() );
                            $('#export_project_form input[name="milestone_id"]').val( $('#milestone_id').val() );
                            $('#export_project_form input[name="milestone_stage_id"]').val( $('#milestone_stage_id').val() );
                            $('#export_project_form input[name="taxnow_signup_status"]').val( $('#taxnow_signup_status').val() );
                            $('#export_project_form input[name="milestonefilter"]').val( $('#milestonefilter').val() );
                            $('#export_project_form input[name="product_id"]').val( $('#product_id').val() );
                            $('#export_project_form input[name="project_name"]').val( $('#project_name').val() );
                            $('#export_project_form input[name="business_name"]').val( $('#business-name-select').val() );
                            $('#export_project_form input[name="project_id"]').val( $('#project_id').val() );
                            jQuery(".popup-overlay, .popup-content").removeClass("active");
                            jQuery('#overlay').hide();
                            jQuery('.pleasewait').hide();
                        },
                        error: function(jqXHR, textStatus, errorThrown) {        
                            $('#btn_search_submit').val('Submit');
                            $('.date_timepicker_submit').val('Submit');
                            jQuery(".popup-overlay, .popup-content").removeClass("active");
                            jQuery('#overlay').hide();
                            jQuery('.pleasewait').hide();    

                         },
                    });
                },

                /**
                 * Filter the URL Query to extract variables
                 *
                 * @see http://css-tricks.com/snippets/javascript/get-url-variables/
                 *
                 * @param    string    query The URL query part containing the variables
                 * @param    string    variable Name of the variable we want to get
                 *
                 * @return   string|boolean The variable value if available, false else.
                 */
                __query: function (query, variable) {

                    var vars = query.split("&");
                    for (var i = 0; i < vars.length; i++) {
                        var pair = vars[i].split("=");
                        if (pair[0] == variable)
                            return pair[1];
                    }
                    return false;
                },
            }
            list.display();
            //event for filters
            $('.date_timepicker_submit, #btn_search_submit').on('click', function (e) {
                jQuery('.pleasewait').show();
                var buttonValue = $(this).val();
                $(this).val('Wait..');
                 var data = {
                    buttonValue: buttonValue,
                    page: '<?php echo $_GET['page']  ?>',
                    paged: '1',
                    project_id: $('#project_id').val() || '',
                    authorized_signatory_name: $('#authorized_signatory_name').val() || '',
                    business_email: $('#business_email').val() || '',
                    business_phone: $('#business_phone').val() || '',
                    milestone_id: $('#milestone_id').val() || '',
                    milestone_stage_id: $('#milestone_stage_id').val() || '',
                    taxnow_signup_status: $('#taxnow_signup_status').val() || '',
                    product_id: $('#product_id').val() || '',
                    project_name: $('#project_name').val() || '',
                    business_name: $('#business-name-select').val() || '',
                    start_date: $('#date_timepicker_start').val() || '',
                    end_date: $('#date_timepicker_end').val() || '',
                };
                list.update(data);
            });
            $('.common_export_btn').on('click', function (e) {
                e.preventDefault();
                //alert(1);
                $(this).val('Wait...');

                var data = {
                    page: '<?php echo $_GET['page']  ?>',
                    download_excel_custom_report: 'yes',
                    project_id: $('#project_id').val() || '',
                    //authorized_signatory_name: $('#authorized_signatory_name').val() || '',
                    authorized_signatory_name: 'satish',                    
                    business_email: $('#business_email').val() || '',
                    business_phone: $('#business_phone').val() || '',
                    milestone_id: $('#milestone_id').val() || '',
                    milestone_stage_id: $('#milestone_stage_id').val() || '',
                    taxnow_signup_status: $('#taxnow_signup_status').val() || '',
                    product_id: $('#product_id').val() || '',
                    project_name: $('#project_name').val() || '',
                    business_name: $('#business-name-select').val() || '',
                    start_date: $('#date_timepicker_start').val() || '',
                    end_date: $('#date_timepicker_end').val() || '',
                };
                
                var siteurl = '<?php echo get_site_url(); ?>/wp-admin/admin.php?';
                $.each(data, function(key,val) {
                    siteurl = siteurl+key+'='+val+'&'; 
                });
                window.location.replace(siteurl);
                $(this).val('Export');
            });
            jQuery(document).on("click",'#doaction2', function(e) {
                e.preventDefault();
                if($('#bulk-action-selector-bottom').val()=='delete'){
                    var checkedCount = $('.wp-list-table').find(".deleteItemID:checked").length;
                    if(checkedCount>0){
                        swal({
                            title: "Are you sure?",
                            text: "Yes, I want to delete selected Projects.",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                                confirm: "Delete",
                            },
                            content: {
                                element: "input",
                                attributes: {
                                    type: "checkbox",
                                    id: "agreeCheckbox"
                                },
                            },
                            dangerMode: true,
                        })
                        
                        .then((willDelete) => {
                            if (willDelete && $('#agreeCheckbox').prop('checked')) {
                                $(this).attr('disabled',true);
                                $("#loader_box").show();
                                var deleteItemID = [];
                                $(".deleteItemID:checked").each(function() {
                                    deleteItemID.push($(this).val());
                                });
                                
                                jQuery.ajax({
                                    url:'<?php echo admin_url('admin-ajax.php'); ?>',
                                    method:'post',
                                    data:{action: 'delete_project', project_id: deleteItemID},
                                    success(response){
                                        window.location.href = '?page=projects';
                                    },
                                    error: function(jqXHR, textStatus, errorThrown) {        
                                        $('#btn_search_submit').val('Submit');
                                        $('.date_timepicker_submit').val('Submit');
                                        jQuery(".popup-overlay, .popup-content").removeClass("active");
                                        jQuery('#overlay').hide();
                                        jQuery('.pleasewait').hide();    

                                     },
                                }); 
                            }
                            $('.swal-modal').removeClass('crm-erp-project-delete-swal');
                        });
                        $('.swal-modal').addClass('crm-erp-project-delete-swal');
                        
                    }else{
                        swal({
                            title: "Please select atleast one item",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                    }
                }else{ 
                    swal({
                            title: "Please select some action.",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                }
            });

        })(jQuery);
    </script>
    <script type="text/javascript">
        $(document).ready(function(){
            $('.export_project_list').click(function(){
                $('#export_project_form').submit();
            });
        });
    </script>
    <?php
}

add_action('admin_footer', 'fetch_ts_script');
function fetch_ts_style()
{
    ?>
        <link rel='stylesheet' id='style-css'  href=<?php echo get_site_url() . '/wp-content/plugins/invoice-create/css/invoice-style.css?ver=5.4'; ?> media='all' />
    <?php
}
add_action('admin_header', 'fetch_ts_style');