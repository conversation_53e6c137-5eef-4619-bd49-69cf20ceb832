
<?php
// Set a flag based on the action value
global $wpdb;
        // ini_set('display_errors', 1);
        // ini_set('display_startup_errors', 1);
        // error_reporting(E_ALL);
        
/*$wpdb->query("ALTER TABLE eccom_crm_products RENAME COLUMN CustomField1 TO CustomFieldLabel1");
$wpdb->query("ALTER TABLE eccom_crm_products RENAME COLUMN CustomField2 TO CustomFieldLabel2");
$wpdb->query("ALTER TABLE eccom_crm_products RENAME COLUMN CustomField3 TO CustomFieldLabel3");
$wpdb->query("ALTER TABLE eccom_crm_products RENAME COLUMN CustomField4 TO CustomFieldLabel4");
$wpdb->query("ALTER TABLE eccom_crm_products RENAME COLUMN CustomField5 TO CustomFieldLabel5");
$wpdb->query("ALTER TABLE eccom_crm_products ADD COLUMN CustomFieldValue1 VARCHAR(100) NOT NULL DEFAULT '' AFTER CustomFieldLabel1");
$wpdb->query("ALTER TABLE eccom_crm_products ADD COLUMN CustomFieldValue2 VARCHAR(100) NOT NULL DEFAULT '' AFTER CustomFieldLabel2");
$wpdb->query("ALTER TABLE eccom_crm_products ADD COLUMN CustomFieldValue3 VARCHAR(100) NOT NULL DEFAULT '' AFTER CustomFieldLabel3");
$wpdb->query("ALTER TABLE eccom_crm_products ADD COLUMN CustomFieldValue4 VARCHAR(100) NOT NULL DEFAULT '' AFTER CustomFieldLabel4");
$wpdb->query("ALTER TABLE eccom_crm_products ADD COLUMN CustomFieldValue5 VARCHAR(100) NOT NULL DEFAULT '' AFTER CustomFieldLabel5");*/

/*$wpdb->query("ALTER TABLE eccom_product_service_head ADD COLUMN CreatedBy VARCHAR(36) NOT NULL DEFAULT ''");
$wpdb->query("ALTER TABLE eccom_product_service_head ADD COLUMN ModifiedAt DATETIME");
$wpdb->query("ALTER TABLE eccom_product_service_head ADD COLUMN ModifiedBy VARCHAR(36) NOT NULL DEFAULT ''");
$wpdb->query("ALTER TABLE eccom_product_service_head ADD COLUMN DeletedAt DATETIME");
$wpdb->query("ALTER TABLE eccom_product_service_head ADD COLUMN DeletedBy VARCHAR(36) NOT NULL DEFAULT ''");*/
//die;
/*$wpdb->query("UPDATE eccom_fee_structure SET fee_structure = 'Retainer + Fixed' WHERE fee_type_id = 10");*/
//die;
$isViewMode = ($_GET['action'] == 'view');
if($_REQUEST['action'] == 'edit' || $_REQUEST['action'] == 'view'){
    $product_id = $_REQUEST['id'];
    $product_manager = new CRM_ERP_Product_Manager();
    $products_data = $product_manager->get_product($product_id);
    $Title = $products_data->Title;
    $SKU = $products_data->SKU;
    $currency_id = $products_data->currency_id;
    $UnitPrice = $products_data->UnitPrice;
    $HourlyPrice = $products_data->HourlyPrice;
    $UnitTypeID = $products_data->UnitTypeID;
    $OwnerID = $products_data->OwnerID;
    $ProductTypeID = $products_data->ProductTypeID;
    $CategoryID = $products_data->CategoryID;
    $ProductLogo = $products_data->ProductLogo;
    $ProductImage = $products_data->ProductImages;
    $LaunchDate = $products_data->LaunchDate;
    $ExpiryDate = $products_data->ExpiryDate;
    $Description = $products_data->Description;
    $Status = $products_data->Status;
    $CustomFieldLabel1 = $products_data->CustomFieldLabel1;
    $CustomFieldValue1 = $products_data->CustomFieldValue1;
    $CustomFieldLabel2 = $products_data->CustomFieldLabel2;
    $CustomFieldValue2 = $products_data->CustomFieldValue2;
    $CustomFieldLabel3 = $products_data->CustomFieldLabel3;
    $CustomFieldValue3 = $products_data->CustomFieldValue3;
    $CustomFieldLabel4 = $products_data->CustomFieldLabel4;
    $CustomFieldValue4 = $products_data->CustomFieldValue4;
    $CustomFieldLabel5 = $products_data->CustomFieldLabel5;
    $CustomFieldValue5 = $products_data->CustomFieldValue5;
    $product_roles = explode(",",$products_data->product_roles);
    $heading_text = $isViewMode ? 'View' : 'Edit';
    $btn_text = 'Update';
    $heading_img = 'edit-crm-icon.png';
}else{
    $product_id = '';
    $Title = '';
    $SKU = '';
    $currency_id = '';
    $UnitPrice = '';
    $HourlyPrice = '';
    $UnitTypeID = '';
    $OwnerID = '';
    $ProductTypeID = '';
    $CategoryID = '';
    $ProductLogo = '';
    $ProductImage = '';
    $LaunchDate = '';
    $ExpiryDate = '';
    $Description = '';
    $Status = '';
    $CustomFieldLabel1 = '';
    $CustomFieldValue1 = '';
    $CustomFieldLabel2 = '';
    $CustomFieldValue2 = '';
    $CustomFieldLabel3 = '';
    $CustomFieldValue3 = '';
    $CustomFieldLabel4 = '';
    $CustomFieldValue4 = '';
    $CustomFieldLabel5 = '';
    $CustomFieldValue5 = '';
    $product_roles = array();
    $heading_text = 'New';
    $heading_img = 'add-crm-icon.png';
    $btn_text = 'Submit';
}


$where_milestone = ' 1=1 ';
$milestone_table = $wpdb->prefix.'milestones';
$milestone_status_table = $wpdb->prefix.'milestone_stages';
$product_setting_table = $wpdb->prefix.'crm_product_setting';

if(isset($product_id)&&!empty($product_id)){
    $where_milestone .= ' AND FIND_IN_SET('.$product_id.','.$milestone_table.'.product_id) AND '.$milestone_table.'.status = "active" AND '.$milestone_table.'.deleted_at IS NULL';
}
$all_milestones =  $wpdb->get_results("SELECT $milestone_table.milestone_id,$milestone_table.milestone_name, $milestone_table.map, $milestone_table.status, $milestone_table.deleted_at FROM $milestone_table WHERE $where_milestone ");

$where_stage = ' 1=1 ';
if(isset($milestone_id)&&!empty($milestone_id)){
    $where_stage .= " AND ".$milestone_table.".product_id is not null AND ".$milestone_status_table.".milestone_id = ".$milestone_id."" ;
}

    $where_stage .= "AND ".$milestone_status_table.".status = 'active' AND ".$milestone_status_table.".deleted_at IS NULL";

    $all_milestone_status =  $wpdb->get_results("SELECT $milestone_status_table.milestone_stage_id,$milestone_status_table.stage_name, $milestone_status_table.status, $milestone_status_table.deleted_at FROM $milestone_status_table left join $milestone_table on $milestone_status_table.milestone_id = $milestone_table.milestone_id WHERE $where_stage ");

    
    $product_setting =  $wpdb->get_row("SELECT * FROM $product_setting_table WHERE product_id=$product_id");
    $create_opportunity_milestone = $product_setting->create_opportunity_milestone;
    $create_opportunity_stage = $product_setting->create_opportunity_stage;
    $create_project_milestone = $product_setting->create_project_milestone;
    $create_project_milestonestage = $product_setting->create_project_milestonestage;
	
	//$billingProfiles = $wpdb->get_results("SELECT id, profile_name FROM eccom_billing_profiles", ARRAY_A);

	$query = "
		SELECT 
			bp.id AS billing_profile_id, 
			bp.profile_name AS billing_profile_name,
			pg.gateway_client_key AS gateway_client_key,
			pg.gateway_client_id AS gateway_client_id,
			pg.organisation_id AS organisation_id
		FROM 
			eccom_billing_profiles AS bp
		INNER JOIN 
			eccom_payment_gateways AS pg 
		ON 
			bp.payment_gateway_id = pg.id
	";

	$billingProfiles = $wpdb->get_results($query, ARRAY_A);
?>
<link rel="stylesheet" id="dataTable_Style-css" href="https://cdn.datatables.net/1.10.22/css/jquery.dataTables.min.css?ver=6.2.2" media="all">

<div class="main_content_iner ">
    <div class="container-fluid p-0">
         <div class="row justify-content-center">
            <div class="col-lg-12">
                <div id="form_2" class="white_card card_height_100 mb_30" style="display: block;">
                     <div class="white_card_header">
                        <div class="box_header m-0 new_report_header">
                            <div class="title_img">
                            <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/<?php echo $heading_img; ?>" class="page-title-img" alt="">
                            <h4><?php echo $heading_text;?> <?php if($product_id != ''){echo ' - '.$Title;}?> Product</h4>
                            </div>
                        </div>
                        <div class="loader_box" id="loader_box" style="display: none;">
                            <div class="loading">
                                <p class="loading__text">Please Wait.</p>
                                <div class="loading__bar"></div>
                            </div>
                        </div>
                        <?php if($product_id != '' && !$isViewMode){?>
                        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                            <li class="nav-item <?php echo (!(isset($_GET['view']) && $_GET['view'] === 'qb'))? 'active' : ''; ?>">
                                <a class="nav-link" id="pills-erc-edit-info" data-toggle="pill" href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true">Edit</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="pills-erc-fees-type" data-toggle="pill" href="#pills-fees-log" role="tab" aria-controls="pills-fees" aria-selected="true">Fee Type</a>
                            </li>
                            <li class="nav-item <?php echo (isset($_GET['view']) && $_GET['view'] === 'qb')? 'active' : ''; ?>">
                                <a class="nav-link" id="pills-erc-quickbook-setting" data-toggle="pill" href="#pills-quickbook-setting" role="tab" aria-controls="pills-quickbook" aria-selected="true">Quickbook Settings</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="pills-erc-role-setting" data-toggle="pill" href="#pills-role-setting" role="tab" aria-controls="pills-role" aria-selected="true">Role Settings</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="pills-erc-audit-log-info" data-toggle="pill" href="#pills-audit-log" role="tab" aria-controls="pills-audit-log" aria-selected="true">Audit Log</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="pills-milestones-stage" data-toggle="pill" href="#pills-milestones-stage-log" role="tab" aria-controls="pills-milestones-stage-log" aria-selected="true">Milestones & stage setting</a>
                            </li>
                        </ul>
                        <?php } ?>
                    </div>
                    <div class="white_card_body p-15">
                        <div class="container-fluid">
                            <div class="tab-content" id="pills-tabContent">
                                <div class="tab-pane scroll_height-business scroll-common <?php echo (!(isset($_GET['view']) && $_GET['view'] === 'qb'))? 'active' : ''; ?>" id="pills-home" role="tabpanel" aria-labelledby="pills-erc-edit-info">
                                    <form id="product-form" method="post" enctype="multipart/form-data">
                                        <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="title">Title:*</label>
                                                <input type="text" name="Title" id="Title" class="crm-erp-field form-control" value="<?php echo $Title;?>"<?php echo ($isViewMode) ? "readonly" : ""; ?>>
                                                <span id="TitleErr" class="error"></span>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="title">SKU:*</label>
                                                <input type="text" name="SKU" id="SKU" class="crm-erp-field form-control" value="<?php echo $SKU;?>"<?php echo ($isViewMode) ? "readonly" : ""; ?>>
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="title">Currency:*</label>
                                                <select id="currency_id" class="crm-erp-field form-control"<?php echo ($isViewMode) ? "disabled" : "disabled"; ?>>
                                                    <option value="">Select Currency</option>
                                                    <?php foreach($currencies as $currency): 
                                                            if($currency->currency_id == 4){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = "";
                                                            }
                                                    ?>
                                                        <option value="<?php echo $currency->currency_id; ?>" <?php echo $sel;?>><?php echo $currency->currency_code.' ('.$currency->currency_name.')'; ?></option>
                                                    <?php endforeach; ?>
                                                </select>
                                                <input type="hidden" name="currency_id" value="4">
                                            </div>
                                            <!-- <div class="floating col-md-6">     
                                                <label for="title">Unit Price:*</label>
                                                <input type="number" step="0.01" name="UnitPrice" id="UnitPrice" class="crm-erp-field form-control" value="<?php echo $UnitPrice;?>"<?php echo ($isViewMode) ? "readonly" : ""; ?>>
                                            </div> -->
                                            <div class="floating col-md-6">     
                                                <label for="title">Owner:*</label>
                                                <select name="OwnerID" id="OwnerID" class="crm-erp-field form-control"<?php echo ($isViewMode) ? "disabled" : ""; ?>>
                                                    <option value="">Select Owner</option>
                                                    <?php foreach($users as $user): 
                                                            if($user->ID == $OwnerID){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = '';
                                                            }
                                                    ?>
                                                        <option value="<?php echo $user->ID; ?>" <?php echo $sel;?>><?php echo $user->display_name; ?></option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                        </div>
                                        <!-- <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="title">Hourly Price:</label>
                                                <input type="number" step="0.01" name="HourlyPrice" id="HourlyPrice" class="crm-erp-field form-control" value="<?php echo $HourlyPrice;?>"<?php echo ($isViewMode) ? "readonly" : ""; ?>>
                                            </div>
                                        </div> -->
                                        <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="title">Unit Type:</label>
                                                <select name="UnitTypeID" id="UnitTypeID" class="crm-erp-field form-control"<?php echo ($isViewMode) ? "disabled" : ""; ?>>
                                                    <option value="0">Select Unit Type</option>
                                                    <?php foreach($unit_types as $unit): 
                                                            if($unit->UnitTypeID == $UnitTypeID){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = '';
                                                            }
                                                    ?>
                                                        <option value="<?php echo $unit->UnitTypeID; ?>" <?php echo $sel;?>><?php echo $unit->UnitName; ?></option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="title">Product Type:*</label>
                                                <select name="ProductTypeID" id="ProductTypeID" class="crm-erp-field form-control"<?php echo ($isViewMode) ? "disabled" : ""; ?>>
                                                    <option value="">Select Product Type</option>
                                                    <?php foreach($product_types as $product_type): 
                                                            if($product_type->ProductTypeID == $ProductTypeID){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = '';
                                                            }
                                                    ?>
                                                        <option value="<?php echo $product_type->ProductTypeID; ?>" <?php echo $sel;?>><?php echo $product_type->TypeName; ?></option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                            
                                        </div>
                                        <div class="row mb-2">
                                            
                                            <div class="floating col-md-6">     
                                                <label for="title">Product Logo:</label>
                                                <?php if ($ProductLogo) {
                                                    $image_file = plugins_url( 'uploads/' . $ProductLogo, dirname(__FILE__) );
                                                    echo "<input type='file' name='ProductLogo' id='ProductLogo' class='crm-erp-field form-control'>";
                                                    echo "<div id='logo-preview' class='product-preview'><img src='$image_file' alt='Preview'><span class='remove-icon'>×</span></div>";
                                                } else {
                                                    echo "<input type='file' name='ProductLogo' id='ProductLogo' class='crm-erp-field form-control'>";
                                                    echo "<div id='logo-preview' class='product-preview' style='display: none;'></div>";
                                                } ?>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="productImages">Product Image:</label>
                                                <?php if ($ProductImage) {
                                                    $image_file = plugins_url( 'uploads/' . $ProductImage, dirname(__FILE__) );
                                                    echo "<input type='file' name='ProductImages' id='ProductImages' class='crm-erp-field form-control'>";
                                                    echo "<div id='images-preview' class='product-preview'><img src='$image_file' alt='Preview'><span class='remove-icon'>×</span></div>";
                                                } else {
                                                    echo "<input type='file' name='ProductImages' id='ProductImages' class='crm-erp-field form-control'>";
                                                    echo "<div id='images-preview' class='product-preview' style='display: none;'></div>";
                                                } ?>
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            
                                            <div class="floating col-md-6">     
                                                <label for="title">Launch Date:*</label>
                                                <input type="text" placeholder="MM/DD/YYYY" name="LaunchDate" id="LaunchDate" class="crm-erp-field form-control date_field" value="<?php
												if(isset($LaunchDate)){											
													echo $LaunchDate = date('m/d/Y', strtotime($LaunchDate));
												}else{
													echo $LaunchDate;
												}
												?>"<?php echo ($isViewMode) ? "readonly" : ""; ?>>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="title">Expiry Date:</label>
                                                <input type="text" placeholder="MM/DD/YYYY" name="ExpiryDate" id="ExpiryDate" class="crm-erp-field form-control date_field" value="<?php
												if(isset($ExpiryDate)){											
													echo $ExpiryDate = date('m/d/Y', strtotime($ExpiryDate));
												}else{
													echo $ExpiryDate;
												}
												?>"<?php echo ($isViewMode) ? "readonly" : ""; ?>>
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            
                                            <div class="floating col-md-6">     
                                                <label for="title">Product Category:</label>
                                                <select name="CategoryID" id="CategoryID" class="crm-erp-field form-control"<?php echo ($isViewMode) ? "disabled" : ""; ?>>
                                                    <option value="0">Select Product Category</option>
                                                    <?php foreach($product_categories as $product_category): 
                                                            if($product_category->CategoryID == $CategoryID){
                                                                $sel = "selected";
                                                            }else{
                                                                $sel = '';
                                                            }
                                                    ?>
                                                        <option value="<?php echo $product_category->CategoryID; ?>" <?php echo $sel;?>><?php echo $product_category->Name; ?></option>
                                                    <?php endforeach; ?>
                                                </select>
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="Status">Status:</label>
                                                <select class="form-control crm-erp-field" name="Status"<?php echo ($isViewMode) ? "disabled" : ""; ?>>
                                                    <option value="active" <?php if($Status == 'active'){echo "selected";}?>>Active</option>
                                                    <option value="inactive" <?php if($Status == 'inactive'){echo "selected";}?>>Inactive</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="floating col-md-2">     
                                                <label for="sno">S.No:</label>
                                            </div>
                                            <div class="floating col-md-5">     
                                                <label for="custom_field_label">Custom Field Label:</label>
                                            </div>
                                            <div class="floating col-md-5">     
                                                <label for="custom_field_value">Custom Field Value:</label>
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="floating col-md-2">     
                                                <input type="text" value="1" class="crm-erp-field form-control" readonly>
                                            </div>
                                            <div class="floating col-md-5">     
                                                <input type="text" name="CustomFieldLabel1" value="<?php echo $CustomFieldLabel1;?>" class="crm-erp-field form-control">
                                            </div>
                                            <div class="floating col-md-5">     
                                                <input type="text" name="CustomFieldValue1" value="<?php echo $CustomFieldValue1;?>" class="crm-erp-field form-control">
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="floating col-md-2">     
                                                <input type="text" value="2" class="crm-erp-field form-control" readonly>
                                            </div>
                                            <div class="floating col-md-5">     
                                                <input type="text" name="CustomFieldLabel2" value="<?php echo $CustomFieldLabel2;?>" class="crm-erp-field form-control">
                                            </div>
                                            <div class="floating col-md-5">     
                                                <input type="text" name="CustomFieldValue2" value="<?php echo $CustomFieldValue2;?>" class="crm-erp-field form-control">
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="floating col-md-2">     
                                                <input type="text" value="3" class="crm-erp-field form-control" readonly>
                                            </div>
                                            <div class="floating col-md-5">     
                                                <input type="text" name="CustomFieldLabel3" value="<?php echo $CustomFieldLabel3;?>" class="crm-erp-field form-control">
                                            </div>
                                            <div class="floating col-md-5">     
                                                <input type="text" name="CustomFieldValue3" value="<?php echo $CustomFieldValue3;?>" class="crm-erp-field form-control">
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="floating col-md-2">     
                                                <input type="text" value="4" class="crm-erp-field form-control" readonly>
                                            </div>
                                            <div class="floating col-md-5">     
                                                <input type="text" name="CustomFieldLabel4" value="<?php echo $CustomFieldLabel4;?>" class="crm-erp-field form-control">
                                            </div>
                                            <div class="floating col-md-5">     
                                                <input type="text" name="CustomFieldValue4" value="<?php echo $CustomFieldValue4;?>" class="crm-erp-field form-control">
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="floating col-md-2">     
                                                <input type="text" value="5" class="crm-erp-field form-control" readonly>
                                            </div>
                                            <div class="floating col-md-5">     
                                                <input type="text" name="CustomFieldLabel5" value="<?php echo $CustomFieldLabel5;?>" class="crm-erp-field form-control">
                                            </div>
                                            <div class="floating col-md-5">     
                                                <input type="text" name="CustomFieldValue5" value="<?php echo $CustomFieldValue5;?>" class="crm-erp-field form-control">
                                            </div>
                                        </div>
                                        <!-- <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="title">Show Product :</label><br/>
                                                <input type="checkbox" name="show_product[]" id="chk" class="" value="" <?php echo ($isViewMode) ? "readonly" : ""; ?>> Create Lead 
                                                <input type="checkbox" name="show_product[]" id="chk" class="" value="" <?php echo ($isViewMode) ? "readonly" : ""; ?>> Send Lead
                                            </div>
                                        </div> -->
                                        <div class="row mb-2">
                                            <div class="floating col-md-12">     
                                                <label for="description">Description:</label>
                                                <textarea name="Description" maxlength="2000" id="Description" rows="5" class="form-control"<?php echo ($isViewMode) ? "readonly" : ""; ?>><?php echo $Description;?></textarea>
                                            </div>
                                        </div>
                                        <input type="hidden" name="product_id" id="product_id" value="<?php echo $product_id;?>">
                                        <?php if (! $isViewMode) { ?>
                                            <div class="button-container">
                                                <button type="submit" id="product_btn" class="crm_erp_btn"><?php echo $btn_text;?></button>
                                            </div>
                                        <?php } ?>
                                    </form>
                                    <div id="form-response"></div>
                                </div>

                                <div class="tab-pane scroll_height-business scroll-common" id="pills-fees-log" role="tabpanel" aria-labelledby="pills-erc-fees-type">
                                    <div class="page-sub-header d-flex justify-content-end">
                                        <div class="invoice_exports my-3">
                                            <a href="javascript:void(0);" class="add-opp-custom-icon add_edit_fee_type_popup"><i class="fa-solid fa-plus"></i> New Fee Type</a>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <table id="fees_type_table">
                                            <thead>
                                                <tr>
                                                    <th>Fee Type ID</th>
                                                    <th>Fee Structure</th>
                                                    <th>Fee Component</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody >
                                                <?php
                                                global $wpdb;
                                                $feeTypeManager = new CRM_ERP_Fee_Structure_Manager();
                                                $fee_datas = $feeTypeManager->get_fee_types();
                                                $c = 0;
                                                $fees_type_history = '';
                                                if (!empty($fee_datas)) {
                                                    $count = 0;
                                                    foreach ($fee_datas as $fee_data) {
                                                        $fee_component = '';
                                                        if($fee_data->retainer_fee_name != ''){
                                                            if($fee_data->retainer_identifier == ''){
                                                                if($fee_data->retainer_fee_options == '$'){
                                                                    $retainer_fee_amount = '$'.$fee_data->retainer_amount_per;
                                                                }
                                                                elseif($fee_data->retainer_fee_options == '%'){
                                                                    $retainer_fee_amount = $fee_data->retainer_amount_per.'%';
                                                                }
                                                            }else{
                                                                if($fee_data->retainer_fee_options == '$'){
                                                                    $retainer_fee_amount = '$'.$fee_data->retainer_amount_per.'@'.$fee_data->retainer_identifier;
                                                                }
                                                                elseif($fee_data->retainer_fee_options == '%'){
                                                                    $retainer_fee_amount = $fee_data->retainer_amount_per.'%@'.$fee_data->retainer_identifier;
                                                                }
                                                            }
                                                            $fee_component = $fee_data->retainer_fee_name.' '.$retainer_fee_amount;
                                                        }
                                                        if($fee_data->selected_fee_name != ''){
                                                            if($fee_data->selected_identifier == ''){
                                                                if($fee_data->selected_fee_options == '$'){
                                                                    $selected_fee_amount = '$'.$fee_data->selected_amount_per;
                                                                }
                                                                elseif($fee_data->selected_fee_options == '%'){
                                                                    $selected_fee_amount = $fee_data->selected_amount_per.'%';
                                                                }
                                                            }else{
                                                                if($fee_data->selected_fee_options == '$'){
                                                                    $selected_fee_amount = '$'.$fee_data->selected_amount_per.'@'.$fee_data->selected_identifier;
                                                                }
                                                                elseif($fee_data->selected_fee_options == '%'){
                                                                    $selected_fee_amount = $fee_data->selected_amount_per.'%@'.$fee_data->selected_identifier;
                                                                }
                                                            }
                                                            if($fee_component != ''){
                                                                $fee_component .= ' + ';
                                                            }
                                                            $fee_component .= $fee_data->selected_fee_name.' '.$selected_fee_amount;
                                                        }
                                                        $fees_type_history .= "<tr>
                                                              <td>" . $fee_data->fee_type_id . "</td>
                                                              <td>" . $fee_data->fee_structure . "</td>
                                                              <td>" . $fee_component . "</td>
                                                              <td><a href='javascript:void(0);' class='edit_fee_type edit-product-fee-type' data-fee_type_id =".$fee_data->fee_type_id." title='Edit'><i class='fa-solid fa-pen'></i></a><button data-fee_type_id =".$fee_data->fee_type_id." class='delete_fee_type delete-product-fee-type' type='button' title='Remove'><i class='fa-solid fa-trash'></i></button></td>
                                                            </tr>";
                                                    }
                                                } else {
                                                    $fees_type_history = "<tr>
                                                            <td></td>
                                                            <td>No Data Found</td>
                                                            <td></td>
                                                            <td></td>
                                                        </tr>";
                                                }
                                                echo $fees_type_history;
                                                ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div class="tab-pane scroll_height-business scroll-common <?php echo (isset($_GET['view']) && $_GET['view'] === 'qb')? 'active' : ''; ?>" id="pills-quickbook-setting" role="tabpanel" aria-labelledby="pills-erc-quickbook-setting">
                                    <div class="page-sub-header d-flex justify-content-end">
                                        <div class="col-md-3" style="margin-right: 10px;margin-top: 11px;">
                                            <select class="form-control" id="billing_profile">
                                                <option value="">Select Profile</option>
                                                <?php
                                                if(isset($_REQUEST['billing_profile'])){
                                                    $billing_profile = $_REQUEST['billing_profile'];
                                                }else{
                                                    $billing_profile = '';
                                                }
                                                $query = "
                                                SELECT 
                                                    bp.id AS billing_profile_id, 
                                                    bp.profile_name AS billing_profile_name
                                                FROM 
                                                    {$wpdb->prefix}billing_profiles AS bp
                                                ";
                                                $billingProfiless = $wpdb->get_results($query, ARRAY_A);
                                                if(!empty($billingProfiless)){
                                                    foreach($billingProfiless as $billingProfile){
                                                        if($billingProfile['billing_profile_id'] == $billing_profile){
                                                            $sel = "selected";
                                                        }else{
                                                            $sel = "";
                                                        }
                                                        echo "<option value=".$billingProfile['billing_profile_id']." ".$sel.">".$billingProfile['billing_profile_name']."</option>";
                                                    }
                                                }
                                                ?>
                                            </select>
                                        </div>
                                        <div class="invoice_exports my-3">
                                            <a href="javascript:void(0);" class="add-opp-custom-icon add_edit_service dd"><i class="fa-solid fa-plus"></i> New Service</a>
                                        </div>
                                    </div>
                                    <?php
                                    echo '<div class="loader_box" id="loader_box" style="display: none;">';
                                    echo    '<div class="loading">';
                                    echo        '<p class="loading__text">Please Wait. Deleting Service.</p>';
                                    echo        '<div class="loading__bar"></div>';
                                    echo    '</div>';
                                    echo '</div>';
                                    ?>
                                    <div class="row custom-invoice-services">
                                        <?php
                                        global $wpdb; 
                                        /*QB Product Api List Start*/                    
                                        $curl = curl_init();
                                        curl_setopt_array($curl, array(
                                            CURLOPT_URL => 'https://portal.occamsadvisory.com/qb-invoices/public/play/products',
                                            CURLOPT_RETURNTRANSFER => true,
                                            CURLOPT_ENCODING => '',
                                            CURLOPT_MAXREDIRS => 10,
                                            CURLOPT_TIMEOUT => 0,
                                            CURLOPT_FOLLOWLOCATION => true,
                                            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                                            CURLOPT_CUSTOMREQUEST => 'GET',
                                        ));
                                        $QBproductsData = curl_exec($curl);
                                        curl_close($curl);
                                        $QBproductDatas = json_decode($QBproductsData, true);

                                        $QBProductList = [];
                                        $QBProductIDs = [];
                                        if(!empty($QBproductDatas)){
                                            foreach($QBproductDatas as $QBproductData){
                                                $QBProductList[$QBproductData['id']] = $QBproductData['name'];
                                                $QBProductIDs[] = $QBproductData['id'];
                                            }
                                        }
                                        /*QB Product Api List End*/   

                                        $url = get_site_url().'/wp-json/productsplugin/v1/product-service-head-list';
                                        $data['product_id'] = $product_id;
                                        if($billing_profile != ''){
                                            $data['billing_profile'] = $billing_profile;
                                        }
                                        $args = array('body' => json_encode($data),'headers' => array('Content-Type' => 'application/json'));
                                        $response = wp_remote_post($url,$args);
                                        if (is_wp_error($response)) {
                                            return $response->get_error_message();
                                        }
                                        $pro_head_list = json_decode(wp_remote_retrieve_body($response));
                                        // echo '<pre>';
                                        // print_r($pro_head_list);
                                        // echo '</pre>';
                                        global $wpdb;
                                        $product_service_head = $wpdb->prefix . 'product_service_head';
                                        
                                        $query = "SELECT DISTINCT `{$product_service_head}`.`qb_product_id`, `{$product_service_head}`.`product_head`
                                                  FROM `{$product_service_head}`
                                                  WHERE `{$product_service_head}`.`product_head` != ''";
                                        
                                        $prodcut_heads = $wpdb->get_results($query);
                                        $selecthtml = '<option value="" data-name="Select Product Service" selected>Select Product Service</option>';
                                        if(!empty($prodcut_heads)){
                                            foreach($prodcut_heads as $product_head){
                                                $selecthtml .= ' <option value="'.$product_head->qb_product_id.'" data-name="'.$product_head->product_head.'">'.$product_head->product_head.'</option>';
                                            }
                                        }
                                        if(!empty($pro_head_list)){
                 
                                            $qb_pro_name = '';
                                            foreach($pro_head_list as $pro_head){
                                                if(in_array($pro_head->qb_product_id, $QBProductIDs)){
                                                    //$qb_pro_name = $QBProductList[$pro_head->qb_product_id];
                                                    $qb_pro_name = $pro_head->product_head;
                                                }
                                            ?>
                                             
                                            <div class="col-sm-4">
                                                <div class="custom_opp_tab">
                                                    <div class="col-sm-12">
                                                        <div class="custom_opp_tab_header">
                                                            <h5><b style="color: #000;">Billing Profile - <?php echo $pro_head->profile_name;?></b> <br> <?php echo $pro_head->product_head;?></h5>
                                                            <div class="opp_edit_dlt_btn projects-iris">
                                                            <a class="edit_pro_service_head" href="javascript:void(0)" title="Edit" data-billing_profile="<?php echo $pro_head->billing_profile;?>" data-qb_product_id="<?php echo $pro_head->qb_product_id;?>" data-alias="<?php echo $pro_head->alias;?>" data-invoice_type="<?php echo $pro_head->invoice_type;?>" data-product_head="<?php echo $pro_head->product_head;?>" data-amount_type="<?php echo $pro_head->amount_type;?>" data-product_rate="<?php echo $pro_head->product_rate;?>" data-product_unit="<?php echo $pro_head->product_unit;?>" data-pro_service_id="<?php echo $pro_head->id;?>" data-selecthtml="<?php echo htmlspecialchars($selecthtml); ?>"><i class="fa-solid fa-pen"></i></a>
                                                            <a class="del_pro_service_head" href="javascript:void(0)" title="Delete" data-qb_product_id="<?php echo $pro_head->qb_product_id;?>" data-pro_service_id="<?php echo $pro_head->id;?>" style="background:#ff0000 !important"><i class="fa-solid fa-trash"></i></a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6 text-left">
                                                        <div class="lead_des">
                                                            <p><b>Invoice Type:</b> <?php echo $pro_head->invoice_type;?></p>
                                                            <p><b>Alias:</b> <?php echo $pro_head->alias;?></p>
                                                            <?php
                                                            if($pro_head->product_unit != ''){
                                                                echo '<p><b>Unit:</b> '.$pro_head->product_unit.'</p>';
                                                            }else{
                                                                echo '<p><b>Unit:</b> N/A</b></p>';
                                                            }
                                                            ?>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6 text-left">
                                                        <div class="lead_des">
                                                            <p><b>Type:</b> <?php echo $pro_head->amount_type;?></p>
                                                            <p><b>Rate:</b> <?php echo $pro_head->product_rate;?></p>
                                                            
                                                        </div>
                                                    </div>
                                                </div>     
                                            </div>
                                            <?php
                                            }
                                        }
                                        ?>
                                    </div>
                                </div>
                                <div class="tab-pane scroll_height-business scroll-common" id="pills-role-setting" role="tabpanel" aria-labelledby="pills-erc-role-setting">
                                    <div class="custom-crm-setting">
                                        <form id="access-settingsForm" class="p_15">
                                            <fieldset>
                                                <legend>Role Settings</legend>
                                                <div class="d-flex align-items-center custom-crm-setting-field mb_15">
                                                    <div class="label-tooltip">
                                                        <label>Select Roles for CRM Plugin :</label>
                                                        <div class="tiptool">
                                                            <i class="info-icon-contacts fa fa-info-circle"></i>
                                                            <span class="tiptooltext">Select the user roles for the access.</span>
                                                        </div>
                                                    </div> 
                                                    <select class="form-control" name="product_roles[]" id="product_roles" multiple="" data-select2-id="opt_select_crm_roles" tabindex="-1" aria-hidden="true">
                                                        <?php
                                                        global $wpdb, $wp_roles;
                                                        $roles = wp_roles();
                                                        $registered_roles = $roles->get_names(); 
                                                        ?>
                                                        <?php foreach ($registered_roles as $role_value => $role_label): ?>
                                                        <?php $isSelected = !empty($product_roles) && in_array($role_value, $product_roles); ?>
                                                            <option value="<?php echo esc_attr($role_value); ?>" <?php echo $isSelected ? 'selected' : ''; ?>><?php echo esc_html($role_label); ?></option>
                                                        <?php endforeach; ?>                                                                           
                                                    </select>
                                                </div>
                                            </fieldset>
                                            <div id="access-settingsMessage" style="margin:15px auto;"></div>
                                            <input type="hidden" name="action" value="oc_save_product_roles">    
                                            <input type="hidden" name="product_id" value="<?php echo $product_id;?>">    
                                            <div class="button-container">
                                                <button type="submit" id="access-settingsSbmt" class="nxt_btn">Save</button>
                                            </div>      
                                         </form>
                                    </div>
                                </div>
                                <?php if($product_id != ''){?>
                                <div class="tab-pane scroll_height-business scroll-common" id="pills-audit-log" role="tabpanel" aria-labelledby="pills-erc-audit-log-info">
                                    <div class="row">
                                        <table id="audit_log_table">
                                            <thead>
                                                <tr>
                                                    <th>Field Name</th>
                                                    <th>Changed From</th>
                                                    <th>Changed To</th>
                                                    <th>Data Type</th>
                                                    <th>Action</th>
                                                    <th>Changed Time</th>
                                                    <th>Changed By</th>
                                                </tr>
                                            </thead>
                                            <tbody class="audit_data1">
                                                <?php
                                                global $wpdb;
                                                $audit_log_table = $wpdb->prefix . 'audit_logs';
                                                $audit_logs = $wpdb->get_results("SELECT $audit_log_table.* FROM $audit_log_table WHERE $audit_log_table.TableName = '" . $wpdb->prefix . "crm_products'  AND $audit_log_table.FieldID = " . $product_id . " ORDER BY $audit_log_table.LogID DESC");
                                                $c = 0;
                                                $audit_log_history = '';
                                                if (!empty($audit_logs)) {
                                                    $count = 0;
                                                    foreach ($audit_logs as $audit_log) {
                                                        $CreatedBy = get_userdata($audit_log->CreatedBy);
                                                        if(!empty($CreatedBy)){
                                                            $changed_by = $CreatedBy->data->display_name;
                                                        }else{
                                                            $changed_by = '';
                                                        }
                                                        $DateCreated = date('m/d/Y H:i a',strtotime($audit_log->DateCreated));
                                                        $audit_log_history .= "<tr>
                                                              <td>" . $audit_log->FieldName . "</td>
                                                              <td>" . $audit_log->BeforeValueString . "</td>
                                                              <td>" . $audit_log->AfterValueString . "</td>
                                                              <td>" . $audit_log->DataType . "</td>
                                                              <td>" . $audit_log->Action . "</td>
                                                              <td>" . $DateCreated . "</td>
                                                              <td>" . $changed_by . "</td>
                                                            </tr>";
                                                    }
                                                } else {
                                                    $audit_log_history .= "<tr>
                                                      <td></td>
                                                      <td></td>
                                                      <td></td>
                                                      <td>No Data Found</td>
                                                      <td></td>
                                                      <td></td>
                                                      <td></td>
                                                    </tr>";
                                                }
                                                echo $audit_log_history;?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <?php } ?>
                                <!-- pills milestone stage  -->
                                <?php if($product_id != ''){?>
                                <div class="tab-pane scroll_height-business scroll-common" id="pills-milestones-stage-log" role="tabpanel" aria-labelledby="pills-milestones-stage-log">
                                    <div class="row custom-crm-setting">
                                        <!-- <label>Milestone & Stage Settings</label> -->
                                        <fieldset>
                                                <legend>Opportunity Milestone & Stage Settings</legend>
                                                <div class="d-flex align-items-center custom-crm-setting-field mb_15">
                                                    <div class="row" style="width: 50%;">
                                                        <label class="">Select Milestones :</label>
                                                        <div class="tiptool form-group col-sm-12">
                                                            <select class="form-control select-milestone" id="opportunity-milestone">
                                                                <option value="">Select milestone</option>
                                                                <?php foreach ($all_milestones as $mile_key => $mile_value) { 
                                                                    $milestone_id = $mile_value->milestone_id;
                                                                    $milestone_name = $mile_value->milestone_name;
                                                                    $selected='';
                                                                    if($create_opportunity_milestone==$milestone_id){
                                                                        $selected='selected';
                                                                    }
                                                                    ?>
                                                                 <option value="<?php echo $milestone_id; ?>" <?php echo $selected;?>><?php echo $milestone_name; ?></option>
                                                                <?php } ?>
                                                            </select> 
                                                            <p id="opportunity-milestone-error"></p>
                                                        </div>
                                                    </div>
                                                    <div class="row" style="width:50%;">
                                                        <label class="">Select Milestones Stage:</label>
                                                        <div class="tiptool form-group col-sm-12">
                                                            <select  class="form-control" id="opportunity-milestonestage">
                                                                <option value="">Select milestone stage</option>
                                                                <?php foreach ($all_milestone_status as $miles_key => $miles_value) { 
                                                                    $stage_id = $miles_value->milestone_stage_id;
                                                                    $stage_name = $miles_value->stage_name;
                                                                    $selected='';
                                                                    if($create_opportunity_stage ==$stage_id){
                                                                        $selected='selected';
                                                                    }
                                                                    ?>
                                                                 <option value="<?php echo $stage_id; ?>" <?php echo $selected; ?>><?php echo $stage_name; ?></option>
                                                                <?php } ?>
                                                            </select> 
                                                            <p id="opportunity-milestonestage-error"></p>
                                                        </div>
                                                    </div> 
                                                </div>
                                            </fieldset>
                                            <fieldset>
                                                <legend>Project Milestone & Stage Settings</legend>
                                                <div class="d-flex align-items-center custom-crm-setting-field mb_15">
                                                    <div class="row" style="width: 50%;">
                                                        <label class="">Select Milestones :</label>
                                                        <div class="tiptool form-group col-sm-12">
                                                            <select  class="form-control select-milestone" id="project-milestone">
                                                                <option value="">Select milestone</option>
                                                                <?php foreach ($all_milestones as $mile_key => $mile_value) { 
                                                                    $milestone_id = $mile_value->milestone_id;
                                                                    $milestone_name = $mile_value->milestone_name;
                                                                    $selected='';
                                                                    if($create_project_milestone == $milestone_id){
                                                                        $selected='selected';
                                                                    }
                                                                    ?>
                                                                 <option value="<?php echo $milestone_id; ?>" <?php echo $selected; ?>><?php echo $milestone_name; ?></option>
                                                                <?php } ?>
                                                            </select> 
                                                            <p id="project-milestone-error"></p>
                                                        </div>
                                                    </div>
                                                    <div class="row" style="width:50%;">
                                                        <label class="">Select Milestones Stage:</label>
                                                        <div class="tiptool form-group col-sm-12">
                                                            <select class="form-control" id="project-milestonestage">
                                                                <option value="">Select milestone stage</option>
                                                                <?php foreach ($all_milestone_status as $miles_key => $miles_value) { 
                                                                    $stage_id = $miles_value->milestone_stage_id;
                                                                    $stage_name = $miles_value->stage_name;
                                                                    $selected='';
                                                                    if($create_project_milestonestage == $stage_id){
                                                                        $selected='selected';
                                                                    }
                                                                    ?>
                                                                 <option value="<?php echo $stage_id; ?>" <?php echo $selected; ?>><?php echo $stage_name; ?></option>
                                                                <?php } ?>
                                                            </select> 
                                                            <p id="project-milestonestage-error"></p>
                                                        </div>
                                                    </div> 
                                                </div>
                                            </fieldset>
                                    </div>
                                    <button type="button" class="btn milestone-submit-btn">Submit</button>
                                </div>
                                <?php } ?>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- .site-main -->
</div><!-- .content-area -->
<script>
    jQuery(document).ready(function() {
		jQuery(".date_field").datepicker({
			dateFormat: "mm/dd/yy",
		});
	});
</script>
<div class="modal add-edit-fee-type-popup" data-bs-backdrop="static" data-bs-keyboard="false" id="add-edit-fee-type" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <p id="fee-type-success-response"></p>
                <p id="fee-type-error-response"></p>
                <h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-square-plus plus_icon"></i> <span class="heading_text">New</span> Fee Type</h5>
                <button type="button" class="close-popup-fee-type close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="custom-product-fee-popup">
                <fieldset>
                        <legend>Fee Component</legend>
                        <div class="row">
                            <div class="floating col-md-6 mb-3">
                                <label>Fee Structure:</label>
                                    <select id="popup-fee-structure" name="fee_structure" class="crm-erp-field  form-control">
                                        <option value="">Select Fee Structure</option>
                                        <option value="Flat">Flat</option>
                                        <option value="Combination">Combination</option>
                                    </select>
                            </div>
                            <div class="floating col-md-6 mb-3">
                                <label>Fee Type:</label>
                                <select id="popup-fee-type" name="fee_type" class="crm-erp-field  form-control">
                                    <option value="">Select Fee Type</option>
                                </select>
                            </div>
                        </div>
                </fieldset>
                </div>
                    
                    <div class="retainer_fee_structure custom-product-fee-popup">
                        <fieldset>
                                <legend>Retainer Fee Structure</legend>
                                <div class="row">
                                    <div class="floating col-md-2 mb-3 select_fee_option">
                                        <label>Fee Options*:</label>
                                        <select id="retainer_fee_options" name="retainer_fee_options" class="crm-erp-field  form-control">
                                            <option value="$">$</option>
                                            <option value="%">%</option>
                                        </select>
                                        <div class="retainer_fee_option_err fee_erros">Please select fee options.</div>
                                    </div>
                                    <div class="floating col-md-4 mb-3 retainer_option_identifier">
                                        <label>Basis of:</label>
                                        <select class="crm-erp-field form-control" id="retainer_identifier" name="retainer_identifier">
                                            <option value="">Select Basis of</option>
                                            <?php
                                                if($CustomFieldLabel1 != ''){
                                                    echo '<option value="'.$CustomFieldLabel1.'">'.$CustomFieldLabel1.'</option>';
                                                }
                                                if($CustomFieldLabel2 != ''){
                                                    echo '<option value="'.$CustomFieldLabel2.'">'.$CustomFieldLabel2.'</option>';
                                                }
                                                if($CustomFieldLabel3 != ''){
                                                    echo '<option value="'.$CustomFieldLabel3.'">'.$CustomFieldLabel3.'</option>';
                                                }
                                                if($CustomFieldLabel4 != ''){
                                                    echo '<option value="'.$CustomFieldLabel4.'">'.$CustomFieldLabel4.'</option>';
                                                }
                                                if($CustomFieldLabel5 != ''){
                                                    echo '<option value="'.$CustomFieldLabel5.'">'.$CustomFieldLabel5.'</option>';
                                                }
                                            ?>
                                        </select>
                                        <div class="retainer_identifier_err fee_erros">Please select basis of.</div>
                                    </div>
                                    <div class="floating col-md-4 mb-4">
                                        <label>Fee Name*:</label>
                                        <input type="text" id="retainer_fee_name" name="retainer_fee_name" class="crm-erp-field form-control" maxlength="75">
                                        <div class="retainer_fee_name_err fee_erros">Please enter fee name.</div>
                                    </div>
                                    <div class="floating col-md-2 mb-3 retainer_option_amount">
                                        <label class="retainer_amount_per_label">Amount*:</label>
                                        <input type="text" id="retainer_amount_per" name="retainer_amount_per" class="crm-erp-field form-control amount_per amount_class">
                                        <div class="retainer_amount_per_err fee_erros">Please enter amount.</div>
                                    </div>
                                    
                                </div>
                        </fieldset>
                    </div>

                    <div class="both_fee_structure custom-product-fee-popup">
                        <fieldset>
                            <legend class="selected_fee_structure">Fee Structure</legend>
                            <div class="row">
                                <div class="floating col-md-2 mb-3 select_fee_option">
                                    <label>Fee Options*:</label>
                                    <select id="selected_fee_options" name="selected_fee_options" class="crm-erp-field  form-control">
                                        <option value="$">$</option>
                                        <option value="%">%</option>
                                    </select>
                                    <div class="selected_fee_option_err fee_erros">Please select fee options.</div>
                                </div>
                                <div class="floating col-md-4 mb-3 selected_option_identifier">
                                    <label>Basis of:</label>
                                    <select class="crm-erp-field form-control" id="selected_identifier" name="selected_identifier">
                                        <option value="">Select Basis of</option>
                                        <?php
                                            if($CustomFieldLabel1 != ''){
                                                echo '<option value="'.$CustomFieldLabel1.'">'.$CustomFieldLabel1.'</option>';
                                            }
                                            if($CustomFieldLabel2 != ''){
                                                echo '<option value="'.$CustomFieldLabel2.'">'.$CustomFieldLabel2.'</option>';
                                            }
                                            if($CustomFieldLabel3 != ''){
                                                echo '<option value="'.$CustomFieldLabel3.'">'.$CustomFieldLabel3.'</option>';
                                            }
                                            if($CustomFieldLabel4 != ''){
                                                echo '<option value="'.$CustomFieldLabel4.'">'.$CustomFieldLabel4.'</option>';
                                            }
                                            if($CustomFieldLabel5 != ''){
                                                echo '<option value="'.$CustomFieldLabel5.'">'.$CustomFieldLabel5.'</option>';
                                            }
                                        ?>
                                    </select>
                                    <div class="selected_identifier_err fee_erros">Please select basis of.</div>
                                </div>
                                <div class="floating col-md-4 mb-3">
                                    <label>Fee Name*:</label>
                                    <input type="text" id="selected_fee_name" name="selected_fee_name" class="crm-erp-field form-control" maxlength="75">
                                    <div class="selected_fee_name_err fee_erros">Please enter fee name.</div>
                                </div>
                                <div class="floating col-md-2 mb-3 selected_option_amount">
                                    <label class="selected_amount_per_label">Amount*:</label>
                                    <input type="text" id="selected_amount_per" name="selected_amount_per" class="crm-erp-field form-control amount_per amount_class">
                                    <div class="selected_amount_per_err fee_erros">Please enter amount.</div>
                                </div>
                                
                            </div>
                        </fieldset>
                    </div>
                <div class="button_next_prev fee_type_popup_btns">
                    <input type="hidden" name="fee_type_id" id="fee_type_id" value="">
                    <a href="javascript:void(0);" id="save_fee_type" class="prdocut-fee-popup-btn">Save</a>
                    <a href="javascript:void(0);" id="save_new_fee_type" class="prdocut-fee-popup-btn">Save & New</a>
                    <a href="javascript:void(0);" id="cancel_fee_type" class="prdocut-fee-popup-btn">Cancel</a>
                    
                </div>
            </div>
        </div>
    </div>
    <style type="text/css">
        .alert-error{color: red;}
    </style>
    <script type="text/javascript">
        jQuery("#popup-fee-structure").change(function(){
            jQuery(".retainer_fee_structure").hide();
            jQuery(".both_fee_structure").hide();
            jQuery(".fee_type_popup_btns").hide();
            jQuery("#retainer_fee_options").val('$').change();
            jQuery("#retainer_fee_name").val('');
            jQuery("#retainer_amount_per").val('');
            jQuery("#retainer_identifier").val('').change();
            jQuery("#selected_fee_options").val('$').change();
            jQuery("#selected_fee_name").val('');
            jQuery("#selected_amount_per").val('');
            jQuery("#selected_identifier").val('').change();

            var fee_structure = jQuery(this).val();
            if(fee_structure == 'Flat'){
                jQuery("#popup-fee-type option").remove();
                jQuery("#popup-fee-type").append('<option value="">Select Fee Structure</option><option value="Success/Completion Basis">Success/Completion Basis</option><option value="Fixed">Fixed</option><option value="Hourly">Hourly</option><option value="Unit">Unit</option>');
            }
            else if(fee_structure == 'Combination'){
                jQuery("#popup-fee-type option").remove();
                jQuery("#popup-fee-type").append('<option value="">Select Fee Structure</option><option value="Retainer + Success/Completion Basis">Retainer + Success/Completion Basis</option><option value="Retainer + Hourly">Retainer + Hourly</option><option value="Retainer + Fixed">Retainer + Fixed</option><option value="Retainer + Unit">Retainer + Unit</option>');
            }else{
                jQuery("#popup-fee-type option").remove();
                jQuery("#popup-fee-type").append('<option value="">Select Fee Structure</option>');
            }
        })
        jQuery("#popup-fee-type").change(function(){
            var fee_structure = jQuery("#popup-fee-structure").val();
            var fee_type = jQuery(this).val();
            if(fee_structure == 'Flat'){
                jQuery(".selected_fee_structure").text(fee_type+' Fee Structure');
                jQuery(".both_fee_structure").show();
                jQuery(".fee_type_popup_btns").show();
            }else if(fee_structure == 'Combination'){
                jQuery(".retainer_fee_structure").show();
                fee_type = fee_type.replace("Retainer + ", "");
                jQuery(".selected_fee_structure").text(fee_type+' Fee Structure');
                jQuery(".both_fee_structure").show();
                jQuery(".fee_type_popup_btns").show();
            }else{
                jQuery(".retainer_fee_structure").hide();
                jQuery(".both_fee_structure").hide();
                jQuery(".fee_type_popup_btns").hide();
            }
        })
        jQuery("#retainer_fee_options").change(function(){
            var fee_options = jQuery(this).val();
            if(fee_options == '$'){
                jQuery(".retainer_amount_per_label").text('Amount*:');
                jQuery("#retainer_amount_per").addClass("amount_class");
                jQuery("#retainer_amount_per").removeClass("precentage_class");
                jQuery("#retainer_amount_per").val('');
                jQuery(".retainer_amount_per_err").text('Please enter fee amount');
                //jQuery(".retainer_option_identifier").hide();
                jQuery(".retainer_option_amount").show();
            }
            else if(fee_options == '%'){
                jQuery(".retainer_amount_per_label").text('Percentage*:');
                jQuery("#retainer_amount_per").addClass("precentage_class");
                jQuery("#retainer_amount_per").removeClass("amount_class");
                jQuery("#retainer_amount_per").val('');
                jQuery(".retainer_amount_per_err").text('Please enter fee percentage');
                //jQuery(".retainer_option_identifier").hide();
                jQuery(".retainer_option_amount").show();
            }
            else if(fee_options == 'Basis of'){
                jQuery(".retainer_option_identifier").show();
                jQuery(".retainer_option_amount").hide();
                jQuery(".retainer_identifier_err").text('Please select identifier');
            }else{
                jQuery(".retainer_option_identifier").hide();
                jQuery(".retainer_option_amount").show();
            }
        })
        jQuery("#selected_fee_options").change(function(){
            var fee_options = jQuery(this).val();
            if(fee_options == '$'){
                jQuery(".selected_amount_per_label").text('Amount*:');
                jQuery("#selected_amount_per").addClass("amount_class");
                jQuery("#selected_amount_per").removeClass("precentage_class");
                jQuery("#selected_amount_per").val('');
                jQuery(".selected_amount_per_err").text('Please enter fee amount');
                //jQuery(".selected_option_identifier").hide();
                jQuery(".selected_option_amount").show();
            }
            else if(fee_options == '%'){
                jQuery(".selected_amount_per_label").text('Percentage*:');
                jQuery("#selected_amount_per").addClass("precentage_class");
                jQuery("#selected_amount_per").removeClass("amount_class");
                jQuery("#selected_amount_per").val('');
                jQuery(".selected_amount_per_err").text('Please enter fee percentage');
                //jQuery(".selected_option_identifier").hide();
                jQuery(".selected_option_amount").show();
            }
            else if(fee_options == 'Basis of'){
                jQuery(".selected_option_identifier").show();
                jQuery(".selected_option_amount").hide();
                jQuery(".selected_identifier_err").text('Please select identifier');
            }else{
                jQuery(".selected_option_identifier").hide();
                jQuery(".selected_option_amount").show();
            }
        })
        jQuery('.amount_per').on('input', function() {
          this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');
        });
        var maxLength = 1000000;
        var percentageLength = 50;
        jQuery(".amount_class").on('keydown keyup change', function(){
            var char = jQuery(this).val();
            if(char > maxLength){
                jQuery(this).val(char.substring(0, char.length - 1));
            }
        })
        jQuery(".precentage_class").on('keydown keyup change', function(){
            var char = jQuery(this).val();
            if(char > percentageLength){
                jQuery(this).val(char.substring(0, char.length - 1));
            }
        })
        jQuery("#cancel_fee_type").click(function(){
            //jQuery("#add-edit-fee-type").modal('hide');
            location.reload();
        })

        jQuery(document).on('click','#save_fee_type',function(e) {
            var product_id = "<?php echo $product_id;?>";
            if(product_id == ''){
                var btn_text = "Save";
            }else{
                var btn_text = "Update";
            }
            var fee_structure = jQuery("#popup-fee-structure").val();
            var fee_type = jQuery("#popup-fee-type").val();
            var selected_fee_options = jQuery("#selected_fee_options").val();
            var selected_fee_name = jQuery("#selected_fee_name").val();
            var selected_amount_per = jQuery("#selected_amount_per").val();
            var selected_identifier = jQuery("#selected_identifier").val();
            var retainer_fee_options = jQuery("#retainer_fee_options").val();
            var retainer_fee_name = jQuery("#retainer_fee_name").val();
            var retainer_amount_per = jQuery("#retainer_amount_per").val();
            var retainer_identifier = jQuery("#retainer_identifier").val();
            var fee_type_id = jQuery("#fee_type_id").val();
            if(fee_structure == 'Flat'){
                if(selected_fee_name == ''){
                    jQuery(".selected_fee_name_err").show();
                }
                if(selected_amount_per == ''){
                    jQuery(".selected_amount_per_err").show();
                }
                if(selected_fee_options == 'Basis of'){
                    if(selected_identifier == ''){
                        jQuery(".selected_identifier_err").show();
                    }
                }
            }
            else{
                if(selected_fee_name == ''){
                    jQuery(".selected_fee_name_err").show();
                }
                if(selected_amount_per == ''){
                    jQuery(".selected_amount_per_err").show();
                }
                if(selected_fee_options == 'Basis of'){
                    if(selected_identifier == ''){
                        jQuery(".selected_identifier_err").show();
                    }
                }
                if(retainer_fee_name == ''){
                    jQuery(".retainer_fee_name_err").show();
                }
                if(retainer_amount_per == ''){
                    jQuery(".retainer_amount_per_err").show();
                }
                if(retainer_fee_options == 'Basis of'){
                    if(retainer_identifier == ''){
                        jQuery(".retainer_identifier_err").show();
                    }
                }
            }
            if(fee_structure == 'Flat' || fee_structure == 'Combination'){
                if(selected_fee_name == '' && selected_amount_per == ''){
                    return false;
                }else{
                    if(selected_fee_options == 'Basis of'){
                        if(selected_identifier == ''){
                            return false;
                        }
                    }
                }
            }
            if(fee_structure == 'Combination'){
                if(retainer_fee_name == '' && retainer_amount_per == ''){
                    return false;
                }else{
                    if(retainer_fee_options == 'Basis of'){
                        if(retainer_identifier == ''){
                            return false;
                        }
                    }
                }
            }
            jQuery("#save_fee_type").html('Please wait..');
            jQuery("#save_fee_type").attr('disabled',true);
            fee_type = fee_type.replace('+','Plus');
            jQuery.ajax({
                type: 'POST',
                url: 'admin-ajax.php',
                data: 'product_id='+product_id+'&fee_structure='+fee_type+'&retainer_fee_options='+retainer_fee_options+'&retainer_fee_name='+retainer_fee_name+'&retainer_amount_per='+retainer_amount_per+'&retainer_identifier='+retainer_identifier+'&selected_fee_options='+selected_fee_options+'&selected_fee_name='+selected_fee_name+'&selected_amount_per='+selected_amount_per+'&selected_identifier='+selected_identifier+"&fee_type_id="+fee_type_id+'&action=fee_type_submit',
                success: function(response) {
                    if(response.data.status == 1){
                        jQuery("#save_fee_type").html(btn_text);
                        jQuery("#save_fee_type").attr('disabled',false);
                        //empty values
                        jQuery("#popup-fee-structure").val('').change();
                        jQuery("#popup-fee-type").val('').change();
                        jQuery("#fee-type-error-response").text();
                        jQuery("#fee-type-error-response").hide();
                        if(fee_type_id != ''){
                            jQuery("#fee-type-success-response").text('Fee Type Updated Successfully.');
                        }else{
                            jQuery("#fee-type-success-response").text('Fee Type Created Successfully.');
                        }
                        jQuery("#fee-type-success-response").show();
                        setTimeout(function(){
                            window.location.href = '?page=add_edit_product&action=edit&id='+product_id
                        }, 2000);
                    }else{
                        $responseDiv.html(response.data.message);
                        jQuery("#save_fee_type").html(btn_text);
                        jQuery("#save_fee_type").attr('disabled',false);
                        jQuery("#fee-type-error-response").text('Something went Wrong for Creating fee type');
                        jQuery("#fee-type-error-response").show();
                        jQuery("#fee-type-success-response").text('');
                        jQuery("#fee-type-success-response").hide();
                    }
                },
                error: function() {

                }
            });
        })
        jQuery("#billing_profile").change(function(){
            var billing_profile = $(this).val();
            var product_id = "<?php echo $product_id;?>";
            window.location.href = '?page=add_edit_product&action=edit&id='+product_id+'&view=qb&billing_profile='+billing_profile;
        })
        jQuery(document).on('click','#save_new_fee_type',function(e) {
            var product_id = "<?php echo $product_id;?>";
            if(product_id == ''){
                var btn_text = "Save & New";
            }else{
                var btn_text = "Update";
            }
            var fee_structure = jQuery("#popup-fee-structure").val();
            var fee_type = jQuery("#popup-fee-type").val();
            var selected_fee_options = jQuery("#selected_fee_options").val();
            var selected_fee_name = jQuery("#selected_fee_name").val();
            var selected_amount_per = jQuery("#selected_amount_per").val();
            var selected_identifier = jQuery("#selected_identifier").val();
            var retainer_fee_options = jQuery("#retainer_fee_options").val();
            var retainer_fee_name = jQuery("#retainer_fee_name").val();
            var retainer_amount_per = jQuery("#retainer_amount_per").val();
            var retainer_identifier = jQuery("#retainer_identifier").val();
            var fee_type_id = jQuery("#fee_type_id").val();
            if(fee_structure == 'Flat'){
                if(selected_fee_name == ''){
                    jQuery(".selected_fee_name_err").show();
                }
                if(selected_amount_per == ''){
                    jQuery(".selected_amount_per_err").show();
                }
                if(selected_fee_options == 'Basis of'){
                    if(selected_identifier == ''){
                        jQuery(".selected_identifier_err").show();
                    }
                }
            }
            else{
                if(selected_fee_name == ''){
                    jQuery(".selected_fee_name_err").show();
                }
                if(selected_amount_per == ''){
                    jQuery(".selected_amount_per_err").show();
                }
                if(selected_fee_options == 'Basis of'){
                    if(selected_identifier == ''){
                        jQuery(".selected_identifier_err").show();
                    }
                }
                if(retainer_fee_name == ''){
                    jQuery(".retainer_fee_name_err").show();
                }
                if(retainer_amount_per == ''){
                    jQuery(".retainer_amount_per_err").show();
                }
                if(retainer_fee_options == 'Basis of'){
                    if(retainer_identifier == ''){
                        jQuery(".retainer_identifier_err").show();
                    }
                }
            }
            if(fee_structure == 'Flat' || fee_structure == 'Combination'){
                if(selected_fee_name == '' || selected_amount_per == ''){
                    return false;
                }else{
                    if(selected_fee_options == 'Basis of'){
                        if(selected_identifier == ''){
                            return false;
                        }
                    }
                }
            }
            if(fee_structure == 'Combination'){
                if(retainer_fee_name == '' || retainer_amount_per == ''){
                    return false;
                }else{
                    if(retainer_fee_options == 'Basis of'){
                        if(retainer_identifier == ''){
                            return false;
                        }
                    }
                }
            }
            jQuery("#save_new_fee_type").html('Please wait..');
            jQuery("#save_new_fee_type").attr('disabled',true);
            fee_type = fee_type.replace('+','Plus');
            jQuery.ajax({
                type: 'POST',
                url: 'admin-ajax.php',
                data: 'product_id='+product_id+'&fee_structure='+fee_type+'&retainer_fee_options='+retainer_fee_options+'&retainer_fee_name='+retainer_fee_name+'&retainer_amount_per='+retainer_amount_per+'&retainer_identifier='+retainer_identifier+'&selected_fee_options='+selected_fee_options+'&selected_fee_name='+selected_fee_name+'&selected_amount_per='+selected_amount_per+'&selected_identifier='+selected_identifier+"&fee_type_id="+fee_type_id+'&action=fee_type_submit',
                success: function(response) {
                    if(response.data.status == 1){
                        jQuery("#save_new_fee_type").html(btn_text);
                        jQuery("#save_new_fee_type").attr('disabled',false);
                        //empty values
                        jQuery("#popup-fee-structure").val('').change();
                        jQuery("#popup-fee-type").val('').change();

                        jQuery("#retainer_fee_options").val('$').change();
                        jQuery("#retainer_fee_name").val('');
                        jQuery("#retainer_amount_per").val('');
                        jQuery("#retainer_identifier").val('').change();

                        jQuery("#selected_fee_options").val('$').change();
                        jQuery("#selected_fee_name").val('');
                        jQuery("#selected_amount_per").val('');
                        jQuery("#selected_identifier").val('').change();

                        jQuery("#fee-type-error-response").text();
                        jQuery("#fee-type-error-response").hide();
                        jQuery("#fee-type-success-response").text('Fee Type Created Successfully.');
                        jQuery("#fee-type-success-response").show();
                        setTimeout(function(){
                            jQuery("#fee-type-success-response").text('');
                            jQuery("#fee-type-success-response").hide();
                        }, 2000);
                    }else{
                        $responseDiv.html(response.data.message);
                        jQuery("#save_new_fee_type").html(btn_text);
                        jQuery("#save_new_fee_type").attr('disabled',false);
                        jQuery("#fee-type-error-response").text('Something went Wrong for Creating fee type');
                        jQuery("#fee-type-error-response").show();
                        jQuery("#fee-type-success-response").text('');
                        jQuery("#fee-type-success-response").hide();
                    }
                },
                error: function() {

                }
            });
        })
    </script>
</div>
<!---- Quickbook Setting Popup -->
<div class="modal add-edit-service-popup" data-bs-backdrop="static" data-bs-keyboard="false" id="add-edit-service" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <p id="fee-type-success-response"></p>
                <p id="fee-type-error-response"></p>
                <h5 class="modal-title" id="exampleModalLabel"><span class="heading_text">New</span> Service</h5>
                <button type="button" class="close-service-popup close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
				
				<div class="floating col-md-12 mb-3">
					<label>Billing Profile*:</label>
					<select id="billing_profile_id" name="billing_profile_id" class="crm-erp-field form-control" required>
						<option value="" selected>Select billing profile</option>
						<?php
						if (!empty($billingProfiles)) {
							foreach ($billingProfiles as $profile) {
								?>
								<option 
									value="<?php echo esc_attr($profile['billing_profile_id']); ?>" 
									gatewaykey="<?php echo esc_attr($profile['gateway_client_key']); ?>" gatewayid="<?php echo esc_attr($profile['gateway_client_id']); ?>" orgid="<?php echo esc_attr($profile['organisation_id']); ?>">
									<?php echo esc_html($profile['billing_profile_name']); ?>
								</option>
								<?php
							}
						} else {
							?>
							<option value="">No Profiles Found</option>
							<?php
						}
						?>
					</select>
					<span class="alert-error billing_profile_err"></span>
					<p id="billing_profileproduct_fetch_loader" class="loading__bar small_loading_bar" style="width: 9rem;height: 6px;display: none;"></p>
				</div>


				
				
				
                    <div class="floating col-md-6 mb-3">
                        <label>QB Product/Service*:</label>
                        <?php                     
                        $curl = curl_init();
                        curl_setopt_array($curl, array(
                            CURLOPT_URL => 'https://portal.occamsadvisory.com/qb-invoices/public/play/products',
                            CURLOPT_RETURNTRANSFER => true,
                            CURLOPT_ENCODING => '',
                            CURLOPT_MAXREDIRS => 10,
                            CURLOPT_TIMEOUT => 0,
                            CURLOPT_FOLLOWLOCATION => true,
                            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                            CURLOPT_CUSTOMREQUEST => 'GET',
                        ));
                        $productsData = curl_exec($curl);
                        curl_close($curl);
                        $productData = json_decode($productsData, true);
                        ?>
                            <select id="qb_product_id" name="qb_product_id" class="crm-erp-field  form-control">
                                <option value="" data-name="Select Product Service" selected>Select Product Service</option>
                                <?php                                       
                                    foreach( $productData as $product ){
                                        ?>
                                        <option value="<?php echo $product['id']; ?>" data-name="<?php echo $product['name']; ?>"> <?php echo $product['name']; ?> </option>
                                        <?php 
                                    }
                                ?>
                            </select>
                            <span class="alert-error product_service_err"></span>
                    </div>
                    <div class="floating col-md-2 mb-2 add_portal_btn">
                        <a href="javascript:void(0)" class="btn btn-primary add_to_portal"> Add To Portal </a>
                    </div>
                    <div class="floating col-md-6 mb-3">
                        <label>Portal Product/Service:</label>
                        <input class="crm-erp-field  form-control" type="text" id="product_head" name="product_head" value="" readonly>
                        <span class="alert-error product_head_err"></span>
                    </div>
                    <div class="floating col-md-2 mb-2">
                        <label>Type*:</label>
                        <select class="crm-erp-field form-control" id="amount_type">
                            <option value="">Select</option>
                            <option value="$">$</option>
                            <option value="%">%</option>
                        </select>
                        <span class="alert-error amount_type_err"></span>
                    </div>
                    <div class="floating col-md-2 mb-2">
                        <label>Rate*:</label>
                        <input class="crm-erp-field  form-control" type="number" step="any" id="product_rate" name="product_rate" value="">
                        <span class="alert-error product_rate_err"></span>
                    </div>
                    <div class="floating col-md-2 mb-2">
                        <label>Unit:</label>
                        <input class="crm-erp-field  form-control" type="text" id="product_unit" name="product_unit" value="">
                        <span class="alert-error product_unit_err"></span>
                    </div>
                    <div class="floating col-md-6 mb-3">
                        <label>Invoice Type*:</label>
                        <!--<input class="crm-erp-field  form-control" type="text" id="invoice_type" name="invoice_type" value="">-->
						<select class="crm-erp-field form-control" name="invoice_type" id="invoice_type">
                            <option value="">Select</option>
                            <option value="services">Services</option>
                            <option value="charges">Charges</option>
                            <option value="miscellaneous">Miscellaneous </option>
                        </select>
                        <span class="alert-error invoice_type_err"></span>
                    </div>
                    <div class="floating col-md-6 mb-3">
                        <label>Alias*:</label>
                        <input class="crm-erp-field  form-control" type="text" id="alias" name="alias" value="">
                        <span class="alert-error alias_err"></span>
                    </div>
                    <div class="button_next_prev mb-3">
                        <input type="hidden" name="product_service_head_id" id="product_service_head_id" value="">
                        <a href="javascript:void(0);" id="save_product_service" class="nxt_btn">Save</a>
                        <p class="success-message"></p>
                        <p class="error-message"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<style type="text/css">
    .custom-crm-setting fieldset {
    border: 1px solid #d5d5d5;
    width: 100%;
    padding: 10px 15px;
    margin-bottom: 20px;
}
.custom-crm-setting legend {
    font-weight: bold;
    border-bottom: 0;
    float: inherit;
    width: auto;
    font-size: 16px;
    padding-left: 10px;
    padding-right: 10px;
    color: #1261ab;
}
.label-tooltip {
    padding-right: 15px;
    min-width: 200px;
    flex-shrink: 0;
}
.custom-crm-setting-field {
    margin-bottom: 15px;
}
.custom-crm-setting-field .input-field{
    height: 46px;
    font-size: 16px;
    transition: border-color 0.2s ease;
    caret-color: var(--color__accent);
    width: 350px;
}
.button-container {
    text-align: center !important;
    margin-top: 40px !important;
}
.nxt_btn {
    padding: 11px 25px;
    color: #FF5F00;
    font-size: 16px;
    font-weight: 600;
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000029;
    border: 2px solid #FF5F00;
    border-radius: 11px;
}
.custom-crm-setting-field .error-message{padding:1px 10px;}
#enable_disable_role_selection, .enable_disable_roles{
    padding: 4px 20px;
    margin: auto 15px;  
}
#access-settingsForm .select2-container--default{
    width:350px!important;
}
#access-settingsForm .select2-selection__clear {
    display:none;
}
#access-settingsForm .select2-container {
    margin: auto 10px!important;
}
.button_next_prev {
  width: 100%;
  text-align: center;
  margin: 40px auto 20px;
}
.nxt_btn:hover {
    color: #FF5F00;
}
.success-message, .error-message {
    display: none;
    background-color: #4CAF50; /* Green background */
    color: white; /* White text color */
    border-radius: 5px; /* Rounded corners */
    padding: 10px 20px; /* Padding inside the message */
    margin: 20px 0; /* Margin around the message */
    font-size: 16px; /* Font size */
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); /* Shadow for a raised effect */
}
.error-message {
    background-color: #f44336; /* Red background */
}
.crm-erp-product-service-delete-swal .swal-text {
  margin-left: 0px;
}

.crm-erp-product-service-delete-swal .swal-content {
  text-align: left;
  margin-top: -23px;
  margin-left: 75px;
}

.crm-erp-product-service-delete-swal .swal-footer {
  margin-top: 25px;
}
.custom-invoice-services .custom_opp_tab .custom_opp_tab_header h5{width: 270px;font-size: 12px;}
.custom-invoice-services .custom_opp_tab .lead_des p{font-size: 12px;}
.milestone-submit-btn{
    border: 1px solid #FF5C00 !important;
    color: #fff !important;
    padding: 1px 15px !important;
    border-radius: 10px !important;
    box-shadow: 0px 0px 5px #0000001a !important;
    font-weight: 500 !important;
    background: #FF5C00 !important;
    width: auto !important;
    height: 32px !important;
}
</style>
<script src="https://play.occamsadvisory.com/portal/wp-content/plugins/oc-crm/admin/partials/project-validations.js"></script>
<link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css">
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<!-- <script src='https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js?ver=5.4'></script> -->
<script type="text/javascript">
    jQuery("#product_roles").select2();
    var accessSettings = $("#access-settingsForm"); 
    if (accessSettings.length > 0) {
        accessSettings.projectValidation({
            rules: {
            },
            messages: {
            },          
            success: function(){
                // AJAX submission for Access Settings
                ajaxSubmit('#access-settingsForm', '#access-settingsSbmt', '#access-settingsMessage');
            }
        });
    }
    // Function to handle AJAX submission start
    function ajaxSubmit(formId, buttonId, responseMessageId) {
        var formData = $(formId).serialize();
        var submitButton = $(buttonId);
        var responseMessageContainer = $(responseMessageId);

        // Store the original button text
        var originalButtonText = submitButton.text();

        // Change button text to "Please Wait..." and disable it to prevent multiple submissions
        submitButton.text('Please Wait...').prop('disabled', true);

        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: formData,
            dataType: 'json', // Expecting JSON response
            success: function(response) {
                var alertClass = response.status === 'success' ? 'alert-success' : 'alert-danger';
                responseMessageContainer.html('<div class="alert ' + alertClass + '">' + response.message + '</div>');

                // Reset button text to its original state and enable it on success
                submitButton.text(originalButtonText).prop('disabled', false);
                if(response.status === 'success') {
                    setTimeout(function() {
                        window.location.reload();
                    }, 2500);
                }               
            },
            error: function(xhr, status, error) {
                // Handle error
                responseMessageContainer.html('<div class="alert alert-danger">An error occurred: ' + error + '</div>');
            }
        });
    }
    jQuery(".add_edit_service").click(function(){
		console.log('test');
        jQuery(".heading_text").text('New');
        jQuery("#save_product_service").text('Save');
        jQuery("#qb_product_id").prop('selectedIndex', 0);
        jQuery("#product_service_head_id").val(0);
        jQuery("#product_head, #amount_type, #product_rate, #product_unit, #invoice_type, #alias").val('');
		jQuery(".add-edit-service-popup").fadeIn();
		jQuery(".add-edit-service-popup").addClass('in');
		jQuery(".add-edit-service-popup").addClass('show');
    })
	

    jQuery(".edit_pro_service_head").click(function(){
        jQuery(".heading_text").text('Edit');
        jQuery("#save_product_service").text('Update');
        if($(this).data('billing_profile')){
            var billing_profile = $(this).data('billing_profile');
            var product_id = $(this).data('qb_product_id');
            jQuery("#billing_profile_id").val($(this).data('billing_profile')).trigger('change', [product_id ]);
            //alert(product_id);
            
        }else{
            var selecthtml = $(this).data('selecthtml');
            jQuery("#qb_product_id").html(selecthtml);
            var product_id = $(this).data('qb_product_id');
            jQuery("#qb_product_id option[value="+product_id+"]").attr('selected','selected');
        }
        
        jQuery("#invoice_type").val($(this).data('invoice_type'));
        jQuery("#alias").val($(this).data('alias'));
        jQuery("#product_head").val($(this).data('product_head'));
        jQuery("#amount_type").val($(this).data('amount_type'));
        jQuery("#product_rate").val($(this).data('product_rate'));
        jQuery("#product_unit").val($(this).data('product_unit'));
        jQuery("#product_service_head_id").val($(this).data('pro_service_id'));
		jQuery(".add-edit-service-popup").fadeIn();
		jQuery(".add-edit-service-popup").addClass('in');
		jQuery(".add-edit-service-popup").addClass('show');
    })
    jQuery(document).on('click','.del_pro_service_head', function () {
        var service_id = jQuery(this).data('pro_service_id');
        var product_id = "<?php echo $product_id;?>";
        swal({
            title: "Are you sure?",
            text: "Yes, I want to delete this Service.",
            icon: "warning",
            buttons: {
                cancel: "Cancel",
                confirm: "Delete",
            },
            content: {
                element: "input",
                attributes: {
                    type: "checkbox",
                    id: "agreeCheckbox"
                },
            },
            dangerMode: true,
        })
        .then((willDelete) => {
            if (willDelete && $('#agreeCheckbox').prop('checked')) {
                $(this).attr('disabled',true); 
                $("#loader_box").show();
                jQuery.ajax({
                    url:'<?php echo admin_url('admin-ajax.php'); ?>',
                    method:'post',
                    data:{action: 'delete_service', service_id:service_id },
                    success(response){
                        window.location.href = '?page=add_edit_product&action=edit&id='+product_id;
                    },
                    error: function(jqXHR, textStatus, errorThrown) {        
                        window.location.href = '?page=add_edit_product&action=edit&id='+product_id;
                        jQuery(".popup-overlay, .popup-content").removeClass("active");
                        jQuery('#overlay').hide();
                        jQuery('#loader_box').hide();  
                    },
                });
            }else{

            }
        });   
        $(".swal-overlay").addClass('swal-overlay--show-modal');
        $('.swal-modal').addClass('crm-erp-product-service-delete-swal'); 
    });  
    jQuery("#qb_product_id").change(function(){
        //$(".product_service_err").text('');
        $("#product_head").val('');
    })
    jQuery(".add_to_portal").click(function(){
        $(".product_service_err").text('');
        var qb_product_id = jQuery("#qb_product_id").val();
        var qb_product_name = jQuery("#qb_product_id option:selected").data('name');
        if(qb_product_id == ''){
            $(".product_service_err").text('Please select QB product service');
            return false;
        }
        jQuery("#product_head").val(qb_product_name);
    })
    jQuery(document).on('click','#save_product_service',function(e) {
        $(".product_service_err, .product_rate_err, .amount_type_err, .product_head_err, .invoice_type_err, .alias_err, .success-message, .error-message").text('');
        $(".success-message, .error-message").hide('');
        var product_id = "<?php echo $product_id;?>";
        var qb_product_id = $("#qb_product_id").val();
        var product_head = $("#product_head").val();
        var amount_type = $("#amount_type").val();
        var product_rate = $("#product_rate").val();
        var product_unit = $("#product_unit").val();
        var invoice_type = $("#invoice_type").val();
        var alias = $("#alias").val();
        var product_service_head_id = $("#product_service_head_id").val();
		
		var billing_profile = $('#billing_profile_id').find(':selected').val();
		console.log('billing_profile:'+billing_profile);
        //var show_product = $("input[name=show_product]").val();
        var error_exists = false;
		if(billing_profile == ''){
            $(".billing_profile_err").text('Please select billing profile');
            error_exists = true;
        }
		
        if(qb_product_id == ''){
            $(".product_service_err").text('Please select QB product service');
            error_exists = true;
        }
        /*if(product_head == ''){
            $(".product_head_err").text('Please enter product head');
            return false;
        }*/
        if(amount_type == ''){
            $(".amount_type_err").text('Please select type');
            error_exists = true;
        }
        if(product_rate == ''){
            $(".product_rate_err").text('Please enter rate');
            error_exists = true;
        }
        if(invoice_type == ''){
            $(".invoice_type_err").text('Please enter invoice type');
            error_exists = true;
        }
        if(alias == ''){
            $(".alias_err").text('Please enter alias');
            error_exists = true;
        }
        if (error_exists) return false;
        $(this).text('Please wait');
        $(this).css('pointer-events','none');
        jQuery.ajax({
            method: 'POST',
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            data: {action: 'product_qb_service',product_id: product_id,qb_product_id:qb_product_id,product_head:product_head,invoice_type:invoice_type,alias:alias,amount_type:amount_type,product_rate:product_rate,product_unit:product_unit,product_service_head_id:product_service_head_id,billing_profile:billing_profile},          
			success(response) {
                var status = response.data.status;
                if(product_service_head_id == 0) {
                    $("#save_product_service").text('Save');
                } else {
                    $("#save_product_service").text('Update');
                }
                $("#save_product_service").css('pointer-events','');
                if(status == 1){
                    $(".success-message").text(response.data.message).show().delay(3000).fadeOut();
                    setTimeout(function() {
						$(".add-edit-service-popup").fadeOut();
						$(".add-edit-service-popup").removeClass('in');
						$(".add-edit-service-popup").removeClass('show');
                        // Check if the view=qb parameter is not already in the URL
                        if (window.location.href.indexOf('view=qb') === -1) {
                            // Add the view=qb parameter to the URL
                            var newUrl = window.location.href + (window.location.href.indexOf('?') !== -1 ? '&' : '?') + 'view=qb';
                            window.location.href = newUrl; // Redirect to the new URL
                        } else {
                            location.reload(); // Reload the page without adding the parameter
                        }                    
                    }, 1000);
                }else{
                    $(".error-message").text(response.data.message).show().delay(3000).fadeOut();
                }
            }
        })
    })
    jQuery(".close-service-popup").click(function(){
		jQuery(".add-edit-service-popup").fadeOut();
		jQuery(".add-edit-service-popup").removeClass('in');
		jQuery(".add-edit-service-popup").removeClass('show');
		
    })
    jQuery(document).ready(function($) {
        jQuery(".edit_fee_type").click(function(){
            var fee_type_id = jQuery(this).data('fee_type_id');
            jQuery("#loader_box").show();
            jQuery.ajax({
                url:'<?php echo admin_url('admin-ajax.php'); ?>',
                method:'post',
                data:{action: 'edit_fee_type',fee_type_id:fee_type_id},
                success(response){
                    var fee_structure = response.data.fee_structure;
                    var retainer_fee_options = response.data.retainer_fee_options;
                    var retainer_fee_name = response.data.retainer_fee_name;
                    var retainer_amount_per = response.data.retainer_amount_per;
                    var retainer_identifier = response.data.retainer_identifier;
                    var selected_fee_options = response.data.selected_fee_options;
                    var selected_fee_name = response.data.selected_fee_name;
                    var selected_amount_per = response.data.selected_amount_per;
                    var selected_identifier = response.data.selected_identifier;
                    jQuery("#fee_type_id").val(fee_type_id);
                    if(retainer_fee_name != ''){
                        jQuery("#popup-fee-type option").remove();
                        jQuery("#popup-fee-type").append('<option value="">Select Fee Structure</option><option value="Retainer + Success/Completion Basis">Retainer + Success/Completion Basis</option><option value="Retainer + Hourly">Retainer + Hourly</option><option value="Retainer + Fixed">Retainer + Fixed</option><option value="Retainer + Unit">Retainer + Unit</option>');

                        jQuery("#popup-fee-structure").val('Combination').attr("selected","selected");
                        jQuery("#popup-fee-type").val(fee_structure).change();
                        jQuery("#retainer_fee_options").val(retainer_fee_options).change();
                        jQuery("#retainer_fee_name").val(retainer_fee_name);
                        if(retainer_amount_per != ''){
                            jQuery("#retainer_amount_per").val(retainer_amount_per);
                        }
                        if(retainer_identifier != ''){
                            jQuery("#retainer_identifier").val(retainer_identifier).change();
                        }
                    }else{
                        jQuery("#popup-fee-type option").remove();
                        jQuery("#popup-fee-type").append('<option value="">Select Fee Structure</option><option value="Success/Completion Basis">Success/Completion Basis</option><option value="Fixed">Fixed</option><option value="Hourly">Hourly</option><option value="Unit">Unit</option>');
                        jQuery("#popup-fee-structure").val('Flat').attr("selected","selected");
                    }
                    jQuery("#popup-fee-type").val(fee_structure).change();
                    jQuery("#selected_fee_options").val(selected_fee_options).change();
                    jQuery("#selected_fee_name").val(selected_fee_name);
                    if(selected_amount_per != ''){
                        jQuery("#selected_amount_per").val(selected_amount_per);
                    }
                    if(selected_identifier != ''){
                        jQuery("#selected_identifier").val(selected_identifier).change();
                    }
                    jQuery("#loader_box").hide();
                    jQuery("#save_fee_type").text('Update'); 
                    jQuery(".heading_text").text('Edit');
                    jQuery(".plus_icon").removeClass('fa-regular');
                    jQuery(".plus_icon").removeClass('fa-square-plus');
                    jQuery(".plus_icon").addClass('fa-solid fa-pen-to-square');
                    //jQuery(".heading_text").html('<i class="fa-solid fa-pen-to-square"></i>');
                    jQuery("#save_new_fee_type").hide();
                    jQuery("#add-edit-fee-type").modal('show');
                }
            });
        })
        $(document).on('change', '#agreeCheckbox', function() {
            // Check if #agreeCheckbox is checked
            if ($(this).prop('checked')) {
                $(".swal-button--confirm").css("pointer-events", "auto");
            } else {
                $(".swal-button--confirm").css("pointer-events", "none");
            }
        });

        $(document).on('click', '.swal-button-container:has(.swal-button--confirm)', function() {
            if ($('#agreeCheckbox').prop('checked')) {
                return true;
            } else {
                // Check if error message already exists
                if (!$('.swal-content + p.error-message').length) {
                    // Append the error message if it doesn't exist
                    $('.swal-content').after('<p class="error-message" style="color: red; margin: 40px;">Please agree to delete the fee type!</p>');
                }
            }
        });
        jQuery(document).on('click','.delete_fee_type',function(){
            swal({
                title: "Are you sure?",
                text: "Yes, I want to delete this fee type.",
                icon: "warning",
                buttons: {
                    cancel: "Cancel",
                    confirm: "Delete",
                },
                content: {
                    element: "input",
                    attributes: {
                        type: "checkbox",
                        id: "agreeCheckbox"
                    },
                },
                dangerMode: true,
            })
            .then((willDelete) => {
                
                if (willDelete && jQuery('#agreeCheckbox').prop('checked')) {
                    jQuery(this).attr('disabled',true);
                    jQuery("#loader_box").show();
                    var fee_type_id = jQuery(this).data('fee_type_id');
                    jQuery.ajax({
                        url:'<?php echo admin_url('admin-ajax.php'); ?>',
                        method:'post',
                        data:{action: 'delete_fee_type',fee_type_id:fee_type_id},
                        success(response){
                            location.reload(true);
                        }
                    });
                }
                jQuery('.swal-modal').removeClass('crm-erp-fee-types-delete-swal');
            });
            jQuery('.swal-modal').addClass('crm-erp-fee-types-delete-swal');
        });   
        var btn_text = "<?php echo $btn_text;?>";
        jQuery("#OwnerID").select2();
        jQuery("#UnitTypeID").select2();
        jQuery("#ProductTypeID").select2();
        jQuery("#CategoryID").select2();
        jQuery("#currency_id").select2();
        jQuery("#audit_log_table").DataTable({"ordering": false});
        jQuery("#fees_type_table").DataTable({"ordering": false});
            jQuery('#product-form').customValidation({
            rules: {
                "Title": ["required", "validName"],
                "SKU": ["required"],
                //"UnitPrice": ["required"],
                "currency_id": ["required"],
                "OwnerID": ["required"],
                "ProductTypeID": ["required"],
                "LaunchDate": ["required"],
            },
            messages: {
                "Title": {
                    "required": "Title is required.",
                    "validName": "Please enter a valid Title."
                },
                "SKU": {
                    "required": "SKU is required."
                },
                /*"UnitPrice": {
                    "required": "Unit Price is required.",
                },*/
                "OwnerID": {
                    "required": "OwnerID is required."
                },
                "currency_id": {
                    "required": "Currency is required.",
                },
                "ProductTypeID": {
                    "required": "Product Type is required.",
                },
                "LaunchDate": {
                    "required": "Launch Date is required.",
                },
                "ProductLogo": {
                    "required": "Product Logo is required.",
                },
            },
            success: function() {
              jQuery("#product_btn").html('Please wait..');
              jQuery("#product_btn").attr('disabled',true);
                var $form = jQuery("#product-form");
                var $responseDiv = jQuery('#form-response');
                
                var logoSrc = $('#logo-preview img').attr('src');
                if (logoSrc && logoSrc.startsWith('data:image')) {
                    // It's a base64 data URI
                    productLogoData = '&logo_data=' + logoSrc; // No need to save product logo if it is not in a base64 format
                }else if (logoSrc != '') {
                    // It's a base64 data URI
                    if(logoSrc == undefined){
                        productLogoData = '';
                    }else{
                        productLogoData = '&exist_logo=' + logoSrc; // No need to save product logo if it is not in a base64 format
                    }
                } else {
                    productLogoData = '';
                }
                console.log("productLogo data is " + productLogoData);

                var imageSrc = $('#images-preview img').attr('src');
                if (imageSrc && imageSrc.startsWith('data:image')) {
                    // It's a base64 data URI
                    productImageData = '&image_data=' + imageSrc; // No need to save product logo if it is not in a base64 format
                }else if (imageSrc != '') {
                    // It's a base64 data URI
                    if(imageSrc == undefined){
                        productImageData = '';
                    }else{
                        productImageData = '&exist_image=' + imageSrc; // No need to save product logo if it is not in a base64 format
                    }
                } else {
                    productImageData = '';
                }
                console.log("productImage data is " + productImageData);

                jQuery.ajax({
                    type: 'POST',
                    url: 'admin-ajax.php',
                    data: $form.serialize() + '&action=product_submit' + productLogoData + productImageData,
                    success: function(response) {
                        if(response.data.status == 1){
                            window.location.href = '?page=crm_products';
                        }else{
                            $responseDiv.html(response.data.message);
                            jQuery("#product_btn").html(btn_text);
                            jQuery("#product_btn").attr('disabled',false);
                        }
                    },
                    
                    error: function() {
                        //$responseDiv.html('An error occurred');
                    }
                });
            }
        /*});*/
    });
    
    jQuery(".add_edit_fee_type_popup").click(function(){
        jQuery(".add-edit-fee-type-popup").modal('show');
    })
    
    jQuery('.close-popup-fee-type').click(function() {
        location.reload();
    });
    // Function to handle file input change for product logo
    $('#ProductLogo').on('change', function(e) {
        var input = e.target;
        if (input.files && input.files[0]) {
            var reader = new FileReader();

            reader.onload = function(e) {
                $('#logo-preview').html('<img src="' + e.target.result + '" alt="Preview">').show();

                // Show remove icon
                $('#logo-preview').append('<span class="remove-icon">&times;</span>');
            };

            reader.readAsDataURL(input.files[0]);
        }
    });

    // Handle click on remove icon for dynamically added elements
    $('#logo-preview').on('click', '.remove-icon', function() {
        $('#logo-preview').hide().html(''); // Clear the preview and hide it
        $('#ProductLogo').val(''); // Clear the file input
    });

    // Function to handle file input change for product logo
    $('#ProductImages').on('change', function(e) {
        var input = e.target;
        if (input.files && input.files[0]) {
            var reader = new FileReader();

            reader.onload = function(e) {
                $('#images-preview').html('<img src="' + e.target.result + '" alt="Preview">').show();

                // Show remove icon
                $('#images-preview').append('<span class="remove-icon">&times;</span>');
            };

            reader.readAsDataURL(input.files[0]);
        }
    });

    // Remove below code later and make single code for .remove-icon
    $('#images-preview').on('click', '.remove-icon', function() {
        $('#images-preview').hide().html(''); // Clear the preview and hide it
        $('#ProductImages').val(''); // Clear the file input
    });
    
});
</script>

<script type="text/javascript">
    $(document).on('click', '.milestone-submit-btn', function() {
         var opportunity_milestone = jQuery("#opportunity-milestone").val();
         var opportunity_milestonestage = jQuery("#opportunity-milestonestage").val();
         var project_milestone = jQuery("#project-milestone").val();
         var project_milestonestage = jQuery("#project-milestonestage").val();
         var product_id = <?php echo $product_id; ?>;
         var valid = 1;
         
         jQuery("#opportunity-milestone-error").html("");
         jQuery("#opportunity-milestonestage-error").html("");
         jQuery("#project-milestone-error").html("");
         jQuery("#project-milestonestage-error").html("");

         if(opportunity_milestone.length==0){
            valid=0;
            jQuery("#opportunity-milestone-error").html("Milestone is required.").css('color','red');
         }
         if(opportunity_milestonestage.length==0){
            valid=0;
            jQuery("#opportunity-milestonestage-error").html("Milestone Stage is required.").css('color','red');
         }
         if(project_milestone.length==0){
            valid=0;
            jQuery("#project-milestone-error").html("Milestone is required.").css('color','red');
         }
         if(project_milestonestage.length==0){
            valid=0;
            jQuery("#project-milestonestage-error").html("Milestone Stage is required.").css('color','red');
         }
         
         if(valid==1){
            jQuery('.milestone-submit-btn').html('loading..').prop('disabled',true);
            jQuery.ajax({
            type: "POST",
            url: 'admin-ajax.php',
            data: {action:'submit_opportunity_project_milestone',create_opportunity_milestone:opportunity_milestone,create_opportunity_stage:opportunity_milestonestage,create_project_milestone:project_milestone,create_project_milestonestage:project_milestonestage,product_id:product_id},
            success: function(response) {
                console.log(response);
                swal({    title: response,
                          icon: 'success',
                          type: "success"
                    });
                location.reload();
            }
            });    
         }

    });    

    jQuery(document).on('change','.select-milestone',function(){
        var id = jQuery(this).attr('id');
        var val = jQuery(this).val();

        if(id == "opportunity-milestone"){
            var stage_id = "#opportunity-milestonestage";
        }else if(id == "project-milestone"){
            var stage_id = "#project-milestonestage";
        }
            var optionStageHTML = `<option value="">Loading..</option>`;
            jQuery(stage_id).html(optionStageHTML);       

        var url = "<?php echo site_url().'/wp-json/productsplugin/v1/milestone-status';?>";
        jQuery.ajax({
            type: "POST",
            url: url,
            data: {id:val},
            success: function(response) {
                jQuery(stage_id).html('');       
               if(response.length!=0){ 
                jQuery.each( response, function (indexes, values){
                    var optionHTML = `<option value="${values.milestone_stage_id}"> ${values.stage_name} </option>`;
                    jQuery(stage_id).append(optionHTML);       
                });
               }else{
                    var optionStageHTML = `<option value="">Select Milestone Stage</option>`;
                    jQuery(stage_id).append(optionStageHTML);       
               }      
            }
        });            
    });
	
	jQuery(document).ready(function ($) {
		// Attach change event to the dropdown
		$('#billing_profile_id').on('change', function (event,product_id ="") {
            //alert(product_id);
			// Get the selected option
			var selectedOption = $(this).find(':selected');

			// Extract attribute values from the selected option
			var gatewayClientKey = selectedOption.attr('gatewaykey');
			var gatewayClientId = selectedOption.attr('gatewayid');
			var organisationId = selectedOption.attr('orgid');

			// Check if all attributes are present
			if (!gatewayClientKey || !gatewayClientId || !organisationId) {
				alert('Please select a valid billing profile.');
				return;
			}

			// Prepare the data to send in the POST request
			var postData = {
				action: 'fetch_product_service_head_by_billingprofile',
				gateway_client_key: gatewayClientKey,
				gateway_client_id: gatewayClientId,
				organisation_id: organisationId
			};

			// Make the AJAX request 
			$.ajax({
				url: '<?php echo admin_url('admin-ajax.php'); ?>',
				type: 'POST',
				data: postData,
				beforeSend: function () {
                $('#billing_profileproduct_fetch_loader').show();
            },
				success: function (response) {
					$('#billing_profileproduct_fetch_loader').hide();
					// Handle success response
					console.log('API Response:', response);
					if (response.success) {
                    const products = response.data;
                    //var qb_product_id = $("#qb_product_id").val();
                    // Clear the dropdown
                    $('#qb_product_id').empty();

                    // Add default option
                   /*  $('#qb_product_id').append(
                        '<option value="" selected>Select Product Service</option>'
                    ); */

                    // Append options dynamically
                    products.forEach(function (product) {
                        $('#qb_product_id').append(`<option value="${product.id}" data-name="${product.name}">${product.name}</option>`
                        );
                    });
                    if(product_id != ''){
                        jQuery("#qb_product_id option[value="+product_id+"]").attr('selected','selected');
                    }
                } else {
                    alert('Failed to fetch product data.');
                }
				},
				error: function (xhr, status, error) {
					// Handle error response
					console.error('Error:', error);
					alert('An error occurred while calling the API. Please try again.');
				}
			});
		});
	});

</script>