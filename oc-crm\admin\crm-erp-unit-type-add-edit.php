<?php

if($_REQUEST['action'] == 'edit'){
    $unit_type_id = $_REQUEST['id'];
    $unit_type_manager = new CRM_ERP_Unit_Type_Manager();
    $units_data = $unit_type_manager->get_unit_type($unit_type_id);
    $UnitName = $units_data->UnitName;
    $Description = $units_data->Description;
    $heading_text = 'Edit';
    $btn_text = 'Update';
    $heading_img = 'edit-crm-icon.png';
}else{
	$unit_type_id = '';
	$UnitName = '';
	$Description = '';
	$heading_text = 'New';
    $heading_img = 'add-crm-icon.png';
	$btn_text = 'Submit';
}
?>
<link rel="stylesheet" id="dataTable_Style-css" href="https://cdn.datatables.net/1.10.22/css/jquery.dataTables.min.css?ver=6.2.2" media="all">
<div class="main_content_iner ">
    <div class="container-fluid p-0">
         <div class="row justify-content-center">
            <div class="col-lg-12">
                <div id="form_2" class="white_card card_height_100 mb_30" style="display: block;">
                     <div class="white_card_header">
                        <div class="box_header m-0 new_report_header">
                            <div class="title_img">
                                <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/<?php echo $heading_img; ?>" class="page-title-img" alt="">
                                <h4><?php echo $heading_text;?> Unit Type</h4>
                            </div>
                        </div>
                        <?php if($unit_type_id != ''){?>
                        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                            <li class="nav-item active">
                                <a class="nav-link" id="pills-erc-edit-info" data-toggle="pill" href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true">Edit</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="pills-erc-audit-log-info" data-toggle="pill" href="#pills-audit-log" role="tab" aria-controls="pills-audit-log" aria-selected="true">Audit Log</a>
                            </li>
                        </ul>
                        <?php } ?>
                    </div>
                    <div class="white_card_body p-15">
                        <div class="container-fluid">
                            <div class="tab-content" id="pills-tabContent">
                                <div class="tab-pane scroll_height-business scroll-common active" id="pills-home" role="tabpanel" aria-labelledby="pills-erc-edit-info">
                                    <form id="unit-type-form" method="post">
                                        <div class="row mb-2">
                                            <div class="floating col-md-12">     
                                                <label for="title">Unit Name:*</label>
                                                <input type="text" name="UnitName" id="UnitName" class="crm-erp-field form-control" value="<?php echo $UnitName;?>">
                                            </div>
                                        </div>
                                        <div class="row mb-2">
                                            <div class="floating col-md-12">     
                                                <label for="description">Description:</label>
                                                <textarea name="Description" id="Description" rows="5" class="form-control" maxlength="2000"><?php echo $Description;?></textarea>
                                            </div>
                                        </div>
                                        <input type="hidden" name="unit_type_id" id="unit_type_id" value="<?php echo $unit_type_id;?>">
                                        <div class="button-container">
                                            <button type="submit" id="crm_erp_unit_type_btn" class="crm_erp_btn"><?php echo $btn_text;?></button>
                                        </div>
                                    </form>
                                    <div id="form-response"></div>
                                </div>
                                <?php if($unit_type_id != ''){?>
                                <div class="tab-pane scroll_height-business scroll-common" id="pills-audit-log" role="tabpanel" aria-labelledby="pills-erc-audit-log-info">
                                    <div class="row">
                                        <table id="audit_log_table">
                                            <thead>
                                                <tr>
                                                    <th>Field Name</th>
                                                    <th>Changed From</th>
                                                    <th>Changed To</th>
                                                    <th>Data Type</th>
                                                    <th>Action</th>
                                                    <th>Changed Time</th>
                                                    <th>Changed By</th>
                                                </tr>
                                            </thead>
                                            <tbody class="audit_data1">
                                                <?php
                                                global $wpdb;
                                                $audit_log_table = $wpdb->prefix . 'audit_logs';
                                                $audit_logs = $wpdb->get_results("SELECT $audit_log_table.* FROM $audit_log_table WHERE $audit_log_table.TableName = '" . $wpdb->prefix . "unit_types'  AND $audit_log_table.FieldID = " . $unit_type_id . " ORDER BY $audit_log_table.LogID DESC");
                                                $c = 0;
                                                $audit_log_history = '';
                                                if (!empty($audit_logs)) {
                                                    $count = 0;
                                                    foreach ($audit_logs as $audit_log) {
                                                        $CreatedBy = get_userdata($audit_log->CreatedBy);
                                                        if(!empty($CreatedBy)){
                                                            $changed_by = $CreatedBy->data->display_name;
                                                        }else{
                                                            $changed_by = '';
                                                        }
                                                        $DateCreated = date('Y-m-d H:i a',strtotime($audit_log->DateCreated));
                                                        $audit_log_history .= "<tr>
                                                              <td>" . $audit_log->FieldName . "</td>
                                                              <td>" . $audit_log->BeforeValueString . "</td>
                                                              <td>" . $audit_log->AfterValueString . "</td>
                                                              <td>" . $audit_log->DataType . "</td>
                                                              <td>" . $audit_log->Action . "</td>
                                                              <td>" . $DateCreated . "</td>
                                                              <td>" . $changed_by . "</td>
                                                            </tr>";
                                                    }
                                                } else {
                                                    $audit_log_history .= "<tr>
                                                      <td></td>
                                                      <td></td>
                                                      <td></td>
                                                      <td>No Data Found</td>
                                                      <td></td>
                                                      <td></td>
                                                      <td></td>
                                                    </tr>";
                                                }
                                                echo $audit_log_history;?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- .site-main -->
</div><!-- .content-area -->

<script type="text/javascript">
    jQuery(document).ready(function($) {
        var btn_text = "<?php echo $btn_text;?>";
        jQuery("#audit_log_table").DataTable({"ordering": false});
        jQuery(document).on('click','#crm_erp_unit_type_btn',function(e) {
            //e.preventDefault();
            jQuery('#unit-type-form').customValidation({
            rules: {
                "UnitName": ["required"],
            },
            messages: {
                "UnitName": {
                    "required": "Unit Name is required.",
                },
            },
            success: function() {
              jQuery("#crm_erp_unit_type_btn").html('Please wait..');
              jQuery("#crm_erp_unit_type_btn").attr('disabled',true);
                var $form = jQuery("#unit-type-form");
                var $responseDiv = jQuery('#form-response');

                jQuery.ajax({
                    type: 'POST',
                    url: 'admin-ajax.php',
                    data: $form.serialize() + '&action=unit_type_submit',
                    success: function(response) {
                        if(response.data.status == 1){
                            jQuery("#crm_erp_unit_type_btn").html(btn_text);
                            jQuery("#crm_erp_unit_type_btn").attr('disabled',false);
                            window.location.href = '?page=unit_types';
                        }else{
                            $responseDiv.html(response.data.message);
                            jQuery("#crm_erp_unit_type_btn").html(btn_text);
                            jQuery("#crm_erp_unit_type_btn").attr('disabled',false);
                        }
                        //$responseDiv.html(response.data);
                    },
                    error: function() {
                        //$responseDiv.html('An error occurred');
                    }
                });
            }
        });
    });
});
</script>