<?php
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
global $wpdb;
$lead_id = $_GET['lead_id'];
$project_id = $_GET['project_id'];

$intake_table_name = $wpdb->prefix."rdc_intake";
echo "SELECT * FROM $intake_table_name WHERE lead_id=".$lead_id." AND project_id=".$project_id."";
/*
$in_sql = $wpdb->prepare("SELECT * FROM $intake_table_name WHERE lead_id = ".$lead_id." AND project_id = ".$project_id."");
$intake_data = $wpdb->get_results($in_sql);

print_r($intake_data); */

?>
<div class="erc-project-view">

    <fieldset>
        <legend>R&D Basic Details</legend>
        <div class="row mb-3">
			<div class="floating mb-3">
				<!-- Internal Sales Agent -->
				<div class="floating col-sm-4 mb-3">
					<label>Internal Sales Agent</label>
					<input name="internal_sales_agent" id="internal_sales_agent" value="" class="crm-erp-field form-control" type="text">
				</div>

				<!-- Affiliate Name -->
				<div class="floating col-sm-4 mb-3">
					<label>Affiliate Name</label>
					<input name="affiliate_name" id="affiliate_name" value="" class="crm-erp-field form-control" type="text">
				</div>
			</div>
		</div>
	 </fieldset>
			
	<fieldset>
		<legend>R&D Year <a href="javascript:void(0)" id="add_rnd_year_row" class="btn btn-primary btn-sm">+</a></legend>
		 <div id="rd-year-rows">

			<div class="floating col-sm-12 mb-3" id="rdyeardivshow" style="display:none;">
				<!-- R&D Year -->
				<div class="floating col-sm-4 mb-3">
					<label>R&D Year</label>
					<input name="rnd_year" id="rnd_year" value="" class="crm-erp-field form-control" type="text">
				</div>
				<!-- Federal R&D Amount -->
				<div class="floating col-sm-4 mb-3">
					<label>Federal R&D Amount</label>
					<input name="federal_rnd_amount" id="federal_rnd_amount" value="" class="crm-erp-field form-control" type="text">
				</div>
				<!-- State R&D Amount -->
				<div class="floating col-sm-4 mb-3">
					<label>State R&D Amount</label>
					<input name="state_rnd_amount" id="state_rnd_amount" value="" class="crm-erp-field form-control" type="text">
				</div>
				<!-- State R&D Amount -->
				<div class="floating col-sm-4 mb-3">
					<label>State Code</label>
				<input name="rnd_state_code" value="" id="rnd_state_code" class="crm-erp-field form-control" type="text">
				</div>
			</div>
		</div>
	</fieldset>
	<!-- Fee Type -->
	<fieldset>
		<legend>Fee Type</legend>
		 <div id="rd-year-rows">
			<div class="floating col-sm-4 mb-3">
				<label>Fee Type</label>
				<select name="fee_type" id="fee_type" class="crm-erp-field form-control">
					<option value="">Select fee type</option>
					<option value="standard">$2,500 R&D Retainer (standard)</option>
					<option value="custom">Custom</option>
				</select>
			</div>
		</div>
	</fieldset>
</div> 

<script>
jQuery(document).ready(function() {
	jQuery("#add_rnd_year_row").click(function(){
		jQuery("#rdyeardivshow").slideToggle(function(){
			if(jQuery("#add_rnd_year_row").text() == "+"){
				jQuery("#add_rnd_year_row").text("-");
			}
			else {
			  jQuery("#add_rnd_year_row").text("+");
			}
		});
	})
	jQuery(".rnd_year").datepicker({
		dateFormat: "mm/dd/yy",
	});
	
	
	/* jQuery(document).on('click', '.update_project', function() {
            var form = '#lead_update_form';
            var form_data = jQuery(form).serialize();
            var form_data_val = form_data;
			console.log('RDC Intake data');
			console.log(form_data_val);
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'post',
                data: form_data_val,
                success(response) {
                    var res = JSON.parse(response);
                    jQuery(".update_project").html('Save');
                    jQuery(".update_project").attr('disabled', false);
                    if(res.status == 200){
                        alert("Project saved successfully");
                    }else{
                        alert(res.message);
                    }
                }
            });
        }) */
});
</script>
<?php  

exit;

?>