<?php

$invoice_view_url = !empty($invoice['invoice_url']) ? esc_url($invoice['invoice_url']) : '';

if ($invoice_view_url == "") {

    // $invoice['token']

    if ($invoice['invoice_type'] == "custom_invoice") { // get tokened url to view invoice 

        if ($invoice['token'] != "") {
            $invoice_view_url = site_url() . '/invoice-view/?token=' . $invoice['token'];
        } else {
            $invoice_view_url = site_url() . '/invoice-view/?invoice_id=' . $invoice['id'];
        }

    } else {

        $invoice_view_url = site_url() . '/invoice-view/?invoice_id=' . $invoice['id'];
    }

}



$inv_id = $invoice['id'];

$userdata = get_user_by('id', get_current_user_id());
$user_roles = $userdata->roles;

$invoice_status_id = !empty($invoice['status']) ? esc_html($invoice['status']) : '';


$invoice_prefix = "";

if (isset($_GET['page']) && $_GET['page'] == "manage-stc-project") {
    $invoice_prefix = "STC-";
}

if (isset($_GET['page']) && $_GET['page'] == "manage-ta-project") {
    $invoice_prefix = "TAX-";
}

if (isset($_GET['page']) && $_GET['page'] == "manage-audit-project") {
    $invoice_prefix = "AAR-";
}


if (isset($_GET['page']) && $_GET['page'] == "manage-rdc-project") {
    $invoice_prefix = "RDC-";
}

if (isset($_GET['page']) && $_GET['page'] == "manage-erc-project") {
    $invoice_prefix = "ERC-";
}



$invoice_status_class = "";

if ($invoice_status_id == 1) {
    // $invoice_status = 'Invoiced';
    $invoice_status = 'Unpaid';
    $invoice_status_class = 'unpaid badge bg-danger';
} else if ($invoice_status_id == 2) {
    $invoice_status = 'Paid';
    $invoice_status_class = 'paid_invoice badge bg-success';
} else if ($invoice_status_id == 3) {
    $invoice_status = 'Cancelled';
    $invoice_status_class = 'cancel badge bg-cancel';
} else if ($invoice_status_id == 4) {
    $invoice_status = 'Draft';
    $invoice_status_class = 'draft';
} else if ($invoice_status_id == 5) {
    $invoice_status = 'Remind';
    $invoice_status_class = 'remind';
} else if ($invoice_status_id == 6) {
    $invoice_status = 'Payment in process';
    $invoice_status_class = 'payment_in_process badge bg-warning';
} else if ($invoice_status_id == 13) {
    $invoice_status = 'Delete';
    $invoice_status_class = 'delete';
} else if ($invoice_status_id == 17) {
    $invoice_status = 'Partially paid';
    $invoice_status_class = 'partially_paid badge bg-primary';
} else {
    $invoice_status = '';
}

if ($invoice_status_id == 3) {
    $class = 'disabled';
} else {
    $class = '';
}


$payment_methods = array(
    "occams_initiated_eCheck" => "Occams Initiated - eCheck",
    "occams_initiated_ach" => "Occams Initiated - ACH",
    "occams_initiated_wire" => "Client Initiated - Wire",
    "client_initiated_ach" => "Client Initiated - ACH",
    "client_initiated_check_mailed" => "Client Initiated - Check Mailed",
    "credit_card_or_debit_card" => "Credit Card or Debit Card",
    "credit_card" => "Credit card",
    "ach" => "Bank transfer",
    "qb-link" => "QB Payment Link",
    "ipn" => "PayPal",
    "other-payment" => "Other Payment",
);




// $payment_methods = array(
//     "credit_card" => "Credit card",
//     "ach" => "Bank transfer",
//     "qb-link" => "QB Payment Link",
//     "ipn" => "PayPal",
//     "other-payment" => "Other Payment",
// );


$payment_mode = "";
$payment_date = "";
$payment_cleared_date = "";

// strtotime($invoice_date) < strtotime('08/31/2024')

// echo "invoice_date => " . $invoice['invoice_date'];

if (strtotime($invoice['invoice_date']) < strtotime('08/14/2024')) {



    $payment_mode = $invoice['payment_mode'];
    $payment_date = $invoice['payment_date'];
    $payment_cleared_date = $invoice['payment_cleared_date'];

} else {

    if ($invoice_status_id == 17 || $invoice['payment_count'] > 1) {
       
        $payment_mode = $invoice['payments_mode'];
        $payment_date = $invoice['payments_date'];
        $payment_cleared_date = $invoice['payments_cleared_date'];
    } else {
       
        $payment_mode = $invoice['payment_mode'];
        $payment_date = $invoice['payment_date'];
        $payment_cleared_date = $invoice['payment_cleared_date'];
    }
}

if($payment_date!=''){
	$payment_date = date('m/d/Y', strtotime($payment_date));
}
if(!empty($payment_cleared_date)){
	$payment_cleared_date = date('m/d/Y', strtotime($payment_cleared_date));
}

// $payment_mode = $invoice['payments_mode'];

// Assume $invoice['payment_method'] is a comma-separated string like "credit_card,ach"
$payment_method_keys = !empty($payment_mode) ? explode(',', $payment_mode) : [];

$selected_values = array();

foreach ($payment_method_keys as $key) {
    $key = trim($key); // Trim any extra whitespace
    if (array_key_exists($key, $payment_methods)) {
        $selected_values[] = $payment_methods[$key];
    }
}

$payment_mode_string = !empty($selected_values) ? implode(', ', $selected_values) : 'N/A';

// $invoice_view_url = $invoice['invoice_url'];

$invoice_url = "";
$edit_access = "true";
$invoice_url = site_url() . "/wp-admin/admin.php?page=edit-custom-invoice&invoice_id=" . $invoice['id'];

if ($invoice_status != 'Unpaid' && $invoice_status != 'partially paid' && $invoice['invoice_type'] != "custom_invoice") {
    $edit_access = "false";
}

if ($invoice_status_id == 3 || $invoice_status_id == 2 || $invoice_status_id == 6) {
    $edit_access = "false";
}

if ($invoice['invoice_url'] == "") {
    $edit_access = "false";
}


/****** Finance Team and Master ops *****/
$dropdown_action_access = "false";
if (in_array("echeck_client", $user_roles) || in_array("master_ops", $user_roles)) {
    $dropdown_action_access = 'true';
}

$invoice_total = "";


if ($invoice['total_amount'] != "") {
    $invoice_total = "$" . $invoice['total_amount'];
}

// $invoice_total = "";
?>

<div class="contact_tab_data ">
    <div class="col-md-12 col-sm-12 contact-card  <?php echo $class; ?>">
        <div class="row custom_opp_tab">
            <div class="col-sm-12">
                <div class="custom_opp_tab_header">
                    <h5>
                        <a href="<?php echo $invoice_view_url; ?>" target="_blank" data-inviceID="<?php echo !empty($invoice['id']) ? esc_html($invoice['id']) : ''; ?>">
                            Invoice
                            <?php echo $invoice_prefix; ?><?php echo !empty($invoice['customer_invoice_no']) ? esc_html($invoice['customer_invoice_no']) : ''; ?>
                        </a>
                        -
                        <span class="status <?php echo $invoice_status_class; ?>"> <?php echo $invoice_status; ?></span>
                    </h5>
                    <div class="opp_edit_dlt_btn projects-iris">
                        <?php
                        if ($dropdown_action_access == "true") {
                            if ($invoice_status_id != 2 && $invoice_status_id != 3 && $invoice_status_id != 6) {
                                echo do_shortcode('[invoice_actionlist invoice_id="' . $invoice['id'] . '"]');
                            }
                        }
                        ?>
                        <?php

                        if ($edit_access == "true") {
                            if (in_array("echeck_client", $user_roles) || in_array("master_ops", $user_roles)) {
                                ?>
                                <a class="edit_opportunity" target="_blank" href="<?php echo $invoice_url; ?>">
                                    <i class="fa-solid fa-pen"></i>
                                </a>
                                <?php
                            }
                        }

                        ?>
                    </div>
                </div>
            </div>


            <div class="col-md-7 text-left">
                <div class="lead_des">
                    <p>
                        <b>Invoice Amount:</b>
                        <?php echo ($invoice_total != "") ? esc_html($invoice_total) : 'N/A'; ?>
                    </p>

                    <p>
                        <b>Invoice Sent Date:</b>
                        <?php echo !empty($invoice['invoice_date']) ? esc_html($invoice['invoice_date']) : 'N/A'; ?>
                    </p>
                    <p>
                        <b>Invoice Due Date:</b>
                        <?php echo !empty($invoice['due_date']) ? esc_html($invoice['due_date']) : 'N/A'; ?>
                    </p>
                    <p>
                        <b>Service Name:</b>
                        <?php echo !empty($invoice['product_names']) ? esc_html($invoice['product_names']) : 'N/A'; ?>
                    </p>
                    <p>
                        <b>Created By:</b>
                        <?php if (!empty($invoice['created_user'])) {
                            $created_id = $invoice['created_user'];
                            $created_user = get_user_by('id', $created_id);
                            echo $created_by = $created_user->display_name;
                        }

                        ?>
                    </p>

                    <?php
                    if ($updatedBy != '') {
                        $updatedBy_user = get_user_by('id', $updatedBy);
                        echo "<p>";
                        echo '<b>Updated By: </b>' . $updatedBy_user = $updatedBy_user->display_name;
                        echo "</p>";
                    }


                    ?>

                </div>

            </div>


            <?php

            if ($invoice_status_id != 17 && $invoice['payment_count'] <= 1) {

                ?>
                <div class="col-md-5">
                    <div class="lead_des">
                        <p>
                            <b>Payment Date:</b>
                            <?php echo !empty($payment_date) ? esc_html($payment_date) : 'N/A'; ?>
                        </p>
                        <p>
                            <b>Payment Cleared Date:</b>
                            <?php echo !empty($payment_cleared_date) ? esc_html($payment_cleared_date) : 'N/A'; ?>
                        </p>
                        <p>
                            <b>Payment Mode:</b>
                            <?php echo esc_html($payment_mode_string); ?>
                        </p>
                    </div>
                </div>
                <?php
            } else {
                ?>
                <div class="col-md-5">
                    <div class="lead_des">
                        <p>
                            <b>Payment Date:</b>
                            <?php echo !empty($payment_date) ? esc_html($payment_date) : 'N/A'; ?>
                        </p>
                        <p>
                            <b>Payment Cleared Date:</b>
                            <?php echo !empty($payment_cleared_date) ? esc_html($payment_cleared_date) : 'N/A'; ?>
                        </p>
                        <p>
                            <b>Payment Mode:</b>
                            <?php echo esc_html($payment_mode_string); ?>
                        </p>
                    </div>
                </div>
                <?php
            }
            ?>




            <?php if ($invoice_status_id == 17 || ($invoice_status_id == 2 && $invoice['payment_count'] > 1)) { ?>
                <a class="expand_pp_div" data-toggle="collapse" href="#invoice_pp_<?php echo $inv_id; ?>"
                    aria-expanded="false" aria-controls="invoice_pp">Payment History
                    <i class="fa-regular fa-circle-arrow-down expand_card_partialdetail" style="font-weight: 700"></i>
                </a>
                <div class="collapse" id="invoice_pp_<?php echo $inv_id; ?>">
                    <div class="card card-body" style="max-width: 100%;padding: 10px;">
                        <?php echo do_shortcode('[partial_payment_list invoice_id="' . $invoice['id'] . '" invoice_amount="' . $invoice['total_amount'] . '"]'); ?>
                    </div>
                </div>
            <?php } ?>

        </div>
    </div>

</div>

<!--/ invoice-card-end -->