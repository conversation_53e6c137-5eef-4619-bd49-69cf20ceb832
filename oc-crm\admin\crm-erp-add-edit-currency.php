<?php
if($_REQUEST['action'] == 'edit'){
    $currency_id = $_REQUEST['id'];
    $currency_manager = new CRM_ERP_Currency_Manager();
    $currency_data = $currency_manager->get_currency($currency_id);
    $currency_code = $currency_data->currency_code;
    $currency_name = $currency_data->currency_name;
    $heading_text = 'Edit';
    $btn_text = 'Update';
    $heading_img = 'edit-crm-icon.png';
}else{
	$currency_id = '';
	$currency_code = '';
	$currency_name = '';
	$heading_text = 'New';
	$btn_text = 'Submit';
    $heading_img = 'add-crm-icon.png';
}
?>
<div class="main_content_iner ">
    <div class="container-fluid p-0">
         <div class="row justify-content-center">
            <div class="col-lg-12">
                <div id="form_2" class="white_card card_height_100 mb_30">
                     <div class="white_card_header">
                        <div class="box_header m-0 new_report_header">
                            <div class="title_img">
                                <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/<?php echo $heading_img; ?>" class="page-title-img" alt="">
                                <h4><?php echo $heading_text;?> Currency</h4>
                            </div>
                        </div>
                        <?php if($currency_id != ''){?>
                        <ul class="nav nav-pills mb-3" id="pills-tab" role="tablist">
                            <li class="nav-item active">
                                <a class="nav-link" id="pills-erc-edit-info" data-toggle="pill" href="#pills-home" role="tab" aria-controls="pills-home" aria-selected="true">Edit</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="pills-erc-audit-log-info" data-toggle="pill" href="#pills-audit-log" role="tab" aria-controls="pills-audit-log" aria-selected="true">Audit Log</a>
                            </li>
                        </ul>
                        <?php } ?>
                    </div>
                    <div class="white_card_body p-15">
                        <div class="container-fluid">
                            <div class="tab-content" id="pills-tabContent">
                                <div class="tab-pane scroll_height-business scroll-common active" id="pills-home" role="tabpanel" aria-labelledby="pills-erc-edit-info">
                                    <form id="currency-form" method="post">
                                        <div class="row mb-2">
                                            <div class="floating col-md-6">     
                                                <label for="currency_code">Currency Code:*</label>
                                                <input type="text" name="currency_code" id="currency_code" class="crm-erp-field form-control" value="<?php echo $currency_code;?>">
                                            </div>
                                            <div class="floating col-md-6">     
                                                <label for="currency_name">Currency Name:*</label>
                                                <input type="text" name="currency_name" id="currency_name" class="crm-erp-field form-control" value="<?php echo $currency_name;?>">
                                            </div>
                                        </div>
                                        <input type="hidden" name="currency_id" id="currency_id" value="<?php echo $currency_id;?>">
                                        <div class="button-container">
                                            <button type="submit" id="crm_erp_cureency_btn" class="crm_erp_btn"><?php echo $btn_text;?></button>
                                        </div>
                                    </form>
                                    <div id="form-response"></div>
                                </div>
                                <?php if($currency_id != ''){?>
                                <div class="tab-pane scroll_height-business scroll-common" id="pills-audit-log" role="tabpanel" aria-labelledby="pills-erc-audit-log-info">
                                    <div class="row">
                                        <table id="audit_log_table">
                                            <thead>
                                                <tr>
                                                    <th>Field Name</th>
                                                    <th>Changed From</th>
                                                    <th>Changed To</th>
                                                    <th>Data Type</th>
                                                    <th>Action</th>
                                                    <th>Changed Time</th>
                                                    <th>Changed By</th>
                                                </tr>
                                            </thead>
                                            <tbody class="audit_data1">
                                                <?php
                                                global $wpdb;
                                                $audit_log_table = $wpdb->prefix . 'audit_logs';
                                                $audit_logs = $wpdb->get_results("SELECT $audit_log_table.* FROM $audit_log_table WHERE $audit_log_table.TableName = '" . $wpdb->prefix . "currencies'  AND $audit_log_table.FieldID = " . $currency_id . " ORDER BY $audit_log_table.LogID DESC");
                                                $c = 0;
                                                $audit_log_history = '';
                                                if (!empty($audit_logs)) {
                                                    $count = 0;
                                                    foreach ($audit_logs as $audit_log) {
                                                        $CreatedBy = get_userdata($audit_log->CreatedBy);
                                                        if(!empty($CreatedBy)){
                                                            $changed_by = $CreatedBy->data->display_name;
                                                        }else{
                                                            $changed_by = '';
                                                        }
                                                        $DateCreated = date('Y-m-d H:i a',strtotime($audit_log->DateCreated));
                                                        $audit_log_history .= "<tr>
                                                              <td>" . $audit_log->FieldName . "</td>
                                                              <td>" . $audit_log->BeforeValueString . "</td>
                                                              <td>" . $audit_log->AfterValueString . "</td>
                                                              <td>" . $audit_log->DataType . "</td>
                                                              <td>" . $audit_log->Action . "</td>
                                                              <td>" . $DateCreated . "</td>
                                                              <td>" . $changed_by . "</td>
                                                            </tr>";
                                                    }
                                                } else {
                                                    $audit_log_history .= "<tr>
                                                      <td></td>
                                                      <td></td>
                                                      <td></td>
                                                      <td>No Data Found</td>
                                                      <td></td>
                                                      <td></td>
                                                      <td></td>
                                                    </tr>";
                                                }
                                                echo $audit_log_history;?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- .site-main -->
</div><!-- .content-area -->

<script type="text/javascript">
    jQuery(document).ready(function($) {
        var btn_text = "<?php echo $btn_text;?>";
        jQuery("#audit_log_table").DataTable({"ordering": false});
        jQuery(document).on('click','#crm_erp_cureency_btn',function(e) {
            //e.preventDefault();
            jQuery('#currency-form').customValidation({
            rules: {
                "currency_code": ["required"],
                "currency_name": ["required"],
            },
            messages: {
                "currency_code": {
                    "required": "Currency code is required.",
                },
                "currency_name": {
                    "required": "Currency name is required.",
                },
            },
            success: function() {
              jQuery("#crm_erp_cureency_btn").html('Please wait..');
              jQuery("#crm_erp_cureency_btn").attr('disabled',true);
                var $form = jQuery("#currency-form");
                var $responseDiv = jQuery('#form-response');

                jQuery.ajax({
                    type: 'POST',
                    url: 'admin-ajax.php',
                    data: $form.serialize() + '&action=currency_submit',
                    success: function(response) {
                        if(response.data.status == 1){
                            jQuery("#crm_erp_cureency_btn").html(btn_text);
                            jQuery("#crm_erp_cureency_btn").attr('disabled',false);
                            //jQuery("#product-form").trigger('reset');
                            window.location.href = '?page=currency-settings';
                            //$responseDiv.html(response.data);
                        }else{
                            $responseDiv.html(response.data.message);
                            jQuery("#crm_erp_cureency_btn").html(btn_text);
                            jQuery("#crm_erp_cureency_btn").attr('disabled',false);
                        }
                    },
                    error: function() {
                        //$responseDiv.html('An error occurred');
                    }
                });
            }
        });
    });
});
</script>