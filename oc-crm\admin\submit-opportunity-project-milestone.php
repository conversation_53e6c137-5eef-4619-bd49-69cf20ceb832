<?php
	global $wpdb;
	$product_setting_table = $wpdb->prefix.'crm_product_setting';

	$create_opportunity_milestone = $_POST['create_opportunity_milestone'];
    $create_opportunity_stage = $_POST['create_opportunity_milestonestage'];
    $create_project_milestone = $_POST['create_project_milestone'];
    $create_project_milestonestage = $_POST['create_project_milestonestage'];
    
    unset($_POST['action']);
    $data = $_POST;
	$product_id = $_POST['product_id'];
	    
	 $product_setting =  $wpdb->get_row("SELECT * FROM $product_setting_table WHERE product_id=$product_id");

	 if($product_setting){
	 	$wpdb->update($product_setting_table, $data, array( 'id' => $product_setting->id));
	 	$response = "Product setting updated.";
	 }else{
    		$data['create_at'] = date("Y-m-d H:i:s");
    		$data['created_by'] = get_current_user_id();
    		$wpdb->insert( $product_setting_table,$data);
    		$response = "Product setting inserted.";
     } 
     echo $response;
     die();
?>