<?php
global $wpdb;
$table_name = $wpdb->prefix.'projects';
$data = $request->get_json_params();
$project_id = $data['project_id'];

$project = $wpdb->get_row("SELECT {$wpdb->prefix}projects.*,{$wpdb->prefix}erc_bank_info.bank_name,{$wpdb->prefix}erc_bank_info.account_holder_name,{$wpdb->prefix}erc_bank_info.account_number,{$wpdb->prefix}erc_bank_info.aba_routing_no
                                    FROM {$wpdb->prefix}projects 
                                    LEFT JOIN {$wpdb->prefix}erc_bank_info 
        							ON {$wpdb->prefix}projects.lead_id = {$wpdb->prefix}erc_bank_info.lead_id
                                    WHERE {$wpdb->prefix}projects.deleted_at IS NULL 
                                    AND {$wpdb->prefix}projects.project_id = ".$project_id." ");
if(!empty($project)){
	$results['status'] = 1;
	$results['message'] = 'Fulfilment Detail';
	$income_2019 = $project->income_2019;
	$income_2020 = $project->income_2020;
	$income_2021 = $project->income_2021;
	$bank_name = $project->bank_name;
	$account_holder_name = $project->account_holder_name;
	$account_number = $project->account_number;
	$aba_routing_no = $project->aba_routing_no;
	$stc_amount_2020 = $project->stc_amount_2020;
	$stc_amount_2021 = $project->stc_amount_2021;
	$maximum_credit = $project->maximum_credit;
	$actual_credit = $project->actual_credit;
	$estimated_fee = $project->estimated_fee;
	$actual_fee = $project->actual_fee;
	$years = $project->years;
	$results['result'][0]['income_2019'] = $income_2019;
	$results['result'][0]['income_2020'] = $income_2020;
	$results['result'][0]['income_2021'] = $income_2021;
	$results['result'][0]['bank_name'] = $bank_name;
	$results['result'][0]['account_holder_name'] = $account_holder_name;
	$results['result'][0]['account_number'] = $account_number;
	$results['result'][0]['aba_routing_no'] = $aba_routing_no;
	$results['result'][0]['stc_amount_2020'] = $stc_amount_2020;
	$results['result'][0]['stc_amount_2021'] = $stc_amount_2021;
	$results['result'][0]['maximum_credit'] = $maximum_credit;
	$results['result'][0]['actual_credit'] = $actual_credit;
	$results['result'][0]['actual_fee'] = $actual_fee;
	$results['result'][0]['estimated_fee'] = $estimated_fee;
	$results['result'][0]['years'] = $years;
}
else{
	$results['status'] = 0;
    $results['message'] = 'No fulfilment data found.';
}
echo json_encode($results);die;