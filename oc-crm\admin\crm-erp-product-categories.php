<?php
/**

 * Create a new table class that will extend the WP_List_Table

 */
class product_categories extends WP_List_Table {
    private $userRole;
    private $limitPerpage;
    function __construct() {
        global $status, $page;
        $this->limitPerpage = 15;
        parent::__construct(array(
            'singular' => 'bulk-delete', //singular name of the listed records
            'plural' => 'bulk-deletes', //plural name of the listed records
            'ajax' => false
                //does this table support ajax?
        ));
    }

    public function column_default($item, $column_name) {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        switch ($column_name) {
            case 'CategoryID':
                return $item->$column_name;
            case 'Name':
                return ucfirst($item->$column_name);
            case 'ParentCategory':
                return ucfirst($item->$column_name);
            case 'Description':
                return ucfirst($item->$column_name);
            case 'Action':
                return $item->$column_name;
            default:
                return print_r($item, true);
        }
    }
    function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" class="chkbox" name="deleteItem[]" value="'.$item->CategoryID.'"/>',
            $this->_args['singular'], //Let's simply repurpose the table's singular label ("plugin")
            /* $2%s */
            $item->CategoryID //The value of the checkbox should be the record's id
        );
    }
    function column_CategoryID($item){
        //Build row actions
        $actions = array(
            'edit'      => sprintf('<a href="?page=add_edit_product_category&action=edit&id='.$item->CategoryID.'">Edit</a>',$_REQUEST['page'],'edit',$item->CategoryID),
        );
        //Return the title contents
        return sprintf('%1$s %2$s',$item->CategoryID,$this->row_actions($actions));
    }
    function process_bulk_action() {
        if ('delete' === $this->current_action()) {
            $nonce = esc_attr($_REQUEST['_wpnonce']);
            if (isset($_REQUEST['action2']) && $_REQUEST['action2']=='delete'){
                
                if(!empty($_REQUEST['deleteItem'])){
                    $product_category_manager = new CRM_ERP_Category_Manager();
                    foreach($_REQUEST['deleteItem'] as $singleProductCategory){
                        $product_category_manager->delete_category($singleProductCategory);
                    }
                    wp_redirect('?page=product_categories');
                    exit;
                }
                
            }
        } 
    }
    /**
     * Prepare the items for the table to process
     *
     * @return Void
     */
    public function prepare_items() { 
        $this->process_bulk_action(); 
        ?>
        <form method="get" action="" id="search_all_form">
        <?php
        $this->search_box_new($_GET); 
        ?>
        <div class="wrap woocommerce">
            <div class="white_card card_height_100 mb_30">
                <div class="white_card_header">
                    <div class="box_header m-0 new_report_header">
                        <div class="title_img">
                            <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/product-categories-icon.png" class="page-title-img" alt="">
                            <h4>Product Categories</h4>
                        </div>
                        <div class="invoice_exports">
                            <a href="admin.php?page=add_edit_product_category&action=add" class="add-opp-custom-icon"><i class="fa-solid fa-plus"></i> New Product Category</a>
                            <a href="javascript:void(0);" class="status_btn_export export_invoice_excel export_product_categories">Export</a>
                        </div>
                    </div>
                </div>
                <div class="loader_box" id="loader_box" style="display: none;">
                    <div class="loading">
                        <p class="loading__text">Please Wait. Deleting Product Category.</p>
                        <div class="loading__bar"></div>
                    </div>
                </div>
                <div class="white_card_body custom-crm-erp-prdocuut-categories-report p-15" id="echeck_report_table_wrap" style="padding:7px;">
            <?php
            $columns = $this->get_columns();
            $hidden = $this->get_hidden_columns();
            $sortable = $this->get_sortable_columns();
            $this->_column_headers = array(
                $columns,
                $hidden,
                $sortable
            );
            //$this->process_bulk_action();
            $search = $_GET;
            $data = $this->table_data($search);
            usort($data, array($this, 'sort_data'));
            $perPage = $this->limitPerpage;
            $currentPage = $this->get_pagenum();
            $totalItems = count($data);
            $this->set_pagination_args(array(
                'total_items' => $totalItems,
                'per_page' => $perPage
            ));
            $data = array_slice($data, (($currentPage - 1) * $perPage), $perPage);
            $this->items = $data;
            $this->display();
            
            ?>
                </div>
            </div>
            </div>
        </form>
            <script type="text/javascript">
                $(document).ready(function() {
                    // Handler for the change event of #agreeCheckbox
                    $(document).on('change', '#agreeCheckbox', function() {
                        // Check if #agreeCheckbox is checked
                        if ($(this).prop('checked')) {
                            $(".swal-button--confirm").css("pointer-events", "auto");
                        } else {
                            $(".swal-button--confirm").css("pointer-events", "none");
                        }
                    });

                    $(document).on('click', '.swal-button-container:has(.swal-button--confirm)', function() {
                        if ($('#agreeCheckbox').prop('checked')) {
                            return true;
                        } else {
                            // Check if error message already exists
                            if (!$('.swal-content + p.error-message').length) {
                                // Append the error message if it doesn't exist
                                $('.swal-content').after('<p class="error-message" style="color: red; margin: 20px;">Please agree to delete the category!</p>');
                            }
                        }
                    });
                });

                jQuery(document).on('click','.delete_category',function(){
                    swal({
                        title: "Are you sure?",
                        text: "Yes, I want to delete this category. On deleting category, product will unlink respective to this category.",
                        icon: "warning",
                        buttons: {
                            cancel: "Cancel",
                            confirm: "Delete",
                        },
                        content: {
                            element: "input",
                            attributes: {
                                type: "checkbox",
                                id: "agreeCheckbox"
                            },
                        },
                        dangerMode: true,
                    })
                    .then((willDelete) => {
                        if (willDelete && $('#agreeCheckbox').prop('checked')) {
                            $(this).attr('disabled',true);
                            $("#loader_box").show();
                            var category_id = jQuery(this).data('category_id');
                            jQuery.ajax({
                                url:'<?php echo admin_url('admin-ajax.php'); ?>',
                                method:'post',
                                data:{action: 'delete_category',category_id:category_id},
                                success(response){
                                    location.reload(true);
                                }
                            });
                        }
                        $('.swal-modal').removeClass('crm-erp-products-categories-type-delete-swal');
                    }); 
                    $('.swal-modal').addClass('crm-erp-products-categories-type-delete-swal');
                });   

                jQuery(document).on("click", ".export_product_categories",function(){
                    jQuery(this).text('Please wait..');
                    jQuery(this).css('pointer-events','none');
                    var category_name = jQuery("#category_name").val();
                    var parent_category = jQuery("#parent_category").val();
                    jQuery.ajax({
                        url:'<?php echo admin_url('admin-ajax.php'); ?>',
                        method:'post',
                        data:{action: 'export_product_categories',category_name: category_name,parent_category: parent_category},
                        success(response){
                            jQuery(".export_product_categories").text('Export');
                            jQuery(".export_product_categories").css('pointer-events','');
                            var downloadLink = document.createElement("a");
                            var responseData = jQuery.trim(response);
                            var fileData = ['\ufeff'+responseData];
                            var blobObject = new Blob(fileData,{
                            type: "text/csv;charset=utf-8;"
                            });
                            var url = URL.createObjectURL(blobObject);
                            downloadLink.href = url;
                            var currentDate = new Date();
                            var dateString = currentDate.toISOString().slice(0, 10); // YYYY-MM-DD
                            var timeString = currentDate
                                .toLocaleTimeString("en-US", {
                                    hour12: false,
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    second: "2-digit",
                                })
                                .replace(/:/g, "-");
                            var filename = "Product_Categories_" + dateString + "_" + timeString + ".csv";
                            downloadLink.download = filename;
                            document.body.appendChild(downloadLink);
                            downloadLink.click();
                            document.body.removeChild(downloadLink);
                        }
                    })
                })
            </script>
            
    <?php
    }

    public function get_columns() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        $columns = array(
            'cb' => '<input type="checkbox" />',
            'CategoryID' => 'Product Category ID',
            'Name' => 'Product Category',
            'ParentCategory' => 'Parent Category',
            'Description' => 'Description',
            'Action' => 'Action'
        );
        //$columns['action'] = 'Action';
        return $columns;
    }
    public function get_sortable_columns() {
        $sortable = array(
            'CategoryID'     => array('CategoryID',true),
            'Name'     => array('Name',true),
            'ParentCategory'     => array('ParentCategory',true),
            'Description'     => array('Description',true)
        );
        return $sortable;
    }
    function get_bulk_actions() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
          if(in_array('administrator', $user_roles) || in_array('echeck_staff', $user_roles) || in_array('echeck_client', $user_roles) || in_array('master_sales', $user_roles)){ 
            return array(
                'delete' => __( 'Delete', ''),
            );
          }else{
            return array();
          }      
    }

    /**
     * Define which columns are hidden
     *
     * @return Array
     */

    public function get_hidden_columns() {
        return array();
    }


    /**

     * Define the sortable columns

     *

     * @return Array

     */
    public function get_display_name($user_id) {
        if (!$user = get_userdata($user_id))
            return false;
        return $user->data->display_name;
    }

    /**

     * Get the table data

     *

     * @return Array

     */

    private function table_data($search) {
        global $wpdb;
        if( (isset($search['category_name']) && $search['category_name'] != '') OR (isset($search['parent_category']) && $search['parent_category'] != '')){
            $product_manager = new CRM_ERP_Category_Manager();
            $product_categories_data = $product_manager->search_categories($search);
        }else{
            $product_manager = new CRM_ERP_Category_Manager();
            $product_categories_data = $product_manager->get_product_categories();
        }
        
        if(!empty($product_categories_data)){
            foreach($product_categories_data as $key => $product_category_data){
                $product_category_data->Action = '<button data-category_id ="'.$product_category_data->CategoryID.'"  class="delete_category" type="button" title="Remove"> <i class="fa-solid fa-trash"></i></button>';
            }
        }
        return $product_categories_data;
    }
    /**

     * Allows you to sort the data by the variables set in the $_GET

     *

     * @return Mixed

     */
    
    private function sort_data($a, $b) {
        // Set defaults
        $orderby = isset($_GET['orderby']) ? sanitize_text_field($_GET['orderby']) : 'CategoryID';
        $order = isset($_GET['order']) ? sanitize_text_field($_GET['order']) : 'desc';
        
        $result = strnatcmp($a->$orderby, $b->$orderby);
    
        return ($order === 'asc') ? $result : -$result;
    }

    protected function pagination( $which ) {
        if ( empty( $this->_pagination_args ) ) {
            return;
        }
        $total_items     = $this->_pagination_args['total_items'];
        $total_pages     = $this->_pagination_args['total_pages'];
        $infinite_scroll = false;
        if ( isset( $this->_pagination_args['infinite_scroll'] ) ) {
            $infinite_scroll = $this->_pagination_args['infinite_scroll'];
        }
        if ( 'top' === $which && $total_pages > 1 ) {
            $this->screen->render_screen_reader_content( 'heading_pagination' );
        }
        $output = '<span class="displaying-num">' . sprintf(
            /* translators: %s: Number of items. */
            _n( '%s item', '%s items', $total_items ),
            number_format_i18n( $total_items )
        ) . '</span>';
        $current              = $this->get_pagenum();
        $removable_query_args = wp_removable_query_args();
        $current_url = set_url_scheme( 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] );
        $current_url = remove_query_arg( $removable_query_args, $current_url );
        $page_links = array();
        $total_pages_before = '<span class="paging-input">';
        $total_pages_after  = '</span></span>';
        $disable_first = false;
        $disable_last  = false;
        $disable_prev  = false;
        $disable_next  = false;
        if ( 1 == $current ) {
            $disable_first = true;
            $disable_prev  = true;
        }
        if ( 2 == $current ) {
            $disable_first = true;
        }
        if ( $total_pages == $current ) {
            $disable_last = true;
            $disable_next = true;
        }
        if ( $total_pages - 1 == $current ) {
            $disable_last = true;
        }
        if ( $disable_first ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&laquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='first-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( remove_query_arg( 'paged', $current_url ) ),
                __( 'First page' ),
                '&laquo;'
            );
        }
        if ( $disable_prev ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&lsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='prev-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( 'paged', max( 1, $current - 1 ), $current_url ) ),
                __( 'Previous page' ),
                '&lsaquo;'
            );
        }
        if ( 'bottom' === $which ) {
            $html_current_page  = $current;
            $total_pages_before = '<span class="screen-reader-text">' . __( 'Current Page' ) . '</span><span id="table-paging" class="paging-input"><span class="tablenav-paging-text">';
        } else {
            $html_current_page = sprintf(
                "%s<input class='current-page' id='current-page-selector' type='text' name='paged' value='%s' size='%d' aria-describedby='table-paging' /><span class='tablenav-paging-text'>",
                '<label for="current-page-selector" class="screen-reader-text">' . __( 'Current Page' ) . '</label>',
                $current,
                strlen( $total_pages )
            );
        }
        $html_total_pages = sprintf( "<span class='total-pages'>%s</span>", number_format_i18n( $total_pages ) );
        $page_links[]     = $total_pages_before . sprintf(
            _x( '%1$s of %2$s', 'paging' ),
            $html_current_page,
            $html_total_pages
        ) . $total_pages_after;
        if ( $disable_next ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&rsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='next-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=> min( $total_pages, $current + 1 )), $current_url ) ),
                __( 'Next page' ),
                '&rsaquo;'
            );
        }
        if ( $disable_last ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&raquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='last-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=>$total_pages), $current_url ) ),
                __( 'Last page' ),
                '&raquo;'
            );
        }
        $pagination_links_class = 'pagination-links';
        if ( ! empty( $infinite_scroll ) ) {
            $pagination_links_class .= ' hide-if-js';
        }
        $output .= "\n<span class='$pagination_links_class'>" . implode( "\n", $page_links ) . '</span>';
        if ( $total_pages ) {
            $page_class = $total_pages < 2 ? ' one-page' : '';
        } else {
            $page_class = ' no-pages';
        }
        $this->_pagination = "<div class='tablenav-pages{$page_class}'>$output</div>";
        echo $this->_pagination;
    }
    public function search_box_new($search_data)
    {
    ?>
        
            <input type="hidden" name="page" value="product_categories">
            <div id="overlay" onclick="overlay_off()"></div>
            <div class="row align-items-center mb-3">
                <div class="col-md-3 search_field">
                    <img src="https://design.echeck.money/portal/wp-content/plugins/echeck-theme111/assets/img/icon/Search.svg" alt="" class="icon">
                    <input type="text" placeholder="Search.." id="show_lead_search" autocomplete="off" class="open">
                    <input type="submit" style="visibility: hidden;position:absolute;">
                </div>
            </div>
            <div class="popup-overlay">
                <div class="popup-content">
                    <div class="search_header">
                        <h4>Search</h4>
                        <span class="close">
                            <i class="fa-solid fa-xmark"></i>
                        </span>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <input type="text" id="category_name" name="category_name" placeholder="Category Name" value="<?php if (isset($search_data['category_name'])) {echo $search_data['category_name'];} ?>" class="search-popup-input-select">
                        </div>
                        <div class="col-md-6">
                            <input type="text" id="parent_category" name="parent_category" placeholder="Parent Category" value="<?php if (isset($search_data['parent_category'])) {echo $search_data['parent_category'];} ?>" class="search-popup-input-select">
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12 text-center">
                        <input type="submit" id="btn_search_submit" class="search-popup-submit-btn" value="Search" />
                        <input type="button" class="search-popup-reset-btn" value="Clear" id="reset_Form_button" onclick="resetAffForm()"/>
                        </div>
                    </div>  
                </div>
            </div>
        <script type="text/javascript">
            jQuery(".open").on("click", function() {
              jQuery('.status_box').hide(); 
              jQuery(".popup-overlay, .popup-content").addClass("active");
              jQuery(".search_lead_id").focus();
              jQuery('#overlay').show();
            });

            jQuery(".close").on("click", function() {
              jQuery(".popup-overlay, .popup-content").removeClass("active");
              jQuery('#overlay').hide();
            });

            function overlay_off(){
              jQuery(".close").trigger("click");
              jQuery('#overlay').hide();
            }
            function resetAffForm(){
                jQuery("#reset_Form_button").val('Clearing...');
                var site_url = '<?php  echo get_site_url() ?>';
                window.location.href = site_url+'/wp-admin/admin.php?page=product_categories';
            }
            jQuery(document).on("click",'#doaction2', function(e) {
                e.preventDefault();
                var is_check = 0;
                $(".chkbox").each(function(){
                    if($(this).is(":checked")){
                        is_check = 1;
                    }
                })
                if($('#bulk-action-selector-bottom').val()=='delete'){
                    if(is_check == 1){
                        swal({
                            title: "Are you sure?",
                            text: "Yes, I want to delete this category. On deleting category, product will unlink respective to this category.",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                                confirm: "Delete",
                            },
                            content: {
                                element: "input",
                                attributes: {
                                    type: "checkbox",
                                    id: "agreeCheckbox"
                                },
                            },
                            dangerMode: true,
                        })
                        .then((willDelete) => {
                            if (willDelete && $('#agreeCheckbox').prop('checked')) {
                                $(this).attr('disabled',true);
                                $("#loader_box").show();
                                $('#search_all_form').submit();
                            }
                            $('.swal-modal').removeClass('crm-erp-products-categories-type-delete-swal');
                        });
                        $('.swal-modal').addClass('crm-erp-products-categories-type-delete-swal');
                    }else{
                        swal({
                            title: "Please select atleast one checkbox",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                    }
                }else{ 
                    swal({
                            title: "Please select some action.",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                }
            });
        </script>
    <?php
    }
}
?>