<?php 
/**
 * Create a new table class that will extend the WP_List_Table
 */
class CRM_ERP_Milestone_Stages extends WP_List_Table {
    private $userRole;
    private $limitPerpage;
    public $mid;
    function __construct() {
        global $page;
        $this->limitPerpage = 15;
        $this->mid = $_GET['mid'];
        parent::__construct(array(
            'singular' => 'bulk-delete', //singular name of the listed records
            'plural' => 'bulk-deletes', //plural name of the listed records
            'ajax' => false
                //does this table support ajax?
        ));

        $this->list_table_remove_referer();
    }

    public function list_table_remove_referer() {
        // If we're on an admin page with the referer passed in the QS, prevent it nesting and becoming too long.
        global $pagenow;

        if( 'admin.php' === $pagenow && isset( $_GET['_wp_http_referer'] ) && preg_match( '/_wp_http_referer/', $_GET['_wp_http_referer'] ) ) :
            wp_redirect( remove_query_arg( array( '_wp_http_referer', '_wpnonce' ), wp_unslash( $_SERVER['REQUEST_URI'] ) ) );
            exit;
        endif;
    }

    public function column_default($item, $column_name) {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        switch ($column_name) {
            case 'milestone_stage_id':
            case 'stage_name':
            case 'status':
            case 'probability':
            case 'created_at':
                return $item->$column_name;
            default:
                return print_r($item, true);
        }
    }
    function column_cb($item)
    {
        return sprintf(
            '<input type="checkbox" class="chkbox" name="deleteItem[]" value="%d"/>',
            
            $item->milestone_stage_id //The value of the checkbox should be the record's id
        );
    }

    function column_stage_name($item)
    {
        // Decode HTML entities and remove backslashes
        $stage_name = htmlspecialchars_decode(stripslashes($item->stage_name));
    
        // Encode milestone name for HTML attribute
        $encoded_stage_name = htmlspecialchars($stage_name, ENT_QUOTES);
        
        if($item->definition) {
            $tooltip = '<div class="tabletiptool">
                            <i class="fa-solid fa-circle-exclamation"></i>
                            <span class="tabletiptooltext">' . $item->definition . '</span>
                        </div>';
        } else {
            $tooltip = '';
        }
        return $encoded_stage_name . $tooltip;
    }

    function column_created_at($item)
    {
        return date('m/d/Y H:i:s', strtotime($item->created_at));
    }

    function column_milestone_stage_id($item){

        // Decode HTML entities and remove backslashes
        $stage_name = htmlspecialchars_decode(stripslashes($item->stage_name));
    
        // Encode milestone name for HTML attribute
        $encoded_stage_name = htmlspecialchars($stage_name, ENT_QUOTES);

        //Build row actions
        $actions = array(
            'edit'      => '<a href="?page=add_edit_product_status&action=edit&id='.$item->milestone_stage_id.'&data-stage_name='.$encoded_stage_name.'&data-status='.$item->status.'&data-probability='.$item->probability.'&data-definition='.$item->definition.'">Edit</a>',
            'delete'    => '<a data-milestone_stage_id ="' . $item->milestone_stage_id . '" class="delete_milestone_stage" href="javascript:void(0);">Delete</a>',
        );
        
        //Return the title contents
        return sprintf('%1$s %2$s',$item->milestone_stage_span,$this->row_actions($actions));
    }

    function process_bulk_action() {
        if ('delete' === $this->current_action()) {
            $nonce = esc_attr($_REQUEST['_wpnonce']);
            if (isset($_REQUEST['action2']) && $_REQUEST['action2']=='delete'){
                
                if(!empty($_REQUEST['deleteItem'])){
                    $milestone_stage_manager = new CRM_ERP_Milestone_Stage_Manager();
                    foreach($_REQUEST['deleteItem'] as $singleMilestoneStage){
                        $milestone_stage_manager->delete_milestone_stage($singleMilestoneStage);
                    }
                    wp_redirect('?page=crm_erp_milestone_stages&mid=' . $this->mid);
                    exit;
                }
                
            }
        } 
    }
    /**
     * Prepare the items for the table to process
     *
     * @return Void
     */
    public function prepare_items() { 
        $this->process_bulk_action(); 
        
        if (!$this->mid || empty($this->mid)) {
            // Handle the case where $mid is not set or is empty
            echo '<h4 style="text-align: center;">No Milestone ID Found</h4>';
            return;
        } else {
            /*global $wpdb;

            $lead_stage_table = $wpdb->prefix.'lead_stage_probability';
            $milestone_stages_table = $wpdb->prefix.'milestone_stages';
            $product_table = $wpdb->prefix.'crm_products'; // Assuming this table has product names and IDs
            $milestone_table = $wpdb->prefix.'milestones'; // Assuming this table has milestone names and IDs
            $opportunity_product_table = $wpdb->prefix.'opportunity_products'; // Assuming this table links opportunities and products
            
            // Fetch data from eccom_lead_stage_probability
            $lead_stage_data = $wpdb->get_results("
                SELECT id, product, milestone, stage, probability
                FROM $lead_stage_table
            ");
            
            // Loop through each row of lead stage data
            foreach ($lead_stage_data as $row) {
                $product = $row->product;
                $milestone = $row->milestone;
                $stage = $row->stage;
                $probability = $row->probability;
            
                // Fetch milestone_id and check if there's a matching product
                $milestone_id = $wpdb->get_var($wpdb->prepare("
                    SELECT mile.milestone_id
                    FROM $opportunity_product_table oppPro
                    LEFT JOIN $product_table prod ON oppPro.product_id = prod.ProductID
                    LEFT JOIN $milestone_table mile ON oppPro.milestone_id = mile.milestone_id
                    WHERE prod.Title = %s
                      AND mile.milestone_name = %s
                      AND EXISTS (
                          SELECT 1
                          FROM $milestone_stages_table ms
                          WHERE ms.milestone_id = mile.milestone_id
                            AND ms.stage_name = %s
                      )
                ", $product, $milestone, $stage));
            
                // Update the probability in eccom_milestone_stages if a matching product and milestone_id is found
                if ($milestone_id) {
                    $result = $wpdb->update(
                        $milestone_stages_table,
                        array('probability' => $probability),
                        array('milestone_id' => $milestone_id, 'stage_name' => $stage)
                    );

                    // Check if the update was successful
                    if ($result !== false) {
                        echo "<br>Update successful for Product: $product, Milestone: $milestone, Stage: $stage with Probability: $probability\n";
                    } else {
                        echo "<br>Update failed for Product: $product, Milestone: $milestone, Stage: $stage\n";
                    }

                }
            }*/
            
            $milestone_manager = new CRM_ERP_Milestone_Manager();
            $fetched_milestone = $milestone_manager->get_milestone($this->mid);
            if ($fetched_milestone) {
                $heading = 'Stages - ' . ucwords( htmlspecialchars_decode(stripslashes( $fetched_milestone->milestone_name ) ) );
                // Unserialize the map
                $mapArray = unserialize($fetched_milestone->map);

                if (is_array($mapArray) && in_array('opportunity', $mapArray)) {
                    $is_opportunity_mapped = true;
                } else {
                    $is_opportunity_mapped = false;
                }
            } else {
                // Handle the case where $mid is invalid
                echo '<h4 style="text-align: center;">Invalid Milestone ID Passed</h4>';
                return;
            }

        }
        ?> 
        <form method="get" action="" id="search_all_form">
            <?php $this->search_box_new($_GET, $is_opportunity_mapped); ?>
            <div class="wrap woocommerce">
                <div class="white_card card_height_100 mb_30">
                    <div class="white_card_header">
                        <div class="box_header m-0 new_report_header">
                            <div class="title_img">
                                <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/milestone-stage-icon.png" class="page-title-img" alt="">
                                <h4><?php echo $heading; ?></h4>
                            </div>
                            <div class="invoice_exports">
                                <a class="add-opp-custom-icon" id="add-milestone" class=""><i class="fa-solid fa-plus"></i> New Stage</a>
                                <a href="javascript:void(0);" class="status_btn_export export_invoice_excel export_milestone">Export</a>
                            </div>
                        </div>
                    </div>
                    <div class="loader_box" id="loader_box" style="display: none;">
                        <div class="loading">
                            <p class="loading__text">Please Wait. Deleting Stage.</p>
                            <div class="loading__bar"></div>
                        </div>
                    </div>
                    <div class="white_card_body custom-crm-erp-milestone-stage-report p-15" id="echeck_report_table_wrap">
                        <?php
                        $columns = $this->get_columns();
                        $hidden = $this->get_hidden_columns();
                        $sortable = $this->get_sortable_columns();
                        $this->_column_headers = array(
                            $columns,
                            $hidden,
                            $sortable
                        );
                        //$this->process_bulk_action();
                        $search = $_GET;
                        $data = $this->table_data($search);
                        usort($data, array($this, 'sort_data'));
                        $perPage = $this->limitPerpage;
                        $currentPage = $this->get_pagenum();
                        $totalItems = count($data);
                        $this->set_pagination_args(array(
                            'total_items' => $totalItems,
                            'per_page' => $perPage
                        ));
                        $data = array_slice($data, (($currentPage - 1) * $perPage), $perPage);
                        $this->items = $data;
                        $this->display();
                        
                        ?>
                    </div>
                </div>
            </div>
        </form>
        
            <script type="text/javascript">
                $(document).ready(function() {

                    // Loop through all elements with the class .stage_name.column-stage_name
                    /*$('.stage_name.column-stage_name').each(function() {
                        // Get the definition data from the closest .milestone_stage_id-col element
                        var definition = $(this).closest('tr').find('.milestone_stage_id-col').data('definition');
                        console.log(definition);
                        
                        // Set the tooltip (title attribute) for each stage name element
                        $(this).attr('title', definition);
                    });*/

                    $('#search-created_at').datetimepicker({
                        format: 'm/d/Y',
                        autoclose: true,
                        orientation: 'bottom',
                        timepicker: false,
                        autocomplete: 'off',
                        maxDate: 'now'
                    });

                    // Handler for the change event of #agreeCheckbox
                    $(document).on('change', '#agreeCheckbox', function() {
                        // Check if #agreeCheckbox is checked
                        if ($(this).prop('checked')) {
                            $(".swal-button--confirm").css("pointer-events", "auto");
                        } else {
                            $(".swal-button--confirm").css("pointer-events", "none");
                        }
                    });

                    $(document).on('click', '.swal-button-container:has(.swal-button--confirm)', function() {
                        if ($('#agreeCheckbox').prop('checked')) {
                            return true;
                        } else {
                            // Check if error message already exists
                            if (!$('.swal-content + p.error-message').length) {
                                // Append the error message if it doesn't exist
                                $('.swal-content').after('<p class="error-message" style="color: red; margin: 25px;">Please agree to delete the Stage!</p>');
                            }
                        }
                    });
                });

                $(document).on("click", ".export_milestone",function(){
                    $(this).text('Please wait..');
                    $(this).css('pointer-events','none');
                    var search_id = $("#search-id").val();
                    var search_name = $("#search-name").val();
                    var search_status = $("#search-status").val();
                    var search_date = $("#search-created_at").val();
                    var order_by = getParameterByName('orderby');
                    var order = getParameterByName('order');
                    var midValue = $('input[name="mid"]').val();
                    $.ajax({
                        url:'<?php echo admin_url('admin-ajax.php'); ?>',
                        method:'post',
                        data:{action: 'export_milestone_stage', 
                            search_id: search_id, 
                            search_name: search_name, 
                            search_status: search_status, 
                            search_date: search_date, 
                            order_by: order_by, 
                            order: order, 
                            mid: midValue},
                        success(response){
                            $(".export_milestone").text('Export');
                            $(".export_milestone").css('pointer-events','');
                            var downloadLink = document.createElement("a");
                            var responseData = $.trim(response);
                            var fileData = ['\ufeff'+responseData];
                            var blobObject = new Blob(fileData,{
                            type: "text/csv;charset=utf-8;"
                            });
                            var url = URL.createObjectURL(blobObject);
                            downloadLink.href = url;
                            var currentDate = new Date();
                            //var dateString = currentDate.toISOString().slice(0, 10); // YYYY-MM-DD
                            
                            // Extract month, day, and year
                            var month = String(currentDate.getMonth() + 1).padStart(2, '0'); // Month is zero-based
                                var day = String(currentDate.getDate()).padStart(2, '0');
                                var year = currentDate.getFullYear();

                                // Combine into MM/DD/YYYY format
                                var dateString = month + '/' + day + '/' + year;

                            var timeString = currentDate
                                .toLocaleTimeString("en-US", {
                                    hour12: false,
                                    hour: "2-digit",
                                    minute: "2-digit",
                                    second: "2-digit",
                                })
                                .replace(/:/g, "_");
                            var filename = "Milestone_Stages_" + dateString + "_" + timeString + ".csv";
                            downloadLink.download = filename;
                            document.body.appendChild(downloadLink);
                            downloadLink.click();
                            document.body.removeChild(downloadLink);
                        }
                    })
                })  
            </script>

    <?php
    }

    public function get_columns() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
        $columns = array(
            'cb' => '<input type="checkbox" />',
            'milestone_stage_id' => 'Stage ID',
            'stage_name' =>'Stage Name',
            'probability' =>'Probability %',
            'status' => 'Status',
            'created_at' => 'Created Date'
        );
        return $columns;
    }
    public function get_sortable_columns() {
        $sortable = array(
            'milestone_stage_id'     => array('milestone_stage_id',true),
            'stage_name' => array('stage_name',true),
            'probability' => array('probability',true),
            'status' => array('status',true),
            'created_at' => array('created_at',true));
        return $sortable;
    }

    public function column_status($item) {
        return ucwords( $item->status );
    }

    function get_bulk_actions() {
        $current_user_id = get_current_user_id();
        $user_data = get_user_by('id',$current_user_id);
        $user_roles = $user_data->roles;
          if(in_array('administrator', $user_roles) || in_array('echeck_staff', $user_roles) || in_array('echeck_client', $user_roles) || in_array('master_sales', $user_roles)){ 
            return array(
                'delete' => __( 'Delete', ''),
            );
          }else{
            return array();
          }      
    }

    /**
     * Define which columns are hidden
     *
     * @return Array
     */

    public function get_hidden_columns() {
        return array();
    }

    /**

     * Define the sortable columns

     *

     * @return Array

     */
    public function get_display_name($user_id) {
        if (!$user = get_userdata($user_id))
            return false;
        return $user->data->display_name;
    }

    /**
     * Get the table data
     *
     * @return Array
     */
    private function table_data($search) {
        global $wpdb;
        if( (isset($search['search_id']) && $search['search_id'] != '') || (isset($search['search_name']) && $search['search_name'] != '') || (isset($search['search_status']) && $search['search_status'] != '') || (isset($search['search_created_at']) && $search['search_created_at'] != '')){
            $milestone_stage_manager = new CRM_ERP_Milestone_Stage_Manager();
            $milestone_stage_data = $milestone_stage_manager->search_milestone_stage($search);
        }else{
            $milestone_stage_manager = new CRM_ERP_Milestone_Stage_Manager();
            $milestone_stage_data = $milestone_stage_manager->get_milestone_stage_list($this->mid);
        }
        if(!empty($milestone_stage_data)){
            foreach($milestone_stage_data as $key => $milestone_stage_datas){
                                
                $milestone_stage_datas->milestone_stage_span = '<span class="milestone_stage_id-col" data-milestone_stage_id='.$milestone_stage_datas->milestone_stage_id.' data-stage_name="'.$milestone_stage_datas->stage_name.'" data-status="'.$milestone_stage_datas->status.'" data-probability="'.$milestone_stage_datas->probability.'" data-definition="'.$milestone_stage_datas->definition.'">'.$milestone_stage_datas->milestone_stage_id.'</span>';
                $milestone_stage_datas->stage_name = ucwords($milestone_stage_datas->stage_name);
            }
        }
        return $milestone_stage_data;
    }

    /**
     * Allows you to sort the data by the variables set in the $_GET
     *
     * @return Mixed
     */
     private function sort_data($a, $b) {
        // Set defaults
        $orderby = isset($_GET['orderby']) ? sanitize_text_field($_GET['orderby']) : 'milestone_stage_id';
        $order = isset($_GET['order']) ? sanitize_text_field($_GET['order']) : 'desc';
        
        $result = strnatcmp($a->$orderby, $b->$orderby);
    
        return ($order === 'asc') ? $result : -$result;
    }    

    protected function pagination( $which ) {
        if ( empty( $this->_pagination_args ) ) {
            return;
        }
        $total_items     = $this->_pagination_args['total_items'];
        $total_pages     = $this->_pagination_args['total_pages'];
        $infinite_scroll = false;
        if ( isset( $this->_pagination_args['infinite_scroll'] ) ) {
            $infinite_scroll = $this->_pagination_args['infinite_scroll'];
        }
        if ( 'top' === $which && $total_pages > 1 ) {
            $this->screen->render_screen_reader_content( 'heading_pagination' );
        }
        $output = '<span class="displaying-num">' . sprintf(
            /* translators: %s: Number of items. */
            _n( '%s item', '%s items', $total_items ),
            number_format_i18n( $total_items )
        ) . '</span>';
        $current              = $this->get_pagenum();
        $removable_query_args = wp_removable_query_args();
        $current_url = set_url_scheme( 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'] );
        $current_url = remove_query_arg( $removable_query_args, $current_url );
        $page_links = array();
        $total_pages_before = '<span class="paging-input">';
        $total_pages_after  = '</span></span>';
        $disable_first = false;
        $disable_last  = false;
        $disable_prev  = false;
        $disable_next  = false;
        if ( 1 == $current ) {
            $disable_first = true;
            $disable_prev  = true;
        }
        if ( 2 == $current ) {
            $disable_first = true;
        }
        if ( $total_pages == $current ) {
            $disable_last = true;
            $disable_next = true;
        }
        if ( $total_pages - 1 == $current ) {
            $disable_last = true;
        }
        if ( $disable_first ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&laquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='first-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( remove_query_arg( 'paged', $current_url ) ),
                __( 'First page' ),
                '&laquo;'
            );
        }
        if ( $disable_prev ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&lsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='prev-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( 'paged', max( 1, $current - 1 ), $current_url ) ),
                __( 'Previous page' ),
                '&lsaquo;'
            );
        }
        if ( 'bottom' === $which ) {
            $html_current_page  = $current;
            $total_pages_before = '<span class="screen-reader-text">' . __( 'Current Page' ) . '</span><span id="table-paging" class="paging-input"><span class="tablenav-paging-text">';
        } else {
            $html_current_page = sprintf(
                "%s<input class='current-page' id='current-page-selector' type='text' name='paged' value='%s' size='%d' aria-describedby='table-paging' /><span class='tablenav-paging-text'>",
                '<label for="current-page-selector" class="screen-reader-text">' . __( 'Current Page' ) . '</label>',
                $current,
                strlen( $total_pages )
            );
        }
        $html_total_pages = sprintf( "<span class='total-pages'>%s</span>", number_format_i18n( $total_pages ) );
        $page_links[]     = $total_pages_before . sprintf(
            _x( '%1$s of %2$s', 'paging' ),
            $html_current_page,
            $html_total_pages
        ) . $total_pages_after;
        if ( $disable_next ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&rsaquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='next-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=> min( $total_pages, $current + 1 )), $current_url ) ),
                __( 'Next page' ),
                '&rsaquo;'
            );
        }
        if ( $disable_last ) {
            $page_links[] = '<span class="tablenav-pages-navspan button disabled" aria-hidden="true">&raquo;</span>';
        } else {
            $page_links[] = sprintf(
                "<a class='last-page button' href='%s'><span class='screen-reader-text'>%s</span><span aria-hidden='true'>%s</span></a>",
                esc_url( add_query_arg( array('paged'=>$total_pages), $current_url ) ),
                __( 'Last page' ),
                '&raquo;'
            );
        }
        $pagination_links_class = 'pagination-links';
        if ( ! empty( $infinite_scroll ) ) {
            $pagination_links_class .= ' hide-if-js';
        }
        $output .= "\n<span class='$pagination_links_class'>" . implode( "\n", $page_links ) . '</span>';
        if ( $total_pages ) {
            $page_class = $total_pages < 2 ? ' one-page' : '';
        } else {
            $page_class = ' no-pages';
        }
        $this->_pagination = "<div class='tablenav-pages{$page_class}'>$output</div>";
        echo $this->_pagination;
    }

    public function search_box_new($search_data, $is_opportunity_mapped)
    {
        /*global $wpdb;
        
        // Table name
        $table_name = $wpdb->prefix . 'lead_stage_probability';
        
        // Data to be inserted
        $data = array(
            'product' => 'Partnership,STC,Tax Amendment,ERC,Audit Advisory,RDC',
            'milestone' => 'Test RDC Prospecting',
            'stage' => 'Opportunity Identified',
            'probability' => 10
        );
        
        // Format of the data to be inserted
        $format = array(
            '%s',  // product (string)
            '%s',  // milestone (string)
            '%s',  // stage (string)
            '%d'   // probability (integer)
        );
        
        // Insert data into the table
        $inserted = $wpdb->insert($table_name, $data, $format);
        
        // Check if the insert was successful
        if ($inserted !== false) {
            echo 'Insert successful!';
        } else {
            echo 'Insert failed: ' . $wpdb->last_error;
        }*/

        /*global $wpdb;

        // Table name
        $table_name = $wpdb->prefix . 'milestone_stages';
        
        // SQL query to add two new columns
        $sql = "ALTER TABLE $table_name 
        ADD COLUMN probability VARCHAR(255) AFTER status,
        ADD COLUMN definition VARCHAR(255) AFTER probability;";
        
        // Execute the query
        $result = $wpdb->query($sql);
        
        // Check if the query was successful
        if ($result !== false) {
            echo 'Columns added successfully!';
        } else {
            echo 'Failed to add columns: ' . $wpdb->last_error;
        }*/

        ?>

        
        <div id="overlay" onclick="overlay_off()"></div>

            <input type="hidden" name="page" value="crm_erp_milestone_stages">
            <input type="hidden" name="mid" value="<?php echo $this->mid; ?>">
            <div class="row align-items-center mb-3">
                <div class="col-md-3 search_field">
                    <img src="https://design.echeck.money/portal/wp-content/plugins/echeck-theme111/assets/img/icon/Search.svg" alt="" class="icon">
                    <input type="text" placeholder="Search.." id="show_lead_search" autocomplete="off" class="open">
                    <input type="submit" style="visibility: hidden;position:absolute;">
                </div>
            </div>
            <div class="popup-overlay">
                <!--<form class="popup-content" id="popup-content" method="get" >-->
                    <div class="search_header">
                        <h4>Search</h4>
                        <span class="close">
                            <i class="fa-solid fa-xmark"></i>
                        </span>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <input type="number" id="search-id" name="search_id" placeholder="Stage ID" value="<?php if (isset($search_data['search_id'])) {echo $search_data['search_id'];} ?>" class="search-popup-input-select">
                        </div>
                        <div class="col-md-6">
                            <input type="text" id="search-name" name="search_name" autocomplete="off" placeholder="Stage Name" value="<?php if (isset($search_data['search_name'])) {echo $search_data['search_name'];} ?>" class="search-popup-input-select">
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <select id="search-status" name="search_status" class="form-control search-popup-input-select">
                                <option value="">Status</option>
                                <option value="active" <?php if (isset($search_data['search_status']) && $search_data['search_status'] === 'active') {echo 'selected';} ?>>Active</option>
                                <option value="inactive" <?php if (isset($search_data['search_status']) && $search_data['search_status'] === 'inactive') {echo 'selected';} ?>>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <input type="text" id="search-created_at" class="search-popup-input-select" value="<?php if (isset($search_data['search_created_at'])) {echo $search_data['search_created_at'];} ?>" name="search_created_at" pattern="\d{2}/\d{2}/\d{4}" placeholder="MM/DD/YYYY" autocomplete="off" onkeydown="return false">
                            <!--<input type="date" id="search-created_at" name="search_created_at" class="search-popup-input-select" pattern="\d{4}-\d{2}-\d{2}" placeholder="Created Date" autocomplete="off" value="<?php if (isset($search_data['search_created_at'])) {echo $search_data['search_created_at'];} ?>">-->
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12 text-center">
                        <input type="submit" id="btn_search_submit" class="search-popup-submit-btn" value="Search" />
                        <input type="button" class="search-popup-reset-btn" value="Clear" id="reset_Form_button" onclick="resetAffForm()"/>
                        </div>
                    </div>  
                <!--</form>-->
            </div>

            <!-- ------- Milstone popup ----- -->

            <div class="mile-popup-overlay" style="left:17%;">
                <link href="https://cdn.rawgit.com/harvesthq/chosen/gh-pages/chosen.min.css" rel="stylesheet"/>
                <script src="https://cdn.rawgit.com/harvesthq/chosen/gh-pages/chosen.jquery.min.js"></script>
                <div class="mile-popup-content text-left" id="add-milestone-form">
                    <div class="mile-header">
                        <h4 id="mile-header-h4">New Stage</h4>
                        <span class="mile-close">
                            <i class="fa-solid fa-xmark"></i>
                        </span>
                    </div>  
                    <input type="hidden" id="popup-milestone_id" name="milestone_id" value="<?php echo $this->mid; ?>">
                    <input type="hidden" id="popup-milestone_stage_id" name="milestone_stage_id" value="">
                    <div class="row mt-2">
                        <div class="col-md-6">
                            <label>Name*:</label>
                            <input type="text" id="popup-stage_name" name="stage_name" autocomplete="off" maxlength="50"  placeholder="" class="search-popup-input-select">
                            <p class="add-mile-name-msg mile-error" style="color:red;"></p>
                        </div>
                        <div class="col-md-6">
                            <label>Status*:</label>
                            <select id="mile-status" name="mile_status" class="search-popup-input-select form-control">
                                <option value="">Select Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                            <p class="add-mile-status-msg mile-error" style="color:red;"></p>
                        </div>
                    </div>
                    
                    <?php if ( isset( $is_opportunity_mapped ) && ($is_opportunity_mapped == true) ) { ?>
                        <div class="row mt-2">
                            <div class="col-md-6">
                                <label>Probability %*:</label>
                                <!--<select name="probability" id="probability" class="search-popup-input-select form-control">
                                    <option value="">Select Probability</option> 
                                    <option value="25">25%</option>
                                    <option value="50">50%</option>
                                    <option value="75">75%</option>
                                    <option value="99">99%</option>     
                                </select>-->
                                <input id="probability" name="probability" class="search-popup-input-select form-control" type="number" min="0" max="100" step="0.01" oninput="if (this.value > 100) this.value = 100; const value = this.value.split('.'); if (value[1] && value[1].length > 2) this.value = value[0] + '.' + value[1].slice(0, 2);">
                                <p class="add-stage-prob-msg mile-error" style="color:red;"></p>
                            </div>
                            <div class="col-md-6">
                                <label>Definition:</label>
                                <input id="definition" name="definition" class="search-popup-input-select form-control" type="text" maxlength="100">
                                <p class="add-stage-definition-msg mile-error" style="color:red;"></p>
                            </div>
                            <div class="col-md-12">
                                <p class="update-stage-warn"><b>Note:</b> On updating, probability will update for all opportunities with this stage.</p>
                            </div>
                        </div>
                    <?php } ?>

                    <div class="row mt-3">
                        <div class="col-md-12 text-center">
                        <input type="button" id="btn_mile_submit" class="search-popup-submit-btn" value="Create" />
                        <input type="button" class="reset-miles" value="Cancel" id="cancel_mile_button" onclick="overlay_off()"/>
                        <p class="add-mile-msgs" style="color:red;"></p>
                        </div>
                    </div>  
                </div>
            <script>
                jQuery(".milestone_dropdown-class").chosen({
                        no_results_text: "Oops, nothing found!"
                    });
            </script>
            </div>
            <!-- ---------- -->
        <script type="text/javascript">
            jQuery(".open").on("click", function() {
              //jQuery('.status_box').hide(); 
              jQuery(".popup-overlay, .popup-content").addClass("active");
              jQuery(".search_lead_id").focus();
              jQuery('#overlay').show();
            });

            jQuery(".close").on("click", function() {
              jQuery(".popup-overlay, .popup-content").removeClass("active");
              jQuery('#overlay').hide();
            });

            function overlay_off(){
                $(".close, .mile-close").trigger("click");
            }
            function resetAffForm(){
                jQuery("#reset_Form_button").val('Clearing...');
                var site_url = '<?php  echo get_site_url() ?>';
                // Get the value of the input field with name "mid"
                var midValue = $('input[name="mid"]').val();
                window.location.href = site_url+'/wp-admin/admin.php?page=crm_erp_milestone_stages&mid=' + midValue;
            }
            jQuery(document).on("click",'#doaction2', function(e) {
                e.preventDefault();
                var is_check = 0;
                $(".chkbox").each(function(){
                    if($(this).is(":checked")){
                        is_check = 1;
                    }
                })
                if($('#bulk-action-selector-bottom').val()=='delete'){
                    if(is_check == 1){
                        swal({
                            title: "Are you sure?",
                            text: "Yes, I want to delete selected Stages. Opportunities will no longer be linked to the Stages.",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                                confirm: "Delete",
                            },
                            content: {
                                element: "input",
                                attributes: {
                                    type: "checkbox",
                                    id: "agreeCheckbox"
                                },
                            },
                            dangerMode: true,
                        })
                        .then((willDelete) => {
                            if (willDelete && $('#agreeCheckbox').prop('checked')) {
                                $(this).html('wait..');
                                $(this).attr('disabled',true);
                                $("#loader_box").show();
                                $('#search_all_form').submit();
                            }
                            $('.swal-modal').removeClass('crm-erp-milestones-stage-delete-swal');
                        });
                        $('.swal-modal').addClass('crm-erp-milestones-stage-delete-swal');
                    }else{
                        swal({
                            title: "Please select atleast one item",
                            text: "",
                            icon: "warning",
                            buttons: {
                                cancel: "Cancel",
                            },
                            dangerMode: true,
                        })
                    }
                }
            });

            // ------------ Add Milestone functionality -------
             jQuery(document).on("click", "#add-milestone",function() {
                jQuery('#mile-header-h4').html('New Stage');
                jQuery("#btn_mile_submit").val("Create");
                jQuery('#popup-stage_name').val('');
                jQuery('#mile-status, #probability, #definition').val('');
                jQuery('#popup-milestone_stage_id').val(0);
                jQuery('.mile-error, .add-mile-msgs').html("");
                jQuery(".mile-popup-overlay, .mile-popup-content").addClass("active");
                jQuery("input[name=stage_name]").focus();
                jQuery('#overlay, #popup_milestone_dropdown_chosen').show();
            });

            jQuery(".mile-close").on("click", function() {
              jQuery(".mile-popup-overlay, .mile-popup-content").removeClass("active");
              jQuery('#overlay, #popup_milestone_dropdown_chosen').hide();
            });

            $(document).on("click", "#btn_mile_submit",function() {
                $('.mile-error, .add-mile-msgs').html("").hide();
                var button_txt = $(this).val();
                var milestone_id = $('#popup-milestone_id').val();
                var mile_stage_name = $('#popup-stage_name').val();
                var status = $('#mile-status').val();
                var probability = $('input#probability').length ? $('input#probability').val() : null;
                var definition = $('#definition').length ? $('#definition').val() : '';
                var milestone_stage_id = $('#popup-milestone_stage_id').val();
                //var forbiddenCharacters = /[@\/\\#]/g;

                if(mile_stage_name.length==0){
                    $('.add-mile-name-msg').html("Name is required.").show();
                }else if(mile_stage_name.length>50){
                    $('.add-mile-name-msg').html("Name length should not be greater than 50.").show();
                /*}else if (forbiddenCharacters.test(mile_stage_name)) {
                    $('.add-mile-name-msg').html('Special characters not allowed. Please remove @, /, \\, or #.').show();*/
                }else if(status.length==0){
                    $('.add-mile-name-msg').html("");
                    $('.add-mile-status-msg').html("Status is required.").show();
                }else if (probability !== null && probability.length == 0) {
                    $('.add-stage-prob-msg, .add-mile-status-msg').html("");
                    $('.add-stage-prob-msg').html("Probability is required.").show();
                }else{
                    $(this).val('Loading..').prop('disabled',true);
                    
                    $.ajax({
                        url:"<?php echo site_url().'/wp-json/productsplugin/v1/check-milestage-unique'; ?>",
                        method:'post',
                        data:{milestone_id:milestone_id,stage_name:mile_stage_name,milestone_stage_id:milestone_stage_id},
                        success(response){
                            if(response.status==400){
                                $('.add-mile-msgs').html(response.message).show();
                                $('#btn_mile_submit').val(button_txt).prop('disabled',false);
                            }else{
                                create_update_milestage(milestone_id,mile_stage_name,status,probability,definition,milestone_stage_id);
                            }
                        }
                    });

                }
              
            });

            function create_update_milestage(milestone_id,mile_stage_name,status,probability,definition,milestone_stage_id){
                $.ajax({
                    url:'<?php echo admin_url('admin-ajax.php'); ?>',
                    method:'post',
                    data:{action: 'add_milestone_stage_action',milestone_id:milestone_id,stage_name:mile_stage_name,status:status,probability:probability,definition:definition,milestone_stage_id:milestone_stage_id},
                    success(response){
                        $(".mile-popup-overlay, .mile-popup-content").removeClass("active");
                        $('#overlay, #popup_milestone_dropdown_chosen').hide();
                        swal({
                                title: response.data.message,
                                icon: 'success',
                                type: 'success'
                        });
                        location.reload(true);
                    }
                });
            }
            
            jQuery(document).on("click", ".milestone_stage_id .edit a",function(e) {
                e.preventDefault();
                jQuery('#mile-header-h4').html('Update Stage');
                jQuery("#btn_mile_submit").val("Update");
                $('.mile-error, .add-mile-msgs').html("");

                // Get the href attribute of the anchor tag
                var href = $(this).attr('href');

                // Parse the href to extract parameters
                var params = {};
                var urlParams = new URLSearchParams(href.split('?')[1]);
                urlParams.forEach(function(value, key) {
                    params[key] = value;
                });

                // Separate variables for each key
                var id = params['id'];
                var dataStageName = params['data-stage_name'];
                var dataStatus = params['data-status'];
                var dataProbability = params['data-probability'];
                var dataDefinition = params['data-definition'];

                jQuery('#popup-stage_name').val(dataStageName);
                jQuery('#mile-status').val(dataStatus);
                jQuery('#probability').val(dataProbability);
                jQuery('#definition').val(dataDefinition);
                jQuery('#popup-milestone_stage_id').val(id);

                jQuery(".mile-popup-overlay, .mile-popup-content").addClass("active");
                jQuery("input[name=stage_name]").focus();
                jQuery('#overlay, #popup_milestone_dropdown_chosen').show();
            });    

            // Delete Milestone stage functionality Start Here ------
            jQuery(document).on('click','.delete_milestone_stage',function(){
                swal({
                    title: "Are you sure?",
                    text: "Yes, I want to delete this Stage. Opportunities will no longer be linked to this Stage.",
                    icon: "warning",
                    buttons: {
                        cancel: "Cancel",
                        confirm: "Delete",
                    },
                    content: {
                        element: "input",
                        attributes: {
                            type: "checkbox",
                            id: "agreeCheckbox"
                        },
                    },
                    dangerMode: true,
                })
                .then((willDelete) => {
                    if (willDelete && $('#agreeCheckbox').prop('checked')) {
                        $(this).html('wait..');
                        $(this).attr('disabled',true);
                        $("#loader_box").show();
                        var milestone_stage_id = jQuery(this).data('milestone_stage_id');
                        jQuery.ajax({
                            url:'<?php echo admin_url('admin-ajax.php'); ?>',
                            method:'post',
                            data:{action: 'delete_milestone_stage',milestone_stage_id:milestone_stage_id},
                            success(response){
                                location.reload(true);
                            }
                        });
                    }
                    $('.swal-modal').removeClass('crm-erp-milestones-stage-delete-swal');     
                });
                $('.swal-modal').addClass('crm-erp-milestones-stage-delete-swal'); 
            }); 

            // Function to get URL parameter by name
            function getParameterByName(name, url) {
                if (!url) url = window.location.href;
                name = name.replace(/[\[\]]/g, "\\$&");
                var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
                    results = regex.exec(url);
                if (!results) return null;
                if (!results[2]) return '';
                return decodeURIComponent(results[2].replace(/\+/g, " "));
            }
        </script>
    <?php
    } // search_box_new function ends
} // CRM_ERP_Milestone_Stages class ends