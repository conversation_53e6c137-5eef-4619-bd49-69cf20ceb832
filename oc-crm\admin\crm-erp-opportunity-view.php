<?php
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
//  error_reporting(E_ALL);

// $opp_notes_table_name = $wpdb->prefix.'erc_opportunity_notes';
// $wpdb->query("ALTER TABLE $opp_notes_table_name ADD `confidential_notes` INT(11) NOT NULL DEFAULT 0 AFTER `note`");
/*
$args = array(
    'exclude' => array(),
    'role__not_in' => array('echeck_client'),
    'fields' => 'all',
);
$users = get_users($args);
*/
// $lead_id = 12;
// global $wpdb;
// $contact_table = $wpdb->prefix.'op_contact_lead'; 
// $wpdb->query("UPDATE $contact_table SET custom1 = 'import_from_lead' WHERE lead_id='".$lead_id."'");

$users = get_users( array( 
    'role__in' => array('master_ops','echeck_staff','echeck_admin','echeck_client','iris_affiliate_users','master_sales','iris_sales_agent','iris_sales_agent_rep' ),
    'orderby' => 'display_name',
    'order' => 'ASC'  ) )

?>

<?php
$additional_table = $wpdb->prefix.'erc_iris_leads_additional_info';
$business_info_table = $wpdb->prefix.'erc_business_info';
$opportunity_table = $wpdb->prefix.'opportunities';
$opportunity_product_table = $wpdb->prefix.'opportunity_products';
$product_table = $wpdb->prefix.'crm_products';
$milestone_table = $wpdb->prefix.'milestones';
$milestone_status_table = $wpdb->prefix.'milestone_stages';
$next_step_table = $wpdb->prefix.'next_step';
$currency_table = $wpdb->prefix.'currencies';
$where = ' WHERE 1=1 ';


// -------- confidence notes code -------
     $confidence_user = 0;
     
     $confidence_check = '';
     if(get_current_user_id() == 44019){ // nedeen id 
        $confidence_check = 'checked';
     }
     $option_table = $wpdb->prefix.'onedrive_options';
      $selected_user = $wpdb->get_var("SELECT meta_value FROM $option_table WHERE meta_key='notes_confidence_users' ");
      if(!empty($selected_user)){
        $selected_users = explode(",",$selected_user);
        $current_user_id = get_current_user_id();
        if(in_array($current_user_id,$selected_users)){
            $confidence_user = 1;
        }
      }

if(isset($_REQUEST['id']) && !empty($_REQUEST['id'])){
    $where .= " AND opp.OpportunityID ='".$_REQUEST['id']."'";
}

$sql = "SELECT opp.*, oppPro.stage_deletedat, oppPro.milestone_deletedat, oppPro.deletedAt as product_deletedat, prod.Title as productnName,prod.ProductID as product_ID, mile.milestone_name as milestoneName, mile.status as milestoneActiveStatus, mile.deleted_at as milestoneDeleteStatus, mile.product_id as milestoneProductStatus, mile.map as milestoneMap,   mile_status.stage_name as milestoneStatus, ns.next_step  as NextStepName, ns.next_step_id, buss.business_legal_name as AccountName, mile.milestone_id, mile_status.milestone_stage_id as milestoneStageId,mile_status.status as StageActiveStatus,mile_status.deleted_at as StageDeleteStatus, ns.next_step, curr.currency_code as currencyName, curr.currency_name as currencyFullName, addt.lead_status, addt.envelop_id as lead_envelop_id FROM $opportunity_table opp left join $opportunity_product_table oppPro on opp.OpportunityID = oppPro.opportunity_id left join $product_table prod on oppPro.product_id = prod.ProductID left join $milestone_table mile on oppPro.milestone_id = mile.milestone_id left join $milestone_status_table mile_status on oppPro.milestone_stage_id = mile_status.milestone_stage_id left join $next_step_table ns on opp.NextStep = ns.next_step_id left join $business_info_table buss on opp.LeadID = buss.lead_id left join $currency_table curr on opp.OpportunityCurrency = curr.currency_id left join $additional_table addt on opp.LeadID = addt.lead_id " . $where;

// echo $sql; 
$opportunity = $wpdb->get_results($sql);

//echo get_current_user_id();

 // $sql = "ALTER TABLE `eccom_crm_activity_log` CHANGE `id` `id` INT(11) NOT NULL AUTO_INCREMENT, CHANGE `lead_id` `lead_id` INT(11) NULL, CHANGE `opportunity_id` `opportunity_id` INT(11) NULL, CHANGE `date` `date` VARCHAR(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL, CHANGE `field_name` `field_name` VARCHAR(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL, CHANGE `changed_from` `changed_from` VARCHAR(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL, CHANGE `changed_to` `changed_to` VARCHAR(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL, CHANGE `note` `note` VARCHAR(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL, CHANGE `changed_by` `changed_by` VARCHAR(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL, CHANGE `created_at` `created_at` VARCHAR(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL, CHANGE `deleted_at` `deleted_at` VARCHAR(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL";
 // $table_schema = $wpdb->get_results($sql);

// $sql = "SELECT *
// FROM INFORMATION_SCHEMA.COLUMNS
// WHERE TABLE_NAME = 'eccom_crm_activity_log'";
// $table_schema = $wpdb->get_results($sql);


// echo '<pre>';
// print_r($all_milestone_status);
// echo '</pre>';

// print_r($opportunity);

// exit();
$single_opportunity = $opportunity[0];
$oppotunityCreateTime = $single_opportunity->CreatedAt;
$oppsLastPaymentLinkTime = $single_opportunity->last_payment_sent;
$oppstunityLastAgreementLinkTime = $single_opportunity->last_agreement_sent;



$envelop_id = $single_opportunity->envelop_id;

$all_nextStep = $wpdb->get_results("SELECT NextStep FROM $opportunity_table WHERE $opportunity_table.DeletedAt IS NULL");
$myarray = array();
foreach($all_nextStep as $da){
    if(!empty($da->NextStep)){
        array_push($myarray,$da->NextStep);
    }
}
$allNextStep = json_encode($myarray);

$where_milestone = ' 1=1 ';
if(isset($single_opportunity->product_ID)&&!empty($single_opportunity->product_ID)){
    $where_milestone .= ' AND FIND_IN_SET('.$single_opportunity->product_ID.','.$milestone_table.'.product_id) ';
}
$all_milestones =  $wpdb->get_results("SELECT $milestone_table.milestone_id,$milestone_table.milestone_name, $milestone_table.map, $milestone_table.status, $milestone_table.deleted_at FROM $milestone_table WHERE $where_milestone ");

$where_stage = ' 1=1 ';
if(isset($single_opportunity->milestone_id)&&!empty($single_opportunity->milestone_id)){
    $where_stage .= " AND ".$milestone_table.".product_id is not null AND ".$milestone_status_table.".milestone_id = ".$single_opportunity->milestone_id."" ;
}
$all_milestone_status =  $wpdb->get_results("SELECT $milestone_status_table.milestone_stage_id,$milestone_status_table.stage_name, $milestone_status_table.status, $milestone_status_table.deleted_at FROM $milestone_status_table left join $milestone_table on $milestone_status_table.milestone_id = $milestone_table.milestone_id WHERE $where_stage ");

$user_details = get_user_by( 'id', $single_opportunity->CreatedBy );
$owner_name = $user_details->display_name;

$login_user_details = get_user_by( 'id', get_current_user_id() );
$login_user_name = $login_user_details->display_name;

$all_users = get_users( array( 'role__in' => array('Master_Sales','Iris_Sales_Agent' ) ) );
// $active_sales_agents = $wpdb->get_results("SELECT userid,full_name FROM {$wpdb->prefix}erc_sales_team WHERE active_sales_agent = 1");

$contacts_table = $wpdb->prefix.'op_contacts'; 
$contact_email_table = $wpdb->prefix.'op_emails';   
if($single_opportunity->ContactID > 0){
    $contact_data = $wpdb->get_row("SELECT first_name,last_name,trash FROM $contacts_table WHERE id = ".$single_opportunity->ContactID."");
    if(!empty($contact_data)){

        if($contact_data->trash==1){
            $primary_contact = $contact_data->first_name.' '.$contact_data->last_name.' (disabled)';
        }else{
            $primary_contact = $contact_data->first_name.' '.$contact_data->last_name;
        }
        
    }else{
        $primary_contact = '';
    }
}else{
    $primary_contact = '';
}

$lead_id = $single_opportunity->LeadID;
$table_name = $wpdb->prefix . 'op_contacts';
$contact_lead_table = $wpdb->prefix . 'op_contact_lead';
$query = $wpdb->prepare("SELECT $table_name.id,$table_name.first_name,$table_name.last_name, $table_name.trash FROM $table_name JOIN $contact_lead_table ON $table_name.id =  $contact_lead_table.contact_id WHERE $contact_lead_table.lead_id = $lead_id AND $table_name.active = 1 AND $table_name.trash = 0");
$all_contacts = $wpdb->get_results($query);

$opportunity_id = $single_opportunity->OpportunityID;
$notes_table = $wpdb->prefix . 'erc_opportunity_notes';

$total_notes = $wpdb->get_results("SELECT id FROM $notes_table WHERE opportunity_id='" . $opportunity_id . "' ORDER BY id DESC ");
$total_notes_count = count($total_notes);

$note_where = "";

if($confidence_user==0){
    $note_where = " AND confidence_notes_access=0";
}

$all_notes = $wpdb->get_results("SELECT * FROM $notes_table WHERE opportunity_id='" . $opportunity_id . "'".$note_where." ORDER BY id DESC"); //LIMIT 10 OFFSET 0


?>
<style type="text/css">
/*
.Opp_Closure.void_agreement {
    background: #ea6c25 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 5px #0000001a;
    border-radius: 10px;
    color: #fff;
    padding: 4px 10px;
    text-align: center;
    border: navajowhite;
    margin: 5px;
}*/
.Opp_Closure.send_stc_agreement, .Opp_Closure.send_stc_ssf, .Opp_Closure.send_ta_agreement, .Opp_Closure.send_rdc_agreement, .Opp_Closure.resend_ta_agreement, .Opp_Closure.resend_rdc_agreement,  .Opp_Closure.reopen_opportunity,.Opp_Closure.void_agreement,.Opp_Closure.send_erc_agreement, .resend_agreement_button {
    border: 0;
    background: #55915a 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 5px #0000001a;
    border-radius: 10px;
    color: #fff;
    padding: 4px 5px;
    display: inline-block;
    text-align: center;
}

.resend_rdc_agreement {
    padding: 7px 15px!important;
}    

.close-popup-document {
    padding: 11px 25px;
    color: #ff5f00;
    font-size: 16px;
    font-weight: 600;
    background: #ffffff 0% 0% no-repeat padding-box;
    box-shadow: 0px 3px 6px #00000029;
    border: 2px solid #ff5f00;
    border-radius: 11px;
}

.close_btn_resendagreement{
    border: 0;
    background: #ff0000 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 5px #0000001a;
    border-radius: 10px;
    color: #fff;
    font-size:inherit;
    padding: 7px 15px;
    display: inline-block;
    text-align: center;
}

.Opp_Closure.hidebutton, .resend_agreement_button.hidebutton{
/*    display: none;*/
    cursor: not-allowed;
    pointer-events:none;
    background-color: #9ec391;
    margin-bottom:2%;
}


.nxt_btn{
    margin-right: 15px;
}
.select-milestone,.custom-mile-status{
    min-width: 70%;   
 }

 .custom-ownership, .custom-contactList{
    min-width: 68%;   
 }
 

.owner-main-div div{
    display: none !important;
}
    .custom-ownership .dropdown-toggle{
    font-size: 14px;
    line-height: 2;
    color: #32373c;
    border-color: #7e8993;
    box-shadow: none;
    border-radius: 3px;
    padding: 0 24px 0 8px;
    min-height: 30px;
    max-width: 25rem;
    -webkit-appearance: none;
    background: #fff url(data:image/svg+xml;charset=US-ASCII,%3Csvg%20width%3D%2220%22%20height%3D%2220%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M5%206l5%205%205-5%202%201-7%207-7-7%202-1z%22%20fill%3D%22%23555%22%2F%3E%3C%2Fsvg%3E) no-repeat right 5px top 55%;
    background-size: 16px 16px;
    cursor: pointer;
    vertical-align: middle;
}

 .swal-button--cancel {
    padding:9px 24px !important;
 }

.resend-notes li{
     list-style-type: disc;
}
.resend-notes li::marker{
    color: rgb(112 115 117);
}

/*---- confidential notes css ---------*/
    .confidential-notes-div{
        background: #FFA500;
    }
    .confidential-notes-div .date-time , .confidential-notes-div .edit_self_notes, .confidential-notes-div .delete_self_notes{
        color: #000 !important;   
    }
  .edit_self_notes , .delete_self_notes{
    font-size: 18px;
    color: #ff5c00 !important;
  }    
  .edit_self_notes{
    padding-right:5px;
  }
  .swal-footer{
    text-align: center;
  }

  .confidential-notes-div{
        background: #FFA500;
    }
    .confidential-notes-div .date-time , .confidential-notes-div .edit_self_notes, .confidential-notes-div .delete_self_notes{
        color: #000 !important;   
    }
  .edit_self_notes , .delete_self_notes{
    font-size: 18px;
    color: #ff5c00 !important;
  }    
  .edit_self_notes{
    padding-right:5px;
  }
  .swal-footer{
    text-align: center;
  }
  
</style>
<div class="main_content_iner">
    <div class="container-fluid p-0">
         <div class="row justify-content-center">
            <div class="col-lg-12">
                <div id="form_2" class="white_card card_height_100 mb_30" style="display: block;">
                     <div class="white_card_header">
                        <div class="box_header m-0 new_report_header">
                            <div class="title_img">
                                <img src="<?php echo get_site_url();?>/wp-content/plugins/oc-crm/assets/img/opportunity-icon.png" class="page-title-img" alt="">
                                <h4><?php echo ucwords($single_opportunity->OpportunityName); ?></h4>
                                </div>
                        </div>
                        <div class="d-flex">
                            <ul class="nav nav-pills" id="pills-tab" role="tablist">
                                <li class="nav-item active">
                                    <a class="nav-link tab-btn" id="eleve-opportunity-tab" data-toggle="tab" href="#eleve-opportunity"
                                        role="tab" aria-controls="first" aria-selected="true">Opportunity</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link tab-btn" id="pills-audit-logs" data-toggle="tab" href="#pills-logs" role="tab" aria-controls="pills-logs" aria-selected="true">Audit Logs</a>
                                </li>                               
                            </ul>
                        </div>
                    </div>
                    <div class="white_card_body p-15">
                        <div class="row">
                            <div class="col-md-8" id="left_section">
                              <div class="tab-content" id="pills-tabContent">
                              <div class="tab-pane active erc-project-scroll" id="eleve-opportunity" role="tabpanel" aria-labelledby="eleve-opportunity-tab">
                                <div class="custom-opportunity-view-edit-page">
                                    <div class="row edit_icon">
                                        <a href="javascript:void(0)" title="Edit" class=""><i class="fa-regular fa-pen-to-square edit_opportunity_data"></i></a>
                                    </div>
                                    <div class="row">
                                        <div class="show_message_popup"></div>
                                        <div class="floating col-md-6 mb-3">
                                           <div id="userDetails" data-id='<?php echo get_current_user_id();  ?>' data-name='<?php echo $login_user_name; ?>' style="display:none;"></div>
                                            <label for="title">Name:*</label>
                                            <input type="text" disabled name="" class="crm-erp-field form-control" id="OpportunityName" value="<?php echo ucwords($single_opportunity->OpportunityName); ?>" >
                                        </div>
                                        <div class="floating col-md-6 mb-3">     
                                            <label for="title">Business:*</label>
                                            <a href="<?php echo get_site_url();  ?>/wp-admin/admin.php?page=iris-fields-v1.php&lead_id=<?php echo $single_opportunity->LeadID;  ?>" target="_blank" class="btn btn-primary view_business">View</a>
                                            <input type="text" disabled name="" class="crm-erp-field form-control" id="LeadID" value="<?php echo ucwords($single_opportunity->AccountName); ?>"  >
                                        </div>
                                        <div class="floating col-md-6 mb-3">  
                                            <label for="title">Products:*</label> 
                                            <input type="hidden" disabled name="" class="" id="product_id_hidden" value="<?php echo $single_opportunity->product_ID; ?>"  >
                                            <input type="text" disabled name="" class="crm-erp-field form-control" id="product_id" value="<?php echo ucwords($single_opportunity->productnName); if($single_opportunity->product_deletedat !== null) echo ' (Deleted)';?>"  >
                                        </div>
                                        <div class="floating col-md-6 mb-3">     
                                            <label for="title">Expected Close Date:*</label>
                                            <input type="text" disabled name="ExpectedCloseDate" id="ExpectedCloseDate" class="crm-erp-field form-control" value="<?php echo date("m/d/Y", strtotime($single_opportunity->ExpectedCloseDate)); ?> " pattern="\d{2}/\d{2}/\d{4}" placeholder="MM/DD/YYYY" onkeydown="return false" autocomplete="off">
                                        </div>
                                        <div class="floating col-md-6 mb-3">     
                                            <label for="title">Currency:*</label>
                                            <input type="text" disabled name="" class="crm-erp-field form-control" id="OpportunityCurrency" value="<?php echo $single_opportunity->currencyName.' ('.$single_opportunity->currencyFullName.')'; if ($single_opportunity->currency_deletedat !== null) echo ' (Deleted)';?>"  >
                                        </div>
                                        <div class="floating col-md-6 mb-3">     
                                            <label for="title">Amount:*</label>
                                            <input type="text" step="0.01" name="OpportunityAmount" id="opportunity_amount" class="crm-erp-field form-control" disabled value="<?php echo number_format($single_opportunity->OpportunityAmount, 2, '.', ','); ?>" min="0"  maxlength="15">
                                        </div>
                                        <div class="floating col-md-6 mb-3">     
                                            <label for="title">Probability (%):</label>
                                            <input id="probability" disabled name="probability" class="crm-erp-field form-control" type="number" min="0" max="100" value="<?php echo $single_opportunity->Probability; ?>" oninput="if(this.value > 100) this.value = 100;">
                                        </div>
                                        <div class="floating col-md-6 mb-3">     
                                            <label for="title">Next Step:</label>
                                            <input type="text" id="NextStep" name="NextStep" class="crm-erp-field form-control " value="<?php if(isset($single_opportunity->NextStep)){ echo $single_opportunity->NextStep; } ?>"  autocomplete="off" disabled maxlength="75">
                                            <div id="nextStepDropdown" class="next-step-dropdown"></div>
                                        </div>
                                        <div class="floating col-md-12 mb-3">     
                                            <label for="title">Description:</label>
                                            <textarea name="OpportunityDescription" id="opportunity_description" rows="3" class=" form-control" disabled min="0"  maxlength="2000"><?php echo $single_opportunity->Description; ?></textarea>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="button-container button_container" style="display: none;">
                                                <button type="submit" class="create_product_btn update_opportunity" data-oppID="<?php echo $single_opportunity->OpportunityID; ?>">Update</button>
                                                <button type="button" class="create_product_btn cancel_opportunity" >Cancel</button>
                                        </div>
                                    </div>
                                  </div>  
                                </div>     

                                   <div class="container tab-pane erc-project-scroll" id="pills-logs" role="tabpanel" aria-labelledby="pills-logs-tab">
                                        <img src="<?php echo get_site_url().'/wp-content/plugins/wp-iris/page-templates/loader.gif';  ?>" style="width:80px;margin-left: 35%;" />
                                   </div>
                              </div> 

                                    

                                <!-- <div class="custom-opportunity-call-text-email mt-4">
                                    <div class="accordion" id="accordionExample">
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="headingOne">
                                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                                Call Logs
                                            </button>
                                            </h2>
                                            <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#accordionExample">
                                                <div class="accordion-body">
                                                    <div class="table-responsive custom-opp-table">
                                                        <table class="table" border="1px">
                                                            <thead>
                                                                <tr>
                                                                    <th>Type</th>
                                                                    <th>From</th>
                                                                    <th>TO</th>
                                                                    <th>Date</th>
                                                                    <th>Duration</th>
                                                                    <th>Initiated By</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr> 
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>  
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>                                           
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>  
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>  
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="headingTwo">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                                Text Logs
                                            </button>
                                            </h2>
                                            <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="headingTwo" data-bs-parent="#accordionExample">
                                                <div class="accordion-body">
                                                <div class="table-responsive custom-opp-table">
                                                        <table class="table" border="1px">
                                                            <thead>
                                                                <tr>
                                                                    <th>Type</th>
                                                                    <th>From</th>
                                                                    <th>TO</th>
                                                                    <th>Date</th>
                                                                    <th>Duration</th>
                                                                    <th>Initiated By</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr> 
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>  
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>                                           
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>  
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>  
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="accordion-item">
                                            <h2 class="accordion-header" id="headingThree">
                                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                                Email Logs
                                            </button>
                                            </h2>
                                            <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="headingThree" data-bs-parent="#accordionExample">
                                                <div class="accordion-body">
                                                <div class="table-responsive custom-opp-table">
                                                        <table class="table" border="1px">
                                                            <thead>
                                                                <tr>
                                                                    <th>Type</th>
                                                                    <th>From</th>
                                                                    <th>TO</th>
                                                                    <th>Date</th>
                                                                    <th>Duration</th>
                                                                    <th>Initiated By</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr> 
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>  
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>                                           
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>  
                                                                <tr>
                                                                    <td>1</td>
                                                                    <td>Lorem</td>
                                                                    <td>Lorem Isum</td>
                                                                    <td>24/01/2024</td>
                                                                    <td>10 Hours</td>
                                                                    <td>Sunny Shekhar</td>
                                                                </tr>  
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        </div>
                                    </div> -->

                                    <?php 
                                    
                                    // global $wpdb;
                                    // echo $single_opportunity->ContactID;
                                    //removed shortcode for communnication log

                                    

                                    
                                    ?>


                                    <!-- Notes -->
                                    <div class="custom-opportunity-notes">
                                        <div class="col-sm-12">
                                            <h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;margin-top: 20px;font-weight:600;">Opportunity Notes
                                            <a href="javascript:void(0)" class="opp-add-notes"><i class="fa fa-plus-circle create-note-btn"></i></a>
                                            </h5>
                                        </div>  
                                        <div class="col-sm-12 custom-opp-notes-scroll"> 
                                            <div class="notes-listing">
                                                <?php 
                                                $i = 1;
                                                $from='UTC';
                                                $to='America/New_York';
                                                $format='Y-m-d h:i:s A';
                                                foreach ($all_notes as $n_key => $n_value) {
                                                    $confidence_notes_access = $n_value->confidential_notes;
                                                    $date = $n_value->created;//UTC time
                                                    date_default_timezone_set($from);
                                                    $newDatetime = strtotime($date);
                                                    date_default_timezone_set($to);
                                                    $newDatetime = date($format, $newDatetime);
                                                    date_default_timezone_set('UTC');
                                                    $datetime = date_create($newDatetime);
                                                    $time = date_format($datetime, "h:ia");
                                                    $day = date_format($datetime, " D ");
                                                    $month = date_format($datetime, " M ");
                                                    $date = date_format($datetime, "dS,");
                                                    $year = date_format($datetime, " Y");
                                                    $actual_date = $time . " on " . $day . $month . $date . $year;
                                                    $notes = $n_value->note;
                                                    //$note_array = explode("https", $n_value->note);
                                                    //$notes = $note_array[0];
                                                    // if (isset($note_array[1]) && !empty($note_array[1])) {
                                                    //    $url_array = explode(" ", $note_array[1]);
                                                    //    if(isset($url_array[0]) && !empty($url_array[0])){
                                                    //         $url = 'https' . $url_array[0];
                                                    //         $notes .= '<a href="' . $url . '">' . $url . '</a>';
                                                    //     }
                                                    //     for($i=1; $i<count($url_array);$i++){
                                                    //         if(isset($url_array[$i]) && !empty($url_array[$i])){
                                                    //             $notes .= " ".$url_array[$i];
                                                    //         }
                                                    //     }
                                                    // }
                                                        
                                                        $conf_class ='';
                                                        if($confidence_notes_access){ 
                                                            $conf_class='confidential-notes-div';    
                                                        }

                                                    ?>
                                                        <div class="note-listing-div shadow <?php echo $conf_class;?>" id="note-list-<?= $n_value->id; ?>">  
                                                                <p class="notes" id="note-<?= $n_value->id; ?>" data-confidece="<?php echo $confidence_notes_access;?>"><?= $notes; ?></p>
                                                                <p class="date-time">(<?= $actual_date;?>)</p>
                                                                <?php 
                                                                    // check confidence_user and self notes
                                                                if($confidence_user == 1 && $current_user_id == $n_value->created_by && $confidence_notes_access){ ?>  
                                                                    <a href="javascript:void(0)" title="Edit Notes" class="edit_self_notes" data-note_id="<?= $n_value->id; ?>">
                                                                        <i class="fa-regular fa-pen-to-square"></i>
                                                                    </a>
                                                                    <a href="javascript:void(0)" title="Delete Notes" class="delete_self_notes" data-note_id="<?= $n_value->id; ?>">
                                                                        <i class="fa fa-trash" aria-hidden="true"></i>
                                                                    </a>
                                                                <?php } ?>
                                                        </div>
                                                    <?php $i++;
                                                } ?>
                                            </div>
                                        </div>    
                                    </div>  
                                        <?php /* if ($total_notes_count >= 11) { ?>
                                            <div class="col-sm-12 text-center">
                                                <button type="button" class="custom-opp-load-more" id="load-more-notes">Load More Notes</button>
                                            </div>
                                            <input type="hidden" id="note-offset" value="10">
                                        <?php } */ ?>  

                                        <!--------Notes End--------->




                            </div>

                            <div class="col-md-4" id="right_section">
                                <div class="custom-opportunity-view-sidebar">
                                    <div class="row edit-icon-milestone">
                                        <a href="javascript:void(0)" title="Edit" ><i class="fa-regular fa-pen-to-square edit_opportunity_milestone" style="font-weight: 700"></i></a>
                                    </div>
                                    <div class="show_message_milestone"></div>
                                    <div class="d-flex opp-owner mb-3 ">
                                        <label><b>Milestone:</b></label>
                                        <p id="showMilestone">
                                        <?php 
                                        $opportunityMilestone = $single_opportunity->milestoneName;
                                        $opportunityMilestoneID = $single_opportunity->milestone_id;
                                          ?>
                                        <?php echo ucwords($single_opportunity->milestoneName); 
                                        $MilestoneIssue = '';
                                        
                                        if($single_opportunity->milestoneActiveStatus == 'inactive'){
                                            $MilestoneIssue = 'Inactive';
                                        }
                                        if($single_opportunity->milestoneDeleteStatus !== null){
                                            $MilestoneIssue = 'Deleted';
                                        }
                                        $milestoneMmap = unserialize($single_opportunity->milestoneMap);
                                        if(empty($single_opportunity->milestoneProductStatus)){
                                            $MilestoneIssue = 'Unassigned';
                                        }elseif(empty($milestoneMmap)){
                                            $MilestoneIssue = 'Unassigned';
                                        }elseif(!in_array("opportunity", $milestoneMmap)){
                                            $MilestoneIssue = 'Unassigned';
                                        }

                                        if(!empty($MilestoneIssue)){
                                            echo '('.$MilestoneIssue.')';
                                        }
                                        
                                        ?> </p>
                                        <select class="form-control custom-mile-status select-milestone"  id="Milestone" on='23' style="display: none;">
                                            
                                            <?php
                                            

                                            if(count($all_milestones)>0){
                                                foreach ($all_milestones as $all_milestone) { 

                                                $milestone_id = $all_milestone->milestone_id;

                                                $MilestoneIssue = '';
                                        
                                                if($all_milestone->status == 'inactive'){
                                                    $MilestoneIssue = 'Inactive';
                                                }
                                                if($all_milestone->deleted_at !== null){
                                                    $MilestoneIssue = 'Deleted';
                                                }
                                                $milestoneMmap = unserialize($all_milestone->map);

                                                if(empty($milestoneMmap)){
                                                    $MilestoneIssue = 'Unassigned';
                                                }elseif(!in_array("opportunity", $milestoneMmap)){
                                                    $MilestoneIssue = 'Unassigned';
                                                }

                                                $milestone_name = '';
                                                $MilestoneID = '';
                                                if(!empty($MilestoneIssue)){
                                                    $milestone_name = $all_milestone->milestone_name.'('.$MilestoneIssue.')';
                                                    $MilestoneID = '';
                                                }else{
                                                    $milestone_name = $all_milestone->milestone_name;
                                                    $MilestoneID = $all_milestone->milestone_id;
                                                }


                                                $sel='';
                                                if($single_opportunity->milestone_id == $milestone_id){
                                                    $sel='selected';
                                                }  else{
                                                    $sel = '';
                                                }

                                                if(!empty($sel)){
                                                    echo '<option value="'.$MilestoneID.'" '.$sel.'>'. ucwords($milestone_name) .'</option>';
                                                }else{
                                                    if(empty($MilestoneIssue)){
                                                        echo '<option value="'.$MilestoneID.'" '.$sel.'>'. ucwords($milestone_name) .'</option>';
                                                    }
                                                }
                                                
                                                }
                                            }else{
                                                ?>
                                                    <option value="">Select Milestone</option>
                                                <?php
                                            }
                                            
                                        
                                            ?>          
                                        </select>
                                    </div>

                                    <div class="d-flex opp-owner">
                                        <label><b>Stage:</b></label>
                                        <p id="showMilestoneStage">
                                        <?php 
                                        $opportunityStage = $single_opportunity->milestoneStatus;
                                        $opportunityStageID = $single_opportunity->milestoneStageId;

                                          ?>
                                            <?php 
                                        echo ucwords($single_opportunity->milestoneStatus); 

                                        $StageIssue = '';
                                        
                                        if($single_opportunity->StageActiveStatus == 'inactive'){
                                            $StageIssue = 'Inactive';
                                        }
                                        if($single_opportunity->StageDeleteStatus !== null){
                                            $StageIssue = 'Deleted';
                                        }
                                        if(empty($single_opportunity->milestoneProductStatus)){
                                            $StageIssue = 'Unassigned';
                                        }
                                        

                                        if(!empty($StageIssue)){
                                            echo '('.$StageIssue.')';
                                        }

                                                             
                                    ?> 


                                        </p>
                                        <select class="form-control custom-mile-status"  id="MilestoneStage" name='milestone_status-23' style="display: none;">
                                            <?php 
                                            if(count($all_milestone_status)>0){
                                            foreach ($all_milestone_status as $allmilestonestatus) { 
                                                $milestone_stage_id = $allmilestonestatus->milestone_stage_id;     
                                                $StageIssue = '';
                                        
                                                if($allmilestonestatus->status == 'inactive'){
                                                    $StageIssue = 'Inactive';
                                                }
                                                
                                                if($allmilestonestatus->deleted_at !== null){
                                                    $StageIssue = 'Deleted';
                                                }
                                                $stageID = '';
                                                $stage_name = '';
                                                if(!empty($StageIssue)){
                                                    $stage_name = $allmilestonestatus->stage_name.'('.$StageIssue.')';
                                                    $stageID = '';
                                                }else{
                                                    $stage_name = $allmilestonestatus->stage_name;
                                                    $stageID = $allmilestonestatus->milestone_stage_id;
                                                }


                                                $sel='';
                                                if($single_opportunity->milestoneStageId == $milestone_stage_id){
                                                    $sel='selected';
                                                }  else{
                                                    $sel = '';
                                                }

                                                if(!empty($sel)){
                                                    echo '<option value="'.$stageID.'" '.$sel.'>'.ucwords($stage_name) .'</option>';
                                                }else{
                                                    if(empty($StageIssue)){
                                                       echo '<option value="'.$stageID.'" '.$sel.'>'.ucwords($stage_name) .'</option>';
                                                    }
                                                }
    
                                                
                                                     }

                                            }else{
                                                ?>
                                                    <option value="">Select Stage</option>
                                                <?php 
                                            }
                                                 ?>         
                                        </select>
                                    </div>
                                    <div class="row">
                                        <div class="button-container milestone_button_container mt-0" style="display: none;">
                                                <button type="submit" class="create_product_btn update_milestone" data-oppID="<?php echo $single_opportunity->OpportunityID; ?>">Update</button>
                                                <button type="button" class="create_product_btn cancel_milestone" >Cancel</button>
                                        </div>
                                    </div>
                                    
                                </div>
                                <div class="custom-opportunity-view-sidebar">
                                    <div class="row edit-icon-owner">
                                        <a href="javascript:void(0)" title="Edit" ><i class="fa-regular fa-pen-to-square edit_opportunity_owner" style="font-weight: 700"></i></a>
                                    </div>
                                    <div class="show_message_owner"></div>
                                    <div class="d-flex opp-owner owner-main-div" id="owner-div-id">
                                        <label><b>Select Owner:</b></label>
                                        <p id="showOwnerList"><?php  echo $owner_name; ?></p>
                                        <!--                                         <select class="form-control custom-ownership" style="display: none;" id="OwnerList">
                                            <?php 
                                            /*
                                            if(!empty($active_sales_agents)){
                                                foreach ($active_sales_agents as $active_sales_agent) {
                                                    if($single_opportunity->CreatedBy == $active_sales_agent->userid){
                                                        $sel='selected';
                                                    }  else{
                                                        $sel = '';
                                                    }
                                                    echo '<option value="'.$active_sales_agent->userid.'" '.$sel.'>'.$active_sales_agent->full_name.'</option>';
                                                }
                                            }else{
                                                echo '<option value="">Select Owner</option>';
                                            }
                                            */
                                        ?>          
                                        </select>
--------->
                                        <select  class="ewc-filter-sub selectpicker custom-ownership" data-live-search="true" title="Select Owner" id="OwnerList" style="display:none;">
                                        <?php    
                                        $groups = [];
                                        foreach ($users as $user) {
                                            $userid = $user->ID;
                                            $display_name = $user->display_name;
                                            if(isset($user->roles[0])){
                                                $user_role = $user->roles[0];
                                            }else{
                                                foreach ($user->roles as $key => $value) {
                                                        $user_role = $value;
                                                }    
                                            }  

                                           $selected = '';
                                           if($single_opportunity->CreatedBy == $userid){
                                                       $selected='selected';
                                                    }
                                                
                                                //'echeck_staff','echeck_admin','echeck_client','iris_affiliate_users','master_sales','iris_sales_agent','iris_sales_agent_rep'
                                                
                                                if($user_role=='master_ops'){
                                                    $user_role = 'Master OPS';
                                                }else if($user_role=='echeck_staff' || $user_role=='echeck_admin'){
                                                    $user_role = 'OPS';
                                                }else if($user_role=='echeck_client'){
                                                    $user_role = 'Finance';    
                                                }else if($user_role=='iris_affiliate_users'){
                                                    $user_role = 'Affiliate';
                                                }else if($user_role=='master_sales'){
                                                    $user_role = 'Master Sales';
                                                }else if($user_role=='iris_sales_agent' || $user_role=='iris_sales_agent_rep'){
                                                    $user_role = 'Sales';
                                                }

                                               // Create an array to store options for each group
                                                 if (!isset($groups[$user_role])) {
                                                    $groups[$user_role] = [];
                                                 }
                                                 $test_display_name = ' '.$display_name;
                                                 if(strpos($test_display_name,"Test") || strpos($test_display_name,"test")){

                                                 }else{
                                                  if(trim($display_name)!=''){      
                                                   $option = "<option value='{$userid}' {$selected}>{$display_name}</option>";
                                                   $groups[$user_role][] = $option;
                                                  } 
                                                 }  
                                        }

                                            // Generate the HTML for each optgroup
                                            foreach ($groups as $group => $options) {
                                                echo "<optgroup label='{$group}'>";
                                            foreach ($options as $option) {
                                                echo $option;
                                            }
                                            echo "</optgroup>";
                                            }
                                            
                                        ?>
                                        </select>
                                    </div>
                                    <div class="row">
                                        <div class="button-container owner_button_container mt-0" style="display: none;">
                                                <button type="submit" class="create_product_btn update_owner" data-oppID="<?php echo $single_opportunity->OpportunityID; ?>">Update</button>
                                                <button type="button" class="create_product_btn cancel_owner" >Cancel</button>
                                        </div>
                                    </div>
                                </div>


                                <div class="custom-opportunity-view-sidebar">
                                    <div class="row edit-icon-owner">
                                        <a href="javascript:void(0)" title="Edit" ><i class="fa-regular fa-pen-to-square edit_opportunity_contact" style="font-weight: 700"></i></a>
                                    </div>
                                    <div class="show_message_contact"></div>
                                    <div class="d-flex opp-owner">
                                        <label><b>Select Contact:</b></label>
                                        <p id="showContactList"><?php echo ucwords($primary_contact); ?></p>
                                        <select class="form-control custom-contactList" style="display: none;" id="ContactList">
                                            <?php 
                                            if(count($all_contacts)>0){
                                            foreach ($all_contacts as $all_contact) { 
                                                $contact_id = $all_contact->id;     
                                                $sel='';
                                                if($single_opportunity->ContactID == $contact_id){
                                                    $sel='selected';
                                                }  else{
                                                    $sel = '';
                                                }
                                               $contact_name = $all_contact->first_name.' '.$all_contact->last_name;     
                                                echo '<option value="'.$contact_id.'" '.$sel.'>'.$contact_name.'</option>';
                                                     }

                                            }else{
                                                ?>
                                                    <option value="">Select Contact</option>
                                                <?php 
                                            }
                                        ?>          
                                        </select>
                                    </div>
                                    <div class="row">
                                        <div class="button-container contact_button_container mt-0" style="display: none;">
                                            <button type="submit" class="create_product_btn update_contact" data-oppID="<?php echo $single_opportunity->OpportunityID; ?>">Update</button>
                                            <button type="button" class="create_product_btn cancel_contact" >Cancel</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="" style="padding:15px">
                                    <div class="show_message_closure"></div>
                                    <?php
                                        require plugin_dir_path(__FILE__) . 'crm-erp-action-option.php';
                                    ?>
                                </div>




                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div><!-- .site-main -->
</div><!-- .content-area -->

    <div class="modal opportunity-add-new-notes" id="add-new-opp-notes" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-comment"></i> Notes</h5>
                        <button type="button" class="close-popup-notes close">
                            <span aria-hidden="true">×</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="show_notes_message">
                            <p class="note-response" style="display: none;"></p>
                            <p class="error-response" style="display: none;">Notes is required.</p>
                        </div>
                        
                        <div class="row">
                            <div class="floating col-md-12">
                                <label>Notes:</label>
                                <textarea id="notes-input" class="form-control" rows="5" maxlength="1000"></textarea>
                                <?php if($confidence_user==1){?>
                                Mark As Confidential  <input type="checkbox" name="confidence_user_check" id="confidence_user_check" value="1" style="margin-top:0px;" <?php echo $confidence_check; ?>> 
                                <?php } ?>
                                <p class="remaining-msg" id="remaining-msg" style="float:right;">1000/1000 characters remaining.</p>
                            </div>
                        </div>
                        <div class="buttion_next_prev">
                            <button type="button" class="nxt_btn" id="create-note">Submit</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- edit notes popup -->
 <div class="modal opportunity-add-new-notes" id="edit-new-opp-notes" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalLabel"><i class="fa-regular fa-comment"></i> Notes</h5>
                <button type="button" class="close-popup-notes close">
                    <span aria-hidden="true">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="show_message_popup">
                    <p class="note-response" style="display: none;"></p>
                    <p class="error-response" style="display: none;">Notes is required.</p>
                </div>
                
                <div class="row">
                    <div class="floating col-md-12">
                        <label>Notes:</label>
                        <textarea id="edit-notes-input" maxlength="1000" style="resize:none;margin-bottom:2%;" class="form-control" rows="5"></textarea>
                        <?php if($confidence_user==1){?>
                                Mark As Confidential  <input type="checkbox" name="confidence_user_check" id="edit_confidence_user_check" value="1" style="margin-top:0px;"> 
                        <?php } ?>
                         <p class="edit-remaining-msg" id="edit-remaining-msg" style="float:right;">1000/1000 characters remaining.</p>
                    </div>
                </div>
                <div class="buttion_next_prev">
                    <button type="button" class="nxt_btn" id="update-note" data-note_id="">Submit</button>
                </div>
            </div>
        </div>
    </div>
</div>
        
<?php  require plugin_dir_path(__FILE__) . 'iris-send-erc-agreement.php';  ?>
<!-- <div class="confirm_box_wraper notes_box" id="notes_box" style="display:none;"><a href="javascript:void(0);" class="close_statuspopup" onclick="close_notes_popup()">x</a>
    <div class="confirm_box_inner notes_box_inner" style="padding-bottom:36px;"> <h5>Create Notes:</h5><hr class="popup-hr">
        <label>Notes:</label>
        <textarea id="notes-input" class="form-control">
         </textarea>   
        <br>
        <p class="note-response"></p>
        <input type="button" id="create-note" value="Create" class="btn btn-primary" style="background-color: #0062cc;">
    </div>
</div>  -->




<script>
jQuery(document).ready(function() {
    $(".opp-add-notes").click(function() {
        $('.note-response').html("");
        $('.note-response').css('display', 'none');
        $('.error-response').css('display', 'none');
        $("#add-new-opp-notes").modal('show');
        
    });
    $('.close-popup-notes').click(function() {
        $('.note-response').css('display', 'none');
        $('.error-response').css('display', 'none');
        $("#add-new-opp-notes").modal('hide');
        $("#edit-new-opp-notes").modal('hide');
    });
});
</script>


<script>
jQuery(document).ready(function() {
    $(".send_declaration").click(function() {
        $("#add-new-opp-send-aggrements").modal('show');
        
    });
    $('.close-popup-send-aggrements, .close_btn_sendagreement').click(function() {
        $("#add-new-opp-send-aggrements").modal('hide');
    });

    $('.close-popup-resend-aggrements, .close_btn_resendagreement').click(function() {
        $("#add-new-opp-resend-aggrements").hide();
    });

});
</script>

<script type="text/javascript">
    $(document).ready(function(){

    jQuery(document).on('change','.select-milestone',function(){
        var no = jQuery(this).data('no');
        var val = jQuery(this).val();
        var product_name = $(this).find(":selected").html();
        console.log(product_name);
        var url = "<?php echo site_url().'/wp-json/productsplugin/v1/milestone-status';?>";
        jQuery.ajax({
            type: "POST",
            url: url,
            data: {id:val},
            beforeSend: function() {
                $('#MilestoneStage').html('<option value="">Loading Stages...</option>');
                $('.show_message_milestone').html('');
            },
            success: function(response) {
                jQuery('#MilestoneStage').html('');       
                
               if(response.length!=0){ 
                jQuery.each( response, function (indexes, values){
                    var stageIssue = '';
                    if(values.deleted_at!=null){
                        stageIssue = 'Deleted';
                    }
                    if(values.status=='inactive'){
                        stageIssue = 'Inactive';
                    }
                    stageName = '';
                    stageID = '';
                    if(stageIssue){
                        stageName = values.stage_name+'('+stageIssue+')';
                        stageID = ''
                    }else{
                        stageName = values.stage_name;
                        stageID = values.milestone_stage_id;
                    }
                    if(stageIssue){

                    }else{
                        var optionHTML = `<option value="${stageID}"> ${stageName} </option>`;
                        jQuery('#MilestoneStage').append(optionHTML);
                    }
                           
                });
               }else{

                    jQuery('#MilestoneStage').html(`<option value="">Select Stage</option>`);
               }    
            }
        });            
    });  


        $(document).on('click','.update_opportunity',function(){

        $('.show_message_popup').html('');
        let OpportunityID = $(this).attr('data-oppID');
        let NextStep = $('#NextStep').val();
        let opportunity_description = $('#opportunity_description').val();
        let ExpectedCloseDate = $('#ExpectedCloseDate').val();
        let OpportunityAmount = $('#opportunity_amount').val().replaceAll(',', '');
        let Probability = $('#Probability').val();

        if(ExpectedCloseDate==''){
            $('.show_message_popup').html('<p class="warning_message">Please Select Expected Close Date.</p>'); 
            return; 
        }

        if(OpportunityAmount==0){
            $('.show_message_popup').html('<p class="warning_message">Please Enter Amount.</p>'); 
            return; 
        }

        if(Probability==''){
            Probability = 0;
        }

        if(NextStep==''){
            NextStep = 0;
        }

        if(opportunity_description==''){
            opportunity_description = 0;
        }

        $('.show_message_popup').html('<p class="alert_message">Please Wait</p>');
        jQuery.ajax({
            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-opportunity-optional-field',
            method: 'post',
            data: {
                OpportunityID: OpportunityID,
                NextStep: NextStep,
                opportunity_description: opportunity_description,
                ExpectedCloseDate: ExpectedCloseDate,
                OpportunityAmount: OpportunityAmount,
                Probability: Probability,
            },
            success(res) {
                console.log(res);
                if(res.status==true){
                    $('.show_message_popup').html('<p class="success_message">Opportunity successfully updated</p>');

                    $('#showExpectedCloseDate').html(ExpectedCloseDate);
                    $('#showOpportunityAmount').html(OpportunityAmount);
                    $('#showProbability').html(Probability);
                    $('#showNextStep').html(res.data.NextStepName);
                    $('#showopportunity_description').html(res.data.opportunity_descriptionName);



                    $('.button_container').hide();
                    $('.show_message_milestone').html('');

                    $('#ExpectedCloseDate').attr('disabled', true);
                    $('#opportunity_amount').attr('disabled', true);
                    $('#Probability').attr('disabled', true);
                    $('#NextStep').attr('disabled', true);
                    $('#opportunity_description').attr('disabled', true);

                    setTimeout(function(){
                        $('.show_message_popup').html('');
                        
                        
                    }, 3000);
    

                }else{
                    $('.show_message_popup').html('<p class="warning_message">Something went worng '+res.message+'</p>');
                }
            },

            error: function(jqXHR, textStatus, errorThrown) {        
                    $('.show_message_popup').html('<p class="warning_message">Something went worng.</p>');  
                }
            });
        });



        $(document).on('click','.update_milestone',function(){
        let OpportunityID = $(this).attr('data-oppID');
        let Milestone = $('#Milestone').val();
        let MilestoneStage = $('#MilestoneStage').val();
        var milestoneName = $('#Milestone').find(":selected").text();
        var milestoneStageName = $('#MilestoneStage').find(":selected").text();
        
        let NextStep = $('#NextStep').val();
        let opportunity_description = $('#opportunity_description').val();
        let ExpectedCloseDate = $('#ExpectedCloseDate').val();
        let OpportunityAmount = $('#opportunity_amount').val().replaceAll(',', '');
        let Probability = $('#Probability').val();
        let productID = $('#product_id_hidden').val();
        var user_id = "<?php echo get_current_user_id(); ?>";
        if(Milestone==''){
            $('.show_message_milestone').html('<p class="warning_message">Please select the valid Milestone.</p>'); 
            return;
        }
        if(MilestoneStage==''){
            $('.show_message_milestone').html('<p class="warning_message">Please select the valid stage.</p>'); 
            return;
        }
        if(Probability==''){
            Probability = 0;
        }

        if(NextStep==''){
            NextStep = 0;
        }

        if(opportunity_description==''){
            opportunity_description = 0;
        }
        // By marking milestone close won it will create a project
        let change_milestone=1;

        if(Milestone==116){
        swal({
                title: "Are you sure?",
                text: "Do you want to mark this milestone as 'WON'? This will create a project.",
                icon: "warning",
                buttons: {
                    cancel: "Cancel",
                    confirm: "Ok",
                },
                dangerMode: true,
        })
        .then((willSave) => {
        if (willSave){
            change_milestone=1;
        }else{
            change_milestone=0;
        }
        });   
    }// close won milestone check

        if(change_milestone==1){
        $('.show_message_milestone').html('<p class="alert_message">Please Wait</p>');
        jQuery.ajax({
            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-opportunity-optional-field',
            method: 'post',
            data: {
                OpportunityID: OpportunityID,
                Milestone: Milestone,
                MilestoneStage: MilestoneStage,
                NextStep: NextStep,
                opportunity_description: opportunity_description,
                ExpectedCloseDate: ExpectedCloseDate,
                OpportunityAmount: OpportunityAmount,
                Probability: Probability,
                user_id:user_id,
            },
            success(res) {
                console.log(res);
                if(res.status==true){
                    $('.show_message_milestone').html('<p class="success_message">'+res.message+'</p>');
                    $('#showMilestone').html(milestoneName);
                    $('#showMilestoneStage').html(milestoneStageName);
                    var name = jQuery('#userDetails').attr('data-name');
                    var noteMessage = name+' added a comment: '+name+' changed milestone to '+milestoneName+' and changed stage to '+milestoneStageName;
                    saveOpportunity(noteMessage);
                    // $('.show_message_milestone').html('');
                    $('.show_message_popup').html('');
                    $('.milestone_button_container').hide();
                    $('#Milestone').hide();
                    $('#MilestoneStage').hide();
                    $('#showMilestone').show();
                    $('#showMilestoneStage').show();

                    location.reload();
                    

                }else{
                    $('.show_message_milestone').html('<p class="warning_message">Oops! '+res.message+'</p>');
                    location.reload();
                }
                // location.reload();
            },

            error: function(jqXHR, textStatus, errorThrown) {        
                    $('.show_message_milestone').html('<p class="warning_message">Something went worng.</p>');  
                }
            });
           }// change_milestone
    });

        $(document).on('click','.update_owner',function(){
        var OpportunityID = $(this).attr('data-oppID');
        var OwnerList = $('#OwnerList').val();
        var ownerName = $("#OwnerList option:selected").text(); 
        var lead_id = "<?php echo $lead_id; ?>";
        var product_id = "<?php echo $single_opportunity->product_ID;?>";


        $('.show_message_owner').html('<p class="alert_message">Please Wait</p>');
        jQuery.ajax({
            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-opportunity-project-owner',
                method: 'post',
                data: {
                    lead_id: lead_id,
                    OpportunityID: OpportunityID,
                    OwnerList: OwnerList,
                    product_id:product_id,
                },
            success(res) {
                console.log(res);
                if(res.status==true){
                    $('.show_message_owner').html('<p class="success_message">Owner successfully updated.</p>');

                    $('#showOwnerList').html(ownerName);
                    var name = jQuery('#userDetails').attr('data-name');
                    var noteMessage = name+' added a comment: '+name+' updated owener to '+ownerName;
                    // var noteMessage = name+' added a comment: Owner updated by '+name;
                    saveOpportunity(noteMessage);
                    $('.show_message_milestone').html('');
                    $('.show_message_popup').html('');
                    // $('.show_message_owner').html('');
                    $('.owner_button_container').hide();
                    $('#OwnerList').hide();
                    $('#showOwnerList').show();
                    $('#owner-div-id').addClass('owner-main-div');
                    $('.owner_button_container').hide();

                }else{
                    $('.show_message_owner').html('<p class="warning_message">Something went worng '+res.message+'</p>');
                }
            },

            error: function(jqXHR, textStatus, errorThrown) {        
                    $('.show_message_owner').html('<p class="warning_message">Something went worng.</p>');  
                }
            });
        });

        $(document).on('click','.update_contact',function(){
        let OpportunityID = $(this).attr('data-oppID');
        let ContactList = $('#ContactList').val();
        let NextStep = $('#NextStep').val();
        let opportunity_description = $('#opportunity_description').val();
        let ExpectedCloseDate = $('#ExpectedCloseDate').val();
        let OpportunityAmount = $('#opportunity_amount').val().replaceAll(',', '');
        let Probability = $('#Probability').val();


        if(Probability==''){
            Probability = 0;
        }

        if(NextStep==''){
            NextStep = 0;
        }

        if(opportunity_description==''){
            opportunity_description = 0;
        }

        $('.show_message_contact').html('<p class="alert_message">Please Wait</p>');
        jQuery.ajax({
            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-opportunity-optional-field',
            method: 'post',
            data: {
                OpportunityID: OpportunityID,
                ContactList: ContactList,
                NextStep: NextStep,
                opportunity_description: opportunity_description,
                ExpectedCloseDate: ExpectedCloseDate,
                OpportunityAmount: OpportunityAmount,
                Probability: Probability,
            },
            success(res) {
                console.log(res);
                if(res.status==true){
                    $('.show_message_contact').html('<p class="success_message">Successfully updated</p>');

                    $('#showContactList').html(res.data.ContactName);
                    var name = jQuery('#userDetails').attr('data-name');
                    var noteMessage = name+' added a comment: '+name+' changed contact to '+res.data.ContactName;
                    saveOpportunity(noteMessage);

                    $('.show_message_milestone').html('');
                    $('.show_message_popup').html('');
                    $('.show_message_contact').html('');
                    $('.contact_button_container').hide();
                    $('#ContactList').hide();
                    $('#showContactList').show();
    

                }else{
                    $('.show_message_contact').html('<p class="warning_message">Something went worng '+res.message+'</p>');
                }
            },

            error: function(jqXHR, textStatus, errorThrown) {        
                    $('.show_message_owner').html('<p class="warning_message">Something went worng.</p>');  
                }
            });
        });

        $(document).on('click','.edit_opportunity_data',function(){
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.button_container').toggle();

            $('#ExpectedCloseDate').attr('disabled', (i, v) => !v);
            $('#opportunity_amount').attr('disabled', (i, v) => !v);
            $('#Probability').attr('disabled', (i, v) => !v);
            $('#NextStep').attr('disabled', (i, v) => !v);
            $('#opportunity_description').attr('disabled', (i, v) => !v);
        });
        $(document).on('click','.cancel_opportunity',function(){
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.button_container').hide();

            $('#ExpectedCloseDate').attr('disabled', true);
            $('#opportunity_amount').attr('disabled', true);
            $('#Probability').attr('disabled', true);
            $('#NextStep').attr('disabled', true);
            $('#opportunity_description').attr('disabled', true);

           
        });

        $(document).on('click','.edit_opportunity_milestone',function(){
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.milestone_button_container').toggle();
            $('#Milestone').toggle();
            $('#MilestoneStage').toggle();
            $('#showMilestone').toggle();
            $('#showMilestoneStage').toggle();
            
        });

        $(document).on('click','.cancel_milestone',function(){
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.milestone_button_container').hide();
            $('#Milestone').hide();
            $('#MilestoneStage').hide();
            $('#showMilestone').show();
            $('#showMilestoneStage').show();
            
        });

        $(document).on('click','.edit_opportunity_owner',function(){
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.show_message_owner').html('');
            $('.owner_button_container').toggle();
            $('#OwnerList').toggle();
            $('#showOwnerList').toggle();
            $('#owner-div-id').removeClass('owner-main-div');
            $('.owner_button_container').show();
            
        });

        $(document).on('click','.cancel_owner',function(){
            $('.show_message_milestone').html('');
            $('.show_message_popup').html('');
            $('.show_message_owner').html('');
            $('.owner_button_container').hide();
            $('#OwnerList').hide();
            $('#showOwnerList').show();
            $('#owner-div-id').addClass('owner-main-div');
            $('.owner_button_container').hide();
        });

        $(document).on('click','.edit_opportunity_contact',function(){
            $('.show_message_popup').html('');
            $('.show_message_contact').html('');
            $('.contact_button_container').toggle();
            $('#ContactList').toggle();
            $('#showContactList').toggle();
            
        });

        $(document).on('click','.cancel_contact',function(){
            $('.show_message_popup').html('');
            $('.show_message_contact').html('');
            $('.contact_button_container').hide();
            $('#ContactList').hide();
            $('#showContactList').show();
            
        });

        //note JS
        // jQuery(document).on('click','.create-note-btn',function(){
        //     jQuery('.note-response').html("");
        //     jQuery('#notes_box').css('display','block');
        // });

         // ---------- load notes jquery functionality --
        jQuery(document).on('click', '#load-more-notes', function() {
            jQuery(this).html('Loading..');
            jQuery(this).attr('disabled', true);
            var offset = jQuery('#note-offset').val();
            var new_offset = parseInt(offset)+parseInt(10);
            var total_notes_c = <?php echo $total_notes_count; ?>; 
            var opportunity_id = <?php echo $opportunity_id; ?>; 
            jQuery('#note-offset').val(new_offset);
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/fetch_opportunity_notes',
                method: 'post',
                data: {
                    offset:offset,
                    opportunity_id:opportunity_id
                },
                success(response) {
                    if(new_offset>=total_notes_c){
                       jQuery('#load-more-notes').css('display','none');
                    }
                    jQuery('.notes-listing').append(response);
                    jQuery('#load-more-notes').html('M');
                    jQuery('#load-more-notes').attr('disabled', false);
                },
                error: function(jqXHR, textStatus, errorThrown) {        
                    console.log('Something went worng.');  
                }
            
            });
        });

        //close pop up
        $(document).on('click','.close_statuspopup',function(){
            jQuery('#notes_box').hide();
        });

        jQuery(document).on('keyup', '#notes-input', function() {
            $('.error-response').css('display', 'none');
            $('.note-response').css('display', 'none');
        });

        //create opportunity note

        jQuery(document).on('click', '#create-note', function() {
            // console.log('click');
        var noteInput = jQuery('#notes-input').val();
        var notes = jQuery.trim(noteInput);
        console.log(noteInput);
        if (noteInput == '' || notes.length == 0 ){
            $('.error-response').css('display', 'block');
            $('.note-response').css('display', 'none');
            return false;
        }else {

            var confidence_notes_access = jQuery("#confidence_user_check:checked").val();
            if(typeof confidence_notes_access === "undefined"){
                confidence_notes_access = 0;
                var note_label = 'comment';
            }else{
                confidence_notes_access = 1;
                var note_label = 'CONFIDENTIAL note';
            }

            jQuery(this).html('Creating..');
            jQuery(this).prop('disabled', true);
            var opportunity_id = <?php echo $opportunity_id; ?>; 
            var name = jQuery('#userDetails').attr('data-name');
            var note = name+' added a '+ note_label +': '+noteInput;
            var user_id = <?php echo get_current_user_id();  ?>;
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/add_edit_notes',
                method: 'post',
                dataType: 'Json',
                data: {note:note,opp_product_id:opportunity_id,user_id:user_id,confidence_notes_access:confidence_notes_access,note_type:'opportunity'},
                success(response) {
                    console.log(response);

                    var res = JSON.parse(response);
                    console.log(res);
                    var note_id = res.note_id;
                    <?php
                    $from='UTC';
                    $to='America/New_York';
                    $format='Y-m-d h:i:s A';
                    $date = date("Y-m-d H:i:s");
                    date_default_timezone_set($from);
                    $newDatetime = strtotime($date);
                    date_default_timezone_set($to);
                    $newDatetime = date($format, $newDatetime);
                    date_default_timezone_set('UTC');
                    $datetime = date_create($newDatetime);
                    $time = date_format($datetime, "h:ia");
                    $day = date_format($datetime, " D ");
                    $month = date_format($datetime, " M ");
                    $date = date_format($datetime, "dS,");
                    $year = date_format($datetime, " Y");
                    $actual_date = $time . " on " . $day . $month . $date . $year;
                    ?>
                    var curr_date = "<?php echo $actual_date; ?>";

                    if(confidence_notes_access){
                      var conf_class='confidential-notes-div';
                    }else{
                      var conf_class='';  
                    }        
                    
                    var confidence_user = "<?php echo $confidence_user; ?>";
                    
                    if(confidence_user == 1 && confidence_notes_access==1){ 
                        var not_edit_btn = '<a href="javascript:void(0)" title="Edit Notes" class="edit_self_notes" data-note_id="'+note_id+'"><i class="fa-regular fa-pen-to-square"></i></a><a href="javascript:void(0)" title="Delete Notes" class="delete_self_notes" data-note_id="'+note_id+'"><i class="fa fa-trash" aria-hidden="true"></i></a>';
                    }else{
                        var not_edit_btn = '';
                    }

                    var not_div = '<div class="note-listing-div shadow '+conf_class+'" id="note-list-'+note_id+'"><p class="notes" id="note-'+note_id+'" data-confidece="'+confidence_notes_access+'">'+note+'</p><p class="date-time">('+curr_date+')</p>'+not_edit_btn+'</div>';

                    jQuery('.notes-listing').prepend(not_div);

                    /*
                    jQuery('.notes-listing').prepend(response);
                    var offset = jQuery('#note-offset').val();
                    var new_offset = parseInt(offset)+parseInt(1);
                    jQuery('#note-offset').val(new_offset);
                    <?php
                    $date = date("Y-m-d H:i:s");
                    $datetime = date_create($date);
                    $time = date_format($datetime, "h:ia");
                    $day = date_format($datetime, " D ");
                    $month = date_format($datetime, " M ");
                    $date = date_format($datetime, "dS,");
                    $year = date_format($datetime, " Y ");
                    $actual_date = $time . " on " . $day . $month . $date . $year;
                    ?>
                    var curr_date = "<?php echo $actual_date; ?>";
                    var not_div = '<div class="note-listing-div"><p class="notes">'+note+'</p><p class="date-time">'+curr_date+'</p></div>';
                    */

                    jQuery('#notes-input').val('');
                    jQuery("#confidence_user_check").prop('checked',false);
                    jQuery('.note-response').css('display', 'block');
                    jQuery('.note-response').html("Notes created successfully.");
                    jQuery('#create-note').html('Submit').attr('disabled', false);
                    jQuery('.error-response').css('display', 'none');
                    setTimeout(function(){
                        jQuery('#add-new-opp-notes').modal('hide')}, 2000);
                }
            });
        }
    });

    function storeCloserStatus(OpportunityID,opportunityClosure,NextStep,opportunity_description,ExpectedCloseDate,OpportunityAmount,Probability){
        jQuery.ajax({
        url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-opportunity-optional-field',
        method: 'post',
        data: {
            OpportunityID: OpportunityID,
            opportunityClosure: opportunityClosure,
            NextStep: NextStep,
            opportunity_description: opportunity_description,
            ExpectedCloseDate: ExpectedCloseDate,
            OpportunityAmount: OpportunityAmount,
            Probability: Probability,
        },
        success(res) {
            console.log(res);
            if(res.status==true){
                var opportunity_id = <?php echo $opportunity_id; ?>;
                textopportunityClosure = textopportunityClosure.replace("_", " ");
                var id = jQuery('#userDetails').attr('data-id');
                var name = jQuery('#userDetails').attr('data-name');
                var note = name+' added a comment: '+name+' '+noteMessage;
                var user_id = <?php echo get_current_user_id();  ?>;
                jQuery.ajax({
                    url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/create_opportunity_notes',
                    method: 'post',
                    data: {note:note,opportunity_id:opportunity_id,user_id:user_id},
                    success(response) {
                        jQuery('.notes-listing').prepend(response);
                        $('.show_message_closure').html('<p class="success_message mb-3">'+res.message+'</p>'); 
                        var offset = jQuery('#note-offset').val();
                        var new_offset = parseInt(offset)+parseInt(1);
                        jQuery('#note-offset').val(new_offset);
                        setTimeout(function(){
                            //$('.show_message_closure').html('');
                            location.reload();
                        }, 1500);
                    }
                });
            }else{
                $('.show_message_closure').html('<p class="warning_message mb-3">Something went worng '+res.message+'</p>');
            }
        },

        error: function(jqXHR, textStatus, errorThrown) {        
                $('.show_message_closure').html('<p class="warning_message mb-3">Something went worng.</p>');  
            }
        });
    }

    $(document).on('click','.Opp_Closure',function(){

        if($(this).attr('data-eventtype')!=''){
            $('#agreement_note_msg').html('');
            $(".swal-button--confirm").css("pointer-events", "auto");
            textopportunityClosure = $(this).attr('data-eventtype');
            productID = $('#product_id_hidden').val();
            statusMessage = '';
            actionMessage = '';
            noteMessage = '';
            console.log(textopportunityClosure);
            if(textopportunityClosure =='send_stc_agreement'){
                let opportunityClosure = textopportunityClosure;
                let last_agreement_sent = $(this).attr('data-last_agreement_sent');
                if(last_agreement_sent == ''){
                    statusMessage = 'You want to send the agreement for this opportunity?';
                }else{
                    statusMessage = 'Send Agreement - Are you sure you want to resend the agreement that was previously sent on '+last_agreement_sent+'?';
                }
                
                noteMessage = 'initiated the action to Send Agreement';
                actionMessage = 'Yes';
            }else if(textopportunityClosure =='send_stc_ssf'){
                let opportunityClosure = textopportunityClosure;
                let last_payment_sent = $(this).attr('data-last_payment_sent');
                if(last_payment_sent == ''){
                    statusMessage = 'You want to send the SSF for this opportunity?';
                }else{
                    statusMessage = 'Send SSF - Are you sure you want to resend the SSF that was previously sent on '+last_payment_sent+'?';
                }
                
                noteMessage = 'initiated the action to Send SSF';
                actionMessage = 'Yes';
            }else if(textopportunityClosure =='send_rdc_agreement'){
                statusMessage = 'You want to send the agreement for this opportunity?';
                noteMessage = 'initiated the action to Send Agreement';
                actionMessage = 'Yes';
                $('#erc_send_aggrement_submit').attr('current_status',textopportunityClosure);
                $('#erc_send_aggrement_submit').attr('noteMessage',noteMessage);
                 $("#add-new-opp-send-aggrements").modal('show');
                return;
            }else if(textopportunityClosure =='send_erc_agreement'){
                statusMessage = 'You want to send the agreement for this opportunity?';
                noteMessage = 'initiated the action to Send Agreement';
                actionMessage = 'Yes';
                $('#erc_send_aggrement_submit').attr('current_status',textopportunityClosure);
                $('#erc_send_aggrement_submit').attr('noteMessage',noteMessage);
                 $("#add-new-opp-send-aggrements").modal('show');
                return;
            }else if(textopportunityClosure =='send_ta_agreement'){
                statusMessage = 'Are you sure you want to send the agreement?';
                noteMessage = 'initiated the action to Send Agreement';
                actionMessage = 'Yes';
                $('#erc_send_aggrement_submit').attr('current_status',textopportunityClosure);
                $('#erc_send_aggrement_submit').attr('noteMessage',noteMessage);
            }
            else if(textopportunityClosure =='resend_rdc_agreement'){
                statusMessage = 'You want to resend the agreement for this opportunity?';
                noteMessage = 'initiated the action to Send Agreement';
                actionMessage = 'Yes';
                last_payment_sent_data = $(this).attr('data-last_agreement_sent');
                if(last_payment_sent_data != ''){
                    statusMessage = 'Are you sure you want to resend the agreement that was previously sent on '+last_payment_sent_data+' ?';;
                }
                $('#erc_send_aggrement_submit').attr('current_status',textopportunityClosure);
                $('#erc_send_aggrement_submit').attr('noteMessage',noteMessage);

                  jQuery('.resend_rdc_agreement').html('<i aria-hidden="true" class="fa fa-spinner fa-spin"></i>&nbsp;Sending').prop('disabled',true);
                        var envelop_id = "<?php echo $envelop_id; ?>";
                        var lead_id = "<?php echo $lead_id; ?>";
                        var opportunity_id = <?php echo $opportunity_id; ?>;

                           jQuery.ajax({
                           url: '<?php echo admin_url("admin-ajax.php"); ?>',
                          method: 'post',
                          data: {action:'erc_resend_aggrement_action',lead_id:lead_id,envelope_id:envelop_id},
                            success: function(response) {
                              var last_agreement_sent = "<?php echo date("Y-m-d H:i:s"); ?>";
                              var Milestone = 101;
                              var MilestoneStage = 140;  
                            jQuery.ajax({
                                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-opportunity-optional-field',
                                method: 'post',
                                data: {
                                    OpportunityID: opportunity_id,
                                    opportunityClosure: 'send_agreement',
                                    envelop_id: envelop_id,
                                    Milestone: Milestone,
                                    MilestoneStage: MilestoneStage,
                                    last_agreement_sent:last_agreement_sent,
                                },
                                success(res) {
                                    $('.show_message_closure').html('<p class="success_message mb-3">Resend agreement success.</p>');
                                    $('#resend_success_msg').html('<p class="success_message mb-3">Resend agreement success.</p>').show();
                                    location.reload();
                                }
                            });       
                            },error: function(jqXHR, textStatus, errorThrown) {        
                                    $('.show_message_closure').html('<p class="warning_message mb-3">Unable to void aggrement.</p>');  
                                    return;
                            }
                        });
                        return;
            }
            else if(textopportunityClosure =='resend_ta_agreement'){
                    statusMessage = 'You want to resend the agreement for this opportunity?';
                    noteMessage = 'initiated the action to Resend Agreement';
                    actionMessage = 'Yes';
                // last_payment_sent_data = $(this).attr('data-last_agreement_sent');
                // if(last_payment_sent_data != ''){
                //     statusMessage = 'Are you sure you want to resend the agreement that was previously sent on '+last_payment_sent_data+' ?';
                // }
                $('#erc_send_aggrement_submit').attr('current_status',textopportunityClosure);
                $('#erc_send_aggrement_submit').attr('noteMessage',noteMessage);
            }
            else if(textopportunityClosure =='void_agreement'){
                statusMessage = 'You want to void the agreement for this opportunity?';
                noteMessage = 'initiated the action to Void the Agreement';
                actionMessage = 'Yes';
            }else if(textopportunityClosure =='reopen_opportunity'){
                let opportunityClosure = textopportunityClosure;
                statusMessage = 'You want to reopen the opportunity?';
                noteMessage = 'initiated the action to reopen opportunity';
                actionMessage = 'Yes';
            }else if(textopportunityClosure =='reopen_stc_opportunity'){
                let opportunityClosure = textopportunityClosure;
                statusMessage = 'You want to reopen the opportunity?';
                noteMessage = 'initiated the action to reopen opportunity';
                actionMessage = 'Yes';
            }else if(textopportunityClosure =='reopen_erc_opportunity'){
                let opportunityClosure = 'reopen_opportunity';
                statusMessage = 'You want to reopen the opportunity?';
                noteMessage = 'initiated the action to reopen opportunity';
                actionMessage = 'Yes';
            }else if(textopportunityClosure =='reopen_rdc_opportunity'){
                let opportunityClosure = 'reopen_opportunity';
                statusMessage = 'You want to reopen the opportunity?';
                noteMessage = 'initiated the action to reopen opportunity';
                actionMessage = 'Yes';
            }
              
            swal({
                title: "Are you sure?",
                text: statusMessage,
                icon: "warning",
                buttons: {
                    cancel: "Cancel",
                    confirm: actionMessage,
                },
                
                dangerMode: true,
            })
            
            .then((willSave) => {
                
                if (willSave){

                    $('.show_message_closure').html('');
                    let OpportunityID = <?php echo $opportunity_id; ?>;
                    let opportunityClosure = $(this).attr('data-eventtype');
                    let NextStep = $('#NextStep').val();
                    let opportunity_description = $('#opportunity_description').val();
                    let ExpectedCloseDate = $('#ExpectedCloseDate').val();
                    let OpportunityAmount = $('#opportunity_amount').val().replaceAll(',', '');
                    let Probability = $('#Probability').val();
                    var product_id = "<?php echo $single_opportunity->product_ID;?>";
            
                    if(Probability==''){
                        Probability = 0;
                    }

                    if(NextStep==''){
                        NextStep = 0;
                    }

                    if(opportunity_description==''){
                        opportunity_description = 0;
                    }

                    $('.show_message_closure').html('<p class="alert_message mb-3">Please Wait</p>');
                    
                    if(opportunityClosure == 'send_ta_agreement' || opportunityClosure == 'resend_ta_agreement'){
                            var closure = 'send_agreement';
                            var lead_id = "<?php echo $lead_id; ?>";
                            var opportunity_id = <?php echo $opportunity_id; ?>;
                            jQuery.ajax({
                                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                                method: 'post',
                                dataType:'Json',
                                data: {action:'send_ta_agreement',lead_id:lead_id,opportunity_id:opportunity_id},
                                success(response) {
                                    if(response.send_agreement==0){
                                        swal({
                                            title: "Alert",
                                            text: "Agreement is not in the system; can't send it at the moment.",
                                            icon: "warning",
                                            buttons: {
                                                ok: "Ok",
                                            },
                                            dangerMode: true,
                                        });
                                        location.reload();
                                    }else{
                                    storeCloserStatus(OpportunityID,closure,NextStep,opportunity_description,ExpectedCloseDate,OpportunityAmount,Probability);
                                    }
                                },
                                error: function(jqXHR, textStatus, errorThrown) {        
                                    $('.show_message_closure').html('<p class="warning_message">Unable to send aggrement.</p>');  
                                    return;
                                }
                            });
                        return;
                    }

                    if(opportunityClosure == 'send_stc_agreement'){
                            var lead_id = "<?php echo $lead_id; ?>";
                            var opportunity_id = <?php echo $opportunity_id; ?>;
                            jQuery.ajax({
                                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                                method: 'post',
                                data: {action:'send_stc_agreement_link',lead_id:lead_id,opportunity_id:opportunity_id},
                                success(response) {
                                    storeCloserStatus(OpportunityID,opportunityClosure,NextStep,opportunity_description,ExpectedCloseDate,OpportunityAmount,Probability);
                                },
                                error: function(jqXHR, textStatus, errorThrown) {        
                                    $('.show_message_closure').html('<p class="warning_message">Unable to send aggrement.</p>');  
                                    return;
                                }
                            });


                        return;
                    }
                    
                    if(opportunityClosure =='send_stc_ssf'){
                            
                            var lead_id = "<?php echo $lead_id; ?>";
                            var opportunity_id = <?php echo $opportunity_id; ?>;
                            jQuery.ajax({
                                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                                method: 'post',
                                data: {action:'send_stc_ssf',lead_id:lead_id,opportunity_id:opportunity_id},
                                success(response) {
                                    var data = jQuery.parseJSON(response);
                                    
                                     $('.show_message_closure').html('<p class="success_message mb-3">'+data.message+'</p>');
                                     storeCloserStatus(OpportunityID,opportunityClosure,NextStep,opportunity_description,ExpectedCloseDate,OpportunityAmount,Probability);
                                },
                                error: function(jqXHR, textStatus, errorThrown) {        
                                    $('.show_message_closure').html('<p class="warning_message mb-3">Unable to Resend SSF Link.</p>');  
                                    return;
                                }
                            });
                        return;
                    }

                    if(opportunityClosure =='void_agreement'){
                        var envelop_id = "<?php echo $envelop_id; ?>";
                        var lead_id = "<?php echo $lead_id; ?>";
                           jQuery.ajax({
                            url: 'https://portal.occamsadvisory.com/qb-invoices/public/api/voided_envelop',
                            type: 'POST',
                            dataType:'json',    
                            data: {envelope_id:envelop_id,voided_reason:'Void'},
                            success: function(response) {
                               if(response.code == 201){
                                      jQuery.ajax({
                                        url: '<?php echo admin_url("admin-ajax.php"); ?>',
                                        method: 'post',
                                        data: {envelope_id:envelop_id,action:'void_agreement',lead_id:lead_id},
                                        success(response) {
                                            storeCloserStatus(OpportunityID,opportunityClosure,NextStep,opportunity_description,ExpectedCloseDate,OpportunityAmount,Probability);
                                        }
                                    }); 
                                }else if(response.code == 302){
                                    $('.show_message_closure').html('<p class="warning_message mb-3">Unable to void aggrement.</p>');
                                    return;
                                }else{
                                    $('.show_message_closure').html('<p class="warning_message mb-3">Unable to void aggrement.</p>');
                                    return;
                                } 
                            },
                            error: function(jqXHR, textStatus, errorThrown) {        
                                    $('.show_message_closure').html('<p class="warning_message mb-3">Unable to void aggrement.</p>');  
                                    return;
                            }
                        });

                        return;
                    }
                    /*
                    if(opportunityClosure =='resend_rdc_agreement'){
                        jQuery('.resend_rdc_agreement').html('<i aria-hidden="true" class="fa fa-spinner fa-spin"></i>&nbsp;Sending').prop('disabled',true);
                        var envelop_id = "<?php echo $envelop_id; ?>";
                        var lead_id = "<?php echo $lead_id; ?>";
                        var opportunity_id = <?php echo $opportunity_id; ?>;

                           jQuery.ajax({
                           url: '<?php echo admin_url("admin-ajax.php"); ?>',
                          method: 'post',
                          data: {action:'erc_resend_aggrement_action',lead_id:lead_id,envelope_id:envelop_id},
                            success: function(response) {
                              var last_agreement_sent = "<?php echo date("Y-m-d H:i:s"); ?>";
                              var Milestone = 101;
                              var MilestoneStage = 140;  
                            jQuery.ajax({
                                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/edit-opportunity-optional-field',
                                method: 'post',
                                data: {
                                    OpportunityID: opportunity_id,
                                    opportunityClosure: 'send_agreement',
                                    envelop_id: envelop_id,
                                    Milestone: Milestone,
                                    MilestoneStage: MilestoneStage,
                                    last_agreement_sent:last_agreement_sent,
                                },
                                success(res) {
                                    $('.show_message_closure').html('<p class="success_message mb-3">Resend agreement success.</p>');
                                    location.reload();
                                }
                            });       
                            },error: function(jqXHR, textStatus, errorThrown) {        
                                    $('.show_message_closure').html('<p class="warning_message mb-3">Unable to void aggrement.</p>');  
                                    return;
                            }
                        });
                        return;
                    }*/

                    if(opportunityClosure =='reopen_opportunity' || opportunityClosure =='reopen_erc_opportunity' || opportunityClosure =='reopen_rdc_opportunity'){
                            var envelop_id = "<?php echo $envelop_id; ?>";
                            var lead_id = "<?php echo $lead_id; ?>";
                            var product_id = "<?php echo $single_opportunity->product_ID;?>";
                            var user_id = <?php echo get_current_user_id();  ?>;
                            jQuery.ajax({
                                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/reopen-opportunity',
                                type: 'POST',
                                dataType:'json',    
                                data: {OpportunityID:OpportunityID,product_id:product_id,lead_id:lead_id,user_id:user_id},
                                success: function(response) {
                                    storeCloserStatus(OpportunityID,opportunityClosure,NextStep,opportunity_description,ExpectedCloseDate,OpportunityAmount,Probability);    
                                },
                                error: function(jqXHR, textStatus, errorThrown) {        
                                        $('.show_message_closure').html('<p class="warning_message mb-3">Unable to reopen opportunity.</p>');  
                                        return;
                                }
                            });

                        return;
                    }


                    if(opportunityClosure =='reopen_stc_opportunity'){
                            var envelop_id = "<?php echo $envelop_id; ?>";
                            var lead_id = "<?php echo $lead_id; ?>";
                            jQuery.ajax({
                                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/reopen-stc-opportunity',
                                type: 'POST',
                                dataType:'json',    
                                data: {OpportunityID:OpportunityID},
                                success: function(response) {
                                    storeCloserStatus(OpportunityID,opportunityClosure,NextStep,opportunity_description,ExpectedCloseDate,OpportunityAmount,Probability);    
                                },
                                error: function(jqXHR, textStatus, errorThrown) {        
                                        $('.show_message_closure').html('<p class="warning_message mb-3">Unable to reopen opportunity.</p>');  
                                        return;
                                }
                            });


                        return;
                    }
     

                storeCloserStatus(OpportunityID,opportunityClosure,NextStep,opportunity_description,ExpectedCloseDate,OpportunityAmount,Probability);

                }else{
                    
                }
                //$('.Opp_Closure').val('').change();
                $('.swal-modal').removeClass('crm-erp-manage-opportunities-delete-swal');
               
            });
            
            $('.swal-modal').addClass('crm-erp-manage-opportunities-delete-swal');

        }
    });

    
    $('.Opp_Closures').each(function(index,item){
        if(!$(item).hasClass('hidebutton')){
                $(item).parent().css('border','1px solid #d8d8d882');
                $(item).parent().css('box-shadow','5px 5px 10px 0px rgb(178 178 178 / 40%)');
        }
    });   

    //convert input international comma saparated
    $('#opportunity_amount').on('input', function() {
        let inputValue = $(this).val();
        inputValue = inputValue.replace(/[^0-9.]/g, '');
        let [integerPart, decimalPart] = inputValue.split('.');
        integerPart = Number(integerPart).toLocaleString('en-US');
        let result = `${integerPart}`;
        if (decimalPart !== undefined) {
            result += `.${decimalPart}`;
        }
        $(this).val(result);
    }); 


    jQuery('#ExpectedCloseDate').datetimepicker({
            format: 'm/d/Y',
            autoclose: true,
            orientation: 'bottom',
            timepicker: false,
            autocomplete: 'off'
    });


    


    });

    jQuery(document).on('click','.resend_agreement_button',function(){
            jQuery(".resend-aggrements-add-new").show();    
    });    

    function saveOpportunity(noteMessage){
        var opportunity_id = <?php echo $opportunity_id; ?>;
        var id = jQuery('#userDetails').attr('data-id');
        var name = jQuery('#userDetails').attr('data-name');
        var note = noteMessage;
        var user_id = <?php echo get_current_user_id();  ?>;
        jQuery.ajax({
            url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/create_opportunity_notes',
            method: 'post',
            data: {note:note,opportunity_id:opportunity_id,user_id:user_id},
            success(response) {
                jQuery('.notes-listing').prepend(response);
                var offset = jQuery('#note-offset').val();
                var new_offset = parseInt(offset)+parseInt(1);
                jQuery('#note-offset').val(new_offset);
            }
        });
    }
</script>
<script type="text/javascript">

    $(document).ready(function(){
    var AllNextSteps = <?php echo $allNextStep ?>;

  $("#NextStep").on("keyup", function() {
    var inputVal = $(this).val();
    var dropdown = $("#nextStepDropdown");
    dropdown.empty();
    if (inputVal.length) {
      var matches = AllNextSteps.filter(function(singleNextSteps) {
        return singleNextSteps.toLowerCase().startsWith(inputVal.toLowerCase());
      });
      let counter = 1;
      matches.forEach(function(match) {
        if(counter<6){
            dropdown.append("<a href='javascript:void(0)'>" + match + "</a>");
        }
        counter = counter + 1;
      });
      if(counter>1){
        dropdown.addClass("showNextStep");
      }
    } else {
      dropdown.removeClass("showNextStep");
    }
  });

  $(document).on("click", ".next-step-dropdown a", function() {
    $("#NextStep").val($(this).text());
    $("#nextStepDropdown").removeClass("showNextStep");
  });

  $(document).click(function(e) {
    if (!$(e.target).closest(".autocompleteNextStep").length) {
      $(".next-step-dropdown").removeClass("showNextStep");
    }
  });
});
</script>



<!-- edit delete notes script -->
<script type="text/javascript">
    jQuery(document).ready(function(){
        jQuery('#notes-input').keypress(function (event) {
            var Length = jQuery("#notes-input").val().length;
              maxLen = 1000;
            if (Length >= maxLen) {
                if (event.which != 8) {
                    //swal("Error", "Only 70 characters are allowed", "error");
                    return false;
                }
            }
        });

            const maxLength = 1000;
            $('#notes-input').on('input', function() {
                const remainingChars = maxLength - $(this).val().length;
                $('#remaining-msg').text(remainingChars + '/1000 characters remaining.');
            });

            
            jQuery('#edit-notes-input').keypress(function (event) {
            var Length = jQuery("#edit-notes-input").val().length;
              maxLen = 1000-Length;
            if (Length >= maxLen) {
                if (event.which != 8) {
                    //swal("Error", "Only 70 characters are allowed", "error");
                    return false;
                }
            }
        });

            $('#edit-notes-input').on('input', function() {
                const remainingChars = maxLength - $(this).val().length;
                $('#edit-remaining-msg').text(remainingChars + '/1000 characters remaining.');
            });

    });

    jQuery(document).on('click', '.delete_self_notes', function() {
                swal({
                    title: "Are you sure?",
                    text: "Do you want to delete this notes?",
                    icon: "warning",
                    buttons: {
                        cancel: "No",
                        confirm: {
                          text: "Yes",
                          value: true,
                          visible: true,
                          className: "",
                          closeModal: true
                        }
                      },
                      dangerMode: true,     
                }).then((isConfirm) => {
                    if(isConfirm){
                    var note_id = jQuery(this).data('note_id');
                    var ajax_url = '<?php echo admin_url('admin-ajax.php'); ?>';
                    jQuery.ajax({
                      url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/delete_notes',
                      method: 'post',
                      dataType: 'json',
                      data: {note_id:note_id,note_type:'opportunity'},
                      success: function(response) {
                        jQuery('#note-list-'+note_id).hide();
                        swal({
                          title: 'Success!',
                          text: 'Notes deleted successfully!',
                          icon: 'success'
                        });
                    }
                    });
                    }
                });
    });            
     
     // on click edit notes
     jQuery(document).on('click', '.edit_self_notes', function() {
        jQuery('.note-response').hide();
        jQuery('.error-response').hide();
        jQuery('#update-note').html('Update');
         jQuery('#update-note').prop('disabled', false);    

        var note_id = jQuery(this).data('note_id');
        var notes = jQuery('#note-'+note_id).html();
        var all_notes = notes.split(':');
        var new_notes = '';
        var note_prefix = '';

        if (typeof all_notes[0] !== 'undefined') {
            note_prefix = all_notes[0];
        }

        if (typeof all_notes[1] !== 'undefined') {
                new_notes = new_notes + all_notes[1];
        }

        if(typeof all_notes[2] !== 'undefined'){
            new_notes = new_notes + all_notes[2];
        }    

        if(new_notes == ''){
            new_notes = notes;
        }    
        
        notes = new_notes;
        
        notes = jQuery.trim( notes );

        var remainingChars = 1000 - notes.length;
        $('#edit-remaining-msg').text(remainingChars + '/1000 characters remaining.');
        
        
        var data_confidece = jQuery('#note-'+note_id).attr('data-confidece');
        console.log(data_confidece);
        jQuery('#update-note').data('note_id',note_id); 
        jQuery("#edit-notes-input").val(notes);
        jQuery("#edit-notes-input").data('note_prefix',note_prefix);
        
        if(data_confidece==1){
            jQuery("#edit_confidence_user_check").prop('checked',true);
        }else{
            jQuery("#edit_confidence_user_check").prop('checked',false);
        }    
        jQuery("#edit-new-opp-notes").modal('show'); 
     });   

     // on submit update notes
     jQuery(document).on('click', '#update-note', function() {
        var note_id = jQuery(this).data('note_id');
        var opportunity_id = <?php echo $opportunity_id; ?>; 
        var note = jQuery('#edit-notes-input').val();
        var notes = jQuery.trim(note);
        if (note == '' || notes.length == 0){
            $('.error-response').css('display', 'block');
            $('.note-response').css('display', 'none');
            return false;
    }else{
        
        var note_prefix = jQuery('#edit-notes-input').data('note_prefix');

        var user_id = <?php echo get_current_user_id();  ?>;    
        var confidence_notes_access = jQuery("#edit_confidence_user_check:checked").val();
        if(typeof confidence_notes_access === "undefined"){
            confidence_notes_access = 0;
            var note_type = 'comment';
            note_prefix = note_prefix.replace("added a CONFIDENTIAL note", "added a comment");
        }else{
            confidence_notes_access = 1;
            var note_type = 'CONFIDENTIAL note';
            note_prefix = note_prefix.replace("added a comment","added a CONFIDENTIAL note");
        }


        jQuery(this).html('Updating..');
         jQuery(this).prop('disabled', true);
         note = note_prefix +" : "+ note;
         // var name = jQuery('#userDetails').attr('data-name');
         // var note = name+' added a '+note_type+' : '+note;
        // var data = {action:'edit_self_notes',note_id:note_id,note_val:note,user_id:user_id,confidence_notes_access:confidence_notes_access};
             
        var ajax_url = '<?php echo admin_url('admin-ajax.php'); ?>';
            jQuery.ajax({
                url: '<?php echo get_site_url();  ?>/wp-json/productsplugin/v1/add_edit_notes',
                method: 'post',
                dataType: 'Json',
                data: {note:note,opp_product_id:opportunity_id,user_id:user_id,confidence_notes_access:confidence_notes_access,note_type:'opportunity',note_id:note_id},
              success: function(response) {
                
                jQuery('#note-'+note_id).html(note);
                

                if(confidence_notes_access==0){
                    jQuery('#note-list-'+note_id).removeClass('confidential-notes-div');
                    jQuery('#note-'+note_id).attr('data-confidece','0');
                }else{
                    jQuery('#note-list-'+note_id).addClass('confidential-notes-div');
                    jQuery('#note-'+note_id).attr('data-confidece','1');
                }
                    swal({
                      title: 'Success!',
                      text: 'Notes updated successfully!',
                      icon: 'success'
                    });
                    $('.close-popup-notes').trigger('click');
                    jQuery(this).html('Update');
                    jQuery(this).prop('disabled', false);
              }
        });
     }
   });

            // Audit log script
        jQuery(document).on("click","#pills-audit-logs",function(){
            var opportunity_id = <?php echo $opportunity_id; ?>; 
            var product_id = "<?php echo $single_opportunity->product_ID; ?>";
            var lead_id = "<?php echo $lead_id; ?>";

            jQuery(".update_project").hide();
            jQuery.ajax({
                url: '<?php echo admin_url("admin-ajax.php"); ?>',
                method: 'post',
                data: {
                    action: "opportunity_audit_logs", 
                    opportunity_id: opportunity_id,
                    product_id:product_id,
                    lead_id:lead_id,
                },
                success: function(response) {
                    jQuery("#pills-logs").html(response);
                    jQuery("#opportunity_audit_log_table").DataTable({"ordering": false});
                    jQuery("#opportunity_milestone_log_table").DataTable({"ordering": false});
                },
                error: function () {
                    console.log("Error in getting document data");
                }
            });
        })

        // on tab click audit log hide right section and comment
        jQuery(document).on('click', '.tab-btn', function(){
            var id = jQuery(this).attr('id');
            if(id =='pills-audit-logs'){
                jQuery("#right_section").hide();
                jQuery(".custom-opportunity-notes").hide();
                jQuery("#left_section").removeClass('col-md-8');
                jQuery("#left_section").addClass('col-md-12');
            }else{
                jQuery("#right_section").show();
                jQuery(".custom-opportunity-notes").show();
                jQuery("#left_section").removeClass('col-md-12');
                jQuery("#left_section").addClass('col-md-8');
            }
        });
        
        jQuery(document).on("click","#eleve-opportunity-tab",function(){
            jQuery(".update_project").show();
        });
            
</script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.8.1/css/bootstrap-select.css">
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-select/1.8.1/js/bootstrap-select.js"></script>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.min.js" ></script> -->
