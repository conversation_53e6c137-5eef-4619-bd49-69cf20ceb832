<?php
global $wpdb;
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);
$record = 1;
$project_id = $_POST['project_id'];
$lead_id = $_POST['lead_id'];
if(isset($project_id)){
	
	if(isset($_POST['review_status'])){
		$old_datas = $wpdb->get_row("SELECT review_status FROM {$wpdb->prefix}projects WHERE project_id=$project_id");

			$wpdb->query("UPDATE {$wpdb->prefix}projects SET review_status = '".$_POST['review_status']."' WHERE project_id=$project_id");
		     	$table = $wpdb->prefix.'projects';
		     	log_audit_entry($project_id, $old_datas , array('review_status'=>$_POST['review_status']) , $table);		
	}

	if(isset($_POST['review_link'])){
		$old_datas = $wpdb->get_row("SELECT review_link FROM {$wpdb->prefix}projects WHERE project_id=$project_id");
		$wpdb->query("UPDATE {$wpdb->prefix}projects SET review_link='".$_POST['review_link']."' WHERE project_id=$project_id");
		$table = $wpdb->prefix.'projects';
		log_audit_entry($project_id, $old_datas , array('review_link'=>$_POST['review_link']) , $table);		
	}

	//check project intake info exist for erc product
	$intake_info = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}erc_erc_intake WHERE lead_id = ".$lead_id."");
	if($_POST['fee_type'] == 'N/A'){
		$fee_type = $_POST['custom_fee'];
	}else{
		$fee_type = $_POST['fee_type'];
	}
	if($_POST['welcome_email'] != ''){
		$welcome_email = date('m/d/Y',strtotime($_POST['welcome_email']));
	}else{
		$welcome_email = '';
	}
	if($_POST['retainer_payment_date'] != ''){
		$retainer_payment_date = date('m/d/Y',strtotime($_POST['retainer_payment_date']));
	}else{
		$retainer_payment_date = '';
	}
	if($_POST['retainer_payment_cleared'] != ''){
		$retainer_payment_cleared = date('m/d/Y',strtotime($_POST['retainer_payment_cleared']));
	}else{
		$retainer_payment_cleared = '';
	}
	if($_POST['retainer_payment_returned'] != ''){
		$retainer_payment_returned = date('m/d/Y',strtotime($_POST['retainer_payment_returned']));
	}else{
		$retainer_payment_returned = '';
	}
	if($_POST['retainer_refund_date'] != ''){
		$retainer_refund_date = date('m/d/Y',strtotime($_POST['retainer_refund_date']));
	}else{
		$retainer_refund_date = '';
	}
	if($_POST['bal_retainer_sent_date'] != ''){
		$bal_retainer_sent_date = date('m/d/Y',strtotime($_POST['bal_retainer_sent_date']));
	}else{
		$bal_retainer_sent_date = '';
	}
	if($_POST['bal_retainer_pay_date'] != ''){
		$bal_retainer_pay_date = date('m/d/Y',strtotime($_POST['bal_retainer_pay_date']));
	}else{
		$bal_retainer_pay_date = '';
	}
	if($_POST['bal_retainer_clear_date'] != ''){
		$bal_retainer_clear_date = date('m/d/Y',strtotime($_POST['bal_retainer_clear_date']));
	}else{
		$bal_retainer_clear_date = '';
	}
	if($_POST['bal_retainer_return_date'] != ''){
		$bal_retainer_return_date = date('m/d/Y',strtotime($_POST['bal_retainer_return_date']));
	}else{
		$bal_retainer_return_date = '';
	}
	if($_POST['ppp_1_date'] != ''){
		$ppp_1_date = date('m/d/Y',strtotime($_POST['ppp_1_date']));
	}else{
		$ppp_1_date = '';
	}
	if($_POST['ppp_1_forgive_app_date'] != ''){
		$ppp_1_forgive_app_date = date('m/d/Y',strtotime($_POST['ppp_1_forgive_app_date']));
	}else{
		$ppp_1_forgive_app_date = '';
	}
	if($_POST['ppp_2_date'] != ''){
		$ppp_2_date = date('m/d/Y',strtotime($_POST['ppp_2_date']));
	}else{
		$ppp_2_date = '';
	}
	if($_POST['ppp_2_forgive_app_date'] != ''){
		$ppp_2_forgive_app_date = date('m/d/Y',strtotime($_POST['ppp_2_forgive_app_date']));
	}else{
		$ppp_2_forgive_app_date = '';
	}
	if($_POST['call_date'] != ''){
		$call_date = date('m/d/Y',strtotime($_POST['call_date']));
	}else{
		$call_date = '';
	}
	if($_POST['memo_received_date'] != ''){
		$memo_received_date = date('m/d/Y',strtotime($_POST['memo_received_date']));
	}else{
		$memo_received_date = '';
	}
	if($_POST['memo_cut_off_date'] != ''){
		$memo_cut_off_date = date('m/d/Y',strtotime($_POST['memo_cut_off_date']));
	}else{
		$memo_cut_off_date = '';
	}
	
	if(!empty($intake_info)){
							
			$new_intake_data = array();
			
	       if(isset($_POST['w2_employees_count'])){		
			$new_intake_data['w2_employees_count'] = $_POST['w2_employees_count'];
			$new_intake_data['initial_retain_fee_amount'] = $_POST['initial_retain_fee_amount'];
			$new_intake_data['w2_ee_difference_count'] = $_POST['w2_ee_difference_count'];
			$new_intake_data['balance_retainer_fee'] = $_POST['balance_retainer_fee'];
			$new_intake_data['total_max_erc_amount'] = $_POST['total_max_erc_amount'];
			$new_intake_data['total_estimated_fees'] = $_POST['total_estimated_fees'];
			$new_intake_data['affiliate_referral_fees'] = $_POST['affiliate_referral_fees'];
			$new_intake_data['sdgr'] = $_POST['sdgr'];								   
		    $new_intake_data['avg_emp_count_2019'] = $_POST['avg_emp_count_2019'];
			$new_intake_data['fee_type'] = $fee_type;
			$new_intake_data['company_folder_link'] = $_POST['company_folder_link'];
			$new_intake_data['document_folder_link'] = $_POST['document_folder_link'];
			$new_intake_data['eligible_quarters'] = $_POST['eligible_quarters'];
			$new_intake_data['retainer_invoice_no'] = $_POST['retainer_invoice_no'];
			$new_intake_data['welcome_email'] = $welcome_email;
			$new_intake_data['retainer_payment_date'] = $retainer_payment_date;
			$new_intake_data['retainer_payment_cleared'] = $retainer_payment_cleared;
			$new_intake_data['retainer_payment_returned'] = $retainer_payment_returned;
			$new_intake_data['retpayment_return_reason'] = $_POST['retpayment_return_reason'];
			$new_intake_data['retainer_refund_date'] = $retainer_refund_date;
			$new_intake_data['retainer_refund_amount'] = $_POST['retainer_refund_amount'];
			$new_intake_data['retainer_payment_amount'] = $_POST['retainer_payment_amount'];
			$new_intake_data['retainer_payment_type'] = $_POST['retainer_payment_type'];
			$new_intake_data['bal_retainer_invoice_no'] = $_POST['bal_retainer_invoice_no'];
			$new_intake_data['bal_retainer_sent_date'] = $bal_retainer_sent_date;
			$new_intake_data['bal_retainer_pay_date'] = $bal_retainer_pay_date;
			$new_intake_data['bal_retainer_clear_date'] = $bal_retainer_clear_date;
			$new_intake_data['bal_retainer_return_date'] = $bal_retainer_return_date;
			$new_intake_data['bal_retainer_return_reaso'] = $_POST['bal_retainer_return_reaso'];
			$new_intake_data['coi_aoi'] = $_POST['coi_aoi'];
			$new_intake_data['voided_check'] = $_POST['voided_check'];
			$new_intake_data['2019_tax_return'] = $_POST['2019_tax_return'];
			$new_intake_data['2020_tax_return'] = $_POST['2020_tax_return'];
			$new_intake_data['2021_financials'] = $_POST['2021_financials'];
			$new_intake_data['2020_q1_4144'] = $_POST['2020_q1_941'];
			$new_intake_data['2020_q2_4145'] = $_POST['2020_q2_941'];
			$new_intake_data['2020_q3_4146'] = $_POST['2020_q3_941'];
			$new_intake_data['2020_q4_4147'] = $_POST['2020_q4_941'];
			$new_intake_data['2021_q1_4149'] = $_POST['2021_q1_941'];
			$new_intake_data['2021_q2_4151'] = $_POST['2021_q2_941'];
			$new_intake_data['2021_q3_4152'] = $_POST['2021_q3_941'];
			$new_intake_data['2020_q1_4155'] = $_POST['2020_q1_payroll'];
			$new_intake_data['2020_q2_4156'] = $_POST['2020_q2_payroll'];
			$new_intake_data['2020_q3_4157'] = $_POST['2020_q3_payroll'];
			$new_intake_data['2020_q4_4158'] = $_POST['2020_q4_payroll'];
			$new_intake_data['2021_q1_4160'] = $_POST['2021_q1_payroll'];
			$new_intake_data['2021_q2_4161'] = $_POST['2021_q2_payroll'];
			$new_intake_data['2021_q3_4162'] = $_POST['2021_q3_payroll'];
			$new_intake_data['f911_status'] = $_POST['f911_status'];
			$new_intake_data['ppp_1_applied'] = $_POST['ppp_1_applied'];
			$new_intake_data['ppp_1_date'] = $ppp_1_date;
			$new_intake_data['ppp_1_forgiveness_applied'] = $_POST['ppp_1_forgiveness_applied'];
			$new_intake_data['ppp_1_forgive_app_date'] = $ppp_1_forgive_app_date;
			$new_intake_data['ppp_1_amount'] = $_POST['ppp_1_amount'];
			$new_intake_data['ppp_1_wages_allocated'] = $_POST['ppp_1_wages_allocated'];
			$new_intake_data['ppp_2_applied'] = $_POST['ppp_2_applied'];
			$new_intake_data['ppp_2_date'] = $ppp_2_date;
			$new_intake_data['ppp_2_forgiveness_applied'] = $_POST['ppp_2_forgiveness_applied'];
			$new_intake_data['ppp_2_forgive_app_date'] = $ppp_2_forgive_app_date;
			$new_intake_data['ppp_2_amount'] = $_POST['ppp_2_amount'];
			$new_intake_data['ppp_2_wages_allocated'] = $_POST['ppp_2_wages_allocated'];
			$new_intake_data['additional_comments'] = $_POST['additional_comments'];
			$new_intake_data['attorney_name'] = $_POST['attorney_name'];
			$new_intake_data['call_date'] = $call_date;
			$new_intake_data['call_time'] = $_POST['call_time'];
			$new_intake_data['memo_received_date'] = $memo_received_date;
			$new_intake_data['memo_cut_off_date'] = $memo_cut_off_date;
			$new_intake_data['fpso_fees'] = $_POST['fpso_fees'];		
			$new_intake_data['opportunity_size'] = $_POST['opportunity_size'];
			$new_intake_data['opportunity_timeline'] = $_POST['opportunity_timeline'];
			$new_intake_data['confidance_label'] = $_POST['confidance_label'];		
			$new_intake_data['interest_percentage'] = $_POST['interest_percentage'];
			$new_intake_data['net_no'] = $_POST['net_no'];		

		     	$int_table = $wpdb->prefix.'erc_erc_intake';
		     	log_audit_entry($lead_id, $intake_info, $new_intake_data , $int_table);		
		      

	   	        $wpdb->query("UPDATE {$wpdb->prefix}erc_erc_intake SET 
	                           w2_employees_count = '".$_POST['w2_employees_count']."',
	                           initial_retain_fee_amount = '".$_POST['initial_retain_fee_amount']."',
	                           w2_ee_difference_count = '".$_POST['w2_ee_difference_count']."',
	                           balance_retainer_fee = '".$_POST['balance_retainer_fee']."',
	                           total_max_erc_amount = '".$_POST['total_max_erc_amount']."',
	                           total_estimated_fees = '".$_POST['total_estimated_fees']."',
	                           affiliate_referral_fees = '".$_POST['affiliate_referral_fees']."',
	                           sdgr = '".$_POST['sdgr']."',								   
	                           avg_emp_count_2019 = '".$_POST['avg_emp_count_2019']."',
	                           fee_type = '".$fee_type."',
	                           company_folder_link = '".$_POST['company_folder_link']."',
	                           document_folder_link = '".$_POST['document_folder_link']."',
	                           eligible_quarters = '".$_POST['eligible_quarters']."',
	                           retainer_invoice_no = '".$_POST['retainer_invoice_no']."',
	                           welcome_email = '".$welcome_email."',
	                           retainer_payment_date = '".$retainer_payment_date."',
	                           retainer_payment_cleared = '".$retainer_payment_cleared."',
	                           retainer_payment_returned = '".$retainer_payment_returned."',
	                           retpayment_return_reason = '".$_POST['retpayment_return_reason']."',
	                           retainer_refund_date = '".$retainer_refund_date."',
	                           retainer_refund_amount = '".$_POST['retainer_refund_amount']."',
	                           retainer_payment_amount = '".$_POST['retainer_payment_amount']."',
	                           retainer_payment_type = '".$_POST['retainer_payment_type']."',
	                           bal_retainer_invoice_no = '".$_POST['bal_retainer_invoice_no']."',
	                           bal_retainer_sent_date = '".$bal_retainer_sent_date."',
	                           bal_retainer_pay_date = '".$bal_retainer_pay_date."',
	                           bal_retainer_clear_date = '".$bal_retainer_clear_date."',
	                           bal_retainer_return_date = '".$bal_retainer_return_date."',
	                           bal_retainer_return_reaso = '".$_POST['bal_retainer_return_reaso']."',
	                           coi_aoi = '".$_POST['coi_aoi']."',
	                           voided_check = '".$_POST['voided_check']."',
	                           2019_tax_return = '".$_POST['2019_tax_return']."',
	                           2020_tax_return = '".$_POST['2020_tax_return']."',
	                           2021_financials = '".$_POST['2021_financials']."',
	                           2020_q1_4144 = '".$_POST['2020_q1_941']."',
	                           2020_q2_4145 = '".$_POST['2020_q2_941']."',
	                           2020_q3_4146 = '".$_POST['2020_q3_941']."',
	                           2020_q4_4147 = '".$_POST['2020_q4_941']."',
	                           2021_q1_4149 = '".$_POST['2021_q1_941']."',
	                           2021_q2_4151 = '".$_POST['2021_q2_941']."',
	                           2021_q3_4152 = '".$_POST['2021_q3_941']."',
	                           2020_q1_4155 = '".$_POST['2020_q1_payroll']."',
	                           2020_q2_4156 = '".$_POST['2020_q2_payroll']."',
	                           2020_q3_4157 = '".$_POST['2020_q3_payroll']."',
	                           2020_q4_4158 = '".$_POST['2020_q4_payroll']."',
	                           2021_q1_4160 = '".$_POST['2021_q1_payroll']."',
	                           2021_q2_4161 = '".$_POST['2021_q2_payroll']."',
	                           2021_q3_4162 = '".$_POST['2021_q3_payroll']."',
	                           f911_status = '".$_POST['f911_status']."',
	                           ppp_1_applied = '".$_POST['ppp_1_applied']."',
	                           ppp_1_date = '".$ppp_1_date."',
	                           ppp_1_forgiveness_applied = '".$_POST['ppp_1_forgiveness_applied']."',
	                           ppp_1_forgive_app_date = '".$ppp_1_forgive_app_date."',
	                           ppp_1_amount = '".$_POST['ppp_1_amount']."',
	                           ppp_1_wages_allocated = '".$_POST['ppp_1_wages_allocated']."',
	                           ppp_2_applied = '".$_POST['ppp_2_applied']."',
	                           ppp_2_date = '".$ppp_2_date."',
	                           ppp_2_forgiveness_applied = '".$_POST['ppp_2_forgiveness_applied']."',
	                           ppp_2_forgive_app_date = '".$ppp_2_forgive_app_date."',
	                           ppp_2_amount = '".$_POST['ppp_2_amount']."',
	                           ppp_2_wages_allocated = '".$_POST['ppp_2_wages_allocated']."',
	                           additional_comments = '".$_POST['additional_comments']."',
	                           attorney_name = '".$_POST['attorney_name']."',
	                           call_date = '".$call_date."',
	                           call_time = '".$_POST['call_time']."',
	                           memo_received_date = '".$memo_received_date."',
	                           memo_cut_off_date = '".$memo_cut_off_date."',
							   fpso_fees = '".$_POST['fpso_fees']."',
	                           opportunity_size = '".$_POST['opportunity_size']."',
	                           opportunity_timeline = '".$_POST['opportunity_timeline']."',
	                           confidance_label = '".$_POST['confidance_label']."',
	                           interest_percentage = '".$_POST['interest_percentage']."',
	                           net_no = '".$_POST['net_no']."'
	                           WHERE lead_id = ".$lead_id."
	                        ");
			}// isset			

	}else{
		$wpdb->query($wpdb->prepare(
			"INSERT INTO {$wpdb->prefix}erc_erc_intake (
				lead_id,w2_employees_count, initial_retain_fee_amount,
				w2_ee_difference_count, balance_retainer_fee, total_max_erc_amount, total_estimated_fees,
				affiliate_referral_fees, sdgr, avg_emp_count_2019, fee_type, company_folder_link,
				document_folder_link, eligible_quarters, welcome_email, retainer_payment_date,
				retainer_payment_cleared, retainer_payment_returned, retpayment_return_reason,
				retainer_refund_date, retainer_refund_amount, retainer_payment_amount, retainer_payment_type,
				bal_retainer_pay_date, bal_retainer_clear_date, bal_retainer_return_date,
				bal_retainer_return_reaso, coi_aoi, voided_check, 2019_tax_return, 2020_tax_return,
				2021_financials, 2020_q1_4144, 2020_q2_4145, 2020_q3_4146, 2020_q4_4147,
				2021_q1_4149, 2021_q2_4151, 2021_q3_4152, 2020_q1_4155, 2020_q2_4156,
				2020_q3_4157, 2020_q4_4158, 2021_q1_4160, 2021_q2_4161, 2021_q3_4162,f911_status,				
				ppp_1_applied, ppp_1_date, ppp_1_forgiveness_applied, ppp_1_forgive_app_date, ppp_1_amount,
				ppp_1_wages_allocated, ppp_2_applied, ppp_2_date, ppp_2_forgiveness_applied,
				ppp_2_forgive_app_date, ppp_2_amount, ppp_2_wages_allocated, additional_comments, attorney_name, call_date, call_time, memo_received_date, memo_cut_off_date,fpso_fees,interest_percentage,net_no 
			) VALUES (
				".$lead_id.",'".$_POST['w2_employees_count']."', '".$_POST['initial_retain_fee_amount']."',
				'".$_POST['w2_ee_difference_count']."', '".$_POST['balance_retainer_fee']."', '".$_POST['total_max_erc_amount']."', '".$_POST['total_estimated_fees']."',
				'".$_POST['affiliate_referral_fees']."', '".$_POST['sdgr']."', '".$_POST['avg_emp_count_2019']."', '".$fee_type."', '".$_POST['company_folder_link']."',
				'".$_POST['document_folder_link']."', '".$_POST['eligible_quarters']."', '".$welcome_email."', '".$retainer_payment_date."',
				'".$retainer_payment_cleared."', '".$retainer_payment_returned."', '".$_POST['retpayment_return_reason']."',
				'".$retainer_refund_date."', '".$_POST['retainer_refund_amount']."', '".$_POST['retainer_payment_amount']."', '".$_POST['retainer_payment_type']."',
				'".$bal_retainer_pay_date."', '".$bal_retainer_clear_date."', '".$bal_retainer_return_date."',
				'".$_POST['bal_retainer_return_reaso']."', '".$_POST['coi_aoi']."', '".$_POST['voided_check']."', '".$_POST['2019_tax_return']."', '".$_POST['2020_tax_return']."', 
				'".$_POST['2021_financials']."', '".$_POST['2020_q1_941']."', '".$_POST['2020_q2_941']."', '".$_POST['2020_q3_941']."', '".$_POST['2020_q4_941']."',
				'".$_POST['2021_q1_941']."', '".$_POST['2021_q2_941']."', '".$_POST['2021_q3_941']."', '".$_POST['2020_q1_payroll']."', '".$_POST['2020_q2_payroll']."',
				'".$_POST['2020_q3_payroll']."', '".$_POST['2020_q4_payroll']."', '".$_POST['2021_q1_payroll']."', '".$_POST['2021_q2_payroll']."', '".$_POST['2021_q3_payroll']."',		'".$_POST['f911_status']."',		
				'".$_POST['ppp_1_applied']."', '".$ppp_1_date."', '".$_POST['ppp_1_forgiveness_applied']."', '".$ppp_1_forgive_app_date."', '".$_POST['ppp_1_amount']."',
				'".$_POST['ppp_1_wages_allocated']."', '".$_POST['ppp_2_applied']."', '".$ppp_2_date."', '".$_POST['ppp_2_forgiveness_applied']."',
				'".$ppp_2_forgive_app_date."', '".$_POST['ppp_2_amount']."', '".$_POST['ppp_2_wages_allocated']."', '".$_POST['additional_comments']."', '".$_POST['attorney_name']."', '".$call_date."', '".$_POST['call_time']."', '".$memo_received_date."', '".$memo_cut_off_date."', '".$_POST['fpso_fees']."', '".$_POST['interest_percentage']."', '".$_POST['net_no']."')"
		));

	}
	// intake value insert update end here

	//check project bank info exist for erc product
	$bank_info = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}erc_bank_info WHERE lead_id = ".$lead_id."");
	if(!empty($bank_info)){
	   	
	   		if(isset($_POST['bank_name'])){				
				   $new_bank_data = array();
				   $new_bank_data['bank_name'] = $_POST['bank_name'];
	                           $new_bank_data['bank_mailing_address'] = $_POST['bank_mailing_address'];
	                           $new_bank_data['city'] = $_POST['bank_city'];
	                           $new_bank_data['state'] = $_POST['bank_state'];
	                           $new_bank_data['zip'] = $_POST['bank_zip'];
	                           $new_bank_data['country'] = $_POST['country'];
	                           $new_bank_data['bank_phone'] = $_POST['bank_phone'];
	                           $new_bank_data['account_holder_name'] = $_POST['account_holder_name'];								   
	                           $new_bank_data['account_type'] = $_POST['account_type'];
	                           $new_bank_data['other'] = $_POST['other'];
	                           $new_bank_data['aba_routing_no'] = $_POST['aba_routing_no'];
	                           $new_bank_data['account_number'] = $_POST['account_number'];
	                           $new_bank_data['swift'] = $_POST['swift'];
	                           $new_bank_data['iban'] = $_POST['iban'];

				   $bank_table = $wpdb->prefix.'erc_bank_info';	
				   log_audit_entry($lead_id, $bank_info, $new_bank_data,$bank_table);
				

	   			$wpdb->query("UPDATE {$wpdb->prefix}erc_bank_info SET 
	                           bank_name = '".$_POST['bank_name']."',
	                           bank_mailing_address = '".$_POST['bank_mailing_address']."',
	                           city = '".$_POST['bank_city']."',
	                           state = '".$_POST['bank_state']."',
	                           zip = '".$_POST['bank_zip']."',
	                           country = '".$_POST['country']."',
	                           bank_phone = '".$_POST['bank_phone']."',
	                           account_holder_name = '".$_POST['account_holder_name']."',								   
	                           account_type = '".$_POST['account_type']."',
	                           other = '".$_POST['other']."',
	                           aba_routing_no = '".$_POST['aba_routing_no']."',
	                           account_number = '".$_POST['account_number']."',
	                           swift = '".$_POST['swift']."',
	                           iban = '".$_POST['iban']."'
	                           WHERE lead_id = ".$lead_id."
	                        ");
	   		    }//isset		

	}else{
		$wpdb->query("INSERT INTO {$wpdb->prefix}erc_bank_info (lead_id,bank_name,bank_mailing_address,city,state,zip,country,bank_phone,account_holder_name,account_type,other,aba_routing_no,account_number,swift,iban) VALUES (".$lead_id.",'".$_POST['bank_name']."','".$_POST['bank_mailing_address']."','".$_POST['bank_city']."','".$_POST['bank_state']."','".$_POST['bank_zip']."','".$_POST['country']."','".$_POST['bank_phone']."', '".$_POST['account_holder_name']."', '".$_POST['account_type']."', '".$_POST['other']."','".$_POST['aba_routing_no']."', '".$_POST['account_number']."', '".$_POST['swift']."', '".$_POST['iban']."')");

	}
	// bank  info insert update end here

	// $project_fee_info = $wpdb->get_row("SELECT project_id FROM {$wpdb->prefix}erc_project_fees WHERE project_id = ".$project_id."");
    /*if(isset($_POST['error_discovered_date'])){	*/
	// check project into fee table
	$fee_info = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}erc_erc_fees WHERE lead_id = ".$lead_id."");
	if($_POST['error_discovered_date'] != ''){
		$error_discovered_date = date('m/d/Y',strtotime($_POST['error_discovered_date']));
	}else{
		$error_discovered_date = '';
	}
	if($_POST['q1_2020_filed_date'] != ''){
		$q1_2020_filed_date = date('m/d/Y',strtotime($_POST['q1_2020_filed_date']));
	}else{
		$q1_2020_filed_date = '';
	}
	if($_POST['q2_2020_filed_date'] != ''){
		$q2_2020_filed_date = date('m/d/Y',strtotime($_POST['q2_2020_filed_date']));
	}else{
		$q2_2020_filed_date = '';
	}
	if($_POST['q3_2020_filed_date'] != ''){
		$q3_2020_filed_date = date('m/d/Y',strtotime($_POST['q3_2020_filed_date']));
	}else{
		$q3_2020_filed_date = '';
	}
	if($_POST['q4_2020_filed_date'] != ''){
		$q4_2020_filed_date = date('m/d/Y',strtotime($_POST['q4_2020_filed_date']));
	}else{
		$q4_2020_filed_date = '';
	}
	if($_POST['q1_2021_filed_date'] != ''){
		$q1_2021_filed_date = date('m/d/Y',strtotime($_POST['q1_2021_filed_date']));
	}else{
		$q1_2021_filed_date = '';
	}
	if($_POST['q2_2021_filed_date'] != ''){
		$q2_2021_filed_date = date('m/d/Y',strtotime($_POST['q2_2021_filed_date']));
	}else{
		$q2_2021_filed_date = '';
	}
	if($_POST['q3_2021_filed_date'] != ''){
		$q3_2021_filed_date = date('m/d/Y',strtotime($_POST['q3_2021_filed_date']));
	}else{
		$q3_2021_filed_date = '';
	}
	if($_POST['q4_2021_filed_date'] != ''){
		$q4_2021_filed_date = date('m/d/Y',strtotime($_POST['q4_2021_filed_date']));
	}else{
		$q4_2021_filed_date = '';
	}
	if($_POST['q1_2020_loop'] != ''){
		$q1_2020_loop = date('m/d/Y',strtotime($_POST['q1_2020_loop']));
	}else{
		$q1_2020_loop = '';
	}
	if($_POST['q2_2020_loop'] != ''){
		$q2_2020_loop = date('m/d/Y',strtotime($_POST['q2_2020_loop']));
	}else{
		$q2_2020_loop = '';
	}
	if($_POST['q3_2020_loop'] != ''){
		$q3_2020_loop = date('m/d/Y',strtotime($_POST['q3_2020_loop']));
	}else{
		$q3_2020_loop = '';
	}
	if($_POST['q4_2020_loop'] != ''){
		$q4_2020_loop = date('m/d/Y',strtotime($_POST['q4_2020_loop']));
	}else{
		$q4_2020_loop = '';
	}
	if($_POST['q1_2021_loop'] != ''){
		$q1_2021_loop = date('m/d/Y',strtotime($_POST['q1_2021_loop']));
	}else{
		$q1_2021_loop = '';
	}
	if($_POST['q2_2021_loop'] != ''){
		$q2_2021_loop = date('m/d/Y',strtotime($_POST['q2_2021_loop']));
	}else{
		$q2_2021_loop = '';
	}
	if($_POST['q3_2021_loop'] != ''){
		$q3_2021_loop = date('m/d/Y',strtotime($_POST['q3_2021_loop']));
	}else{
		$q3_2021_loop = '';
	}
	if($_POST['q4_2021_loop'] != ''){
		$q4_2021_loop = date('m/d/Y',strtotime($_POST['q4_2021_loop']));
	}else{
		$q4_2021_loop = '';
	}
	if($_POST['i_invoice_sent_date'] != ''){
		$i_invoice_sent_date = date('m/d/Y',strtotime($_POST['i_invoice_sent_date']));
	}else{
		$i_invoice_sent_date = '';
	}
	if($_POST['i_invoice_payment_date'] != ''){
		$i_invoice_payment_date = date('m/d/Y',strtotime($_POST['i_invoice_payment_date']));
	}else{
		$i_invoice_payment_date = '';
	}
	if($_POST['i_invoice_pay_cleared'] != ''){
		$i_invoice_pay_cleared = date('m/d/Y',strtotime($_POST['i_invoice_pay_cleared']));
	}else{
		$i_invoice_pay_cleared = '';
	}
	if($_POST['i_invoice_pay_returned'] != ''){
		$i_invoice_pay_returned = date('m/d/Y',strtotime($_POST['i_invoice_pay_returned']));
	}else{
		$i_invoice_pay_returned = '';
	}
	if($_POST['ii_invoice_sent_date'] != ''){
		$ii_invoice_sent_date = date('m/d/Y',strtotime($_POST['ii_invoice_sent_date']));
	}else{
		$ii_invoice_sent_date = '';
	}
	if($_POST['ii_invoice_payment_date'] != ''){
		$ii_invoice_payment_date = date('m/d/Y',strtotime($_POST['ii_invoice_payment_date']));
	}else{
		$ii_invoice_payment_date = '';
	}
	if($_POST['ii_invoice_pay_cleared'] != ''){
		$ii_invoice_pay_cleared = date('m/d/Y',strtotime($_POST['ii_invoice_pay_cleared']));
	}else{
		$ii_invoice_pay_cleared = '';
	}
	if($_POST['ii_invoice_pay_returned'] != ''){
		$ii_invoice_pay_returned = date('m/d/Y',strtotime($_POST['ii_invoice_pay_returned']));
	}else{
		$ii_invoice_pay_returned = '';
	}
	if($_POST['iii_invoice_sent_date'] != ''){
		$iii_invoice_sent_date = date('m/d/Y',strtotime($_POST['iii_invoice_sent_date']));
	}else{
		$iii_invoice_sent_date = '';
	}
	if($_POST['iii_invoice_payment_date'] != ''){
		$iii_invoice_payment_date = date('m/d/Y',strtotime($_POST['iii_invoice_payment_date']));
	}else{
		$iii_invoice_payment_date = '';
	}
	if($_POST['iii_invoice_pay_cleared'] != ''){
		$iii_invoice_pay_cleared = date('m/d/Y',strtotime($_POST['iii_invoice_pay_cleared']));
	}else{
		$iii_invoice_pay_cleared = '';
	}
	if($_POST['iii_invoice_pay_returned'] != ''){
		$iii_invoice_pay_returned = date('m/d/Y',strtotime($_POST['iii_invoice_pay_returned']));
	}else{
		$iii_invoice_pay_returned = '';
	}
	if($_POST['iv_invoice_sent_date'] != ''){
		$iv_invoice_sent_date = date('m/d/Y',strtotime($_POST['iv_invoice_sent_date']));
	}else{
		$iv_invoice_sent_date = '';
	}
	if($_POST['iv_invoice_payment_date'] != ''){
		$iv_invoice_payment_date = date('m/d/Y',strtotime($_POST['iv_invoice_payment_date']));
	}else{
		$iv_invoice_payment_date = '';
	}
	if($_POST['iv_invoice_pay_cleared'] != ''){
		$iv_invoice_pay_cleared = date('m/d/Y',strtotime($_POST['iv_invoice_pay_cleared']));
	}else{
		$iv_invoice_pay_cleared = '';
	}
	if($_POST['iv_invoice_pay_returned'] != ''){
		$iv_invoice_pay_returned = date('m/d/Y',strtotime($_POST['iv_invoice_pay_returned']));
	}else{
		$iv_invoice_pay_returned = '';
	}
	// V dates
	if($_POST['v_invoice_sent_date'] != ''){
		$v_invoice_sent_date = date('m/d/Y', strtotime($_POST['v_invoice_sent_date']));
	} else { $v_invoice_sent_date = ''; }
	if($_POST['v_invoice_payment_date'] != ''){
		$v_invoice_payment_date = date('m/d/Y', strtotime($_POST['v_invoice_payment_date']));
	} else { $v_invoice_payment_date = ''; }
	if($_POST['v_invoice_pay_cleared'] != ''){
		$v_invoice_pay_cleared = date('m/d/Y', strtotime($_POST['v_invoice_pay_cleared']));
	} else { $v_invoice_pay_cleared = ''; }
	if($_POST['v_invoice_pay_returned'] != ''){
		$v_invoice_pay_returned = date('m/d/Y', strtotime($_POST['v_invoice_pay_returned']));
	} else { $v_invoice_pay_returned = ''; }

	// VI dates
	if($_POST['vi_invoice_sent_date'] != ''){
		$vi_invoice_sent_date = date('m/d/Y', strtotime($_POST['vi_invoice_sent_date']));
	} else { $vi_invoice_sent_date = ''; }
	if($_POST['vi_invoice_payment_date'] != ''){
		$vi_invoice_payment_date = date('m/d/Y', strtotime($_POST['vi_invoice_payment_date']));
	} else { $vi_invoice_payment_date = ''; }
	if($_POST['vi_invoice_pay_cleared'] != ''){
		$vi_invoice_pay_cleared = date('m/d/Y', strtotime($_POST['vi_invoice_pay_cleared']));
	} else { $vi_invoice_pay_cleared = ''; }
	if($_POST['vi_invoice_pay_returned'] != ''){
		$vi_invoice_pay_returned = date('m/d/Y', strtotime($_POST['vi_invoice_pay_returned']));
	} else { $vi_invoice_pay_returned = ''; }

	// VII dates
	if($_POST['vii_invoice_sent_date'] != ''){
		$vii_invoice_sent_date = date('m/d/Y', strtotime($_POST['vii_invoice_sent_date']));
	} else { $vii_invoice_sent_date = ''; }
	if($_POST['vii_invoice_payment_date'] != ''){
		$vii_invoice_payment_date = date('m/d/Y', strtotime($_POST['vii_invoice_payment_date']));
	} else { $vii_invoice_payment_date = ''; }
	if($_POST['vii_invoice_pay_cleared'] != ''){
		$vii_invoice_pay_cleared = date('m/d/Y', strtotime($_POST['vii_invoice_pay_cleared']));
	} else { $vii_invoice_pay_cleared = ''; }
	if($_POST['vii_invoice_pay_returned'] != ''){
		$vii_invoice_pay_returned = date('m/d/Y', strtotime($_POST['vii_invoice_pay_returned']));
	} else { $vii_invoice_pay_returned = ''; }
	
	
	
	
	if(!empty($fee_info)){
	   	
							
							if(isset($_POST['error_discovered_date']) || isset($_POST['q2_2020_941_wages'])){   
							   $new_fees_data = array();
		     	    	       $new_fees_data['error_discovered_date'] = $error_discovered_date;
	                           $new_fees_data['q2_2020_941_wages'] = $_POST['q2_2020_941_wages'];
	                           $new_fees_data['q3_2020_941_wages'] = $_POST['q3_2020_941_wages'];
	                           $new_fees_data['q4_2020_941_wages'] = $_POST['q4_2020_941_wages'];
	                           $new_fees_data['q1_2021_941_wages'] = $_POST['q1_2021_941_wages'];
	                           $new_fees_data['q2_2021_941_wages'] = $_POST['q2_2021_941_wages'];
	                           $new_fees_data['q3_2021_941_wages'] = $_POST['q3_2021_941_wages'];
	                           $new_fees_data['q4_2021_941_wages'] = $_POST['q4_2021_941_wages'];
	                           $new_fees_data['affiliate_name'] = $_POST['affiliate_name'];
	                           $new_fees_data['affiliate_percentage'] = $_POST['affiliate_percentage'];
	                           $new_fees_data['erc_claim_filed'] = $_POST['erc_claim_filed'];
	                           $new_fees_data['erc_amount_received'] = $_POST['erc_amount_received'];
	                           $new_fees_data['total_erc_fees'] = $_POST['total_erc_fees'];
	                           $new_fees_data['legal_fees'] = $_POST['legal_fees'];
	                           $new_fees_data['total_erc_fees_paid'] = $_POST['total_erc_fees_paid'];
	                           $new_fees_data['total_erc_fees_pending'] = $_POST['total_erc_fees_pending'];
	                           $new_fees_data['total_occams_share'] = $_POST['total_occams_share'];
	                           $new_fees_data['total_aff_ref_share'] = $_POST['total_aff_ref_share'];
	                           $new_fees_data['retain_occams_share'] = $_POST['retain_occams_share'];
	                           $new_fees_data['retain_aff_ref_share'] = $_POST['retain_aff_ref_share'];
	                           $new_fees_data['bal_retain_occams_share'] = $_POST['bal_retain_occams_share'];
	                           $new_fees_data['bal_retain_aff_ref_share'] = $_POST['bal_retain_aff_ref_share'];
	                           $new_fees_data['total_occams_share_paid'] = $_POST['total_occams_share_paid'];
	                           $new_fees_data['total_aff_ref_share_paid'] = $_POST['total_aff_ref_share_paid'];
	                           $new_fees_data['total_occams_share_pendin'] = $_POST['total_occams_share_pendin'];
	                           $new_fees_data['total_aff_ref_share_pend'] = $_POST['total_aff_ref_share_pend'];
	                           $new_fees_data['q1_2020_filed_status'] = $_POST['q1_2020_filed_status'];
	                           $new_fees_data['filing_date_4267'] = $q1_2020_filed_date;
	                           $new_fees_data['amount_filed_4263'] = $_POST['q1_2020_amount_filed'];
	                           $new_fees_data['q1_2020_benefits'] = $_POST['q1_2020_benefits'];
	                           $new_fees_data['q1_2020_eligibility_basis'] = $_POST['q1_2020_eligibility_basis'];
	                           $new_fees_data['q2_2020_filed_status'] = $_POST['q2_2020_filed_status'];
	                           $new_fees_data['filing_date_4268'] = $q2_2020_filed_date;
	                           $new_fees_data['amount_filed_4269'] = $_POST['q2_2020_amount_filed'];
	                           $new_fees_data['q2_2020_benefits'] = $_POST['q2_2020_benefits'];
	                           $new_fees_data['q2_2020_eligibility_basis'] = $_POST['q2_2020_eligibility_basis'];
	                           $new_fees_data['q3_2020_filed_status'] = $_POST['q3_2020_filed_status'];
	                           $new_fees_data['filing_date_4270'] = $q3_2020_filed_date;
	                           $new_fees_data['amount_filed_4266'] = $_POST['q3_2020_amount_filed'];
	                           $new_fees_data['q3_2020_benefits'] = $_POST['q3_2020_benefits'];
	                           $new_fees_data['q3_2020_eligibility_basis'] = $_POST['q3_2020_eligibility_basis'];
	                           $new_fees_data['q4_2020_filed_status'] = $_POST['q4_2020_filed_status'];
	                           $new_fees_data['filing_date_4272'] = $q4_2020_filed_date;
	                           $new_fees_data['amount_filed_4273'] = $_POST['q4_2020_amount_filed'];
	                           $new_fees_data['q4_2020_benefits'] = $_POST['q4_2020_benefits'];
	                           $new_fees_data['q4_2020_eligibility_basis'] = $_POST['q4_2020_eligibility_basis'];
	                           $new_fees_data['q1_2021_filed_status'] = $_POST['q1_2021_filed_status'];
	                           $new_fees_data['filing_date_4276'] = $q1_2021_filed_date;
	                           $new_fees_data['amount_filed_4277'] = $_POST['q1_2021_amount_filed'];
	                           $new_fees_data['q1_2021_benefits'] = $_POST['q1_2021_benefits'];
	                           $new_fees_data['q1_2021_eligibility_basis'] = $_POST['q1_2021_eligibility_basis'];
	                           $new_fees_data['q2_2021_filed_status'] = $_POST['q2_2021_filed_status'];
	                           $new_fees_data['filing_date_4279'] = $q2_2021_filed_date;
	                           $new_fees_data['amount_filed_4280'] = $_POST['q2_2021_amount_filed'];
	                           $new_fees_data['q2_2021_benefits'] = $_POST['q2_2021_benefits'];
	                           $new_fees_data['q2_2021_eligibility_basis'] = $_POST['q2_2021_eligibility_basis'];
	                           $new_fees_data['q3_2021_filed_status'] = $_POST['q3_2021_filed_status'];
	                           $new_fees_data['filing_date_4282'] = $q3_2021_filed_date;
	                           $new_fees_data['amount_filed_4283'] = $_POST['q3_2021_amount_filed'];
	                           $new_fees_data['q3_2021_benefits'] = $_POST['q3_2021_benefits'];
	                           $new_fees_data['q3_2021_eligibility_basis'] = $_POST['q3_2021_eligibility_basis'];
	                           $new_fees_data['q4_2021_filed_status'] = $_POST['q4_2021_filed_status'];
	                           $new_fees_data['filing_date_4285'] = $q4_2021_filed_date;
	                           $new_fees_data['amount_filed_4286'] = $_POST['q4_2021_amount_filed'];
	                           $new_fees_data['q4_2021_benefits'] = $_POST['q4_2021_benefits'];
	                           $new_fees_data['q4_2021_eligibility_basis'] = $_POST['q4_2021_eligibility_basis'];
	                           $new_fees_data['q1_2020_loOP'] = $q1_2020_loop;
	                           $new_fees_data['q1_2020_letter'] = $_POST['q1_2020_letter'];
	                           $new_fees_data['q1_2020_check'] = $_POST['q1_2020_check'];
	                           $new_fees_data['q1_2020_chq_amt'] = $_POST['q1_2020_chq_amt'];
	                           $new_fees_data['q2_2020_loOP'] = $q2_2020_loop;
	                           $new_fees_data['q2_2020_letter'] = $_POST['q2_2020_letter'];
	                           $new_fees_data['q2_2020_check'] = $_POST['q2_2020_check'];
	                           $new_fees_data['q2_2020_chq_amt'] = $_POST['q2_2020_chq_amt'];
	                           $new_fees_data['q3_2020_loOP'] = $q3_2020_loop;
	                           $new_fees_data['q3_2020_letter'] = $_POST['q3_2020_letter'];
	                           $new_fees_data['q3_2020_check'] = $_POST['q3_2020_check'];
	                           $new_fees_data['q3_2020_chq_amt'] = $_POST['q3_2020_chq_amt'];
	                           $new_fees_data['q4_2020_loOP'] = $q4_2020_loop;
	                           $new_fees_data['q4_2020_letter'] = $_POST['q4_2020_letter'];
	                           $new_fees_data['q4_2020_check'] = $_POST['q4_2020_check'];
	                           $new_fees_data['q4_2020_chq_amt'] = $_POST['q4_2020_chq_amt'];
	                           $new_fees_data['q1_2021_loOP'] = $q1_2021_loop;
	                           $new_fees_data['q1_2021_letter'] = $_POST['q1_2021_letter'];
	                           $new_fees_data['q1_2021_check'] = $_POST['q1_2021_check'];
	                           $new_fees_data['q1_2021_chq_amt'] = $_POST['q1_2021_chq_amt'];
	                           $new_fees_data['q2_2021_loOP'] = $q2_2021_loop;
	                           $new_fees_data['q2_2021_letter'] = $_POST['q2_2021_letter'];
	                           $new_fees_data['q2_2021_check'] = $_POST['q2_2021_check'];
				   			   $new_fees_data['q2_2021_chq_amt'] = $_POST['q2_2021_chq_amt'];
	                           $new_fees_data['q3_2021_loOP'] = $q3_2021_loop;
	                           $new_fees_data['q3_2021_letter'] = $_POST['q3_2021_letter'];
	                           $new_fees_data['q3_2021_check'] = $_POST['q3_2021_check'];
	                           $new_fees_data['q3_2021_chq_amt'] = $_POST['q3_2021_chq_amt'];
	                           $new_fees_data['q4_2021_loOP'] = $q4_2021_loop;
	                           $new_fees_data['q4_2021_letter'] = $_POST['q4_2021_letter'];
	                           $new_fees_data['q4_2021_check'] = $_POST['q4_2021_check'];
	                           $new_fees_data['q4_2021_chq_amt'] = $_POST['q4_2021_chq_amt'];
	                           $new_fees_data['i_invoice_no'] = $_POST['i_invoice_no'];
	                           $new_fees_data['i_invoice_amount'] = $_POST['i_invoice_amount'];
	                           $new_fees_data['i_invoiced_qtrs'] = $_POST['i_invoiced_qtrs'];
	                           $new_fees_data['i_invoice_sent_date'] = $i_invoice_sent_date;
	                           $new_fees_data['i_invoice_payment_type'] = $_POST['i_invoice_payment_type'];
	                           $new_fees_data['i_invoice_payment_date'] = $i_invoice_payment_date;
	                           $new_fees_data['i_invoice_pay_cleared'] = $i_invoice_pay_cleared;
	                           $new_fees_data['i_invoice_pay_returned'] = $i_invoice_pay_returned;
	                           $new_fees_data['i_invoice_return_reason'] = $_POST['i_invoice_return_reason'];
	                           $new_fees_data['i_invoice_occams_share'] = $_POST['i_invoice_occams_share'];
	                           $new_fees_data['i_invoice_aff_ref_share'] = $_POST['i_invoice_aff_ref_share'];
	                           $new_fees_data['ii_invoice_no'] = $_POST['ii_invoice_no'];
	                           $new_fees_data['ii_invoice_amount'] = $_POST['ii_invoice_amount'];
	                           $new_fees_data['ii_invoiced_qtrs'] = $_POST['ii_invoiced_qtrs'];
	                           $new_fees_data['ii_invoice_sent_date'] = $ii_invoice_sent_date;
	                           $new_fees_data['ii_invoice_payment_type'] = $_POST['ii_invoice_payment_type'];
	                           $new_fees_data['ii_invoice_payment_date'] = $ii_invoice_payment_date;
	                           $new_fees_data['ii_invoice_pay_cleared'] = $ii_invoice_pay_cleared;
	                           $new_fees_data['ii_invoice_pay_returned'] = $ii_invoice_pay_returned;
	                           $new_fees_data['ii_invoice_return_reason'] = $_POST['ii_invoice_return_reason'];
	                           $new_fees_data['ii_invoice_occams_share'] = $_POST['ii_invoice_occams_share'];
	                           $new_fees_data['ii_invoice_aff_ref_share'] = $_POST['ii_invoice_aff_ref_share'];
	                           $new_fees_data['iii_invoice_no'] = $_POST['iii_invoice_no'];
	                           $new_fees_data['iii_invoice_amount'] = $_POST['iii_invoice_amount'];
	                           $new_fees_data['iii_invoiced_qtrs'] = $_POST['iii_invoiced_qtrs'];
	                           $new_fees_data['iii_invoice_sent_date'] = $iii_invoice_sent_date;
	                           $new_fees_data['iii_invoice_payment_type'] = $_POST['iii_invoice_payment_type'];
	                           $new_fees_data['iii_invoice_payment_date'] = $iii_invoice_payment_date;
	                           $new_fees_data['iii_invoice_pay_cleared'] = $iii_invoice_pay_cleared;
	                           $new_fees_data['iii_invoice_pay_returned'] = $iii_invoice_pay_returned;
	                           $new_fees_data['iii_invoice_return_reason'] = $_POST['iii_invoice_return_reason'];
	                           $new_fees_data['iii_invoice_occams_share'] = $_POST['iii_invoice_occams_share'];
	                           $new_fees_data['iii_invoice_aff_ref_share'] = $_POST['iii_invoice_aff_ref_share'];
	                           $new_fees_data['iv_invoice_no'] = $_POST['iv_invoice_no'];
	                           $new_fees_data['iv_invoice_amount'] = $_POST['iv_invoice_amount'];
	                           $new_fees_data['iv_invoiced_qtrs'] = $_POST['iv_invoiced_qtrs'];
	                           $new_fees_data['iv_invoice_sent_date'] = $iv_invoice_sent_date;
	                           $new_fees_data['iv_invoice_payment_type'] = $_POST['iv_invoice_payment_type'];
	                           $new_fees_data['iv_invoice_payment_date'] = $iv_invoice_payment_date;
	                           $new_fees_data['iv_invoice_pay_cleared'] = $iv_invoice_pay_cleared;
	                           $new_fees_data['iv_invoice_pay_returned'] = $iv_invoice_pay_returned;
	                           $new_fees_data['iv_invoice_return_reason'] = $_POST['iv_invoice_return_reason'];
	                           $new_fees_data['iv_invoice_occams_share'] = $_POST['iv_invoice_occams_share'];
	                           $new_fees_data['iv_invoice_aff_ref_share'] = $_POST['iv_invoice_aff_ref_share'];
	                           $new_fees_data['q1_2021_max_erc_amount'] = $_POST['q1_2021_max_erc_amount'];
	                           $new_fees_data['q2_2021_max_erc_amount'] = $_POST['q2_2021_max_erc_amount'];
	                           $new_fees_data['q3_2021_max_erc_amount'] = $_POST['q3_2021_max_erc_amount'];
	                           $new_fees_data['q4_2021_max_erc_amount'] = $_POST['q4_2021_max_erc_amount'];
	                           $new_fees_data['q1_2020_max_erc_amount'] = $_POST['q1_2020_max_erc_amount'];
	                           $new_fees_data['q2_2020_max_erc_amount'] = $_POST['q2_2020_max_erc_amount'];
	                           $new_fees_data['q3_2020_max_erc_amount'] = $_POST['q3_2020_max_erc_amount'];
	                           $new_fees_data['q4_2020_max_erc_amount'] = $_POST['q4_2020_max_erc_amount'];

				   				$fees_table = $wpdb->prefix.'erc_erc_fees';	
				   				log_audit_entry($lead_id, $fee_info, $new_fees_data,$fees_table);
				
				   
	   						$wpdb->query("UPDATE {$wpdb->prefix}erc_erc_fees SET 
	                           error_discovered_date = '".$error_discovered_date."',
	                           q2_2020_941_wages = '".$_POST['q2_2020_941_wages']."',
	                           q3_2020_941_wages = '".$_POST['q3_2020_941_wages']."',
	                           q4_2020_941_wages = '".$_POST['q4_2020_941_wages']."',
	                           q1_2021_941_wages = '".$_POST['q1_2021_941_wages']."',
	                           q2_2021_941_wages = '".$_POST['q2_2021_941_wages']."',
	                           q3_2021_941_wages = '".$_POST['q3_2021_941_wages']."',
	                           q4_2021_941_wages = '".$_POST['q4_2021_941_wages']."',	
	                           affiliate_name = '".$_POST['affiliate_name']."',
	                           affiliate_percentage = '".$_POST['affiliate_percentage']."',
	                           erc_claim_filed = '".$_POST['erc_claim_filed']."',
	                           erc_amount_received = '".$_POST['erc_amount_received']."',
	                           total_erc_fees = '".$_POST['total_erc_fees']."',
	                           legal_fees = '".$_POST['legal_fees']."',
	                           total_erc_fees_paid = '".$_POST['total_erc_fees_paid']."',
	                           total_erc_fees_pending = '".$_POST['total_erc_fees_pending']."',
	                           total_occams_share = '".$_POST['total_occams_share']."',
	                           total_aff_ref_share = '".$_POST['total_aff_ref_share']."',
	                           retain_occams_share = '".$_POST['retain_occams_share']."',
	                           retain_aff_ref_share = '".$_POST['retain_aff_ref_share']."',
	                           bal_retain_occams_share = '".$_POST['bal_retain_occams_share']."',
	                           bal_retain_aff_ref_share = '".$_POST['bal_retain_aff_ref_share']."',
	                           total_occams_share_paid = '".$_POST['total_occams_share_paid']."',
	                           total_aff_ref_share_paid = '".$_POST['total_aff_ref_share_paid']."',
	                           total_occams_share_pendin = '".$_POST['total_occams_share_pendin']."',
	                           total_aff_ref_share_pend = '".$_POST['total_aff_ref_share_pend']."',
	                           q1_2020_filed_status = '".$_POST['q1_2020_filed_status']."',
	                           filing_date_4267 = '".$q1_2020_filed_date."',
	                           amount_filed_4263 = '".$_POST['q1_2020_amount_filed']."',
	                           q1_2020_benefits = '".$_POST['q1_2020_benefits']."',
	                           q1_2020_eligibility_basis = '".$_POST['q1_2020_eligibility_basis']."',
	                           q2_2020_filed_status = '".$_POST['q2_2020_filed_status']."',
	                           filing_date_4268 = '".$q2_2020_filed_date."',
	                           amount_filed_4269 = '".$_POST['q2_2020_amount_filed']."',
	                           q2_2020_benefits = '".$_POST['q2_2020_benefits']."',
	                           q2_2020_eligibility_basis = '".$_POST['q2_2020_eligibility_basis']."',
	                           q3_2020_filed_status = '".$_POST['q3_2020_filed_status']."',
	                           filing_date_4270 = '".$q3_2020_filed_date."',
	                           amount_filed_4266 = '".$_POST['q3_2020_amount_filed']."',
	                           q3_2020_benefits = '".$_POST['q3_2020_benefits']."',
	                           q3_2020_eligibility_basis = '".$_POST['q3_2020_eligibility_basis']."',
	                           q4_2020_filed_status = '".$_POST['q4_2020_filed_status']."',
	                           filing_date_4272 = '".$q4_2020_filed_date."',
	                           amount_filed_4273 = '".$_POST['q4_2020_amount_filed']."',
	                           q4_2020_benefits = '".$_POST['q4_2020_benefits']."',
	                           q4_2020_eligibility_basis = '".$_POST['q4_2020_eligibility_basis']."',
	                           q1_2021_filed_status = '".$_POST['q1_2021_filed_status']."',
	                           filing_date_4276 = '".$q1_2021_filed_date."',
	                           amount_filed_4277 = '".$_POST['q1_2021_amount_filed']."',
	                           q1_2021_benefits = '".$_POST['q1_2021_benefits']."',
	                           q1_2021_eligibility_basis = '".$_POST['q1_2021_eligibility_basis']."',
	                           q2_2021_filed_status = '".$_POST['q2_2021_filed_status']."',
	                           filing_date_4279 = '".$q2_2021_filed_date."',
	                           amount_filed_4280 = '".$_POST['q2_2021_amount_filed']."',
	                           q2_2021_benefits = '".$_POST['q2_2021_benefits']."',
	                           q2_2021_eligibility_basis = '".$_POST['q2_2021_eligibility_basis']."',
	                           q3_2021_filed_status = '".$_POST['q3_2021_filed_status']."',
	                           filing_date_4282 = '".$q3_2021_filed_date."',
	                           amount_filed_4283 = '".$_POST['q3_2021_amount_filed']."',
	                           q3_2021_benefits = '".$_POST['q3_2021_benefits']."',
	                           q3_2021_eligibility_basis = '".$_POST['q3_2021_eligibility_basis']."',
	                           q4_2021_filed_status = '".$_POST['q4_2021_filed_status']."',
	                           filing_date_4285 = '".$q4_2021_filed_date."',
	                           amount_filed_4286 = '".$_POST['q4_2021_amount_filed']."',
	                           q4_2021_benefits = '".$_POST['q4_2021_benefits']."'
	                           ,q4_2021_eligibility_basis = '".$_POST['q4_2021_eligibility_basis']."'
	                           ,q1_2020_loOP = '".$q1_2020_loop."'
	                           ,q1_2020_letter = '".$_POST['q1_2020_letter']."'
	                           ,q1_2020_check = '".$_POST['q1_2020_check']."'
	                           ,q1_2020_chq_amt = '".$_POST['q1_2020_chq_amt']."'
	                           ,q2_2020_loOP = '".$q2_2020_loop."'
	                           ,q2_2020_letter = '".$_POST['q2_2020_letter']."'
	                           ,q2_2020_check = '".$_POST['q2_2020_check']."'
	                           ,q2_2020_chq_amt = '".$_POST['q2_2020_chq_amt']."'
	                           ,q3_2020_loOP = '".$q3_2020_loop."'
	                           ,q3_2020_letter = '".$_POST['q3_2020_letter']."'
	                           ,q3_2020_check = '".$_POST['q3_2020_check']."'
	                           ,q3_2020_chq_amt = '".$_POST['q3_2020_chq_amt']."'
	                           ,q4_2020_loOP = '".$q4_2020_loop."'
	                           ,q4_2020_letter = '".$_POST['q4_2020_letter']."'
	                           ,q4_2020_check = '".$_POST['q4_2020_check']."'
	                           ,q4_2020_chq_amt = '".$_POST['q4_2020_chq_amt']."'
	                           ,q1_2021_loOP = '".$q1_2021_loop."'
	                           ,q1_2021_letter = '".$_POST['q1_2021_letter']."'
	                           ,q1_2021_check = '".$_POST['q1_2021_check']."'
	                           ,q1_2021_chq_amt = '".$_POST['q1_2021_chq_amt']."'
	                           ,q2_2021_loOP = '".$q2_2021_loop."'
	                           ,q2_2021_letter = '".$_POST['q2_2021_letter']."'
	                           ,q2_2021_check = '".$_POST['q2_2021_check']."'
								,q2_2021_chq_amt = '".$_POST['q2_2021_chq_amt']."'
	                           ,q3_2021_loOP = '".$q3_2021_loop."'
	                           ,q3_2021_letter = '".$_POST['q3_2021_letter']."'
	                           ,q3_2021_check = '".$_POST['q3_2021_check']."'
	                           ,q3_2021_chq_amt = '".$_POST['q3_2021_chq_amt']."'
	                           ,q4_2021_loOP = '".$q4_2021_loop."'
	                           ,q4_2021_letter = '".$_POST['q4_2021_letter']."'
	                           ,q4_2021_check = '".$_POST['q4_2021_check']."'
	                           ,q4_2021_chq_amt = '".$_POST['q4_2021_chq_amt']."'

	                          ,q1_2021_max_erc_amount = '".$_POST['q1_2021_max_erc_amount']."'
	                          ,q2_2021_max_erc_amount = '".$_POST['q2_2021_max_erc_amount']."'
	                          ,q3_2021_max_erc_amount = '".$_POST['q3_2021_max_erc_amount']."'
	                          ,q4_2021_max_erc_amount = '".$_POST['q4_2021_max_erc_amount']."'
	                          ,q1_2020_max_erc_amount = '".$_POST['q1_2020_max_erc_amount']."'
	                          ,q2_2020_max_erc_amount = '".$_POST['q2_2020_max_erc_amount']."'
	                          ,q3_2020_max_erc_amount = '".$_POST['q3_2020_max_erc_amount']."'
	                          ,q4_2020_max_erc_amount = '".$_POST['q4_2020_max_erc_amount']."'
	                           WHERE lead_id = ".$lead_id."
	                        ");
			}//isset
	}else{
		$wpdb->query($wpdb->prepare(
			"INSERT INTO {$wpdb->prefix}erc_erc_fees (
				lead_id,
				error_discovered_date,
				q2_2020_941_wages,
				q3_2020_941_wages,
				q4_2020_941_wages,
				q1_2021_941_wages,
				q2_2021_941_wages,
				q3_2021_941_wages,
				q4_2021_941_wages,
				affiliate_name,
				affiliate_percentage,
				erc_claim_filed,
				erc_amount_received,
				total_erc_fees,
				legal_fees,
				total_erc_fees_paid,
				total_erc_fees_pending,
				total_occams_share,
				total_aff_ref_share,
				retain_occams_share,
				retain_aff_ref_share,
				bal_retain_occams_share,
				bal_retain_aff_ref_share,
				total_occams_share_paid,
				total_aff_ref_share_paid,
				total_occams_share_pendin,
				total_aff_ref_share_pend,
				q1_2020_filed_status,
				filing_date_4267,
				amount_filed_4263,
				q1_2020_benefits,
				q1_2020_eligibility_basis,
				q2_2020_filed_status,
				filing_date_4268,
				amount_filed_4269,
				q2_2020_benefits,
				q2_2020_eligibility_basis,
				q3_2020_filed_status,
				filing_date_4270,
				amount_filed_4266,
				q3_2020_benefits,
				q3_2020_eligibility_basis,
				q4_2020_filed_status,
				filing_date_4272,
				amount_filed_4273,
				q4_2020_benefits,
				q4_2020_eligibility_basis,
				q1_2021_filed_status,
				filing_date_4276,
				amount_filed_4277,
				q1_2021_benefits,
				q1_2021_eligibility_basis,
				q2_2021_filed_status,
				filing_date_4279,
				amount_filed_4280,
				q2_2021_benefits,
				q2_2021_eligibility_basis,
				q3_2021_filed_status,
				filing_date_4282,
				amount_filed_4283,
				q3_2021_benefits,
				q3_2021_eligibility_basis,
				q4_2021_filed_status,
				filing_date_4285,
				amount_filed_4286,
				q4_2021_benefits,
				q4_2021_eligibility_basis,
				q1_2020_loOP,
				q1_2020_letter,
				q1_2020_check,
				q1_2020_chq_amt,
				q2_2020_loOP,
				q2_2020_letter,
				q2_2020_check,
				q2_2020_chq_amt,
				q3_2020_loOP,
				q3_2020_letter,
				q3_2020_check,
				q3_2020_chq_amt,
				q4_2020_loOP,
				q4_2020_letter,
				q4_2020_check,
				q4_2020_chq_amt,
				q1_2021_loOP,
				q1_2021_letter,
				q1_2021_check,
				q1_2021_chq_amt,
				q2_2021_loOP,
				q2_2021_letter,
				q2_2021_check,
				q2_2021_chq_amt,
				q3_2021_loOP,
				q3_2021_letter,
				q3_2021_check,
				q3_2021_chq_amt,
				q4_2021_loOP,
				q4_2021_letter,
				q4_2021_check,
				q4_2021_chq_amt,
				q1_2021_max_erc_amount,
				q2_2021_max_erc_amount,
				q3_2021_max_erc_amount,
				q4_2021_max_erc_amount,
				q1_2020_max_erc_amount,
				q2_2020_max_erc_amount,
				q3_2020_max_erc_amount,
				q4_2020_max_erc_amount 
			) VALUES (
				".$lead_id.", '".$error_discovered_date."', '".$_POST['q2_2020_941_wages']."', '".$_POST['q3_2020_941_wages']."',
				'".$_POST['q4_2020_941_wages']."', '".$_POST['q1_2021_941_wages']."', '".$_POST['q2_2021_941_wages']."', '".$_POST['q3_2021_941_wages']."',
				'".$_POST['q4_2021_941_wages']."', '".$_POST['affiliate_name']."', '".$_POST['affiliate_percentage']."', '".$_POST['erc_claim_filed']."', '".$_POST['erc_amount_received']."',
				'".$_POST['total_erc_fees']."', '".$_POST['legal_fees']."', '".$_POST['total_erc_fees_paid']."','".$_POST['total_erc_fees_pending']."','".$_POST['total_occams_share']."','".$_POST['total_aff_ref_share']."','".$_POST['retain_occams_share']."','".$_POST['retain_aff_ref_share']."','".$_POST['bal_retain_occams_share']."','".$_POST['bal_retain_aff_ref_share']."','".$_POST['total_occams_share_paid']."','".$_POST['total_aff_ref_share_paid']."','".$_POST['total_occams_share_pendin']."','".$_POST['total_aff_ref_share_pend']."','".$_POST['q1_2020_filed_status']."','".$q1_2020_filed_date."','".$_POST['q1_2020_amount_filed']."','".$_POST['q1_2020_benefits']."','".$_POST['q1_2020_eligibility_basis']."','".$_POST['q2_2020_filed_status']."','".$q2_2020_filed_date."','".$_POST['q2_2020_amount_filed']."','".$_POST['q2_2020_benefits']."','".$_POST['q2_2020_eligibility_basis']."','".$_POST['q3_2020_filed_status']."','".$q3_2020_filed_date."','".$_POST['q3_2020_amount_filed']."','".$_POST['q3_2020_benefits']."','".$_POST['q3_2020_eligibility_basis']."','".$_POST['q4_2020_filed_status']."','".$q4_2020_filed_date."','".$_POST['q4_2020_amount_filed']."','".$_POST['q4_2020_benefits']."','".$_POST['q4_2020_eligibility_basis']."','".$_POST['q1_2021_filed_status']."','".$q1_2021_filed_date."','".$_POST['q1_2021_amount_filed']."','".$_POST['q1_2021_benefits']."','".$_POST['q1_2021_eligibility_basis']."','".$_POST['q2_2021_filed_status']."','".$q2_2021_filed_date."','".$_POST['q2_2021_amount_filed']."','".$_POST['q2_2021_benefits']."','".$_POST['q2_2021_eligibility_basis']."','".$_POST['q3_2021_filed_status']."','".$q3_2021_filed_date."','".$_POST['q3_2021_amount_filed']."','".$_POST['q3_2021_benefits']."','".$_POST['q3_2021_eligibility_basis']."','".$_POST['q4_2021_filed_status']."','".$q4_2021_filed_date."','".$_POST['q4_2021_amount_filed']."','".$_POST['q4_2021_benefits']."','".$_POST['q4_2021_eligibility_basis']."','".$q1_2020_loop."','".$_POST['q1_2020_letter']."','".$_POST['q1_2020_check']."','".$_POST['q1_2020_chq_amt']."','".$q2_2020_loop."','".$_POST['q2_2020_letter']."','".$_POST['q2_2020_check']."','".$_POST['q2_2020_chq_amt']."','".$q3_2020_loop."','".$_POST['q3_2020_letter']."','".$_POST['q3_2020_check']."','".$_POST['q3_2020_chq_amt']."','".$q4_2020_loop."','".$_POST['q4_2020_letter']."','".$_POST['q4_2020_check']."','".$_POST['q4_2020_chq_amt']."','".$q1_2021_loop."','".$_POST['q1_2021_letter']."','".$_POST['q1_2021_check']."','".$_POST['q1_2021_chq_amt']."','".$q2_2021_loop."','".$_POST['q2_2021_letter']."','".$_POST['q2_2021_check']."','".$_POST['q2_2021_chq_amt']."','".$q3_2021_loop."','".$_POST['q3_2021_letter']."','".$_POST['q3_2021_check']."','".$_POST['q3_2021_chq_amt']."','".$q4_2021_loop."','".$_POST['q4_2021_letter']."','".$_POST['q4_2021_check']."','".$_POST['q4_2021_chq_amt']."',
				'".$_POST['q1_2021_max_erc_amount']."','".$_POST['q2_2021_max_erc_amount']."','".$_POST['q3_2021_max_erc_amount']."','".$_POST['q4_2021_max_erc_amount']."','".$_POST['q1_2020_max_erc_amount']."','".$_POST['q2_2020_max_erc_amount']."','".$_POST['q3_2020_max_erc_amount']."','".$_POST['q4_2020_max_erc_amount']."')"
			
		));
	}
    /*}*/// check error_discovered_date
		
	// Handle invoice data updates in invoice_success table
	$invoice_info = $wpdb->get_row("SELECT * FROM {$wpdb->prefix}invoice_success WHERE lead_id = ".$lead_id."");
	if(!empty($invoice_info)){
		// Update existing invoice record
		if (
			isset($_POST['i_invoice_no'])  || isset($_POST['ii_invoice_no'])  ||
			isset($_POST['iii_invoice_no'])|| isset($_POST['iv_invoice_no'])  ||
			isset($_POST['v_invoice_no'])  || isset($_POST['vi_invoice_no'])  ||
			isset($_POST['vii_invoice_no'])
		) {
			$new_invoice_data = array();
			$new_invoice_data['i_invoice_no'] = $_POST['i_invoice_no'];
			$new_invoice_data['i_invoice_amount'] = $_POST['i_invoice_amount'];
			$new_invoice_data['i_invoiced_qtrs'] = $_POST['i_invoiced_qtrs'];
			$new_invoice_data['i_invoice_sent_date'] = $i_invoice_sent_date;
			$new_invoice_data['i_invoice_payment_type'] = $_POST['i_invoice_payment_type'];
			$new_invoice_data['i_invoice_payment_date'] = $i_invoice_payment_date;
			$new_invoice_data['i_invoice_pay_cleared'] = $i_invoice_pay_cleared;
			$new_invoice_data['i_invoice_pay_returned'] = $i_invoice_pay_returned;
			$new_invoice_data['i_invoice_return_reason'] = $_POST['i_invoice_return_reason'];
			$new_invoice_data['i_invoice_occams_share'] = $_POST['i_invoice_occams_share'];
			$new_invoice_data['i_invoice_aff_ref_share'] = $_POST['i_invoice_aff_ref_share'];
			
			$new_invoice_data['ii_invoice_no'] = $_POST['ii_invoice_no'];
			$new_invoice_data['ii_invoice_amount'] = $_POST['ii_invoice_amount'];
			$new_invoice_data['ii_invoiced_qtrs'] = $_POST['ii_invoiced_qtrs'];
			$new_invoice_data['ii_invoice_sent_date'] = $ii_invoice_sent_date;
			$new_invoice_data['ii_invoice_payment_type'] = $_POST['ii_invoice_payment_type'];
			$new_invoice_data['ii_invoice_payment_date'] = $ii_invoice_payment_date;
			$new_invoice_data['ii_invoice_pay_cleared'] = $ii_invoice_pay_cleared;
			$new_invoice_data['ii_invoice_pay_returned'] = $ii_invoice_pay_returned;
			$new_invoice_data['ii_invoice_return_reason'] = $_POST['ii_invoice_return_reason'];
			$new_invoice_data['ii_invoice_occams_share'] = $_POST['ii_invoice_occams_share'];
			$new_invoice_data['ii_invoice_aff_ref_share'] = $_POST['ii_invoice_aff_ref_share'];
			
			$new_invoice_data['iii_invoice_no'] = $_POST['iii_invoice_no'];
			$new_invoice_data['iii_invoice_amount'] = $_POST['iii_invoice_amount'];
			$new_invoice_data['iii_invoiced_qtrs'] = $_POST['iii_invoiced_qtrs'];
			$new_invoice_data['iii_invoice_sent_date'] = $iii_invoice_sent_date;
			$new_invoice_data['iii_invoice_payment_type'] = $_POST['iii_invoice_payment_type'];
			$new_invoice_data['iii_invoice_payment_date'] = $iii_invoice_payment_date;
			$new_invoice_data['iii_invoice_pay_cleared'] = $iii_invoice_pay_cleared;
			$new_invoice_data['iii_invoice_pay_returned'] = $iii_invoice_pay_returned;
			$new_invoice_data['iii_invoice_return_reason'] = $_POST['iii_invoice_return_reason'];
			$new_invoice_data['iii_invoice_occams_share'] = $_POST['iii_invoice_occams_share'];
			$new_invoice_data['iii_invoice_aff_ref_share'] = $_POST['iii_invoice_aff_ref_share'];
			
			$new_invoice_data['iv_invoice_no'] = $_POST['iv_invoice_no'];
			$new_invoice_data['iv_invoice_amount'] = $_POST['iv_invoice_amount'];
			$new_invoice_data['iv_invoiced_qtrs'] = $_POST['iv_invoiced_qtrs'];
			$new_invoice_data['iv_invoice_sent_date'] = $iv_invoice_sent_date;
			$new_invoice_data['iv_invoice_payment_type'] = $_POST['iv_invoice_payment_type'];
			$new_invoice_data['iv_invoice_payment_date'] = $iv_invoice_payment_date;
			$new_invoice_data['iv_invoice_pay_cleared'] = $iv_invoice_pay_cleared;
			$new_invoice_data['iv_invoice_pay_returned'] = $iv_invoice_pay_returned;
			$new_invoice_data['iv_invoice_return_reason'] = $_POST['iv_invoice_return_reason'];
			$new_invoice_data['iv_invoice_occams_share'] = $_POST['iv_invoice_occams_share'];
			$new_invoice_data['iv_invoice_aff_ref_share'] = $_POST['iv_invoice_aff_ref_share'];
			
			$new_invoice_data['v_invoice_no'] = $_POST['v_invoice_no'];
			$new_invoice_data['v_invoice_amount'] = $_POST['v_invoice_amount'];
			$new_invoice_data['v_invoiced_qtrs'] = $_POST['v_invoiced_qtrs'];
			$new_invoice_data['v_invoice_sent_date'] = $v_invoice_sent_date;
			$new_invoice_data['v_invoice_payment_type'] = $_POST['v_invoice_payment_type'];
			$new_invoice_data['v_invoice_payment_date'] = $v_invoice_payment_date;
			$new_invoice_data['v_invoice_pay_cleared'] = $v_invoice_pay_cleared;
			$new_invoice_data['v_invoice_pay_returned'] = $v_invoice_pay_returned;
			$new_invoice_data['v_invoice_return_reason'] = $_POST['v_invoice_return_reason'];
			$new_invoice_data['v_invoice_occams_share'] = $_POST['v_invoice_occams_share'];
			$new_invoice_data['v_invoice_aff_ref_share'] = $_POST['v_invoice_aff_ref_share'];	

			$new_invoice_data['vi_invoice_no'] = $_POST['vi_invoice_no'];
			$new_invoice_data['vi_invoice_amount'] = $_POST['vi_invoice_amount'];
			$new_invoice_data['vi_invoiced_qtrs'] = $_POST['vi_invoiced_qtrs'];
			$new_invoice_data['vi_invoice_sent_date'] = $vi_invoice_sent_date;
			$new_invoice_data['vi_invoice_payment_type'] = $_POST['vi_invoice_payment_type'];
			$new_invoice_data['vi_invoice_payment_date'] = $vi_invoice_payment_date;
			$new_invoice_data['vi_invoice_pay_cleared'] = $vi_invoice_pay_cleared;
			$new_invoice_data['vi_invoice_pay_returned'] = $vi_invoice_pay_returned;
			$new_invoice_data['vi_invoice_return_reason'] = $_POST['vi_invoice_return_reason'];
			$new_invoice_data['vi_invoice_occams_share'] = $_POST['vi_invoice_occams_share'];
			$new_invoice_data['vi_invoice_aff_ref_share'] = $_POST['vi_invoice_aff_ref_share'];

			$new_invoice_data['vii_invoice_no'] = $_POST['vii_invoice_no'];
			$new_invoice_data['vii_invoice_amount'] = $_POST['vii_invoice_amount'];
			$new_invoice_data['vii_invoiced_qtrs'] = $_POST['vii_invoiced_qtrs'];
			$new_invoice_data['vii_invoice_sent_date'] = $vii_invoice_sent_date;
			$new_invoice_data['vii_invoice_payment_type'] = $_POST['vii_invoice_payment_type'];
			$new_invoice_data['vii_invoice_payment_date'] = $vii_invoice_payment_date;
			$new_invoice_data['vii_invoice_pay_cleared'] = $vii_invoice_pay_cleared;
			$new_invoice_data['vii_invoice_pay_returned'] = $vii_invoice_pay_returned;
			$new_invoice_data['vii_invoice_return_reason'] = $_POST['vii_invoice_return_reason'];
			$new_invoice_data['vii_invoice_occams_share'] = $_POST['vii_invoice_occams_share'];
			$new_invoice_data['vii_invoice_aff_ref_share'] = $_POST['vii_invoice_aff_ref_share'];			
			
			$invoice_table = $wpdb->prefix.'invoice_success';
			log_audit_entry($lead_id, $invoice_info, $new_invoice_data, $invoice_table);
			
			$wpdb->query("UPDATE {$wpdb->prefix}invoice_success SET 
				i_invoice_no = '".$_POST['i_invoice_no']."',
				i_invoice_amount = '".$_POST['i_invoice_amount']."',
				i_invoiced_qtrs = '".$_POST['i_invoiced_qtrs']."',
				i_invoice_sent_date = '".$i_invoice_sent_date."',
				i_invoice_payment_type = '".$_POST['i_invoice_payment_type']."',
				i_invoice_payment_date = '".$i_invoice_payment_date."',
				i_invoice_pay_cleared = '".$i_invoice_pay_cleared."',
				i_invoice_pay_returned = '".$i_invoice_pay_returned."',
				i_invoice_return_reason = '".$_POST['i_invoice_return_reason']."',
				i_invoice_occams_share = '".$_POST['i_invoice_occams_share']."',
				i_invoice_aff_ref_share = '".$_POST['i_invoice_aff_ref_share']."',
				ii_invoice_no = '".$_POST['ii_invoice_no']."',
				ii_invoice_amount = '".$_POST['ii_invoice_amount']."',
				ii_invoiced_qtrs = '".$_POST['ii_invoiced_qtrs']."',
				ii_invoice_sent_date = '".$ii_invoice_sent_date."',
				ii_invoice_payment_type = '".$_POST['ii_invoice_payment_type']."',
				ii_invoice_payment_date = '".$ii_invoice_payment_date."',
				ii_invoice_pay_cleared = '".$ii_invoice_pay_cleared."',
				ii_invoice_pay_returned = '".$ii_invoice_pay_returned."',
				ii_invoice_return_reason = '".$_POST['ii_invoice_return_reason']."',
				ii_invoice_occams_share = '".$_POST['ii_invoice_occams_share']."',
				ii_invoice_aff_ref_share = '".$_POST['ii_invoice_aff_ref_share']."',
				iii_invoice_no = '".$_POST['iii_invoice_no']."',
				iii_invoice_amount = '".$_POST['iii_invoice_amount']."',
				iii_invoiced_qtrs = '".$_POST['iii_invoiced_qtrs']."',
				iii_invoice_sent_date = '".$iii_invoice_sent_date."',
				iii_invoice_payment_type = '".$_POST['iii_invoice_payment_type']."',
				iii_invoice_payment_date = '".$iii_invoice_payment_date."',
				iii_invoice_pay_cleared = '".$iii_invoice_pay_cleared."',
				iii_invoice_pay_returned = '".$iii_invoice_pay_returned."',
				iii_invoice_return_reason = '".$_POST['iii_invoice_return_reason']."',
				iii_invoice_occams_share = '".$_POST['iii_invoice_occams_share']."',
				iii_invoice_aff_ref_share = '".$_POST['iii_invoice_aff_ref_share']."',
				iv_invoice_no = '".$_POST['iv_invoice_no']."',
				iv_invoice_amount = '".$_POST['iv_invoice_amount']."',
				iv_invoiced_qtrs = '".$_POST['iv_invoiced_qtrs']."',
				iv_invoice_sent_date = '".$iv_invoice_sent_date."',
				iv_invoice_payment_type = '".$_POST['iv_invoice_payment_type']."',
				iv_invoice_payment_date = '".$iv_invoice_payment_date."',
				iv_invoice_pay_cleared = '".$iv_invoice_pay_cleared."',
				iv_invoice_pay_returned = '".$iv_invoice_pay_returned."',
				iv_invoice_return_reason = '".$_POST['iv_invoice_return_reason']."',
				iv_invoice_occams_share = '".$_POST['iv_invoice_occams_share']."',
				iv_invoice_aff_ref_share = '".$_POST['iv_invoice_aff_ref_share']."',

				v_invoice_no = '".$_POST['v_invoice_no']."',
				v_invoice_amount = '".$_POST['v_invoice_amount']."',
				v_invoiced_qtrs = '".$_POST['v_invoiced_qtrs']."',
				v_invoice_sent_date = '".$v_invoice_sent_date."',
				v_invoice_payment_type = '".$_POST['v_invoice_payment_type']."',
				v_invoice_payment_date = '".$v_invoice_payment_date."',
				v_invoice_pay_cleared = '".$v_invoice_pay_cleared."',
				v_invoice_pay_returned = '".$v_invoice_pay_returned."',
				v_invoice_return_reason = '".$_POST['v_invoice_return_reason']."',
				v_invoice_occams_share = '".$_POST['v_invoice_occams_share']."',
				v_invoice_aff_ref_share = '".$_POST['v_invoice_aff_ref_share']."',

				vi_invoice_no = '".$_POST['vi_invoice_no']."',
				vi_invoice_amount = '".$_POST['vi_invoice_amount']."',
				vi_invoiced_qtrs = '".$_POST['vi_invoiced_qtrs']."',
				vi_invoice_sent_date = '".$vi_invoice_sent_date."',
				vi_invoice_payment_type = '".$_POST['vi_invoice_payment_type']."',
				vi_invoice_payment_date = '".$vi_invoice_payment_date."',
				vi_invoice_pay_cleared = '".$vi_invoice_pay_cleared."',
				vi_invoice_pay_returned = '".$vi_invoice_pay_returned."',
				vi_invoice_return_reason = '".$_POST['vi_invoice_return_reason']."',
				vi_invoice_occams_share = '".$_POST['vi_invoice_occams_share']."',
				vi_invoice_aff_ref_share = '".$_POST['vi_invoice_aff_ref_share']."',

				vii_invoice_no = '".$_POST['vii_invoice_no']."',
				vii_invoice_amount = '".$_POST['vii_invoice_amount']."',
				vii_invoiced_qtrs = '".$_POST['vii_invoiced_qtrs']."',
				vii_invoice_sent_date = '".$vii_invoice_sent_date."',
				vii_invoice_payment_type = '".$_POST['vii_invoice_payment_type']."',
				vii_invoice_payment_date = '".$vii_invoice_payment_date."',
				vii_invoice_pay_cleared = '".$vii_invoice_pay_cleared."',
				vii_invoice_pay_returned = '".$vii_invoice_pay_returned."',
				vii_invoice_return_reason = '".$_POST['vii_invoice_return_reason']."',
				vii_invoice_occams_share = '".$_POST['vii_invoice_occams_share']."',
				vii_invoice_aff_ref_share = '".$_POST['vii_invoice_aff_ref_share']."'				
				WHERE lead_id = ".$lead_id."
			");
		}
	}else{
		// Insert new invoice record
		$wpdb->query("INSERT INTO {$wpdb->prefix}invoice_success (
			lead_id,
			i_invoice_no, i_invoice_amount, i_invoiced_qtrs, i_invoice_sent_date, i_invoice_payment_type,
			i_invoice_payment_date, i_invoice_pay_cleared, i_invoice_pay_returned, i_invoice_return_reason,
			i_invoice_occams_share, i_invoice_aff_ref_share,
			ii_invoice_no, ii_invoice_amount, ii_invoiced_qtrs, ii_invoice_sent_date, ii_invoice_payment_type,
			ii_invoice_payment_date, ii_invoice_pay_cleared, ii_invoice_pay_returned, ii_invoice_return_reason,
			ii_invoice_occams_share, ii_invoice_aff_ref_share,
			iii_invoice_no, iii_invoice_amount, iii_invoiced_qtrs, iii_invoice_sent_date, iii_invoice_payment_type,
			iii_invoice_payment_date, iii_invoice_pay_cleared, iii_invoice_pay_returned, iii_invoice_return_reason,
			iii_invoice_occams_share, iii_invoice_aff_ref_share,
			iv_invoice_no, iv_invoice_amount, iv_invoiced_qtrs, iv_invoice_sent_date, iv_invoice_payment_type,
			iv_invoice_payment_date, iv_invoice_pay_cleared, iv_invoice_pay_returned, iv_invoice_return_reason,
			iv_invoice_occams_share, iv_invoice_aff_ref_share,
			
			v_invoice_no, v_invoice_amount, v_invoiced_qtrs, v_invoice_sent_date, v_invoice_payment_type,
			v_invoice_payment_date, v_invoice_pay_cleared, v_invoice_pay_returned, v_invoice_return_reason,
			v_invoice_occams_share, v_invoice_aff_ref_share,

			vi_invoice_no, vi_invoice_amount, vi_invoiced_qtrs, vi_invoice_sent_date, vi_invoice_payment_type,
			vi_invoice_payment_date, vi_invoice_pay_cleared, vi_invoice_pay_returned, vi_invoice_return_reason,
			vi_invoice_occams_share, vi_invoice_aff_ref_share,

			vii_invoice_no, vii_invoice_amount, vii_invoiced_qtrs, vii_invoice_sent_date, vii_invoice_payment_type,
			vii_invoice_payment_date, vii_invoice_pay_cleared, vii_invoice_pay_returned, vii_invoice_return_reason,
			vii_invoice_occams_share, vii_invoice_aff_ref_share		
		) VALUES (
			".$lead_id.",
			'".$_POST['i_invoice_no']."', '".$_POST['i_invoice_amount']."', '".$_POST['i_invoiced_qtrs']."', '".$i_invoice_sent_date."', '".$_POST['i_invoice_payment_type']."',
			'".$i_invoice_payment_date."', '".$i_invoice_pay_cleared."', '".$i_invoice_pay_returned."', '".$_POST['i_invoice_return_reason']."',
			'".$_POST['i_invoice_occams_share']."', '".$_POST['i_invoice_aff_ref_share']."',
			'".$_POST['ii_invoice_no']."', '".$_POST['ii_invoice_amount']."', '".$_POST['ii_invoiced_qtrs']."', '".$ii_invoice_sent_date."', '".$_POST['ii_invoice_payment_type']."',
			'".$ii_invoice_payment_date."', '".$ii_invoice_pay_cleared."', '".$ii_invoice_pay_returned."', '".$_POST['ii_invoice_return_reason']."',
			'".$_POST['ii_invoice_occams_share']."', '".$_POST['ii_invoice_aff_ref_share']."',
			'".$_POST['iii_invoice_no']."', '".$_POST['iii_invoice_amount']."', '".$_POST['iii_invoiced_qtrs']."', '".$iii_invoice_sent_date."', '".$_POST['iii_invoice_payment_type']."',
			'".$iii_invoice_payment_date."', '".$iii_invoice_pay_cleared."', '".$iii_invoice_pay_returned."', '".$_POST['iii_invoice_return_reason']."',
			'".$_POST['iii_invoice_occams_share']."', '".$_POST['iii_invoice_aff_ref_share']."',
			'".$_POST['iv_invoice_no']."', '".$_POST['iv_invoice_amount']."', '".$_POST['iv_invoiced_qtrs']."', '".$iv_invoice_sent_date."', '".$_POST['iv_invoice_payment_type']."',
			'".$iv_invoice_payment_date."', '".$iv_invoice_pay_cleared."', '".$iv_invoice_pay_returned."', '".$_POST['iv_invoice_return_reason']."',
			'".$_POST['iv_invoice_occams_share']."', '".$_POST['iv_invoice_aff_ref_share']."',
			
			'".$_POST['v_invoice_no']."', '".$_POST['v_invoice_amount']."', '".$_POST['v_invoiced_qtrs']."', '".$v_invoice_sent_date."', '".$_POST['v_invoice_payment_type']."',
			'".$v_invoice_payment_date."', '".$v_invoice_pay_cleared."', '".$v_invoice_pay_returned."', '".$_POST['v_invoice_return_reason']."',
			'".$_POST['v_invoice_occams_share']."', '".$_POST['v_invoice_aff_ref_share']."',

			'".$_POST['vi_invoice_no']."', '".$_POST['vi_invoice_amount']."', '".$_POST['vi_invoiced_qtrs']."', '".$vi_invoice_sent_date."', '".$_POST['vi_invoice_payment_type']."',
			'".$vi_invoice_payment_date."', '".$vi_invoice_pay_cleared."', '".$vi_invoice_pay_returned."', '".$_POST['vi_invoice_return_reason']."',
			'".$_POST['vi_invoice_occams_share']."', '".$_POST['vi_invoice_aff_ref_share']."',

			'".$_POST['vii_invoice_no']."', '".$_POST['vii_invoice_amount']."', '".$_POST['vii_invoiced_qtrs']."', '".$vii_invoice_sent_date."', '".$_POST['vii_invoice_payment_type']."',
			'".$vii_invoice_payment_date."', '".$vii_invoice_pay_cleared."', '".$vii_invoice_pay_returned."', '".$_POST['vii_invoice_return_reason']."',
			'".$_POST['vii_invoice_occams_share']."', '".$_POST['vii_invoice_aff_ref_share']."'			
		)");
	}
		
	$curl = curl_init();

		curl_setopt_array($curl, array(
		CURLOPT_URL => get_site_url() . '/wp-json/v1/lead-quarter-summary/update/single/',
		CURLOPT_RETURNTRANSFER => true,
		CURLOPT_ENCODING => '',
		CURLOPT_MAXREDIRS => 10,
		CURLOPT_TIMEOUT => 0,
		CURLOPT_FOLLOWLOCATION => true,
		CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
		CURLOPT_CUSTOMREQUEST => 'POST',
		CURLOPT_POSTFIELDS => json_encode(array(
			"lead_id" => $lead_id
		)),
		CURLOPT_HTTPHEADER => array(
			'Content-Type: application/json',
			'Cookie: PHPSESSID=qvipbg14duh22kinkot19eqtte'
		),
		));

		$response = curl_exec($curl);

		curl_close($curl);
		// echo $response;

}// project id check 


function log_audit_entry($lead_id, $old_data, $new_data,$table_name) {
	// echo $lead_id; 
	// print_r($old_data);
	// print_r($new_data);
	// echo $table_name;

    global $wpdb;
    // Get the current user ID
    $user_id = get_current_user_id();

    // Ensure old data is retrieved
    if ($old_data) {
        // Compare old and new data to find changes
 
        $changes = array();
        foreach ($new_data as $field_name => $new_value) {
            $old_value = isset($old_data->$field_name) ? $old_data->$field_name : null;
            
            if($new_value =='N/A' && $old_value==''){
            	$new_value = '';
            	$old_value = '';
            }else if($new_value =='' && $old_value=='N/A'){
            	$new_value = '';
            	$old_value = '';
            }else if($new_value =='' && $old_value=='NO'){
            	$new_value = '';
            	$old_value = '';
            }else if($new_value =='NO' && $old_value==''){
            	$new_value = '';
            	$old_value = '';
            }

            if (trim($new_value) !== trim($old_value) ) {
            	      //if(($new_value !='N/A' && $old_value!='N/A') && ($new_value !='' && $old_value!='N/A') && ($new_value !='N/A' && $old_value!='')){
                		$changes[$field_name] = array('old' => $old_value, 'new' => $new_value);
                	  // }
           }
        }
        
        // echo "-------changes-----";
        // echo $table_name;
        // print_r($changes);
	// die();
        
        // Insert each change into the audit log
        foreach ($changes as $field_name => $change) {
            $audit_data = array(
                            'DateCreated' => current_time('mysql'),
                            'CreatedBy' => $user_id,
                            'TableName' => $table_name,
                            'FieldName' => $field_name,
                            'DataType' => gettype($change['new']),
                            'BeforeValueString' => $change['old'],
                            'AfterValueString' => $change['new'],
                            'FieldID' => $lead_id,
                            'Action' => 'Update',
                        );

            // Insert the audit log entry
            $result = $wpdb->insert($wpdb->prefix . 'audit_logs', $audit_data);
            
            if ($result === false) {
                error_log("Error inserting audit log: " . $wpdb->last_error);
            }
        }
    } else {
        // Log an error or take action if old data cannot be retrieved
        error_log("Error: Could not retrieve old data for lead_id $lead_id");
    }
}

// --- Always return clean JSON for jQuery (STRING JSON) ---
if (ob_get_length()) { ob_clean(); }                 // कोई भी accidental output हटाएँ
if (!headers_sent()) { header('Content-Type: text/plain; charset=utf-8'); }

echo json_encode([
    'status'  => 200,
    'message' => 'Project has been updated successfully'
], JSON_UNESCAPED_UNICODE);

wp_die();
