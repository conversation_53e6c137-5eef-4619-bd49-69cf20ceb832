<?php
// ini_set('display_errors', 1);
// ini_set('display_startup_errors', 1);
// error_reporting(E_ALL);
// get affilate user id for current lead of page
global $wpdb;
$lead_id = $_GET['lead_id'];
$getLeadId = $_GET['lead_id'];
$additional_info_table = $wpdb->prefix . 'erc_business_info';
$lead_additional_data = $wpdb->prepare("SELECT business_email FROM $additional_info_table WHERE lead_id = %d", $getLeadId);
$lead_buisness_mail = $wpdb->get_var($lead_additional_data);
require_once 'link-generated-function.php';
//---= end user id affilate =---
$sales_team_table = $wpdb->prefix . 'erc_sales_team';
// ------ get sales support value -----------
$active_sales_support = $wpdb->get_results("SELECT userid FROM $sales_team_table WHERE active_sales_support = 1");
// Create an array of user IDs
$active_sales_supportid = array();
foreach ($active_sales_support as $row) {
    $a_user_id = $row->userid;
    if ($a_user_id != 0) {
        $active_sales_supportid[] = $a_user_id;
    }
}
if (!empty($active_sales_supportid)) {
    $sales_support_users = new WP_User_Query(
        array(
            'fields' => array('ID', 'display_name'),
            'include' => $active_sales_supportid,
        )
    );
    $sales_support_user = $sales_support_users->get_results();
} else {
    $sales_support_user = array();
}
//------------------------------------- Active sales agent ------------
$active_sales_team = $wpdb->get_results("SELECT user_id,userid FROM $sales_team_table WHERE active_sales_agent = 1");

// Create an array of user IDs
$activ_sales_userids = array();
foreach ($active_sales_team as $row) {
    $a_user_id = $row->userid;
    if ($a_user_id != 0) {
        $activ_sales_userids[] = $a_user_id;
    }
}
if (!empty($activ_sales_userids)) {
    $sales_agent_users = new WP_User_Query(
        array(
            'fields' => array('ID', 'display_name'),
            'include' => $activ_sales_userids,
        )
    );
    $sales_agent_user = $sales_agent_users->get_results();
} else {
    $sales_agent_user = array();
}
$current_user_id = get_current_user_id();
$current_user_data = get_user_by('id', $current_user_id);
$current_user_name = $current_user_data->data->display_name;

$can_send_sms = false;
$can_send_sms = do_shortcode('[comm_log_can_send_sms]');
$can_send_email = false;
$can_send_email = do_shortcode('[comm_log_can_send_email]');
//------------- Assign user list -----------
$sales_users = $wpdb->get_results("SELECT full_name,user_id,user_role FROM $sales_team_table WHERE user_id!=0 AND userid!=0 ORDER BY full_name ASC");
$userdata = get_user_by("id", get_current_user_id());
$user_roles = $userdata->roles;
$col_class_according_role = "col-md-8";
$role_css = "";
$opportunity_css = 'style="display:None;"';
if (isset($user_roles[0])) {
    if ($user_roles[0] == "iris_affiliate_users" || $user_roles[0] == "account_manager" || $user_roles[0] == "lead") {
    } elseif ($user_roles[0] == "iris_sales_agent" || $user_roles[0] == "master_sales") {
        $opportunity_css = 'style="display:block;"';
    }
}
$readonly = "";
$disabled = "";
$affiliate_user = 0;
if (in_array("iris_affiliate_users", $user_roles) || in_array("account_manager", $user_roles) || in_array("iris_employee", $user_roles) || $user_roles[0] == "lead") {
    $readonly = "readonly";
    $disabled = "disabled";
    $affiliate_user = 1;
} elseif (in_array("iris_sales_agent", $user_roles)) {
    $opportunity_css = 'style="display:block;"';
}
$ercbusi_business_legal_name = "";
$ercbusi_doing_business_as = "";
$ercbusi_business_category = "";
$ercbusi_website_url = "";
$ercbusi_mid = "";
$ercbusi_authorized_signatory_name = "";
$ercbusi_business_phone = "";
$ercbusi_business_email = "";
$ercbusi_street_address = "";
$ercbusi_city = "";
$ercbusi_state = "";
$ercbusi_zip = "";
$ercbusi_country = "";
$ercbusi_business_title = "";
$ercbusi_payment_type = "";
$ercbusi_pain_points = "";
$ercbusi_fees_paid_today = "";
$ercbusi_average_sales_per_month = "";
$ercbusi_average_ticket_size = "";
$ercbusi_store_type = "";
$ercbusi_business_entity_type = "";
$ercbusi_if_other = "";
$ercbusi_registration_number = "";
$ercbusi_registration_date = "";
$ercbusi_state_of_registration = "";
$ercbusi_ein = "";
$ercbusi_tax_id_type = "";
$ercbusd_system_name = "";
$ercbusd_sales_agent_name = "";
$ercbusd_sales_agent_email = "";
$ercbusd_tracking_url = "";
$ercci_signer_full_name = "";
$ercci_signer_email = "";
$ercci_signer_work_phone = "";
$ercci_signer_cell_phone = "";
$ercci_signer_home_address = "";
$ercci_signer_city = "";
$ercci_signer_state = "";
$ercci_signer_zip = "";
$ercci_signer_country = "";
$ercci_signer_date_of_birth = "";
$ercci_signer_photo_id_type = "";
$ercci_signer_photo_id_number = "";
$ercci_primary_contact_full_name = "";
$ercci_primary_contact_email = "";
$ercci_primary_contact_work_phone = "";
$ercci_primary_contact_cell_phone = "";
$ercci_primary_contact_street_address = "";
$ercci_primary_contact_city = "";
$ercci_primary_contact_state = "";
$ercci_primary_contact_zip = "";
$ercci_primary_contact_country = "";
$ercci_customer_service_contact_full_name = "";
$ercci_customer_service_contact_email = "";
$ercci_customer_service_contact_work_phone = "";
$ercci_finance_contact_full_name = "";
$ercci_finance_contact_email = "";
$ercci_finance_contact_work_phone = "";
$ercci_sales_contact_full_name = "";
$ercci_sales_contact_email = "";
$ercci_sales_contact_work_phone = "";
$ercci_techical_contact_full_name = "";
$ercci_techical_contact_email = "";
$ercci_techical_contact_work_phone = "";
$ercown_first_name = "";
$ercown_last_name = "";
$ercown_email = "";
$ercown_work_phone = "";
$ercown_cell_phone = "";
$ercown_director = "";
$ercown_home_address = "";
$ercown_city = "";
$ercown_state = "";
$ercown_zip = "";
$ercown_country = "";
$ercown_date_of_birth = "";
$ercown_ssn = "";
$ercown_photo_id_type = "";
$ercown_photo_id_number = "";
$ercown_id_issuing_state_country = "";
$ercown_ownership = "";
$ercbi_bank_name = "";
$ercbi_bank_mailing_address = "";
$ercbi_city = "";
$ercbi_state = "";
$ercbi_zip = "";
$ercbi_country = "";
$ercbi_bank_phone = "";
$ercbi_account_holder_name = "";
$ercbi_account_type = "";
$ercbi_other = "";
$ercbi_aba_routing_number = "";
$ercbi_account_number = "";
$ercbi_swift = "";
$ercbi_iban = "";
$ercin_w2_employees_count = "";
$ercin_initial_retain_fee_amount = "";
$ercin_w2_ee_difference_count = "";
$ercin_balance_retainer_fee = "";
$ercin_total_max_erc_amount = "";
$ercin_total_estimated_fees = "";
$ercin_affiliate_referral_fees = "";
$ercin_avg_emp_count_2019 = "";
$ercin_fee_type = "";
$custom_fee = "";
$ercin_company_folder_link = "";
$ercin_document_folder_link = "";
$ercin_eligible_quarters = "";
$ercin_welcome_email = "";
$ercin_retainer_payment_date = "";
$ercin_retainer_payment_cleared = "";
$ercin_retainer_payment_returned = "";
$ercin_ret_payment_return_reason = "";
$ercin_retainer_refund_date = "";
$ercin_retainer_refund_amount = "";
$ercin_retainer_payment_amount = "";
$ercin_retainer_payment_type = "";
$ercin_bal_retainer_pay_date = "";
$ercin_bal_retainer_clear_date = "";
$ercin_bal_retainer_return_date = "";
$ercin_bal_retainer_return_reaso = "";
$ercin_coi_aoi = "";
$ercin_voided_check = "";
$ercin_2019_tax_return = "";
$ercin_2020_tax_return = "";
$ercin_2021_financials = "";
$ercin_2020_q1 = "";
$ercin_2020_q2 = "";
$ercin_2020_q3 = "";
$ercin_2020_q4 = "";
$ercin_2021_q1 = "";
$ercin_payroll_register_2021_q2 = "";
$ercin_2021_q3 = "";
$ercin_payroll_register_2020_q1 = "";
$ercin_payroll_register_2020_q2 = "";
$ercin_payroll_register_2020_q3 = "";
$ercin_payroll_register_2020_q4 = "";
$ercin_payroll_register_2021_q1 = "";
$ercin_2021_q2 = "";
$ercin_payroll_register_2021_q3 = "";
$ercin_ppp_1_applied = "";
$ercin_ppp_1_date = "";
$ercin_ppp_1_forgiveness_applied = "";
$ercin_ppp_1_forgive_app_date = "";
$ercin_ppp_1_amount = "";
$ercin_ppp_1_wages_allocated = "";
$ercin_ppp_2_applied = "";
$ercin_ppp_2_date = "";
$ercin_ppp_2_forgiveness_applied = "";
$ercin_ppp_2_forgive_app_date = "";
$ercin_ppp_2_amount = "";
$ercin_ppp_2_wages_allocated = "";
$ercin_additional_comments = "";
$ercfee_sdgr = "";
$ercfee_error_discovered_date = "";
$ercfee_q2_2020_941_wages = "";
$ercfee_q3_2020_941_wages = "";
$ercfee_q4_2020_941_wages = "";
$ercfee_q1_2021_941_wages = "";
$ercfee_q2_2021_941_wages = "";
$ercfee_q3_2021_941_wages = "";
$ercfee_q4_2021_941_wages = "";
$ercfee_affiliate_name = "";
$ercfee_affiliate_percentage = "";
$ercfee_erc_claim_filed = "";
$ercfee_erc_amount_received = "";
$ercfee_total_erc_fees = "";
$ercfee_legal_fees = "";
$ercfee_total_erc_fees_paid = "";
$ercfee_total_erc_fees_pending = "";
$ercfee_total_occams_share = "";
$ercfee_total_aff_ref_share = "";
$ercfee_retain_occams_share = "";
$ercfee_retain_aff_ref_share = "";
$ercfee_bal_retain_occams_share = "";
$ercfee_bal_retain_aff_ref_share = "";
$ercfee_total_occams_share_paid = "";
$ercfee_total_aff_ref_share_paid = "";
$ercfee_total_occams_share_pendin = "";
$ercfee_total_aff_ref_share_pend = "";
$ercfee_q1_2020_filed_status = "";
$ercfee_q1_2020_filing_date = "";
$ercfee_q1_2020_amount_filed = "";
$ercfee_q1_2020_benefits = "";
$ercfee_q2_2020_filed_status = "";
$ercfee_q2_2020_filing_date = "";
$ercfee_q2_2020_amount_filed = "";
$ercfee_q2_2020_benefits = "";
$ercfee_q3_2020_filed_status = "";
$ercfee_q3_2020_filing_date = "";
$ercfee_q3_2020_amount_filed = "";
$ercfee_q3_2020_benefits = "";
$ercfee_q4_2020_filed_status = "";
$ercfee_q4_2020_filing_date = "";
$ercfee_q4_2020_amount_filed = "";
$ercfee_q4_2020_benefits = "";
$ercfee_q1_2021_filed_status = "";
$ercfee_q1_2021_filing_date = "";
$ercfee_q1_2021_amount_filed = "";
$ercfee_q1_2021_benefits = "";
$ercfee_q2_2021_filed_status = "";
$ercfee_q2_2021_filing_date = "";
$ercfee_q2_2021_amount_filed = "";
$ercfee_q2_2021_benefits = "";
$ercfee_q3_2021_filed_status = "";
$ercfee_q3_2021_filing_date = "";
$ercfee_q3_2021_amount_filed = "";
$ercfee_q3_2021_benefits = "";
$ercfee_q4_2021_filed_status = "";
$ercfee_q4_2021_filing_date = "";
$ercfee_q4_2021_amount_filed = "";
$ercfee_q4_2021_benefits = "";
$ercfee_q1_2020_loop = "";
$ercfee_q1_2020_letter = "";
$ercfee_q1_2020_check = "";
$ercfee_q1_2020_chq_amt = "";
$ercfee_q2_2020_loop = "";
$ercfee_q2_2020_letter = "";
$ercfee_q2_2020_check = "";
$ercfee_q2_2020_chq_amt = "";
$ercfee_q3_2020_loop = "";
$ercfee_q3_2020_letter = "";
$ercfee_q3_2020_check = "";
$ercfee_q3_2020_chq_amt = "";
$ercfee_q4_2020_loop = "";
$ercfee_q4_2020_letter = "";
$ercfee_q4_2020_check = "";
$ercfee_q4_2020_chq_amt = "";
$ercfee_q1_2021_loop = "";
$ercfee_q1_2021_letter = "";
$ercfee_q1_2021_check = "";
$ercfee_q1_2021_chq_amt = "";
$ercfee_q2_2021_loop = "";
$ercfee_q2_2021_letter = "";
$ercfee_q2_2021_check = "";
$ercfee_q2_2021_chq_amt = "";
$ercfee_q3_2021_loop = "";
$ercfee_q3_2021_letter = "";
$ercfee_q3_2021_check = "";
$ercfee_q3_2021_chq_amt = "";
$ercfee_q4_2021_loop = "";
$ercfee_q4_2021_letter = "";
$ercfee_q4_2021_check = "";
$ercfee_q4_2021_chq_amt = "";
$ercfee_i_invoice_number = "";
$ercfee_i_invoice_amount = "";
$ercfee_i_invoiced_qtrs = "";
$ercfee_i_invoice_sent_date = "";
$ercfee_i_invoice_payment_type = "";
$ercfee_i_invoice_payment_date = "";
$ercfee_i_invoice_pay_cleared = "";
$ercfee_i_invoice_pay_returned = "";
$ercfee_i_invoice_return_reason = "";
$ercfee_i_invoice_occams_share = "";
$ercfee_i_invoice_aff_ref_share = "";
$ercfee_ii__invoice_number = "";
$ercfee_ii_invoice_amount = "";
$ercfee_ii_invoiced_qtrs = "";
$ercfee_ii_invoice_sent_date = "";
$ercfee_ii_invoice_payment_type = "";
$ercfee_ii_invoice_payment_date = "";
$ercfee_ii_invoice_pay_cleared = "";
$ercfee_ii_invoice_pay_returned = "";
$ercfee_ii_invoice_return_reason = "";
$ercfee_ii_invoice_occams_share = "";
$ercfee_ii_invoice_aff_ref_share = "";
$ercfee_iii_invoice_number = "";
$ercfee_iii_invoice_amount = "";
$ercfee_iii_invoiced_qtrs = "";
$ercfee_iii_invoice_sent_date = "";
$ercfee_iii_invoice_payment_type = "";
$ercfee_iii_invoice_payment_date = "";
$ercfee_iii_invoice_pay_cleared = "";
$ercfee_iii_invoice_pay_returned = "";
$ercfee_iii_invoice_return_reason = "";
$ercfee_iii_invoice_occams_share = "";
$ercfee_iii_invoice_aff_ref_share = "";
$ercfee_iv_invoice_number = "";
$ercfee_iv_invoice_amount = "";
$ercfee_iv_invoiced_qtrs = "";
$ercfee_iv_invoice_sent_date = "";
$ercfee_iv_invoice_payment_type = "";
$ercfee_iv_invoice_payment_date = "";
$ercfee_iv_invoice_pay_cleared = "";
$ercfee_iv_invoice_pay_returned = "";
$ercfee_iv_invoice_return_reason = "";
$ercfee_iv_invoice_occams_share = "";
$ercfee_iv_invoice_aff_ref_share = "";

// Invoice Stage V Variables
$ercfee_v_invoice_number = "";
$ercfee_v_invoice_amount = "";
$ercfee_v_invoiced_qtrs = "";
$ercfee_v_invoice_sent_date = "";
$ercfee_v_invoice_payment_type = "";
$ercfee_v_invoice_payment_date = "";
$ercfee_v_invoice_pay_cleared = "";
$ercfee_v_invoice_pay_returned = "";
$ercfee_v_invoice_return_reason = "";
$ercfee_v_invoice_occams_share = "";
$ercfee_v_invoice_aff_ref_share = "";

// Invoice Stage VI Variables
$ercfee_vi_invoice_number = "";
$ercfee_vi_invoice_amount = "";
$ercfee_vi_invoiced_qtrs = "";
$ercfee_vi_invoice_sent_date = "";
$ercfee_vi_invoice_payment_type = "";
$ercfee_vi_invoice_payment_date = "";
$ercfee_vi_invoice_pay_cleared = "";
$ercfee_vi_invoice_pay_returned = "";
$ercfee_vi_invoice_return_reason = "";
$ercfee_vi_invoice_occams_share = "";
$ercfee_vi_invoice_aff_ref_share = "";

// Invoice Stage VII Variables
$ercfee_vii_invoice_number = "";
$ercfee_vii_invoice_amount = "";
$ercfee_vii_invoiced_qtrs = "";
$ercfee_vii_invoice_sent_date = "";
$ercfee_vii_invoice_payment_type = "";
$ercfee_vii_invoice_payment_date = "";
$ercfee_vii_invoice_pay_cleared = "";
$ercfee_vii_invoice_pay_returned = "";
$ercfee_vii_invoice_return_reason = "";
$ercfee_vii_invoice_occams_share = "";
$ercfee_vii_invoice_aff_ref_share = "";

$opportunity_size = "";
$opportunity_timeline = "";
$confidance_label = "";
$affiliate_commision_basis = '';
$affiliate_commision_type = '';
$referrer_fixed = 0;
$referrer_percentage = 0;
$referrer_percentage2 = 0;

$tier2_affiliate_commision_basis = '';
$tier2_affiliate_commision_type = '';
$tier2_referrer_fixed = 0;
$tier2_referrer_percentage = 0;
$tier2_referrer_percentage2 = 0;

$tier3_affiliate_commision_basis = '';
$tier3_affiliate_commision_type = '';
$tier3_referrer_fixed = 0;
$tier3_referrer_percentage = 0;
$tier3_referrer_percentage2 = 0;
$current_tier = '';
$slab1_applied_on = '';
$slab1_commision_type = '';
$slab1_commision_value = '';
$slab2_applied_on = '';
$slab2_commision_type = '';
$slab2_commision_value = '';
$slab3_applied_on = '';
$slab3_commision_type = '';
$slab3_commision_value = '';
$affiliate_fixed = 0;
$affiliate_percentage_of_subaffiliate = 0;
$affiliate_percentage_of_total = 0;
$master_commision_basis = '';
$master_commision_type = '';
$master_commision_value = '';
$ercfee_q1_2020_max_erc_amount = '';
$ercfee_q2_2020_max_erc_amount = '';
$ercfee_q3_2020_max_erc_amount = '';
$ercfee_q4_2020_max_erc_amount = '';
$ercfee_q1_2021_max_erc_amount = '';
$ercfee_q2_2021_max_erc_amount = '';
$ercfee_q3_2021_max_erc_amount = '';
$ercfee_q4_2021_max_erc_amount = '';
$ercfee_q1_2020_eligibility_basis = '';
$ercfee_q2_2020_eligibility_basis = '';
$ercfee_q3_2020_eligibility_basis = '';
$ercfee_q4_2020_eligibility_basis = '';
$ercfee_q1_2021_eligibility_basis = '';
$ercfee_q2_2021_eligibility_basis = '';
$ercfee_q3_2021_eligibility_basis = '';
$ercfee_q4_2021_eligibility_basis = '';
// ----------------- Get data from db ------
global $wpdb;
$contact_table_name = "eccom_erc_contact_info";
$sql = "SELECT * FROM $contact_table_name WHERE `lead_id`=$lead_id";
$contact_info_data = $wpdb->get_results($sql);
foreach ($contact_info_data as $con_key => $con_value) {
    $con_value = (array) $con_value;
    $ercci_signer_full_name = $con_value["president_full_name"];
    $ercci_signer_email = $con_value["president_email"];
    $ercci_signer_work_phone = $con_value["president_work_phone"];
    $ercci_signer_cell_phone = $con_value["president_cell_phone"];
    $ercci_signer_home_address = $con_value["president_home_address"];
    $ercci_signer_city = $con_value["president_city"];
    $ercci_signer_state = $con_value["president_state"];
    $ercci_signer_zip = $con_value["president_zip"];
    $ercci_signer_country = $con_value["president_country"];
    $ercci_signer_date_of_birth = $con_value["date_of_birth"];
    $ercci_signer_photo_id_type = $con_value["photo_id_type"];
    $ercci_signer_photo_id_number = $con_value["photo_id_number"];
    $ercci_primary_contact_full_name = $con_value["primary_full_name"];
    $ercci_primary_contact_email = $con_value["primary_email"];
    $ercci_primary_contact_work_phone = $con_value["primary_work_phone"];
    $ercci_primary_contact_cell_phone = $con_value["primary_cell_phone"];
    $ercci_primary_contact_street_address = $con_value["street_address"];
    $ercci_primary_contact_city = $con_value["primary_city"];
    $ercci_primary_contact_state = $con_value["primary_state"];
    $ercci_primary_contact_zip = $con_value["primary_zip"];
    $ercci_primary_contact_country = $con_value["primary_country"];
    $ercci_customer_service_contact_full_name =$con_value["customer_service_full_name"];
    $ercci_customer_service_contact_email = $con_value["customer_service_email"];
    $ercci_customer_service_contact_work_phone = $con_value["customer_service_work_phone"];
    $ercci_finance_contact_full_name = $con_value["finance_full_name"];
    $ercci_finance_contact_email = $con_value["finance_email"];
    $ercci_finance_contact_work_phone = $con_value["finance_work_phone"];
    $ercci_sales_contact_full_name = $con_value["sales_full_name"];
    $ercci_sales_contact_email = $con_value["sales_email"];
    $ercci_sales_contact_work_phone = $con_value["sales_work_phone"];
    $ercci_techical_contact_full_name = $con_value["technical_full_name"];
    $ercci_techical_contact_email = $con_value["technical_email"];
    $ercci_techical_contact_work_phone = $con_value["technical_work_phone"];
}
$intake_table_name = "eccom_erc_erc_intake";
$in_sql = "SELECT * FROM $intake_table_name WHERE `lead_id`=$lead_id";
$intake_data = $wpdb->get_results($in_sql);
$data = [];
foreach ($intake_data as $in_key => $in_value) {
    $in_value = (array) $in_value;
    $ercin_w2_employees_count = $in_value["w2_employees_count"];
    $ercin_initial_retain_fee_amount = $in_value["initial_retain_fee_amount"];
    $ercin_w2_ee_difference_count = $in_value["w2_ee_difference_count"];
    $ercin_balance_retainer_fee = $in_value["balance_retainer_fee"];
    $ercin_total_max_erc_amount = $in_value["total_max_erc_amount"];
    $ercin_total_estimated_fees = $in_value["total_estimated_fees"];
    $ercin_affiliate_referral_fees = $in_value["affiliate_referral_fees"];
    $ercin_avg_emp_count_2019 = $in_value["avg_emp_count_2019"];
    $ercin_fee_type = $in_value["fee_type"];
    $custom_fee = $in_value["fee_type"];
    $ercin_company_folder_link = $in_value["company_folder_link"];
    $ercin_document_folder_link = $in_value["document_folder_link"];
    $ercin_eligible_quarters = $in_value["eligible_quarters"];
    $ercin_welcome_email = $in_value["welcome_email"];
    $ercin_retainer_payment_date = $in_value["retainer_payment_date"];
    $ercin_retainer_payment_cleared = $in_value["retainer_payment_cleared"];
    $ercin_retainer_payment_returned = $in_value["retainer_payment_returned"];
    $ercin_ret_payment_return_reason = $in_value["retpayment_return_reason"];
    $ercin_retainer_refund_date = $in_value["retainer_refund_date"];
    $ercin_retainer_refund_amount = $in_value["retainer_refund_amount"];
    $ercin_retainer_payment_amount = $in_value["retainer_payment_amount"];
    $ercin_retainer_payment_type = $in_value["retainer_payment_type"];
    $ercin_bal_retainer_pay_date = $in_value["bal_retainer_pay_date"];
    $ercin_bal_retainer_clear_date = $in_value["bal_retainer_clear_date"];
    $ercin_bal_retainer_return_date = $in_value["bal_retainer_return_date"];
    $ercin_bal_retainer_return_reaso = $in_value["bal_retainer_return_reaso"];
    $ercin_coi_aoi = $in_value["coi_aoi"];
    $ercin_voided_check = $in_value["voided_check"];
    $ercin_2019_tax_return = $in_value["2019_tax_return"];
    $ercin_2020_tax_return = $in_value["2020_tax_return"];
    $ercin_2021_financials = $in_value["2021_financials"];
    $ercin_2020_q1 = $in_value["2020_q1_4144"];
    $ercin_2020_q2 = $in_value["2020_q2_4145"];
    $ercin_2020_q3 = $in_value["2020_q3_4146"];
    $ercin_2020_q4 = $in_value["2020_q4_4147"];
    $ercin_2021_q1 = $in_value["2021_q1_4149"];
    $ercin_2021_q2 = $in_value["2021_q2_4151"];
    $ercin_2021_q3 = $in_value["2021_q3_4152"];
    $ercin_payroll_register_2020_q1 = $in_value["2020_q1_4155"];
    $ercin_payroll_register_2020_q2 = $in_value["2020_q2_4156"];
    $ercin_payroll_register_2020_q3 = $in_value["2020_q3_4157"];
    $ercin_payroll_register_2020_q4 = $in_value["2020_q4_4158"];
    $ercin_payroll_register_2021_q1 = $in_value["2021_q1_4160"];
    $ercin_payroll_register_2021_q2 = $in_value["2021_q2_4161"];
    $ercin_payroll_register_2021_q3 = $in_value["2021_q3_4162"];
    $ercin_ppp_1_applied = $in_value["ppp_1_applied"];
    $ercin_ppp_1_date = $in_value["ppp_1_date"];
    $ercin_ppp_1_forgiveness_applied = $in_value["ppp_1_forgiveness_applied"];
    $ercin_ppp_1_forgive_app_date = $in_value["ppp_1_forgive_app_date"];
    $ercin_ppp_1_amount = $in_value["ppp_1_amount"];
    $ercin_ppp_1_wages_allocated = $in_value["ppp_1_wages_allocated"];
    $ercin_ppp_2_applied = $in_value["ppp_2_applied"];
    $ercin_ppp_2_date = $in_value["ppp_2_date"];
    $ercin_ppp_2_forgiveness_applied = $in_value["ppp_2_forgiveness_applied"];
    $ercin_ppp_2_forgive_app_date = $in_value["ppp_2_forgive_app_date"];
    $ercin_ppp_2_amount = $in_value["ppp_2_amount"];
    $ercin_ppp_2_wages_allocated = $in_value["ppp_2_wages_allocated"];
    $ercin_additional_comments = $in_value["additional_comments"];
    $opportunity_size = $in_value["opportunity_size"];
    $opportunity_timeline = $in_value["opportunity_timeline"];
    $confidance_label = $in_value["confidance_label"];
}
// ---------- ERC FEES ---------
$erc_fees_data = [
    "ercfee_sdgr" => "sdgr",
    "ercfee_error_discovered_date" => "error_discovered_date",
    "ercfee_q2_2020_941_wages" => "q2_2020_941_wages",
    "ercfee_q3_2020_941_wages" => "q3_2020_941_wages",
    "ercfee_q4_2020_941_wages" => "q4_2020_941_wages",
    "ercfee_q1_2021_941_wages" => "q1_2021_941_wages",
    "ercfee_q2_2021_941_wages" => "q2_2021_941_wages",
    "ercfee_q3_2021_941_wages" => "q3_2021_941_wages",
    "ercfee_q4_2021_941_wages" => "q4_2021_941_wages",
    "ercfee_affiliate_name" => "affiliate_name",
    "ercfee_affiliate_percentage" => "affiliate_percentage",
    "ercfee_erc_claim_filed" => "erc_claim_filed",
    "ercfee_erc_amount_received" => "erc_amount_received",
    "ercfee_total_erc_fees" => "total_erc_fees",
    "ercfee_legal_fees" => "legal_fees",
    "ercfee_total_erc_fees_paid" => "total_erc_fees_paid",
    "ercfee_total_erc_fees_pending" => "total_erc_fees_pending",
    "ercfee_total_occams_share" => "total_occams_share",
    "ercfee_total_aff_ref_share" => "total_aff_ref_share",
    "ercfee_retain_occams_share" => "retain_occams_share",
    "ercfee_retain_aff_ref_share" => "retain_aff_ref_share",
    "ercfee_bal_retain_occams_share" => "bal_retain_occams_share",
    "ercfee_bal_retain_aff_ref_share" => "bal_retain_aff_ref_share",
    "ercfee_total_occams_share_paid" => "total_occams_share_paid",
    "ercfee_total_aff_ref_share_paid" => "total_aff_ref_share_paid",
    "ercfee_total_occams_share_pendin" => "total_occams_share_pendin",
    "ercfee_total_aff_ref_share_pend" => "total_aff_ref_share_pend",
    "ercfee_q1_2020_filed_status" => "q1_2020_filed_status",
    "ercfee_q1_2020_filing_date" => "filing_date_4267",
    "ercfee_q1_2020_amount_filed" => "amount_filed_4263",
    "ercfee_q1_2020_benefits" => "q1_2020_benefits",
    "ercfee_q2_2020_filed_status" => "q2_2020_filed_status",
    "ercfee_q2_2020_filing_date" => "filing_date_4268",
    "ercfee_q2_2020_amount_filed" => "amount_filed_4269",
    "ercfee_q2_2020_benefits" => "q2_2020_benefits",
    "ercfee_q3_2020_filed_status" => "q3_2020_filed_status",
    "ercfee_q3_2020_filing_date" => "filing_date_4270",
    "ercfee_q3_2020_amount_filed" => "amount_filed_4266",
    "ercfee_q3_2020_benefits" => "q3_2020_benefits",
    "ercfee_q4_2020_filed_status" => "q4_2020_filed_status",
    "ercfee_q4_2020_filing_date" => "filing_date_4272",
    "ercfee_q4_2020_amount_filed" => "amount_filed_4273",
    "ercfee_q4_2020_benefits" => "q4_2020_benefits",
    "ercfee_q1_2021_filed_status" => "q1_2021_filed_status",
    "ercfee_q1_2021_filing_date" => "filing_date_4276",
    "ercfee_q1_2021_amount_filed" => "amount_filed_4277",
    "ercfee_q1_2021_benefits" => "q1_2021_benefits",
    "ercfee_q2_2021_filed_status" => "Q2_2021_filed_status",
    "ercfee_q2_2021_filing_date" => "filing_date_4279",
    "ercfee_q2_2021_amount_filed" => "amount_filed_4280",
    "ercfee_q2_2021_benefits" => "q2_2021_benefits",
    "ercfee_q3_2021_filed_status" => "q3_2021_filed_status",
    "ercfee_q3_2021_filing_date" => "filing_date_4282",
    "ercfee_q3_2021_amount_filed" => "amount_filed_4283",
    "ercfee_q3_2021_benefits" => "q3_2021_benefits",
    "ercfee_q4_2021_filed_status" => "q4_2021_filed_status",
    "ercfee_q4_2021_filing_date" => "filing_date_4285",
    "ercfee_q4_2021_amount_filed" => "amount_filed_4286",
    "ercfee_q4_2021_benefits" => "q4_2021_benefits",
    "ercfee_q1_2020_loop" => "q1_2020_loop",
    "ercfee_q1_2020_letter" => "q1_2020_letter",
    "ercfee_q1_2020_check" => "q1_2020_check",
    "ercfee_q1_2020_chq_amt" => "q1_2020_chq_amt",
    "ercfee_q2_2020_loop" => "q2_2020_loop",
    "ercfee_q2_2020_letter" => "q2_2020_letter",
    "ercfee_q2_2020_check" => "q2_2020_check",
    "ercfee_q2_2020_chq_amt" => "q2_2020_chq_amt",
    "ercfee_q3_2020_loop" => "q3_2020_loop",
    "ercfee_q3_2020_letter" => "q3_2020_letter",
    "ercfee_q3_2020_check" => "q3_2020_check",
    "ercfee_q3_2020_chq_amt" => "q3_2020_chq_amt",
    "ercfee_q4_2020_loop" => "q4_2020_loop",
    "ercfee_q4_2020_letter" => "q4_2020_letter",
    "ercfee_q4_2020_check" => "q4_2020_check",
    "ercfee_q4_2020_chq_amt" => "q4_2020_chq_amt",
    "ercfee_q1_2021_loop" => "q1_2021_loop",
    "ercfee_q1_2021_letter" => "q1_2021_letter",
    "ercfee_q1_2021_check" => "q1_2021_check",
    "ercfee_q1_2021_chq_amt" => "q1_2021_chq_amt",
    "ercfee_q2_2021_loop" => "q2_2021_loop",
    "ercfee_q2_2021_letter" => "q2_2021_letter",
    "ercfee_q2_2021_check" => "q2_2021_check",
    "ercfee_q2_2021_chq_amt" => "q2_2021_chq_amt",
    "ercfee_q3_2021_loop" => "q3_2021_loop",
    "ercfee_q3_2021_letter" => "q3_2021_letter",
    "ercfee_q3_2021_check" => "q3_2021_check",
    "ercfee_q3_2021_chq_amt" => "q3_2021_chq_amt",
    "ercfee_q4_2021_loop" => "q4_2021_loop",
    "ercfee_q4_2021_letter" => "q4_2021_letter",
    "ercfee_q4_2021_check" => "q4_2021_check",
    "ercfee_q4_2021_chq_amt" => "q4_2021_chq_amt",
    "ercfee_q1_2020_max_erc_amount" => "q1_2020_max_erc_amount",
    "ercfee_q2_2020_max_erc_amount" => "q2_2020_max_erc_amount",
    "ercfee_q3_2020_max_erc_amount" => "q3_2020_max_erc_amount",
    "ercfee_q4_2020_max_erc_amount" => "q4_2020_max_erc_amount",
    "ercfee_q1_2021_max_erc_amount" => "q1_2021_max_erc_amount",
    "ercfee_q2_2021_max_erc_amount" => "q2_2021_max_erc_amount",
    "ercfee_q3_2021_max_erc_amount" => "q3_2021_max_erc_amount",
    "ercfee_q4_2021_max_erc_amount" => "q4_2021_max_erc_amount",
    "ercfee_q1_2020_eligibility_basis" => "q1_2020_eligibility_basis",
    "ercfee_q2_2020_eligibility_basis" => "q2_2020_eligibility_basis",
    "ercfee_q3_2020_eligibility_basis" => "q3_2020_eligibility_basis",
    "ercfee_q4_2020_eligibility_basis" => "q4_2020_eligibility_basis",
    "ercfee_q1_2021_eligibility_basis" => "q1_2021_eligibility_basis",
    "ercfee_q2_2021_eligibility_basis" => "q2_2021_eligibility_basis",
    "ercfee_q3_2021_eligibility_basis" => "q3_2021_eligibility_basis",
    "ercfee_q4_2021_eligibility_basis" => "q4_2021_eligibility_basis"
];

// ---------- INVOICE SUCCESS DATA ---------
$invoice_success_data = [
    "ercfee_i_invoice_number" => "i_invoice_no",
    "ercfee_i_invoice_amount" => "i_invoice_amount",
    "ercfee_i_invoiced_qtrs" => "i_invoiced_qtrs",
    "ercfee_i_invoice_sent_date" => "i_invoice_sent_date",
    "ercfee_i_invoice_payment_type" => "i_invoice_payment_type",
    "ercfee_i_invoice_payment_date" => "i_invoice_payment_date",
    "ercfee_i_invoice_pay_cleared" => "i_invoice_pay_cleared",
    "ercfee_i_invoice_pay_returned" => "i_invoice_pay_returned",
    "ercfee_i_invoice_return_reason" => "i_invoice_return_reason",
    "ercfee_i_invoice_occams_share" => "i_invoice_occams_share",
    "ercfee_i_invoice_aff_ref_share" => "i_invoice_aff_ref_share",
    "ercfee_ii__invoice_number" => "ii_invoice_no",
    "ercfee_ii_invoice_amount" => "ii_invoice_amount",
    "ercfee_ii_invoiced_qtrs" => "ii_invoiced_qtrs",
    "ercfee_ii_invoice_sent_date" => "ii_invoice_sent_date",
    "ercfee_ii_invoice_payment_type" => "ii_invoice_payment_type",
    "ercfee_ii_invoice_payment_date" => "ii_invoice_payment_date",
    "ercfee_ii_invoice_pay_cleared" => "ii_invoice_pay_cleared",
    "ercfee_ii_invoice_pay_returned" => "ii_invoice_pay_returned",
    "ercfee_ii_invoice_return_reason" => "ii_invoice_return_reason",
    "ercfee_ii_invoice_occams_share" => "ii_invoice_occams_share",
    "ercfee_ii_invoice_aff_ref_share" => "ii_invoice_aff_ref_share",
    "ercfee_iii_invoice_number" => "iii_invoice_no",
    "ercfee_iii_invoice_amount" => "iii_invoice_amount",
    "ercfee_iii_invoiced_qtrs" => "iii_invoiced_qtrs",
    "ercfee_iii_invoice_sent_date" => "iii_invoice_sent_date",
    "ercfee_iii_invoice_payment_type" => "iii_invoice_payment_type",
    "ercfee_iii_invoice_payment_date" => "iii_invoice_payment_date",
    "ercfee_iii_invoice_pay_cleared" => "iii_invoice_pay_cleared",
    "ercfee_iii_invoice_pay_returned" => "iii_invoice_pay_returned",
    "ercfee_iii_invoice_return_reason" => "iii_invoice_return_reason",
    "ercfee_iii_invoice_occams_share" => "iii_invoice_occams_share",
    "ercfee_iii_invoice_aff_ref_share" => "iii_invoice_aff_ref_share",
    "ercfee_iv_invoice_number" => "iv_invoice_no",
    "ercfee_iv_invoice_amount" => "iv_invoice_amount",
    "ercfee_iv_invoiced_qtrs" => "iv_invoiced_qtrs",
    "ercfee_iv_invoice_sent_date" => "iv_invoice_sent_date",
    "ercfee_iv_invoice_payment_type" => "iv_invoice_payment_type",
    "ercfee_iv_invoice_payment_date" => "iv_invoice_payment_date",
    "ercfee_iv_invoice_pay_cleared" => "iv_invoice_pay_cleared",
    "ercfee_iv_invoice_pay_returned" => "iv_invoice_pay_returned",
    "ercfee_iv_invoice_return_reason" => "iv_invoice_return_reason",
    "ercfee_iv_invoice_occams_share" => "iv_invoice_occams_share",
    "ercfee_iv_invoice_aff_ref_share" => "iv_invoice_aff_ref_share",
    // Invoice Stage V Data
    "ercfee_v_invoice_number" => "v_invoice_no",
    "ercfee_v_invoice_amount" => "v_invoice_amount",
    "ercfee_v_invoiced_qtrs" => "v_invoiced_qtrs",
    "ercfee_v_invoice_sent_date" => "v_invoice_sent_date",
    "ercfee_v_invoice_payment_type" => "v_invoice_payment_type",
    "ercfee_v_invoice_payment_date" => "v_invoice_payment_date",
    "ercfee_v_invoice_pay_cleared" => "v_invoice_pay_cleared",
    "ercfee_v_invoice_pay_returned" => "v_invoice_pay_returned",
    "ercfee_v_invoice_return_reason" => "v_invoice_return_reason",
    "ercfee_v_invoice_occams_share" => "v_invoice_occams_share",
    "ercfee_v_invoice_aff_ref_share" => "v_invoice_aff_ref_share",

    // Invoice Stage VI Data
    "ercfee_vi_invoice_number" => "vi_invoice_no",
    "ercfee_vi_invoice_amount" => "vi_invoice_amount",
    "ercfee_vi_invoiced_qtrs" => "vi_invoiced_qtrs",
    "ercfee_vi_invoice_sent_date" => "vi_invoice_sent_date",
    "ercfee_vi_invoice_payment_type" => "vi_invoice_payment_type",
    "ercfee_vi_invoice_payment_date" => "vi_invoice_payment_date",
    "ercfee_vi_invoice_pay_cleared" => "vi_invoice_pay_cleared",
    "ercfee_vi_invoice_pay_returned" => "vi_invoice_pay_returned",
    "ercfee_vi_invoice_return_reason" => "vi_invoice_return_reason",
    "ercfee_vi_invoice_occams_share" => "vi_invoice_occams_share",
    "ercfee_vi_invoice_aff_ref_share" => "vi_invoice_aff_ref_share",

    // Invoice Stage VII Data
    "ercfee_vii_invoice_number" => "vii_invoice_no",
    "ercfee_vii_invoice_amount" => "vii_invoice_amount",
    "ercfee_vii_invoiced_qtrs" => "vii_invoiced_qtrs",
    "ercfee_vii_invoice_sent_date" => "vii_invoice_sent_date",
    "ercfee_vii_invoice_payment_type" => "vii_invoice_payment_type",
    "ercfee_vii_invoice_payment_date" => "vii_invoice_payment_date",
    "ercfee_vii_invoice_pay_cleared" => "vii_invoice_pay_cleared",
    "ercfee_vii_invoice_pay_returned" => "vii_invoice_pay_returned",
    "ercfee_vii_invoice_return_reason" => "vii_invoice_return_reason",
    "ercfee_vii_invoice_occams_share" => "vii_invoice_occams_share",
    "ercfee_vii_invoice_aff_ref_share" => "vii_invoice_aff_ref_share"    
];
foreach ($erc_fees_data as $var_name => $column_name) {
    if ($column_name != "") {
        $leadData = $wpdb->get_row(
            "SELECT $column_name FROM {$wpdb->prefix}erc_erc_fees_2 WHERE lead_id = $lead_id"
        );
        if (!empty($leadData)) {
            $column_val = $leadData->$column_name;
        } else {
            $column_val = "";
        }
        $$var_name = $column_val;
    } else {
        $$var_name = "__N/A";
    }
} // erc fees loop

// Fetch invoice data from invoice_success table
foreach ($invoice_success_data as $var_name => $column_name) {
    if ($column_name != "") {
        $leadData = $wpdb->get_row(
            "SELECT $column_name FROM {$wpdb->prefix}invoice_success_2 WHERE lead_id = $lead_id"
        );
        if (!empty($leadData)) {
            $column_val = $leadData->$column_name;
        } else {
            $column_val = "";
        }
        $$var_name = $column_val;
    } else {
        $$var_name = "__N/A";
    }
} // invoice success loop


// Owner Data Here
$owner_data_arr = [
    "ercown_first_name" => "first_name",
    "ercown_last_name" => "last_name",
    "ercown_email" => "email",
    "ercown_work_phone" => "work_phone",
    "ercown_cell_phone" => "cell_phone",
    "ercown_director" => "director",
    "ercown_home_address" => "home_address",
    "ercown_city" => "city",
    "ercown_state" => "state",
    "ercown_zip" => "zip",
    "ercown_country" => "country",
    "ercown_date_of_birth" => "date_of_birth",
    "ercown_ssn" => "ssn",
    "ercown_photo_id_type" => "photo_id_type",
    "ercown_photo_id_number" => "photo_id_number",
    "ercown_id_issuing_state_country" => "id_issuing_state_country",
    "ercown_ownership" => "ownership",
];
foreach ($owner_data_arr as $ownvar_name => $owncolumn_name) {
    $ownleadData = $wpdb->get_row(
        "SELECT $owncolumn_name FROM {$wpdb->prefix}erc_ownership WHERE lead_id = $lead_id"
    );
    if (!empty($ownleadData)) {
        $owncolumn_val = $ownleadData->$owncolumn_name;
    } else {
        $owncolumn_val = "_NA";
    }
    $$ownvar_name = $owncolumn_val;
} //owner loop

$agreement_sent_date = '';
// ------------ additional info ---------
$iris_leads_additional_info = [
    "Category" => "category",
    "Lead_group" => "lead_group",
    "Lead_status" => "lead_status",
    "Campaign" => "campaign",
    "Source" => "source",
    "Affiliate_user_id" => 'affiliate_user_id',
    "Erc_reffrer_id" => 'erc_reffrer_id',
    "Loop_date" => "loop_date",
    "Last_update" => "last_update",
    "Sales_user_id" => "sales_user_id",
    "sales_support_id" => "sales_support_id",
    "envelop_id" => "envelop_id",
    "agreement_signed" => "agreement_signed",
    "agreement_signed_date" => "agreement_signed_date",
    "agreement_sent_date" => "agreement_sent_date",
    "avoid_agreement" => "avoid_agreement",
    "Created" => "created",
];
foreach ($iris_leads_additional_info as $var_name => $column_name) {
    if ($column_name != "") {
        $leadData = $wpdb->get_row(
            "SELECT $column_name FROM {$wpdb->prefix}erc_iris_leads_additional_info WHERE lead_id = $lead_id"
        );
        if (!empty($leadData)) {
            $column_val = $leadData->$column_name;

            if ($column_name == "lead_status") {
                $lead_status = $column_val;
            }
        } else {
            $column_val = "";
        }

        $$var_name = $column_val;
    } else {
        $$var_name = "__N/A";
    }
}
$affiliate_user = 0;
// get source id --------
if (isset($Affiliate_user_id) && !empty($Affiliate_user_id)) {
    $affiliate_user = $Affiliate_user_id;
} else if (isset($Erc_reffrer_id) && !empty($Erc_reffrer_id)) {
    $affiliate_user = $Erc_reffrer_id;
}
$source_user_id = 0;
if (isset($affiliate_user) && !empty($affiliate_user)) {
    $aff_source_map_table = $wpdb->prefix . 'affiliate_source_mapping';
    $source_data = $wpdb->get_row("SELECT * FROM $aff_source_map_table WHERE affiliate_id='" . $affiliate_user . "'");
    if (isset($source_data)) {
        $source_user_id = $source_data->source_user_id;
    }
}

$NOtes_table = $wpdb->prefix . 'erc_lead_notes';
$NOtes_table = 'eccom_erc_lead_notes';
$total_NOtes = $wpdb->get_results("SELECT id FROM $NOtes_table WHERE lead_id='" . $lead_id . "' ORDER BY created DESC ");
$total_NOtes_count = count($total_NOtes);
$all_NOtes = $wpdb->get_results("SELECT * FROM $NOtes_table WHERE lead_id='" . $lead_id . "' ORDER BY created DESC LIMIT 10 OFFSET 0");


?>


    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;">941 Details</h5>
        </div>
    </div>
    <div class="row">
        <div class="floating col-md-3">
            <input name="lf_4357" data-name='ercfee_error_discovered_date' value='<?php
            if (
                !empty($ercfee_error_discovered_date)
            ) {
                $ercfee_error_discovered_date = date(
                    "Y-m-d",
                    strtotime(
                        $ercfee_error_discovered_date
                    )
                );
            }
            echo $ercfee_error_discovered_date;
            ?>' class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Error Discovered Date">
            <label class="floating__label" data-content="Error Discovered Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4350" data-name='ercfee_q2_2020_941_wages' value='<?php echo $ercfee_q2_2020_941_wages; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q2 2020 941 Wages">
            <label class="floating__label" data-content="Q2 2020 941 Wages"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4351" data-name='ercfee_q3_2020_941_wages' value='<?php echo $ercfee_q3_2020_941_wages; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q3 2020 941 Wages">
            <label class="floating__label" data-content="Q3 2020 941 Wages"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4352" data-name='ercfee_q4_2020_941_wages' value='<?php echo $ercfee_q4_2020_941_wages; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q4 2020 941 Wages">
            <label class="floating__label" data-content="Q4 2020 941 Wages"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4353" data-name='ercfee_q1_2021_941_wages' value='<?php echo $ercfee_q1_2021_941_wages; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q1 2021 941 Wages">
            <label class="floating__label" data-content="Q1 2021 941 Wages"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4354" data-name='ercfee_q2_2021_941_wages' value='<?php echo $ercfee_q2_2021_941_wages; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q2 2021 941 Wages">
            <label class="floating__label" data-content="Q2 2021 941 Wages"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4355" data-name='ercfee_q3_2021_941_wages' value='<?php echo $ercfee_q3_2021_941_wages; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q3 2021 941 Wages">
            <label class="floating__label" data-content="Q3 2021 941 Wages"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4356" data-name='ercfee_q4_2021_941_wages' value='<?php echo $ercfee_q4_2021_941_wages; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q4 2021 941 Wages">
            <label class="floating__label" data-content="Q4 2021 941 Wages"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="col-sm-12">
            <h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;">TOTAL ERC AMOUNT AND FEES</h5>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4368" data-name='ercfee_affiliate_name' value='<?php echo $ercfee_affiliate_name; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Affiliate Name">
            <label class="floating__label" data-content="Affiliate Name"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4367" data-name='ercfee_affiliate_percentage' value='<?php echo $ercfee_affiliate_percentage; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Affiliate Percentage">
            <label class="floating__label" data-content="Affiliate Percentage"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4218" data-name='ercfee_erc_claim_filed' value='<?php echo $ercfee_erc_claim_filed; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="ERC Claim Filed">
            <label class="floating__label" data-content="ERC Claim Filed"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4219" data-name='ercfee_erc_amount_received' value='<?php echo $ercfee_erc_amount_received; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="ERC Amount Received">
            <label class="floating__label" data-content="ERC Amount Received"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4220" data-name='ercfee_total_erc_fees' value='<?php echo $ercfee_total_erc_fees; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total ERC Fees">
            <label class="floating__label" data-content="Total ERC Fees"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4340" data-name='ercfee_legal_fees' value='<?php echo $ercfee_legal_fees; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Legal Fees">
            <label class="floating__label" data-content="Legal Fees"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4221" data-name='ercfee_total_erc_fees_paid' value='<?php echo $ercfee_total_erc_fees_paid; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total ERC Fees Paid">
            <label class="floating__label" data-content="Total ERC Fees Paid"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4222" data-name='ercfee_total_erc_fees_pending' value='<?php echo $ercfee_total_erc_fees_pending; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total ERC Fees Pending">
            <label class="floating__label" data-content="Total ERC Fees Pending"></label>
            <span class="error" id="last_nameErr"></span>
        </div>


        <div class="floating col-md-3">
            <input name="lf_4223" data-name='ercfee_total_occams_share' value='<?php echo $ercfee_total_occams_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Occams Share">
            <label class="floating__label" data-content="Total Occams Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4224" data-name='ercfee_total_aff_ref_share' value='<?php echo $ercfee_total_aff_ref_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Aff_Ref Share">
            <label class="floating__label" data-content="Total Aff_Ref Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4316" data-name='ercfee_retain_occams_share' value='<?php echo $ercfee_retain_occams_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Retain Occams Share">
            <label class="floating__label" data-content="Retain Occams Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4317" data-name='ercfee_retain_aff_ref_share' value='<?php echo $ercfee_retain_aff_ref_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Retain Aff_Ref Share">
            <label class="floating__label" data-content="Retain Aff_Ref Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4318" data-name='ercfee_bal_retain_occams_share' value='<?php echo $ercfee_bal_retain_occams_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Bal Retain Occams Share">
            <label class="floating__label" data-content="Bal Retain Occams Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4319" data-name='ercfee_bal_retain_aff_ref_share' value='<?php echo $ercfee_bal_retain_aff_ref_share; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Bal Retain Aff_Ref Share">
            <label class="floating__label" data-content="Bal Retain Aff_Ref Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4320" data-name='ercfee_total_occams_share_paid' value='<?php echo $ercfee_total_occams_share_paid; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Occams Share Paid">
            <label class="floating__label" data-content="Total Occams Share Paid"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4321" data-name='ercfee_total_aff_ref_share_paid' value='<?php echo $ercfee_total_aff_ref_share_paid; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Aff_Ref Share Paid">
            <label class="floating__label" data-content="Total Aff_Ref Share Paid"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4322" data-name='ercfee_total_occams_share_pendin' value='<?php echo $ercfee_total_occams_share_pendin; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Occams Share Pendin">
            <label class="floating__label" data-content="Total Occams Share Pendin"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4323" data-name='ercfee_total_aff_ref_share_pend' value='<?php echo $ercfee_total_aff_ref_share_pend; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Total Aff_Ref Share Pend">
            <label class="floating__label" data-content="Total Aff_Ref Share Pend"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <!-- <div class="floating col-md-3">
                 <input name="full_name" id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Error">
                <label class="floating__label" data-content="Error"></label> 
                <span class="error" id="last_nameErr"></span>
            </div> -->
    </div>
    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;">Total Max ERC Amount 2020</h5>
            <h5 style="margin-bottom: 20px;font-weight: 500;font-size: 14px ">2020</h5>
        </div>
        <div class="col-md-3">
            <input type='text' class='floating__input form-control' id="q1_2020_max_erc_amount" name="q1_2020_max_erc_amount" data-name='q1_2020_max_erc_amount' value='<?php echo $ercfee_q1_2020_max_erc_amount; ?>' placeholder="Q1 2020 Max ERC Amount">
            <label class="floating__label" data-content="Q1 2020 Max ERC Amount"></label>
        </div>
        <div class="floating col-md-3">
            <input type='text' class='floating__input form-control' id="q2_2020_max_erc_amount" name="q2_2020_max_erc_amount" data-name='q2_2020_max_erc_amount' value='<?php echo $ercfee_q2_2020_max_erc_amount; ?>' placeholder="Q2 2020 Max ERC Amount">
            <label class="floating__label" data-content="Q2 2020 Max ERC Amount"></label>
        </div>
        <div class="floating col-md-3">
            <input type='text' class='floating__input form-control' id="q3_2020_max_erc_amount" name="q3_2020_max_erc_amount" data-name='q3_2020_max_erc_amount' value='<?php echo $ercfee_q3_2020_max_erc_amount; ?>' placeholder="Q3 2020 Max ERC Amount">
            <label class="floating__label" data-content="Q3 2020 Max ERC Amount"></label>
        </div>
        <div class="floating col-md-3">
            <input type='text' class='floating__input form-control' id="q4_2020_max_erc_amount" name="q4_2020_max_erc_amount" data-name='q4_2020_max_erc_amount' value='<?php echo $ercfee_q4_2020_max_erc_amount; ?>' placeholder="Q4 2020 Max ERC Amount">
            <label class="floating__label" data-content="Q4 2020 Max ERC Amount"></label>
        </div>
    </div>

    <div class="row  m-b-8">

        <div class="col-sm-12">
            <h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;">Total Max ERC Amount 2021</h5>
            <h5 style="margin-bottom: 20px;font-weight: 500;font-size: 14px ">2021</h5>
        </div>
        <div class="col-md-3">
            <input type='text' class='floating__input form-control' id="q1_2021_max_erc_amount" name="q1_2021_max_erc_amount" data-name='q1_2021_max_erc_amount' value='<?php echo $ercfee_q1_2021_max_erc_amount; ?>' placeholder="Q1 2021 Max ERC Amount">
            <label class="floating__label" data-content="Q1 2021 Max ERC Amount"></label>
        </div>
        <div class="floating col-md-3">
            <input type='text' class='floating__input form-control' id="q2_2021_max_erc_amount" name="q2_2021_max_erc_amount" data-name='q2_2021_max_erc_amount' value='<?php echo $ercfee_q2_2021_max_erc_amount; ?>' placeholder="Q2 2021 Max ERC Amount">
            <label class="floating__label" data-content="Q2 2021 Max ERC Amount"></label>
        </div>
        <div class="floating col-md-3">
            <input type='text' class='floating__input form-control' id="q3_2021_max_erc_amount" name="q3_2021_max_erc_amount" data-name='q3_2021_max_erc_amount' value='<?php echo $ercfee_q3_2021_max_erc_amount; ?>' placeholder="Q3 2021 Max ERC Amount">
            <label class="floating__label" data-content="Q3 2021 Max ERC Amount"></label>
        </div>
        <div class="floating col-md-3">
            <input type='text' class='floating__input form-control' id="q4_2021_max_erc_amount" name="q4_2021_max_erc_amount" data-name='q4_2021_max_erc_amount' value='<?php echo $ercfee_q4_2021_max_erc_amount; ?>' placeholder="Q4 2021 Max ERC Amount">
            <label class="floating__label" data-content="Q4 2021 Max ERC Amount"></label>
        </div>
    </div>
    <div class="row  m-b-8">

        <div class="col-sm-12">
            <h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;">ERC Filed Quarter wise 2020</h5>
            <h5 style="margin-bottom: 20px;font-weight: 500;font-size: 14px ">2020</h5>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type='checkbox' class='custom-control-input' id="q1200" name="lf_4262" data-name='ercfee_q1_2020_filed_status' size='10' maxlength='-1' value='<?php echo $ercfee_q1_2020_filed_status; ?>' <?php if (
                       $ercfee_q1_2020_filed_status == "Yes"
                   ) {
                       echo "checked";
                   } ?>>


                <label class="custom-control-label" for="q1200">Q1 2020 Filed Status</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4267" data-name='ercfee_q1_2020_filing_date' value='<?php
            if (
                !empty($ercfee_q1_2020_filing_date)
            ) {
                $ercfee_q1_2020_filing_date = date(
                    "Y-m-d",
                    strtotime(
                        $ercfee_q1_2020_filing_date
                    )
                );
            }
            echo $ercfee_q1_2020_filing_date;
            ?>' class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q1 2020 Filing Date">
            <label class="floating__label" data-content="Q1 2020 Filing Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4263" data-name='ercfee_q1_2020_amount_filed' value='<?php echo $ercfee_q1_2020_amount_filed; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q1 2020 Amount Filed">
            <label class="floating__label" data-content="Q1 2020 Amount Filed"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4359" data-name='ercfee_q1_2020_benefits' value='<?php echo $ercfee_q1_2020_benefits; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q1 2020 Benefits">
            <label class="floating__label" data-content="Q1 2020 Benefits"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="q1_2020_eligibility_basis floating__input form-control" name="q1_2020_eligibility_basis" data-name='ercfee_q1_2020_eligibility_basis'>
                <option value="N/A" <?php if($ercfee_q1_2020_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                <option value="FPSO" <?php if($ercfee_q1_2020_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                <option value="SDGR" <?php if($ercfee_q1_2020_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
            </select>
            <label class="floating__label" data-content="Q1 2020 Eligibility Basis"></label>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input name="lf_4264" type='checkbox' class="custom-control-input" id="q2200" data-name='ercfee_q2_2020_filed_status' size='10' maxlength='-1' value='<?php echo $ercfee_q2_2020_filed_status; ?>' <?php if (
                       $ercfee_q2_2020_filed_status == "Yes"
                   ) {
                       echo "checked";
                   } ?>>


                <label class="custom-control-label" for="q2200">Q2 2020 Filed Status</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4268" data-name='ercfee_q2_2020_filing_date' value='<?php
            if (
                !empty($ercfee_q2_2020_filing_date)
            ) {
                $ercfee_q2_2020_filing_date = date(
                    "Y-m-d",
                    strtotime(
                        $ercfee_q2_2020_filing_date
                    )
                );
            }
            echo $ercfee_q2_2020_filing_date;
            ?>' class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q2 2020 Filing Date">
            <label class="floating__label" data-content="Q2 2020 Filing Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4269" data-name='ercfee_q2_2020_amount_filed' value='<?php echo $ercfee_q2_2020_amount_filed; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q2 2020 Amount Filed">
            <label class="floating__label" data-content="Q2 2020 Amount Filed"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4360" data-name='ercfee_q2_2020_benefits' value='<?php echo $ercfee_q2_2020_benefits; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q2 2020 Benefits">
            <label class="floating__label" data-content="Q2 2020 Benefits"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="q2_2020_eligibility_basis floating__input form-control" name="q2_2020_eligibility_basis" data-name='ercfee_q2_2020_eligibility_basis'>
                <option value="N/A" <?php if($ercfee_q2_2020_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                <option value="FPSO" <?php if($ercfee_q2_2020_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                <option value="SDGR" <?php if($ercfee_q2_2020_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
            </select>
            <label class="floating__label" data-content="Q2 2020 Eligibility Basis"></label>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type='checkbox' class="custom-control-input" id="q3200" name="lf_4265" data-name='ercfee_q3_2020_filed_status' size='10' maxlength='-1' value='<?php echo $ercfee_q3_2020_filed_status; ?>' <?php if (
                       $ercfee_q3_2020_filed_status == "Yes"
                   ) {
                       echo "checked";
                   } ?>>

                <label class="custom-control-label" for="q3200">Q3 2020 Filed Status</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4270" data-name='ercfee_q3_2020_filing_date' value='<?php
            if (
                !empty($ercfee_q3_2020_filing_date)
            ) {
                $ercfee_q3_2020_filing_date = date(
                    "Y-m-d",
                    strtotime(
                        $ercfee_q3_2020_filing_date
                    )
                );
            }
            echo $ercfee_q3_2020_filing_date;
            ?>' class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q3 2020 Filing Date">
            <label class="floating__label" data-content="Q3 2020 Filing Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4266" data-name='ercfee_q3_2020_amount_filed' value='<?php echo $ercfee_q3_2020_amount_filed; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q3 2020 Amount Filed">
            <label class="floating__label" data-content="Q3 2020 Amount Filed"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4361" data-name='ercfee_q3_2020_benefits' value='<?php echo $ercfee_q3_2020_benefits; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q3 2020 Benefits">
            <label class="floating__label" data-content="Q3 2020 Benefits"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="q3_2020_eligibility_basis floating__input form-control" name="q3_2020_eligibility_basis" data-name='ercfee_q3_2020_eligibility_basis'>
                <option value="N/A" <?php if($ercfee_q3_2020_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                <option value="FPSO" <?php if($ercfee_q3_2020_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                <option value="SDGR" <?php if($ercfee_q3_2020_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
            </select>
            <label class="floating__label" data-content="Q3 2020 Eligibility Basis"></label>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type='checkbox' class="custom-control-input" id="q4200" name="lf_4271" data-name='ercfee_q4_2020_filed_status' size='10' maxlength='-1' value='<?php echo $ercfee_q4_2020_filed_status; ?>' <?php if (
                       $ercfee_q4_2020_filed_status == "Yes"
                   ) {
                       echo "checked";
                   } ?>>


                <label class="custom-control-label" for="q4200">Q4 2020 Filed Status</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4272" data-name='ercfee_q4_2020_filing_date' value='<?php
            if (
                !empty($ercfee_q4_2020_filing_date)
            ) {
                $ercfee_q4_2020_filing_date = date(
                    "Y-m-d",
                    strtotime(
                        $ercfee_q4_2020_filing_date
                    )
                );
            }
            echo $ercfee_q4_2020_filing_date;
            ?>' class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q4 2020 Filing Date">
            <label class="floating__label" data-content="Q4 2020 Filing Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4273" data-name='ercfee_q4_2020_amount_filed' value='<?php echo $ercfee_q4_2020_amount_filed; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q4 2020 Amount Filed">
            <label class="floating__label" data-content="Q4 2020 Amount Filed"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input name="lf_4362" data-name='ercfee_q4_2020_benefits' value='<?php echo $ercfee_q4_2020_benefits; ?>' class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q4 2020 Benefits">
            <label class="floating__label" data-content="Q4 2020 Benefits"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="q4_2020_eligibility_basis floating__input form-control" name="q4_2020_eligibility_basis" data-name='ercfee_q4_2020_eligibility_basis'>
                <option value="N/A" <?php if($ercfee_q4_2020_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                <option value="FPSO" <?php if($ercfee_q4_2020_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                <option value="SDGR" <?php if($ercfee_q4_2020_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
            </select>
            <label class="floating__label" data-content="Q4 2020 Eligibility Basis"></label>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="font-weight: 500;font-size: 14px margin-bottom: 20px;">2021</h5>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q12021" name="lf_4275" data-name='ercfee_q1_2021_filed_status' value='<?php echo $ercfee_q1_2021_filed_status; ?>' <?php if (
                       $ercfee_q1_2021_filed_status == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q12021">Q1 2021 Filed Status</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q1 2021 Filing Date" name="lf_4276" data-name='ercfee_q1_2021_filing_date' value='<?php
               if (!empty($ercfee_q1_2021_filing_date)) {
                   $ercfee_q1_2021_filing_date = date(
                       "Y-m-d",
                       strtotime($ercfee_q1_2021_filing_date)
                   );
               }
               echo $ercfee_q1_2021_filing_date;
               ?>'>
            <label class="floating__label" data-content="Q1 2021 Filing Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q1 2021 Amount Filed" name="lf_4277" data-name='ercfee_q1_2021_amount_filed' value='<?php echo $ercfee_q1_2021_amount_filed; ?>'>
            <label class="floating__label" data-content="Q1 2021 Amount Filed"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q1 2021 Benefits" name="lf_4363" data-name='ercfee_q1_2021_benefits' value='<?php echo $ercfee_q1_2021_benefits; ?>'>
            <label class="floating__label" data-content="Q1 2021 Benefits"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="q1_2021_eligibility_basis floating__input form-control" name="q1_2021_eligibility_basis" data-name='ercfee_q1_2021_eligibility_basis'>
                <option value="N/A" <?php if($ercfee_q1_2021_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                <option value="FPSO" <?php if($ercfee_q1_2021_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                <option value="SDGR" <?php if($ercfee_q1_2021_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
            </select>
            <label class="floating__label" data-content="Q1 2021 Eligibility Basis"></label>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q22021" name="lf_4278" data-name='ercfee_q2_2021_filed_status' value='<?php echo $ercfee_q2_2021_filed_status; ?>' <?php if (
                       $ercfee_q2_2021_filed_status == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q22021">Q2 2021 Filed Status</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q2 2021 Filing Date" name="lf_4279" data-name='ercfee_q2_2021_filing_date' value='<?php
               if (!empty($ercfee_q2_2021_filing_date)) {
                   $ercfee_q2_2021_filing_date = date(
                       "Y-m-d",
                       strtotime($ercfee_q2_2021_filing_date)
                   );
               }
               echo $ercfee_q2_2021_filing_date;
               ?>'>
            <label class="floating__label" data-content="Q2 2021 Filing Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q2 2021 Amount Filed" name="lf_4280" data-name='ercfee_q2_2021_amount_filed' value='<?php echo $ercfee_q2_2021_amount_filed; ?>'>
            <label class="floating__label" data-content="Q2 2021 Amount Filed"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q2 2021 Benefits" name="lf_4364" data-name='ercfee_q2_2021_benefits' value='<?php echo $ercfee_q2_2021_benefits; ?>'>
            <label class="floating__label" data-content="Q2 2021 Benefits"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="q2_2021_eligibility_basis floating__input form-control" name="q2_2021_eligibility_basis" data-name='ercfee_q2_2021_eligibility_basis'>
                <option value="N/A" <?php if($ercfee_q2_2021_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                <option value="FPSO" <?php if($ercfee_q2_2021_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                <option value="SDGR" <?php if($ercfee_q2_2021_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
            </select>
            <label class="floating__label" data-content="Q2 2021 Eligibility Basis"></label>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q32021" name="lf_4281" data-name='ercfee_q3_2021_filed_status' value='<?php echo $ercfee_q3_2021_filed_status; ?>' <?php if (
                       $ercfee_q3_2021_filed_status == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q32021">Q3 2021 Filed Status</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q3 2021 Filing Date" name="lf_4282" data-name='ercfee_q3_2021_filing_date' value='<?php
               if (!empty($ercfee_q3_2021_filing_date)) {
                   $ercfee_q3_2021_filing_date = date(
                       "Y-m-d",
                       strtotime($ercfee_q3_2021_filing_date)
                   );
               }
               echo $ercfee_q3_2021_filing_date;
               ?>'>
            <label class="floating__label" data-content="Q3 2021 Filing Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q3 2021 Amount Filed" name="lf_4283" data-name='ercfee_q3_2021_amount_filed' value='<?php echo $ercfee_q3_2021_amount_filed; ?>'>
            <label class="floating__label" data-content="Q3 2021 Amount Filed"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q3 2021 Benefits" name="lf_4365" data-name='ercfee_q3_2021_benefits' value='<?php echo $ercfee_q3_2021_benefits; ?>'>
            <label class="floating__label" data-content="Q3 2021 Benefits"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="q3_2021_eligibility_basis floating__input form-control" name="q3_2021_eligibility_basis" data-name='ercfee_q3_2021_eligibility_basis'>
                <option value="N/A" <?php if($ercfee_q3_2021_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                <option value="FPSO" <?php if($ercfee_q3_2021_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                <option value="SDGR" <?php if($ercfee_q3_2021_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
            </select>
            <label class="floating__label" data-content="Q3 2021 Eligibility Basis"></label>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q42021" name="lf_4284" data-name='ercfee_q4_2021_filed_status' value='<?php echo $ercfee_q4_2021_filed_status; ?>' <?php if (
                       $ercfee_q4_2021_filed_status == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q42021">Q4 2021 Filed Status</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q4 2021 Filing Date" name="lf_4285" data-name='ercfee_q4_2021_filing_date' value='<?php
               if (!empty($ercfee_q4_2021_filing_date)) {
                   $ercfee_q4_2021_filing_date = date(
                       "Y-m-d",
                       strtotime($ercfee_q4_2021_filing_date)
                   );
               }
               echo $ercfee_q4_2021_filing_date;
               ?>'>
            <label class="floating__label" data-content="Q4 2021 Filing Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q4 2021 Amount Filed" name="lf_4286" data-name='ercfee_q4_2021_amount_filed' value='<?php echo $ercfee_q4_2021_amount_filed; ?>'>
            <label class="floating__label" data-content="Q4 2021 Amount Filed"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q4 2021 Benefits" name="lf_4366" data-name='ercfee_q4_2021_benefits' value='<?php echo $ercfee_q4_2021_benefits; ?>'>
            <label class="floating__label" data-content="Q4 2021 Benefits"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="q4_2021_eligibility_basis floating__input form-control" name="q4_2021_eligibility_basis" data-name='ercfee_q4_2021_eligibility_basis'>
                <option value="N/A" <?php if($ercfee_q4_2021_eligibility_basis == 'N/A'){echo "selected";}?>>N/A</option>
                <option value="FPSO" <?php if($ercfee_q4_2021_eligibility_basis == 'FPSO'){echo "selected";}?>>FPSO</option>
                <option value="SDGR" <?php if($ercfee_q4_2021_eligibility_basis == 'SDGR'){echo "selected";}?>>SDGR</option>
            </select>
            <label class="floating__label" data-content="Q4 2021 Eligibility Basis"></label>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;">ERC Letter, Check & Amount</h5>
            <h5 style="margin-bottom: 20px;font-weight: 500;font-size: 14px ">2020</h5>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q1 2020 LoOP" name="lf_4341" data-name='ercfee_q1_2020_loop' value='<?php
               if (!empty($ercfee_q1_2020_loop)) {
                   $ercfee_q1_2020_loop = date(
                       "Y-m-d",
                       strtotime($ercfee_q1_2020_loop)
                   );
               }
               echo $ercfee_q1_2020_loop;
               ?>'>
            <label class="floating__label" data-content="Q1 2020 LoOP"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q1200letter" name="lf_4226" data-name='ercfee_q1_2020_letter' value='<?php echo $ercfee_q1_2020_letter; ?>' <?php if (
                       $ercfee_q1_2020_letter == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q1200letter">Q1 2020 Letter</label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q12020check" name="lf_4227" data-name='ercfee_q1_2020_check' value='<?php echo $ercfee_q1_2020_check; ?>' <?php if (
                       $ercfee_q1_2020_check == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q12020check">Q1 2020 Check</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q1 2020 Chq Amt" name="lf_4228" data-name='ercfee_q1_2020_chq_amt' value='<?php echo $ercfee_q1_2020_chq_amt; ?>'>
            <label class="floating__label" data-content="Q1 2020 Chq Amt"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q2 2020 LoOP" name="lf_4342" data-name='ercfee_q2_2020_loop' value='<?php
               if (!empty($ercfee_q2_2020_loop)) {
                   $ercfee_q2_2020_loop = date(
                       "Y-m-d",
                       strtotime($ercfee_q2_2020_loop)
                   );
               }
               echo $ercfee_q2_2020_loop;
               ?>'>
            <label class="floating__label" data-content="Q2 2020 LoOP"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q2200letter" name="lf_4211" data-name='ercfee_q2_2020_letter' value='<?php echo $ercfee_q2_2020_letter; ?>' <?php if (
                       $ercfee_q2_2020_letter == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q2200letter">Q2 2020 Letter</label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q22020check" name="lf_4229" data-name='ercfee_q2_2020_check' value='<?php echo $ercfee_q2_2020_check; ?>' <?php if (
                       $ercfee_q2_2020_check == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q22020check">Q2 2020 Check</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q2 2020 Chq Amt" name="lf_4230" data-name='ercfee_q2_2020_chq_amt' value='<?php echo $ercfee_q2_2020_chq_amt; ?>'>
            <label class="floating__label" data-content="Q2 2020 Chq Amt"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q3 2020 LoOP" name="lf_4343" data-name='ercfee_q3_2020_loop' value='<?php
               if (!empty($ercfee_q3_2020_loop)) {
                   $ercfee_q3_2020_loop = date(
                       "Y-m-d",
                       strtotime($ercfee_q3_2020_loop)
                   );
               }
               echo $ercfee_q3_2020_loop;
               ?>'>
            <label class="floating__label" data-content="Q3 2020 LoOP"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q3200letter" name="lf_4210" data-name='ercfee_q3_2020_letter' value='<?php echo $ercfee_q3_2020_letter; ?>' <?php if (
                       $ercfee_q3_2020_letter == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q3200letter">Q3 2020 Letter</label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q32020check" name="lf_4215" data-name='ercfee_q3_2020_check' value='<?php echo $ercfee_q3_2020_check; ?>' <?php if (
                       $ercfee_q3_2020_check == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q32020check">Q3 2020 Check</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q3 2020 Chq Amt" name="lf_4216" data-name='ercfee_q3_2020_chq_amt' value='<?php echo $ercfee_q3_2020_chq_amt; ?>'>
            <label class="floating__label" data-content="Q3 2020 Chq Amt"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q4 2020 LoOP" name="lf_4344" data-name='ercfee_q4_2020_loop' value='<?php
               if (!empty($ercfee_q4_2020_loop)) {
                   $ercfee_q4_2020_loop = date(
                       "Y-m-d",
                       strtotime($ercfee_q4_2020_loop)
                   );
               }
               echo $ercfee_q4_2020_loop;
               ?>'>
            <label class="floating__label" data-content="Q4 2020 LoOP"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q4200letter" name="lf_4231" data-name='ercfee_q4_2020_letter' value='<?php echo $ercfee_q4_2020_letter; ?>' <?php if (
                       $ercfee_q4_2020_letter == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q4200letter">Q4 2020 Letter</label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q42020check" name="lf_4232" data-name='ercfee_q4_2020_check' value='<?php echo $ercfee_q4_2020_check; ?>' <?php if (
                       $ercfee_q4_2020_check == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q42020check">Q4 2020 Check</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q4 2020 Chq Amt" name="lf_4233" data-name='ercfee_q4_2020_chq_amt' value='<?php echo $ercfee_q4_2020_chq_amt; ?>'>
            <label class="floating__label" data-content="Q4 2020 Chq Amt"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="font-weight: 500;font-size: 14px ;margin-bottom: 20px;">2021</h5>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q1 2021 LoOP" name="lf_4345" data-name='ercfee_q1_2021_loop' value='<?php
               if (!empty($ercfee_q1_2021_loop)) {
                   $ercfee_q1_2021_loop = date(
                       "Y-m-d",
                       strtotime($ercfee_q1_2021_loop)
                   );
               }
               echo $ercfee_q1_2021_loop;
               ?>'>
            <label class="floating__label" data-content="Q1 2021 LoOP"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q12001letter" name="lf_4234" data-name='ercfee_q1_2021_letter' value='<?php echo $ercfee_q1_2021_letter; ?>' <?php if (
                       $ercfee_q1_2021_letter == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q12001letter">Q1 2021 Letter</label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q12021check" name="lf_4235" data-name='ercfee_q1_2021_check' value='<?php echo $ercfee_q1_2021_check; ?>' <?php if (
                       $ercfee_q1_2021_check == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q12021check">Q1 2021 Check</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q1 2021 Chq Amt" name="lf_4236" data-name='ercfee_q1_2021_chq_amt' value='<?php echo $ercfee_q1_2021_chq_amt; ?>'>
            <label class="floating__label" data-content="Q1 2021 Chq Amt"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q2 2021 LoOP" name="lf_4346" data-name='ercfee_q2_2021_loop' value='<?php
               if (!empty($ercfee_q2_2021_loop)) {
                   $ercfee_q2_2021_loop = date(
                       "Y-m-d",
                       strtotime($ercfee_q2_2021_loop)
                   );
               }
               echo $ercfee_q2_2021_loop;
               ?>'>
            <label class="floating__label" data-content="Q2 2021 LoOP"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q22001letter" name="lf_4237" data-name='ercfee_q2_2021_letter' value='<?php echo $ercfee_q2_2021_letter; ?>' <?php if (
                       $ercfee_q2_2021_letter == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q22001letter">Q2 2021 Letter</label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q22021check" name="lf_4238" data-name='ercfee_q2_2021_check' value='<?php echo $ercfee_q2_2021_check; ?>' <?php if (
                       $ercfee_q2_2021_check == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q22021check">Q2 2021 Check</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q2 2021 Chq Amt" name="lf_4239" data-name='ercfee_q2_2021_chq_amt' value='<?php echo $ercfee_q2_2021_chq_amt; ?>'>
            <label class="floating__label" data-content="Q2 2021 Chq Amt"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q3 2021 LoOP" name="lf_4347" data-name='ercfee_q3_2021_loop' value='<?php
               if (!empty($ercfee_q3_2021_loop)) {
                   $ercfee_q3_2021_loop = date(
                       "Y-m-d",
                       strtotime($ercfee_q3_2021_loop)
                   );
               }
               echo $ercfee_q3_2021_loop;
               ?>'>
            <label class="floating__label" data-content="Q3 2021 LoOP"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q32001letter" name="lf_4240" data-name='ercfee_q3_2021_letter' value='<?php echo $ercfee_q3_2021_letter; ?>' <?php if (
                       $ercfee_q3_2021_letter == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q32001letter">Q3 2021 Letter</label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q32021check" name="lf_4241" data-name='ercfee_q3_2021_check' value='<?php echo $ercfee_q3_2021_check; ?>' <?php if (
                       $ercfee_q3_2021_check == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q32021check">Q3 2021 Check</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q3 2021 Chq Amt" name="lf_4242" data-name='ercfee_q3_2021_chq_amt' value='<?php echo $ercfee_q3_2021_chq_amt; ?>'>
            <label class="floating__label" data-content="Q3 2021 Chq Amt"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="Q4 2021 LoOP" name="lf_4348" data-name='ercfee_q4_2021_loop' value='<?php
               if (!empty($ercfee_q4_2021_loop)) {
                   $ercfee_q4_2021_loop = date(
                       "Y-m-d",
                       strtotime($ercfee_q4_2021_loop)
                   );
               }
               echo $ercfee_q4_2021_loop;
               ?>'>
            <label class="floating__label" data-content="Q4 2021 LoOP"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q42001letter" name="lf_4257" data-name='ercfee_q4_2021_letter' value='<?php echo $ercfee_q4_2021_letter; ?>' <?php if (
                       $ercfee_q4_2021_letter == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q42001letter">Q4 2021 Letter</label>
            </div>
        </div>
        <div class="col-md-3">
            <div class="custom-control custom-checkbox">
                <input type="checkbox" class="custom-control-input" id="q42021check" name="lf_4258" data-name='ercfee_q4_2021_check' value='<?php echo $ercfee_q4_2021_check; ?>' <?php if (
                       $ercfee_q4_2021_check == "Yes"
                   ) {
                       echo "checked";
                   } ?>>
                <label class="custom-control-label" for="q42021check">Q4 2021 Check</label>
            </div>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="Q4 2021 Chq Amt" name="lf_4259" data-name='ercfee_q4_2021_chq_amt' value='<?php echo $ercfee_q4_2021_chq_amt; ?>'>
            <label class="floating__label" data-content="Q4 2021 Chq Amt"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="border-bottom: 2px solid #ccc;margin-bottom: 20px;">Success Fee Invoice Details</h5>
            <h5 style="margin-bottom: 20px;font-size:14px;font-weight: 500">I - Invoice Details</h5>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="I Invoice number" name="lf_4249" data-name='ercfee_i_invoice_number' value='<?php echo $ercfee_i_invoice_number; ?>'>
            <label class="floating__label" data-content="I Invoice number"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="I Invoice Amount" name="lf_4250" data-name='ercfee_i_invoice_amount' value='<?php echo $ercfee_i_invoice_amount; ?>'>
            <label class="floating__label" data-content="I Invoice Amount"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="I Invoiced Qtrs" name="lf_4252" data-name='ercfee_i_invoiced_qtrs' value='<?php echo $ercfee_i_invoiced_qtrs; ?>'>
            <label class="floating__label" data-content="I Invoiced Qtrs"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="I Invoice Sent Date" name="lf_4251" data-name='ercfee_i_invoice_sent_date' value='<?php
               if (!empty($ercfee_i_invoice_sent_date)) {
                   $ercfee_i_invoice_sent_date = date(
                       "Y-m-d",
                       strtotime($ercfee_i_invoice_sent_date)
                   );
               }
               echo $ercfee_i_invoice_sent_date;
               ?>'>
            <label class="floating__label" data-content="I Invoice Sent Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="bank_account_type floating__input form-control" name="lf_4290" data-name='ercfee_i_invoice_payment_type' id="2019 Tax Return">
                <option value=""></option>
                <?php
                $fees_section = [
                    "1" =>
                        "Occams Initiated - eCheck",
                    "2" =>
                        "Occams Initiated - ACH",
                    "3" =>
                        "Client Initiated - Wire",
                    "4" =>
                        "Client Initiated - ACH",
                    "5" =>
                        "Client Initiated - Check Mailed",
                    "6" =>
                        "Credit Card or Debit Card",
                ];

                foreach ($fees_section as $inkey => $invalue) {
                    if (
                        $ercfee_i_invoice_payment_type ==
                        $invalue
                    ) {
                        $selected =
                            "selected";
                    } else {
                        $selected = "";
                    } ?>
                                    <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                                <?php
                }
                ?>
            </select>
            <label class="floating__label" data-content="I Invoice Payment Type"></label>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="I Invoice Payment Date" name="lf_4253" data-name='ercfee_i_invoice_payment_date' value='<?php
               if (!empty($ercfee_i_invoice_payment_date)) {
                   $ercfee_i_invoice_payment_date = date(
                       "Y-m-d",
                       strtotime($ercfee_i_invoice_payment_date)
                   );
               }
               echo $ercfee_i_invoice_payment_date;
               ?>'>
            <label class="floating__label" data-content="I Invoice Payment Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="I Invoice Pay Cleared" name="lf_4254" data-name='ercfee_i_invoice_pay_cleared' value='<?php
               if (!empty($ercfee_i_invoice_pay_cleared)) {
                   $ercfee_i_invoice_pay_cleared = date(
                       "Y-m-d",
                       strtotime($ercfee_i_invoice_pay_cleared)
                   );
               }
               echo $ercfee_i_invoice_pay_cleared;
               ?>'>
            <label class="floating__label" data-content="I Invoice Pay Cleared"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="I Invoice Pay Returned" name="lf_4255" data-name='ercfee_i_invoice_pay_returned' value='<?php
               if (!empty($ercfee_i_invoice_pay_returned)) {
                   $ercfee_i_invoice_pay_returned = date(
                       "Y-m-d",
                       strtotime($ercfee_i_invoice_pay_returned)
                   );
               }
               echo $ercfee_i_invoice_pay_returned;
               ?>'>
            <label class="floating__label" data-content="I Invoice Pay Returned"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">

        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="I Invoice Return Reason" name="lf_4256" data-name='ercfee_i_invoice_return_reason' value='<?php echo $ercfee_i_invoice_return_reason; ?>'>
            <label class="floating__label" data-content="I Invoice Return Reason"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="I Invoice Occams Share" name="lf_4288" data-name='ercfee_i_invoice_occams_share' value='<?php echo $ercfee_i_invoice_occams_share; ?>'>
            <label class="floating__label" data-content="I Invoice Occams Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="I Invoice Aff/Ref Share" name="lf_4289" data-name='ercfee_i_invoice_aff/ref_share' value='<?php echo $ercfee_i_invoice_aff_ref_share; ?>'>
            <label class="floating__label" data-content="I Invoice Aff/Ref Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="font-weight: 500; font-size: 14px;margin-bottom: 20px;">II - Invoice Details</h5>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="II Invoice number" name="lf_4293" data-name='ercfee_ii__invoice_number' value='<?php echo $ercfee_ii__invoice_number; ?>'>
            <label class="floating__label" data-content="II Invoice number"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="II Invoice Amount" name="lf_4294" data-name='ercfee_ii_invoice_amount' value='<?php echo $ercfee_ii_invoice_amount; ?>'>
            <label class="floating__label" data-content="II Invoice Amount"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="II Invoiced Qtrs" name="lf_4295" data-name='ercfee_ii_invoiced_qtrs' value='<?php echo $ercfee_ii_invoiced_qtrs; ?>'>
            <label class="floating__label" data-content="II Invoiced Qtrs"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="II Invoice Sent Date" name="lf_4296" data-name='ercfee_ii_invoice_sent_date' value='<?php
               if (!empty($ercfee_ii_invoice_sent_date)) {
                   $ercfee_ii_invoice_sent_date = date(
                       "Y-m-d",
                       strtotime($ercfee_ii_invoice_sent_date)
                   );
               }
               echo $ercfee_ii_invoice_sent_date;
               ?>'>
            <label class="floating__label" data-content="II Invoice Sent Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="bank_account_type floating__input form-control" id="2019 Tax Return" name="lf_4297" data-name='ercfee_ii_invoice_payment_type'>
                <option value=""></option>
                <?php
                $fees_section = [
                    "1" =>
                        "Occams Initiated - eCheck",
                    "2" =>
                        "Occams Initiated - ACH",
                    "3" =>
                        "Client Initiated - Wire",
                    "4" =>
                        "Client Initiated - ACH",
                    "5" =>
                        "Client Initiated - Check Mailed",
                    "6" =>
                        "Credit Card or Debit Card",
                ];

                foreach ($fees_section as $inkey => $invalue) {
                    if (
                        $ercfee_ii_invoice_payment_type ==
                        $invalue
                    ) {
                        $selected =
                            "selected";
                    } else {
                        $selected = "";
                    } ?>
                                    <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                                <?php
                }
                ?>
            </select>
            <label class="floating__label" data-content="II Invoice Payment Type"></label>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="II Invoice Payment Date" name="lf_4298" data-name='ercfee_ii_invoice_payment_date' value='<?php
               if (!empty($ercfee_ii_invoice_payment_date)) {
                   $ercfee_ii_invoice_payment_date = date(
                       "Y-m-d",
                       strtotime($ercfee_ii_invoice_payment_date)
                   );
               }
               echo $ercfee_ii_invoice_payment_date;
               ?>'>
            <label class="floating__label" data-content="II Invoice Payment Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="II Invoice Pay Cleared" name="lf_4299" data-name='ercfee_ii_invoice_pay_cleared' value='<?php
               if (!empty($ercfee_ii_invoice_pay_cleared)) {
                   $ercfee_ii_invoice_pay_cleared = date(
                       "Y-m-d",
                       strtotime($ercfee_ii_invoice_pay_cleared)
                   );
               }
               echo $ercfee_ii_invoice_pay_cleared;
               ?>'>
            <label class="floating__label" data-content="II Invoice Pay Cleared"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="II Invoice Pay Returned" name="lf_4300" data-name='ercfee_ii_invoice_pay_returned' value='<?php
               if (!empty($ercfee_ii_invoice_pay_returned)) {
                   $ercfee_ii_invoice_pay_returned = date(
                       "Y-m-d",
                       strtotime($ercfee_ii_invoice_pay_returned)
                   );
               }
               echo $ercfee_ii_invoice_pay_returned;
               ?>'>
            <label class="floating__label" data-content="II Invoice Pay Returned"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">

        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="II Invoice Return Reason" name="lf_4301" data-name='ercfee_ii_invoice_return_reason' value='<?php echo $ercfee_ii_invoice_return_reason; ?>'>
            <label class="floating__label" data-content="II Invoice Return Reason"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="II Invoice Occams Share" name="lf_4302" data-name='ercfee_ii_invoice_occams_share' value='<?php echo $ercfee_ii_invoice_occams_share; ?>'>
            <label class="floating__label" data-content="II Invoice Occams Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="II Invoice Aff/Ref Share" name="lf_4303" data-name='ercfee_ii_invoice_aff/ref_share' value='<?php echo $ercfee_ii_invoice_aff_ref_share; ?>'>
            <label class="floating__label" data-content="II Invoice Aff/Ref Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="font-weight: 500; font-size: 14px;margin-bottom: 20px;">III - Invoice Details</h5>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="III Invoice number" name="lf_4305" data-name='ercfee_iii_invoice_number' value='<?php echo $ercfee_iii_invoice_number; ?>'>
            <label class="floating__label" data-content="III Invoice number"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="III Invoice Amount" name="lf_4311" data-name='ercfee_iii_invoice_amount' value='<?php echo $ercfee_iii_invoice_amount; ?>'>
            <label class="floating__label" data-content="III Invoice Amount"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="III Invoiced Qtrs" name="lf_4306" data-name='ercfee_iii_invoiced_qtrs' value='<?php echo $ercfee_iii_invoiced_qtrs; ?>'>
            <label class="floating__label" data-content="III Invoiced Qtrs"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="III Invoice Sent Date" name="lf_4307" data-name='ercfee_iii_invoice_sent_date' value='<?php
               if (!empty($ercfee_iii_invoice_sent_date)) {
                   $ercfee_iii_invoice_sent_date = date(
                       "Y-m-d",
                       strtotime($ercfee_iii_invoice_sent_date)
                   );
               }
               echo $ercfee_iii_invoice_sent_date;
               ?>'>
            <label class="floating__label" data-content="III Invoice Sent Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="bank_account_type floating__input form-control" name="lf_4308" id="2019 Tax Return" data-name='ercfee_iii_invoice_payment_type'>
                <option value=""></option>
                <?php
                $fees_section = [
                    "1" =>
                        "Occams Initiated - eCheck",
                    "2" =>
                        "Occams Initiated - ACH",
                    "3" =>
                        "Client Initiated - Wire",
                    "4" =>
                        "Client Initiated - ACH",
                    "5" =>
                        "Client Initiated - Check Mailed",
                    "6" =>
                        "Credit Card or Debit Card",
                ];

                foreach ($fees_section as $inkey => $invalue) {
                    if (
                        $ercfee_iii_invoice_payment_type ==
                        $invalue
                    ) {
                        $selected =
                            "selected";
                    } else {
                        $selected = "";
                    } ?>
                                    <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                                <?php
                }
                ?>
            </select>
            <label class="floating__label" data-content="III Invoice Payment Type"></label>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="III Invoice Payment Date" name="lf_4309" data-name='ercfee_iii_invoice_payment_date' value='<?php
               if (!empty($ercfee_iii_invoice_payment_date)) {
                   $ercfee_iii_invoice_payment_date = date(
                       "Y-m-d",
                       strtotime($ercfee_iii_invoice_payment_date)
                   );
               }
               echo $ercfee_iii_invoice_payment_date;
               ?>'>
            <label class="floating__label" data-content="III Invoice Payment Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="III Invoice Pay Cleared" name="lf_4310" data-name='ercfee_iii_invoice_pay_cleared' value='<?php
               if (!empty($ercfee_iii_invoice_pay_cleared)) {
                   $ercfee_iii_invoice_pay_cleared = date(
                       "Y-m-d",
                       strtotime($ercfee_iii_invoice_pay_cleared)
                   );
               }
               echo $ercfee_iii_invoice_pay_cleared;
               ?>'>
            <label class="floating__label" data-content="III Invoice Pay Cleared"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="III Invoice Pay Returned" name="lf_4312" data-name='ercfee_iii_invoice_pay_returned' value='<?php
               if (!empty($ercfee_iii_invoice_pay_returned)) {
                   $ercfee_iii_invoice_pay_returned = date(
                       "Y-m-d",
                       strtotime($ercfee_iii_invoice_pay_returned)
                   );
               }
               echo $ercfee_iii_invoice_pay_returned;
               ?>'>
            <label class="floating__label" data-content="III Invoice Pay Returned"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">

        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="III Invoice Return Reason" name="lf_4313" data-name='ercfee_iii_invoice_return_reason' value='<?php echo $ercfee_iii_invoice_return_reason; ?>'>
            <label class="floating__label" data-content="III Invoice Return Reason"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="III Invoice Occams Share" name="lf_4314" data-name='ercfee_iii_invoice_occams_share' value='<?php echo $ercfee_iii_invoice_occams_share; ?>'>
            <label class="floating__label" data-content="III Invoice Occams Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="III Invoice Aff/Ref Share" name="lf_4315" data-name='ercfee_iii_invoice_aff_ref_share' value='<?php echo $ercfee_iii_invoice_aff_ref_share; ?>'>
            <label class="floating__label" data-content="III Invoice Aff/Ref Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="font-weight: 500; font-size: 14px;margin-bottom: 20px;">IV - Invoice Details </h5>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="IV Invoice number" name="lf_4326" data-name='ercfee_iv_invoice_number' value='<?php echo $ercfee_iv_invoice_number; ?>'>
            <label class="floating__label" data-content="IV Invoice number"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="IV Invoice Amount" name="lf_4327" data-name='ercfee_iv_invoice_amount' value='<?php echo $ercfee_iv_invoice_amount; ?>'>
            <label class="floating__label" data-content="IV Invoice Amount"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="IV Invoiced Qtrs" name="lf_4328" data-name='ercfee_iv_invoiced_qtrs' value='<?php echo $ercfee_iv_invoiced_qtrs; ?>'>
            <label class="floating__label" data-content="IV Invoiced Qtrs"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="IV Invoice Sent Date" name="lf_4329" data-name='ercfee_iv_invoice_sent_date' value='<?php
               if (!empty($ercfee_iv_invoice_sent_date)) {
                   $ercfee_iv_invoice_sent_date = date(
                       "Y-m-d",
                       strtotime($ercfee_iv_invoice_sent_date)
                   );
               }
               echo $ercfee_iv_invoice_sent_date;
               ?>'>
            <label class="floating__label" data-content="IV Invoice Sent Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="bank_account_type floating__input form-control" id="2019 Tax Return" name="lf_4330" data-name='ercfee_iv_invoice_payment_type'>
                <option value=""></option>
                <?php
                $fees_section = [
                    "1" =>
                        "Occams Initiated - eCheck",
                    "2" =>
                        "Occams Initiated - ACH",
                    "3" =>
                        "Client Initiated - Wire",
                    "4" =>
                        "Client Initiated - ACH",
                    "5" =>
                        "Client Initiated - Check Mailed",
                    "6" =>
                        "Credit Card or Debit Card",
                ];

                foreach ($fees_section as $inkey => $invalue) {
                    if (
                        $ercfee_iv_invoice_payment_type ==
                        $invalue
                    ) {
                        $selected =
                            "selected";
                    } else {
                        $selected = "";
                    } ?>
                                    <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                                <?php
                }
                ?>
            </select>
            <label class="floating__label" data-content="IV Invoice Payment Type"></label>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="IV Invoice Payment Date" name="lf_4331" data-name='ercfee_iv_invoice_payment_date' value='<?php
               if (!empty($ercfee_iv_invoice_payment_date)) {
                   $ercfee_iv_invoice_payment_date = date(
                       "Y-m-d",
                       strtotime($ercfee_iv_invoice_payment_date)
                   );
               }
               echo $ercfee_iv_invoice_payment_date;
               ?>'>
            <label class="floating__label" data-content="IV Invoice Payment Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="IV Invoice Pay Cleared" name="lf_4332" data-name='ercfee_iv_invoice_pay_cleared' value='<?php
               if (!empty($ercfee_iv_invoice_pay_cleared)) {
                   $ercfee_iv_invoice_pay_cleared = date(
                       "Y-m-d",
                       strtotime($ercfee_iv_invoice_pay_cleared)
                   );
               }
               echo $ercfee_iv_invoice_pay_cleared;
               ?>'>
            <label class="floating__label" data-content="IV Invoice Pay Cleared"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="IV Invoice Pay Returned" name="lf_4333" data-name='ercfee_iv_invoice_pay_returned' value='<?php
               if (!empty($ercfee_iv_invoice_pay_returned)) {
                   $ercfee_iv_invoice_pay_returned = date(
                       "Y-m-d",
                       strtotime($ercfee_iv_invoice_pay_returned)
                   );
               }
               echo $ercfee_iv_invoice_pay_returned;
               ?>'>
            <label class="floating__label" data-content="IV Invoice Pay Returned"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">

        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="IV Invoice Return Reason" name="lf_4334" data-name='ercfee_iv_invoice_return_reason' value='<?php echo $ercfee_iv_invoice_return_reason; ?>'>
            <label class="floating__label" data-content="IV Invoice Return Reason"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="IV Invoice Occams Share" name="lf_4335" data-name='ercfee_iv_invoice_occams_share' value='<?php echo $ercfee_iv_invoice_occams_share; ?>'>
            <label class="floating__label" data-content="IV Invoice Occams Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="IV Invoice Aff/Ref Share" name="lf_4336" data-name='ercfee_iv_invoice_aff_ref_share' value='<?php echo $ercfee_iv_invoice_aff_ref_share; ?>'>
            <label class="floating__label" data-content="IV Invoice Aff/Ref Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>

    <!-- Invoice Stage V -->
    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="font-weight: 500; font-size: 14px;margin-bottom: 20px;">V - Invoice Details</h5>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="V Invoice number" name="lf_5001" data-name='ercfee_v_invoice_number' value='<?php echo $ercfee_v_invoice_number; ?>'>
            <label class="floating__label" data-content="V Invoice number"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="V Invoice Amount" name="lf_5002" data-name='ercfee_v_invoice_amount' value='<?php echo $ercfee_v_invoice_amount; ?>'>
            <label class="floating__label" data-content="V Invoice Amount"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="V Invoiced Qtrs" name="lf_5003" data-name='ercfee_v_invoiced_qtrs' value='<?php echo $ercfee_v_invoiced_qtrs; ?>'>
            <label class="floating__label" data-content="V Invoiced Qtrs"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="V Invoice Sent Date" name="lf_5004" data-name='ercfee_v_invoice_sent_date' value='<?php
               if (!empty($ercfee_v_invoice_sent_date)) {
                   $ercfee_v_invoice_sent_date = date("Y-m-d", strtotime($ercfee_v_invoice_sent_date));
               }
               echo $ercfee_v_invoice_sent_date;
               ?>'>
            <label class="floating__label" data-content="V Invoice Sent Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="bank_account_type floating__input form-control" name="lf_5005" data-name='ercfee_v_invoice_payment_type'>
                <option value=""></option>
                <?php
                $fees_section = [
                    "1" => "Occams Initiated - eCheck",
                    "2" => "Occams Initiated - ACH",
                    "3" => "Client Initiated - Wire",
                    "4" => "Client Initiated - ACH",
                    "5" => "Client Initiated - Check Mailed",
                    "6" => "Credit Card or Debit Card",
                ];
                foreach ($fees_section as $inkey => $invalue) {
                    if ($ercfee_v_invoice_payment_type == $invalue) {
                        $selected = "selected";
                    } else {
                        $selected = "";
                    } ?>
                    <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                <?php } ?>
            </select>
            <label class="floating__label" data-content="V Invoice Payment Type"></label>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="V Invoice Payment Date" name="lf_5006" data-name='ercfee_v_invoice_payment_date' value='<?php
               if (!empty($ercfee_v_invoice_payment_date)) {
                   $ercfee_v_invoice_payment_date = date("Y-m-d", strtotime($ercfee_v_invoice_payment_date));
               }
               echo $ercfee_v_invoice_payment_date;
               ?>'>
            <label class="floating__label" data-content="V Invoice Payment Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="V Invoice Pay Cleared" name="lf_5007" data-name='ercfee_v_invoice_pay_cleared' value='<?php
               if (!empty($ercfee_v_invoice_pay_cleared)) {
                   $ercfee_v_invoice_pay_cleared = date("Y-m-d", strtotime($ercfee_v_invoice_pay_cleared));
               }
               echo $ercfee_v_invoice_pay_cleared;
               ?>'>
            <label class="floating__label" data-content="V Invoice Pay Cleared"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="V Invoice Pay Returned" name="lf_5008" data-name='ercfee_v_invoice_pay_returned' value='<?php
               if (!empty($ercfee_v_invoice_pay_returned)) {
                   $ercfee_v_invoice_pay_returned = date("Y-m-d", strtotime($ercfee_v_invoice_pay_returned));
               }
               echo $ercfee_v_invoice_pay_returned;
               ?>'>
            <label class="floating__label" data-content="V Invoice Pay Returned"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="V Invoice Return Reason" name="lf_5009" data-name='ercfee_v_invoice_return_reason' value='<?php echo $ercfee_v_invoice_return_reason; ?>'>
            <label class="floating__label" data-content="V Invoice Return Reason"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="V Invoice Occams Share" name="lf_5010" data-name='ercfee_v_invoice_occams_share' value='<?php echo $ercfee_v_invoice_occams_share; ?>'>
            <label class="floating__label" data-content="V Invoice Occams Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="V Invoice Aff/Ref Share" name="lf_5011" data-name='ercfee_v_invoice_aff_ref_share' value='<?php echo $ercfee_v_invoice_aff_ref_share; ?>'>
            <label class="floating__label" data-content="V Invoice Aff/Ref Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <!-- Invoice Stage VI -->
    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="font-weight: 500; font-size: 14px;margin-bottom: 20px;">VI - Invoice Details</h5>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VI Invoice number" name="lf_5012" data-name='ercfee_vi_invoice_number' value='<?php echo $ercfee_vi_invoice_number; ?>'>
            <label class="floating__label" data-content="VI Invoice number"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VI Invoice Amount" name="lf_5013" data-name='ercfee_vi_invoice_amount' value='<?php echo $ercfee_vi_invoice_amount; ?>'>
            <label class="floating__label" data-content="VI Invoice Amount"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VI Invoiced Qtrs" name="lf_5014" data-name='ercfee_vi_invoiced_qtrs' value='<?php echo $ercfee_vi_invoiced_qtrs; ?>'>
            <label class="floating__label" data-content="VI Invoiced Qtrs"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="VI Invoice Sent Date" name="lf_5015" data-name='ercfee_vi_invoice_sent_date' value='<?php
               if (!empty($ercfee_vi_invoice_sent_date)) {
                   $ercfee_vi_invoice_sent_date = date("Y-m-d", strtotime($ercfee_vi_invoice_sent_date));
               }
               echo $ercfee_vi_invoice_sent_date;
               ?>'>
            <label class="floating__label" data-content="VI Invoice Sent Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="bank_account_type floating__input form-control" name="lf_5016" data-name='ercfee_vi_invoice_payment_type'>
                <option value=""></option>
                <?php
                $fees_section = [
                    "1" => "Occams Initiated - eCheck",
                    "2" => "Occams Initiated - ACH",
                    "3" => "Client Initiated - Wire",
                    "4" => "Client Initiated - ACH",
                    "5" => "Client Initiated - Check Mailed",
                    "6" => "Credit Card or Debit Card",
                ];
                foreach ($fees_section as $inkey => $invalue) {
                    if ($ercfee_vi_invoice_payment_type == $invalue) {
                        $selected = "selected";
                    } else {
                        $selected = "";
                    } ?>
                    <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                <?php } ?>
            </select>
            <label class="floating__label" data-content="VI Invoice Payment Type"></label>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="VI Invoice Payment Date" name="lf_5017" data-name='ercfee_vi_invoice_payment_date' value='<?php
               if (!empty($ercfee_vi_invoice_payment_date)) {
                   $ercfee_vi_invoice_payment_date = date("Y-m-d", strtotime($ercfee_vi_invoice_payment_date));
               }
               echo $ercfee_vi_invoice_payment_date;
               ?>'>
            <label class="floating__label" data-content="VI Invoice Payment Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="VI Invoice Pay Cleared" name="lf_5018" data-name='ercfee_vi_invoice_pay_cleared' value='<?php
               if (!empty($ercfee_vi_invoice_pay_cleared)) {
                   $ercfee_vi_invoice_pay_cleared = date("Y-m-d", strtotime($ercfee_vi_invoice_pay_cleared));
               }
               echo $ercfee_vi_invoice_pay_cleared;
               ?>'>
            <label class="floating__label" data-content="VI Invoice Pay Cleared"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="VI Invoice Pay Returned" name="lf_5019" data-name='ercfee_vi_invoice_pay_returned' value='<?php
               if (!empty($ercfee_vi_invoice_pay_returned)) {
                   $ercfee_vi_invoice_pay_returned = date("Y-m-d", strtotime($ercfee_vi_invoice_pay_returned));
               }
               echo $ercfee_vi_invoice_pay_returned;
               ?>'>
            <label class="floating__label" data-content="VI Invoice Pay Returned"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VI Invoice Return Reason" name="lf_5020" data-name='ercfee_vi_invoice_return_reason' value='<?php echo $ercfee_vi_invoice_return_reason; ?>'>
            <label class="floating__label" data-content="VI Invoice Return Reason"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VI Invoice Occams Share" name="lf_5021" data-name='ercfee_vi_invoice_occams_share' value='<?php echo $ercfee_vi_invoice_occams_share; ?>'>
            <label class="floating__label" data-content="VI Invoice Occams Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VI Invoice Aff/Ref Share" name="lf_5022" data-name='ercfee_vi_invoice_aff_ref_share' value='<?php echo $ercfee_vi_invoice_aff_ref_share; ?>'>
            <label class="floating__label" data-content="VI Invoice Aff/Ref Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <!-- Invoice Stage VII -->
    <div class="row  m-b-8">
        <div class="col-sm-12">
            <h5 style="font-weight: 500; font-size: 14px;margin-bottom: 20px;">VII - Invoice Details</h5>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VII Invoice number" name="lf_5023" data-name='ercfee_vii_invoice_number' value='<?php echo $ercfee_vii_invoice_number; ?>'>
            <label class="floating__label" data-content="VII Invoice number"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VII Invoice Amount" name="lf_5024" data-name='ercfee_vii_invoice_amount' value='<?php echo $ercfee_vii_invoice_amount; ?>'>
            <label class="floating__label" data-content="VII Invoice Amount"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VII Invoiced Qtrs" name="lf_5025" data-name='ercfee_vii_invoiced_qtrs' value='<?php echo $ercfee_vii_invoiced_qtrs; ?>'>
            <label class="floating__label" data-content="VII Invoiced Qtrs"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="VII Invoice Sent Date" name="lf_5026" data-name='ercfee_vii_invoice_sent_date' value='<?php
               if (!empty($ercfee_vii_invoice_sent_date)) {
                   $ercfee_vii_invoice_sent_date = date("Y-m-d", strtotime($ercfee_vii_invoice_sent_date));
               }
               echo $ercfee_vii_invoice_sent_date;
               ?>'>
            <label class="floating__label" data-content="VII Invoice Sent Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-sm-3">
            <select <?= $disabled ?> class="bank_account_type floating__input form-control" name="lf_5027" data-name='ercfee_vii_invoice_payment_type'>
                <option value=""></option>
                <?php
                $fees_section = [
                    "1" => "Occams Initiated - eCheck",
                    "2" => "Occams Initiated - ACH",
                    "3" => "Client Initiated - Wire",
                    "4" => "Client Initiated - ACH",
                    "5" => "Client Initiated - Check Mailed",
                    "6" => "Credit Card or Debit Card",
                ];
                foreach ($fees_section as $inkey => $invalue) {
                    if ($ercfee_vii_invoice_payment_type == $invalue) {
                        $selected = "selected";
                    } else {
                        $selected = "";
                    } ?>
                    <option value='<?= $inkey ?>' <?= $selected ?>><?php echo $invalue; ?></option>
                <?php } ?>
            </select>
            <label class="floating__label" data-content="VII Invoice Payment Type"></label>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="VII Invoice Payment Date" name="lf_5028" data-name='ercfee_vii_invoice_payment_date' value='<?php
               if (!empty($ercfee_vii_invoice_payment_date)) {
                   $ercfee_vii_invoice_payment_date = date("Y-m-d", strtotime($ercfee_vii_invoice_payment_date));
               }
               echo $ercfee_vii_invoice_payment_date;
               ?>'>
            <label class="floating__label" data-content="VII Invoice Payment Date"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="VII Invoice Pay Cleared" name="lf_5029" data-name='ercfee_vii_invoice_pay_cleared' value='<?php
               if (!empty($ercfee_vii_invoice_pay_cleared)) {
                   $ercfee_vii_invoice_pay_cleared = date("Y-m-d", strtotime($ercfee_vii_invoice_pay_cleared));
               }
               echo $ercfee_vii_invoice_pay_cleared;
               ?>'>
            <label class="floating__label" data-content="VII Invoice Pay Cleared"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="date" <?= $readonly ?> placeholder="VII Invoice Pay Returned" name="lf_5030" data-name='ercfee_vii_invoice_pay_returned' value='<?php
               if (!empty($ercfee_vii_invoice_pay_returned)) {
                   $ercfee_vii_invoice_pay_returned = date("Y-m-d", strtotime($ercfee_vii_invoice_pay_returned));
               }
               echo $ercfee_vii_invoice_pay_returned;
               ?>'>
            <label class="floating__label" data-content="VII Invoice Pay Returned"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
    <div class="row  m-b-8">
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VII Invoice Return Reason" name="lf_5031" data-name='ercfee_vii_invoice_return_reason' value='<?php echo $ercfee_vii_invoice_return_reason; ?>'>
            <label class="floating__label" data-content="VII Invoice Return Reason"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VII Invoice Occams Share" name="lf_5032" data-name='ercfee_vii_invoice_occams_share' value='<?php echo $ercfee_vii_invoice_occams_share; ?>'>
            <label class="floating__label" data-content="VII Invoice Occams Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
        <div class="floating col-md-3">
            <input id="full_name" class="floating__input form-control" type="text" <?= $readonly ?> placeholder="VII Invoice Aff/Ref Share" name="lf_5033" data-name='ercfee_vii_invoice_aff_ref_share' value='<?php echo $ercfee_vii_invoice_aff_ref_share; ?>'>
            <label class="floating__label" data-content="VII Invoice Aff/Ref Share"></label>
            <span class="error" id="last_nameErr"></span>
        </div>
    </div>
                                                

<?php  

exit;

?>