<?php
global $wpdb;
$table_name = $wpdb->prefix.'projects';
$data = $request->get_json_params();
$project_id = $data['project_id'];

$project = $wpdb->get_row("SELECT {$wpdb->prefix}projects.*,{$wpdb->prefix}erc_business_info.business_legal_name,{$wpdb->prefix}erc_business_info.doing_business_as,{$wpdb->prefix}erc_business_info.business_category,{$wpdb->prefix}erc_business_info.website_url,{$wpdb->prefix}erc_business_info.authorized_signatory_name,{$wpdb->prefix}erc_business_info.business_phone,{$wpdb->prefix}erc_business_info.business_email,{$wpdb->prefix}erc_business_info.business_title,{$wpdb->prefix}erc_business_info.zip,{$wpdb->prefix}erc_business_info.street_address,{$wpdb->prefix}erc_business_info.state,{$wpdb->prefix}erc_business_info.city,{$wpdb->prefix}erc_business_info.identity_document_type,{$wpdb->prefix}erc_business_info.identity_document_number,{$wpdb->prefix}erc_business_info.business_entity_type,{$wpdb->prefix}erc_business_info.registration_number,{$wpdb->prefix}erc_business_info.registration_date,{$wpdb->prefix}erc_business_info.state_of_registration,{$wpdb->prefix}crm_products.Title as product_name,{$wpdb->prefix}milestones.milestone_name, {$wpdb->prefix}milestones.status as milestoneActiveStatus, {$wpdb->prefix}milestones.map as milestoneMap,{$wpdb->prefix}milestone_stages.stage_name as milestoneStatus,{$wpdb->prefix}milestone_stages.status as StageActiveStatus,{$wpdb->prefix}milestone_stages.deleted_at as StageDeleteStatus,{$wpdb->prefix}erc_erc_intake.fee_type
                                                                    FROM {$wpdb->prefix}projects 
                                                                    JOIN {$wpdb->prefix}erc_business_info 
                                                                    ON {$wpdb->prefix}projects.lead_id = {$wpdb->prefix}erc_business_info.lead_id
                                                                    JOIN {$wpdb->prefix}crm_products 
                                                                    ON {$wpdb->prefix}projects.product_id = {$wpdb->prefix}crm_products.ProductID 
                                                                    LEFT JOIN {$wpdb->prefix}milestones 
                                                                    ON {$wpdb->prefix}projects.milestone_id = {$wpdb->prefix}milestones.milestone_id 
                                                                    LEFT JOIN {$wpdb->prefix}milestone_stages
                                                                    ON {$wpdb->prefix}projects.milestone_stage_id = {$wpdb->prefix}milestone_stages.milestone_stage_id 
                                                                    LEFT JOIN {$wpdb->prefix}erc_erc_intake
                                                                    ON {$wpdb->prefix}projects.lead_id = {$wpdb->prefix}erc_erc_intake.lead_id 
                                                                    WHERE {$wpdb->prefix}projects.deleted_at IS NULL 
                                                                    AND {$wpdb->prefix}projects.project_id = ".$project_id." ");
if(!empty($project)){
	$lead_id = $project->lead_id;
	$company_folder = '';
	$erc_document_folder = '';
	$stc_document_folder = '';
	$agreement_folder = '';

	//for erc project
	$intake_table_name = $wpdb->prefix."erc_erc_intake";
	$in_sql = $wpdb->prepare("SELECT company_folder_link,document_folder_link FROM `$intake_table_name` WHERE lead_id = ".$lead_id."");
	$intaken_data = $wpdb->get_row($in_sql);
	if($intaken_data){
	    $company_folder = $intaken_data->company_folder_link;
	    $erc_document_folder = $intaken_data->document_folder_link;
	}

	//for stc project
	$lead_mapping_data = $wpdb->get_row("SELECT document_folder,agreement_folder FROM {$wpdb->prefix}lead_folder_mapping WHERE lead_id = ".$lead_id."");
	if(!empty($lead_mapping_data)){
	    $stc_document_folder = "https://occams-my.sharepoint.com/personal/admin_occamsparadigm_com/_layouts/15/onedrive.aspx?id=%2Fpersonal%2Fadmin%5Foccamsparadigm%5Fcom%2FDocuments%2FOccams%2FUSA%2FBusiness%20Segments%2FFinancial%20%26%20Tax%20Advisory%20%28FTA%29%2FTax%20Credits%2FERC%2FERC%20Client%27s%2FAutoX/".$lead_mapping_data->document_folder;
	    $agreement_folder = "https://occams-my.sharepoint.com/personal/admin_occamsparadigm_com/_layouts/15/onedrive.aspx?id=%2Fpersonal%2Fadmin%5Foccamsparadigm%5Fcom%2FDocuments%2FOccams%2FUSA%2FBusiness%20Segments%2FFinancial%20%26%20Tax%20Advisory%20%28FTA%29%2FTax%20Credits%2FERC%2FERC%20Client%27s%2FAutoX/".$lead_mapping_data->agreement_folder;
	}
	$results['status'] = 1;
	$results['message'] = 'Project Detail';
	$results['result'][0]['lead_id'] = $lead_id;
	$results['result'][0]['project_name'] = $project->project_name;
	$results['result'][0]['business_legal_name'] = $project->business_legal_name;
	$results['result'][0]['product_id'] = $project->product_id;
	$results['result'][0]['product_name'] = $project->product_name;
	$results['result'][0]['fee_type'] = $project->fee_type;
	$results['result'][0]['project_fee'] = $project->project_fee;
	$results['result'][0]['review_status'] = $project->review_status;
	$results['result'][0]['review_link'] = $project->review_link;
	$results['result'][0]['authorized_signatory_name'] = $project->authorized_signatory_name;
	$results['result'][0]['business_phone'] = $project->business_phone;
	$results['result'][0]['business_email'] = $project->business_email;
	$results['result'][0]['business_title'] = $project->business_title;
	$results['result'][0]['zip'] = $project->zip;
	$results['result'][0]['street_address'] = $project->street_address;
	$results['result'][0]['city'] = $project->city;
	$results['result'][0]['state'] = $project->state;
	$results['result'][0]['identity_document_type'] = $project->identity_document_type;
	$results['result'][0]['identity_document_number'] = $project->identity_document_number;
	$results['result'][0]['doing_business_as'] = $project->doing_business_as;
	$results['result'][0]['business_category'] = $project->business_category;
	$results['result'][0]['website_url'] = $project->website_url;
	$results['result'][0]['business_entity_type'] = $project->business_entity_type;
	$results['result'][0]['registration_number'] = $project->registration_number;
	$results['result'][0]['registration_date'] = $project->registration_date;
	$results['result'][0]['state_of_registration'] = $project->state_of_registration;
	$results['result'][0]['erc_document_folder'] = $erc_document_folder;
	$results['result'][0]['company_folder'] = $company_folder;
	$results['result'][0]['stc_document_folder'] = $stc_document_folder;
	$results['result'][0]['agreement_folder'] = $agreement_folder;
}else{
	$results['status'] = 0;
    $results['message'] = 'No project found.';
}
echo json_encode($results);die;