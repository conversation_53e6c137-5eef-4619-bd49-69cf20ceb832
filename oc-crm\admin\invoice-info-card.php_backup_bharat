<?php
// Handle the invoice URL separately to ensure it is set to blank if empty
$invoice_url = !empty($invoice['invoice_url']) ? esc_url($invoice['invoice_url']) : '';
$inv_id = $invoice['id'];

$invoice_status_id = !empty($invoice['status']) ? esc_html($invoice['status']) : '';


$invoice_prefix = "";

if (isset($_GET['page']) && $_GET['page'] == "manage-stc-project") {
    $invoice_prefix = "STC-";
}

if (isset($_GET['page']) && $_GET['page'] == "manage-ta-project") {
    $invoice_prefix = "TAX-";
}

if (isset($_GET['page']) && $_GET['page'] == "manage-audit-project") {
    $invoice_prefix = "AAR-";
}


if (isset($_GET['page']) && $_GET['page'] == "manage-rdc-project") {
    $invoice_prefix = "RDC-";
}



$invoice_status_class = "";

if ($invoice_status_id == 1) {
    // $invoice_status = 'Invoiced';
    $invoice_status = 'Unpaid';
    $invoice_status_class = 'unpaid badge bg-danger';
} else if ($invoice_status_id == 2) {
    $invoice_status = 'Paid';
    $invoice_status_class = 'paid_invoice badge bg-success';
} else if ($invoice_status_id == 3) {
    $invoice_status = 'Cancel';
    $invoice_status_class = 'cancel badge bg-cancel';
} else if ($invoice_status_id == 4) {
    $invoice_status = 'Draft';
    $invoice_status_class = 'draft';
} else if ($invoice_status_id == 5) {
    $invoice_status = 'Remind';
    $invoice_status_class = 'remind';
} else if ($invoice_status_id == 6) {
    $invoice_status = 'Payment in process';
    $invoice_status_class = 'payment_in_process badge bg-warning';
} else if ($invoice_status_id == 13) {
    $invoice_status = 'Delete';
    $invoice_status_class = 'delete';
} else if ($invoice_status_id == 17) {
    $invoice_status = 'Partially paid';
    $invoice_status_class = 'partially_paid badge bg-primary';
} else {
    $invoice_status = '';
}

if ($invoice_status_id == 3) {
    $class = 'disabled';
} else {
    $class = '';
}


$payment_methods = array(
    "occams_initiated_eCheck" => "Occams Initiated - eCheck",
    "occams_initiated_ach" => "Occams Initiated - ACH",
    "occams_initiated_wire" => "Client Initiated - Wire",
    "client_initiated_ach" => "Client Initiated - ACH",
    "client_initiated_check_mailed" => "Client Initiated - Check Mailed",
    "credit_card_or_debit_card" => "Credit Card or Debit Card"
);




// $payment_methods = array(
//     "credit_card" => "Credit card",
//     "ach" => "Bank transfer",
//     "qb-link" => "QB Payment Link",
//     "ipn" => "PayPal",
//     "other-payment" => "Other Payment",
// );

// Assume $invoice['payment_method'] is a comma-separated string like "credit_card,ach"
$payment_method_keys = !empty($invoice['payment_mode']) ? explode(',', $invoice['payment_mode']) : [];

$selected_values = array();

foreach ($payment_method_keys as $key) {
    $key = trim($key); // Trim any extra whitespace
    if (array_key_exists($key, $payment_methods)) {
        $selected_values[] = $payment_methods[$key];
    }
}

$payment_mode_string = !empty($selected_values) ? implode(', ', $selected_values) : 'N/A';

$invoice_url = "";
$edit_access = "true";
$invoice_url = site_url() . "/wp-admin/admin.php?page=edit-custom-invoice&invoice_id=" . $invoice['id'];

if ($invoice_status != 'Unpaid' && $invoice_status != 'partially paid' && $invoice['invoice_type'] != "custom_invoice") {
    $edit_access = "false";
}

if ($invoice_status_id == 3 || $invoice_status_id == 2 || $invoice_status_id == 6) {
    $edit_access = "false";
}

if ($invoice['invoice_url'] == "") {
    $edit_access = "false";
}


$invoice_total = "";


if ($invoice['total_amount'] != "") {
    $invoice_total = "$" . $invoice['total_amount'];
}

// $invoice_total = "";

?>

<div class="contact_tab_data ">
    <div class="col-md-12 col-sm-12 contact-card  <?php echo $class; ?>">
        <div class="row custom_opp_tab">
            <div class="col-sm-12">
                <div class="custom_opp_tab_header">
                    <h5>
                        <a href="<?php echo $invoice_url; ?>" target="_blank">
                            Invoice
                            #<?php echo $invoice_prefix; ?><?php echo !empty($invoice['customer_invoice_no']) ? esc_html($invoice['customer_invoice_no']) : ''; ?>
                        </a>
                        -
                        <span class="status <?php echo $invoice_status_class; ?>"> <?php echo $invoice_status; ?></span>
                    </h5>
                    <div class="opp_edit_dlt_btn projects-iris">
                        <?php
                        if ($invoice_status_id != 2 && $invoice_status_id != 3 && $invoice_status_id != 6) {
                            echo do_shortcode('[invoice_actionlist invoice_id="' . $invoice['id'] . '"]');
                        }
                        ?>
                        <?php

                        if ($edit_access == "true") {

                            ?>
                            <a class="edit_opportunity" target="_blank" href="<?php echo $invoice_url; ?>">
                                <i class="fa-solid fa-pen"></i>
                            </a>
                            <?php

                        }

                        ?>
                    </div>
                </div>
            </div>


            <div class="col-md-7 text-left">
                <div class="lead_des">
                    <p>
                        <b>Invoice Amount:</b>
                        <?php echo ($invoice_total != "") ? esc_html($invoice_total) : 'N/A'; ?>
                    </p>

                    <p>
                        <b>Invoice Sent Date:</b>
                        <?php echo !empty($invoice['invoice_date']) ? esc_html($invoice['invoice_date']) : 'N/A'; ?>
                    </p>
                    <p>
                        <b>Invoice Due Date:</b>
                        <?php echo !empty($invoice['due_date']) ? esc_html($invoice['due_date']) : 'N/A'; ?>
                    </p>
                    <p>
                        <b>Service Name:</b>
                        <?php echo !empty($invoice['product_names']) ? esc_html($invoice['product_names']) : 'N/A'; ?>
                    </p>
                </div>

            </div>


            <?php

            if ($invoice_status_id != 17) {

                ?>

                <div class="col-md-5">
                    <div class="lead_des">

                        <p>
                            <b>Payment Date:</b>
                            <?php echo !empty($invoice['payment_date']) ? esc_html($invoice['payment_date']) : 'N/A'; ?>
                        </p>
                        <p>
                            <b>Payment Cleared Date:</b>
                            <?php echo !empty($invoice['payment_cleared_date']) ? esc_html($invoice['payment_cleared_date']) : 'N/A'; ?>
                        </p>
                        <p>
                            <b>Payment Mode:</b>
                            <?php echo esc_html($payment_mode_string); ?>
                        </p>
                    </div>

                </div>

                <?php
            }

            ?>




            <?php if ($invoice_status_id == 17) { ?>
                <a class="expand_pp_div" data-toggle="collapse" href="#invoice_pp_<?php echo $inv_id; ?>"
                    aria-expanded="false" aria-controls="invoice_pp">Partially payment history
                    <i class="fa-regular fa-circle-arrow-down expand_card_partialdetail" style="font-weight: 700"></i>
                </a>
                <div class="collapse" id="invoice_pp_<?php echo $inv_id; ?>">
                    <div class="card card-body" style="max-width: 100%;padding: 10px;">
                        <?php echo do_shortcode('[partial_payment_list invoice_id="' . $invoice['id'] . '" invoice_amount="' . $invoice['total_amount'] . '"]'); ?>
                    </div>
                </div>
            <?php } ?>

        </div>
    </div>

</div>

<!--/ invoice-card-end -->