                <?php 
                    $lead_id = $_POST['lead_id']; 
                    // $_COOKIE['iris_business_lead_id']='';
                    // $_COOKIE['iris_business_lead_id']=$lead_id;
                    
                    global $wpdb;
                    $doc_mapping_table = $wpdb->prefix . 'leads_document_mapping';
                    $doc_uplod_table =  $wpdb->prefix . 'leads_document_upload';
                    $doc_uplod_status = $wpdb->prefix . 'leads_document_status';
                    $doc_uplod_notes = $wpdb->prefix . 'leads_document_notes';
                    $doc_required_mapping = $wpdb->prefix . 'leads_document_required_mapping';  
                    $sdgr_calculator_table = $wpdb->prefix . 'sdgr_calculator';

                //------- Last agreement date check
                $erc_product_id = 935;

                global $wpdb;
                $bsns_table = $wpdb->prefix . 'erc_business_info';
                $opportunity_product_table = $wpdb->prefix . 'opportunity_products';
                $opportunities_table = $wpdb->prefix . 'opportunities';
                $products_table = $wpdb->prefix . 'crm_products';

                $is_last_agrrement = '';
                if($lead_id) {
                    $last_agreement_sent = "SELECT $opportunities_table.last_agreement_sent 
    FROM $opportunities_table 
    LEFT JOIN $opportunity_product_table ON $opportunities_table.OpportunityID = $opportunity_product_table.opportunity_id 
    WHERE $opportunities_table.LeadID = $lead_id 
    AND $opportunities_table.DeletedAt IS NULL
    AND $opportunity_product_table.product_id = $erc_product_id
    AND $opportunities_table.last_agreement_sent > '2024-06-21' ";
                    $is_last_agrrement = $wpdb->get_row($last_agreement_sent);
                }
                //------- Last agreement date check

                ?>
                <div class="tab-pane" id="eleve-documents" role="tabpanel" aria-labelledby="eleve-documents-tab">
                <ul class="nav nav-pills erc_document_tabs manage_project_sub_heading" id="pills-tab" role="tablist">
                    <li class="nav-item active">
                        <a class="nav-link" id="erc-doc-tab" data-toggle="tab" href="#erc-docs" role="tab" aria-controls="second" aria-selected="false">ERC Documents</a>
                    </li>
                      <li class="nav-item">
                          <a class="nav-link" id="company-doc-tab" data-toggle="tab" href="#company-docs" role="tab" aria-controls="first" aria-selected="true">Company Documents</a>
                      </li>
                      <li class="nav-item">
                          <a class="nav-link" id="payroll-doc-tab" data-toggle="tab" href="#payroll-docs" role="tab" aria-controls="second" aria-selected="false">Payroll Documents</a>
                      </li>

                      <li class="nav-item">
                          <a class="nav-link" id="other-doc-tab" data-toggle="tab" href="#other-docs" role="tab" aria-controls="second" aria-selected="false">Other Documents</a>
                      </li>
                  </ul>
                  <div class="tab-content">
                    
                    <div class="tab-pane erc-project-scroll" id="company-docs" role="tabpanel" aria-labelledby="company-docs">
                      <div class="white_card_body company-docs">
                          <!-- For OPS user table Start -->
                          <h5 class="mt-3 mb-3 title_text"><b>Company Documents</b></h5>
                          <div class="main_content_iner">
                          <div class="container-fluid p-0">
                <div class="row justify-content-center mb-4">
                    <div class="col-lg-12">
                        <div class="white_card">
                            <?php
             $parent_folder = 'Company Documents';
             $form_id = 1;
                 $company_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `form_id` = %d",$form_id));          
                 $test = doc_master_User;
                 
                            ?>
                            <div class="white_card_body">
               <?php 
                  $userdata = get_user_by( 'id', get_current_user_id() );
                  $user_roles=$userdata->roles;
                if (in_array("lead", $user_roles ) || in_array("lead_associate", $user_roles)) {?> 
           <div class="table-responsive">
             <table class="table com_doc_display">
                 <thead>
                     <tr>
           <th style="text-align: left;">Documents</th>
           <th>Files </th>
           <th class="hideshow_onupload">Status</th>
           <th class="hideshow_onupload" >Comments</th>
                     </tr>
                 </thead>
                 <tbody>
               <?php foreach ($company_docs as $key => $value) {
               $doc_type_id = $value->doc_type_id;
               $doc_key =  $value->doc_key;
               $doc_label = $value->doc_label;
                   if($is_last_agrrement) {
                       if($doc_type_id == 1){
                           $doc_label = "Certificate or Articles of Incorporation";
                       }
                       if($doc_type_id == 2){
                           $doc_label = "Tax Id- Employee Identification Number(EIN) Issued by IRS";
                       }
                   }
                   ?>
                             <tr>
                   
                             <?php 

                   $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
               
                   $file_url = $result[0]->uploded_documents;
                   $file_name = $result[0]->local_path_filename;
                   $doc_id = $result[0]->id ;
                   $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                  ?> 
                             <?php 
                    
           ?>
           <td class="first_column">
           <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
           </td>
           <td>
           <?php if($file_url == ''){
              
            ?>
               <label class="custom-file-upload" >
               <span class="material-symbols-outlined">upload_file</span> 
               <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
               </label>
               <label class="file_remove_label" style="display:none;">
                   <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
               </label>
           <?php }else{ 
              $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
              $file_status = $status_data[0]->doc_status;
             ?>
               <label class="custom-file-upload" style="display:none;">
               <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
               </label>
               <label class="file_remove_label" >
                   <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
               </label>
               <?php } ?>
           </td>
           <td  class="hideshow_onupload" >
            <span class="progress_status" style="display: <?php echo isset($file_url) && $file_status === 'In review' ? 'block' : 'none'; ?>">In review</span>
           <span class="approved_status" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'block' : 'none'; ?>">Approved</span>
           <span class="rejected_status" style="display: <?php echo isset($file_url) && $file_status === 'Rejected' ? 'block' : 'none'; ?>">Rejected</span>
           <span class="not_uploaded" style="display: <?php echo empty($file_url) ? 'block' : 'none'; ?>">Yet to upload</span> 
           </td>
           <?php if( !empty($comment_data)) { ?>    
           <td>
             <div style="display: flex;">
               <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
               <i class="fa-regular fa-comment"></i>
               </span>
               <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
               <i class="fa-solid fa-comment-medical"></i>
               </span>
             </div> 
           </td>
           <?php } ?>
                     </tr> 
                      <?php } ?>
                 </tbody>
             </table>
           </div>
                      <?php } else {?>
             
             <!-- For OPS user table Start -->
             <div class="table-responsive">
             <table class="table com_doc_display">
                 <thead>
                     <tr>
                     <th style="text-align: left;">Documents</th>
                     <th>Files </th>
                     <th>Status</th>
                     <th>Comments</th>
                     </tr>
                 </thead>
                 <tbody>
                 <?php foreach ($company_docs as $key => $value) {
                   $doc_type_id = $value->doc_type_id;
                   $doc_key =  $value->doc_key;
                   $doc_label = $value->doc_label;
                     if($is_last_agrrement) {
                         if($doc_type_id == 1){
                             $doc_label = "Certificate or Articles of Incorporation";
                         }
                         if($doc_type_id == 2){
                             $doc_label = "Tax Id- Employee Identification Number(EIN) Issued by IRS";
                         }
                     }
                          ?>
                     <tr>
                   
                     <?php 
                   
                      $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                     $file_url = $result[0]->uploded_documents;
                      $file_name = $result[0]->local_path_filename;
                      $doc_id = $result[0]->id ;
                      $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                     ?> 
           <td class="first_column">
           <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
           </td>
           <?php if($file_url != ''){
               $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
               $file_status = $status_data[0]->doc_status;
            ?>
           <td style="text-align: center;">
           <label class="file_remove_label" >
                  <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
               </label>  
              
           </td>
           <td>
              <select name="change_document_status" id="change_erc_document_status" class="change_doc" <?php echo isset($file_url) &&  $file_status != 'In review' && get_current_user_id() != $test ? 'disabled' : ''; ?> >
                   <option value="">- Select Status -</option>
                   <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>">In review</option>
                   <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                   <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>"  data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
               </select>
           </td>
           <td style="text-align: center;">
           <?php if( !empty($comment_data)) { ?>     
               <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
             <i class="fa-regular fa-comment"></i>
             </span>
             <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
           <i class="fa-solid fa-comment-medical"></i>
             </span> 
           <?php } ?>   

           </td>
           <?php }else {?>
             <td>
           <?php if($file_url == '' && get_current_user_id() == $test ){
              
            ?>
               <label class="custom-file-upload" >
               <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
               </label>
               <label class="file_remove_label" style="display:none;">
                   <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
               </label>
           <?php } ?>
           </td>
               <td style="text-align: center;"><span class="not_uploaded" >Yet to upload</span></td>
               <td style="text-align: center;"><?php if( !empty($comment_data)) { ?>     
               <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
             <i class="fa-regular fa-comment"></i>
             </span>
           <?php } ?>   </td>
                      <?php   } ?>
                     </tr>
             <?php } ?>
                 </tbody>
             </table>
             </div>
             <!-- For OPS user table END -->

                       <?php   }?>
                            </div>
                        </div>
                    </div>
                </div>
                          </div>
                      </div>          
                      
                      </div>    
                    </div>
                    <div class="tab-pane erc-project-scroll" id="payroll-docs" role="tabpanel" aria-labelledby="payroll-docs">
                      <div class="white_card_body payroll-docs">
                          <!-- For OPS user table Start -->
                          <h5 class="mt-3 mb-3 title_text"><b>Payroll Documents</b></h5>
                          <div class="main_content_iner">
    <div class="container-fluid p-0">
        <div class="row justify-content-center mb-4">
            <div class="col-lg-12">
                <div class="white_card">
                    <?php
                        $parent_folder = 'Payroll Documents';
                        $form_id = 2;


                    if($is_last_agrrement) {
                        $quat_payroll_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `doc_key` IN ('quat_payroll_2021_q1','quat_payroll_2021_q2','quat_payroll_2021_q3') "));
                        $payroll_register_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `doc_key` IN ('payroll_registers_2021_q1','payroll_registers_2021_q2','payroll_registers_2021_q3') "));
                        $payroll_healthcare_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `doc_key` IN ('payroll_health_care_2021_q1','payroll_health_care_2021_q2','payroll_health_care_2021_q3') "));
                    }else{
                        $quat_payroll_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `doc_key` IN ('quat_payroll_2020_q1','quat_payroll_2020_q2','quat_payroll_2020_q3','quat_payroll_2020_q4','quat_payroll_2021_q1','quat_payroll_2021_q2','quat_payroll_2021_q3') "));
                        $payroll_register_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `doc_key` IN ('payroll_registers_2020_q1','payroll_registers_2020_q2','payroll_registers_2020_q3','payroll_registers_2020_q4','payroll_registers_2021_q1','payroll_registers_2021_q2','payroll_registers_2021_q3') "));
                        $payroll_healthcare_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `doc_key` IN ('payroll_health_care_2020_q1','payroll_health_care_2020_q2','payroll_health_care_2020_q3','payroll_health_care_2020_q4','payroll_health_care_2021_q1','payroll_health_care_2021_q2','payroll_health_care_2021_q3') "));
                    }
                    $doc_keys = array('payroll_2019_average_fte','payroll_additional_field1', 'payroll_additional_field2', );


              //$quat_payroll_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `doc_key` IN ('quat_payroll_2020_q1','quat_payroll_2020_q2','quat_payroll_2020_q3','quat_payroll_2020_q4','quat_payroll_2021_q1','quat_payroll_2021_q2','quat_payroll_2021_q3') "));
              //$payroll_register_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `doc_key` IN ('payroll_registers_2020_q1','payroll_registers_2020_q2','payroll_registers_2020_q3','payroll_registers_2020_q4','payroll_registers_2021_q1','payroll_registers_2021_q2','payroll_registers_2021_q3') "));
              //$payroll_healthcare_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `doc_key` IN ('payroll_health_care_2020_q1','payroll_health_care_2020_q2','payroll_health_care_2020_q3','payroll_health_care_2020_q4','payroll_health_care_2021_q1','payroll_health_care_2021_q2','payroll_health_care_2021_q3') "));
              //$doc_keys = array('payroll_2019_average_fte','payroll_additional_field1', 'payroll_additional_field2', );
              $doc_keys_placeholder = implode("','", $doc_keys);
              $query = "SELECT * FROM $doc_mapping_table WHERE `doc_key` IN ('$doc_keys_placeholder') ORDER BY FIELD(`doc_key`, '$doc_keys_placeholder')";
              $payroll_additional_docs = $wpdb->get_results($query);
              $test = doc_master_User;
              $test = doc_master_User;
                    ?>
                    <div class="white_card_body">
                          <?php 
          $userdata = get_user_by( 'id', get_current_user_id() );
          $user_roles=$userdata->roles;
        if (in_array("lead", $user_roles ) || in_array("lead_associate", $user_roles)) {?> 

                <h5 class="mb-3 title_text"><b>
                        <?php
                        if($is_last_agrrement) {
                            echo "Payroll Tax Return 941’s for quarters- 2021: Q1-Q3";
                        }else{
                            echo "Quarterly Payroll Tax Return 941’s for Eligible Quarters – 2020: Q1-Q4 & 2021: Q1-Q3";
                        }
                        ?>
                        </b></h5>
                        <div class="row">
              <div class="col-md-12 table-responsive">
                        <table class="table com_doc_display payroll_sec">
              <thead>
                  <tr>
                      <th>Quarters</th>
                      <th>Did your Company have W2 Employee in this Quarter? </th>
                      <th>Files</th>
                      <th class="hideshow_onupload" >Status</th>
                      <th class="hideshow_onupload" >Comments</th>
                  </tr>
              </thead>
              <tbody>
                          <?php foreach ($quat_payroll_docs as $key => $value) {
                          $doc_type_id = $value->doc_type_id;
                          $doc_key =  $value->doc_key;
                          $doc_label = $value->doc_label;
                ?>
                     <tr>
                
                     <?php 

                $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                          
                $file_url = $result[0]->uploded_documents;
                $file_name = $result[0]->local_path_filename;
                $doc_id = $result[0]->id ;
                $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                $doc_required_mapping_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_required_mapping WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                $doc_required = empty($doc_required_mapping_data) ? '0' : $doc_required_mapping_data[0]->required_flag;

              ?> 
                     <?php 
                 
                      ?>
                      <td class="first_column">
                      <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                      </td>
                      <td>
                      <input type="radio" id="" name="payroll_doc_section_required_<?php echo $doc_type_id ;?>" class="doc_section_required" data-index_key = "<?php echo $key.'yes' ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" value="Yes" <?php echo (isset($doc_required) && $doc_required === '0') ? 'checked' : ''; ?> data-doc_id = "<?php echo $doc_id ?>" data-section="section1">
                        <label for="html">Yes</label>
                        <input type="radio" id="" name="payroll_doc_section_required_<?php echo $doc_type_id ;?>" class="doc_section_required" data-index_key = "<?php echo $key.'no' ?>" data-doc_type_id="<?php echo $doc_type_id ;?>"  value="No" <?php echo (isset($doc_required) && $doc_required === '1') ? 'checked' : ''; ?>  data-doc_id = "<?php echo $doc_id ?>" data-section="section1">
                        <label for="css">No</label>
                      </td>
                      <td>

                      <?php 
                           if($file_url == ''){
                       ?>
                          <label class="custom-file-upload" style= "<?php if($doc_required == '1') {  echo 'filter: grayscale(100%); pointer-events: none;' ;}?>">
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                          </label>
                          <label class="file_remove_label" style="display:none;">
           <span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                          </label>
                      <?php }else{ 
                
                         $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                
                       $file_status = $status_data[0]->doc_status;
                        
                        ?>
                          <label class="custom-file-upload" style="display:none;">
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                          </label>
                          <label class="file_remove_label" >
           <span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="color: initial; display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                          </label>
                          <?php }
                          
                     ?>                     
                      </td>
                      <td  class="hideshow_onupload" >
                       <span class="progress_status" style="display: <?php echo isset($file_url) && $file_status === 'In review' ? 'block' : 'none'; ?>">In review</span>
                      <span class="approved_status" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'block' : 'none'; ?>">Approved</span>
                      <span class="rejected_status" style="display: <?php echo isset($file_url) && $file_status === 'Rejected' ? 'block' : 'none'; ?>">Rejected</span>
                      <span class="not_uploaded" style="display: <?php echo empty($file_url) && $doc_required == '0' ? 'block' : 'none'; ?>">Yet to upload</span>
                      <span class="not_required" style="display: <?php  echo $doc_required == '1' ? 'block' : 'none'; ?>">Not Applicable</span>
                      </td>
                      <?php if( !empty($comment_data)) { ?>    
                      <td>
                        <div style="display: flex;">  
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                          <i class="fa-regular fa-comment"></i>
                          </span>
                          <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
                          <i class="fa-solid fa-comment-medical"></i>
                          </span>
                        </div> 
                      </td>
                      <?php } ?>
                  </tr> 
<?php } ?>
              </tbody>
                        </table>
                    </div>
                </div>


                <h5 class="mt-3 mb-3 title_text"><b>
                        <?php
                        if($is_last_agrrement) {
                            echo "Payroll Registers for quarters- 2021: Q1-Q3";
                        }else{
                            echo "Payroll Registers for all Eligible Quarters – 2020: Q1-Q4 & 2021: Q1-Q3";
                        }
                        ?>
                        </b></h5>
                        <div class="row">
              <div class="col-md-12 table-responsive">
                 
                        <table class="table com_doc_display payroll_sec">
              <thead>
                  <tr>
                      <th>Quarters</th>
                      <th>Did your Company have W2 Employee in this Quarter? </th>
                      <th>Files</th>
                      
                      <th class="hideshow_onupload" >Status</th>
                      <th class="hideshow_onupload" >Comments</th>
                  </tr>
              </thead>
              <tbody>
                          <?php foreach ($payroll_register_docs as $key => $value) {
                          $doc_type_id = $value->doc_type_id;
                          $doc_key =  $value->doc_key;
                          $doc_label = $value->doc_label;
                ?>
                     <tr>
                
                     <?php 

                $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                          
                $file_url = $result[0]->uploded_documents;
                $file_name = $result[0]->local_path_filename;
                $doc_id = $result[0]->id ;
                $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                $doc_required_mapping_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_required_mapping WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                $doc_required = empty($doc_required_mapping_data) ? '0' : $doc_required_mapping_data[0]->required_flag;
              ?> 
                     <?php 
                 
                      ?>
                      <td class="first_column">
                      <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                      </td>
                      <td style="text-align: center;">
                      <input type="radio" id="" name="payroll_doc_section_required_<?php echo $doc_type_id ;?>" class="doc_section_required" data-index_key = "<?php echo $key.'yes' ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" value="Yes" <?php echo (isset($doc_required) && $doc_required === '0') ? 'checked' : ''; ?>  data-doc_id = "<?php echo $doc_id ?>" data-section="section2">
                        <label for="html">Yes</label>
                        <input type="radio" id="" name="payroll_doc_section_required_<?php echo $doc_type_id ;?>" class="doc_section_required" data-index_key = "<?php echo $key.'no' ?>" data-doc_type_id="<?php echo $doc_type_id ;?>"  value="No" <?php echo (isset($doc_required) && $doc_required === '1') ? 'checked' : ''; ?> data-doc_id = "<?php echo $doc_id ?>" data-section="section2">
                        <label for="css">No</label>
                      </td>
                      <td style="text-align: center;">
                      <?php if($file_url == ''){
                         
                       ?>
                          <label class="custom-file-upload" style= "<?php if($doc_required == '1') {  echo 'filter: grayscale(100%); pointer-events: none;' ;}?>">
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                          </label>
                          <label class="file_remove_label" style="display:none;">
           <span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                          </label>
                      <?php }else{ 
                         $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                         $file_status = $status_data[0]->doc_status;
                        ?>
                          <label class="custom-file-upload" style="display:none;">
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                          </label>
                          <label class="file_remove_label" >
           <span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="color: initial; display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                          </label>
                          <?php } ?>
                      </td>
                      <td  class="hideshow_onupload" >
                       <span class="progress_status" style="display: <?php echo isset($file_url) && $file_status === 'In review' ? 'block' : 'none'; ?>">In review</span>
                      <span class="approved_status" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'block' : 'none'; ?>">Approved</span>
                      <span class="rejected_status" style="display: <?php echo isset($file_url) && $file_status === 'Rejected' ? 'block' : 'none'; ?>">Rejected</span>
                      <span class="not_uploaded" style="display: <?php echo empty($file_url) && $doc_required == '0' ? 'block' : 'none'; ?>">Yet to upload</span>
                      <span class="not_required" style="display: <?php  echo $doc_required == '1' ? 'block' : 'none'; ?>">Not Applicable</span>                      </td>
                      <?php if( !empty($comment_data)) { ?>    
                      <td>
                        <div style="display: flex;"> 
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                          <i class="fa-regular fa-comment"></i>
                          </span>
                          <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
                          <i class="fa-solid fa-comment-medical"></i>
                          </span>
                        </div> 
                      </td>
                      <?php } ?>
                  </tr> 
<?php } ?>
              </tbody>
                        </table>
                    </div>
                </div>


                <h5 class="mt-3 mb-3 title_text"><b>
                        <?php
                        if($is_last_agrrement) {
                            echo "Health Care Expenses per employee per payroll for 2021: Q1-Q3 (if applicable)";
                        }else{
                            echo "Total Health Care Expenses per Employee per Payroll for 2020: Q1-Q4 & 2021: Q1-Q3";
                        }
                        ?>
                        </b></h5>
                        <div class="row">
              <div class="col-md-12 table-responsive">
                        <table class="table com_doc_display payroll_sec">
              <thead>
                  <tr>
                      <th>Quarters</th>
                      <th>Did your Company have W2 Employee in this Quarter? </th>
                      <th>Files </th>
                      <th class="hideshow_onupload" >Status</th>
                      <th class="hideshow_onupload" >Comments</th>
                  </tr>
              </thead>
              <tbody>
                          <?php foreach ($payroll_healthcare_docs as $key => $value) {
                          $doc_type_id = $value->doc_type_id;
                          $doc_key =  $value->doc_key;
                          $doc_label = $value->doc_label;
                ?>
                     <tr>
                
                     <?php 

                $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                          
                $file_url = $result[0]->uploded_documents;
                $file_name = $result[0]->local_path_filename;
                $doc_id = $result[0]->id ;
                $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                $doc_required_mapping_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_required_mapping WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                $doc_required = empty($doc_required_mapping_data) ? '0' : $doc_required_mapping_data[0]->required_flag;
              ?> 
                     <?php 
                 
                      ?>
                      <td class="first_column">
                      <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                      </td>
                      <td>
                      <input type="radio" id="" name="payroll_doc_section_required_<?php echo $doc_type_id ;?>" class="doc_section_required" data-index_key = "<?php echo $key.'yes' ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" value="Yes" <?php echo (isset($doc_required) && $doc_required === '0') ? 'checked' : ''; ?>  data-doc_id = "<?php echo $doc_id ?>" data-section="section3">
                        <label for="html">Yes</label>
                        <input type="radio" id="" name="payroll_doc_section_required_<?php echo $doc_type_id ;?>" class="doc_section_required" data-index_key = "<?php echo $key.'no' ?>" data-doc_type_id="<?php echo $doc_type_id ;?>"  value="No" <?php echo (isset($doc_required) && $doc_required === '1') ? 'checked' : ''; ?>  data-doc_id = "<?php echo $doc_id ?>" data-section="section3">
                        <label for="css">No</label>
                      </td>
                      <td>
                      <?php if($file_url == ''){
                         
                       ?>
                          <label class="custom-file-upload" style= "<?php if($doc_required == '1') {  echo 'filter: grayscale(100%); pointer-events: none;' ;}?>">
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                          </label>
                          <label class="file_remove_label" style="display:none;">
           <span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                          </label>
                      <?php }else{ 
                         $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                         $file_status = $status_data[0]->doc_status;
                        ?>
                          <label class="custom-file-upload" style="display:none;">
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                          </label>
                          <label class="file_remove_label" >
           <span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="color: initial; display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                          </label>
                          <?php } ?>
                      </td>
                      <td  class="hideshow_onupload" >
                       <span class="progress_status" style="display: <?php echo isset($file_url) && $file_status === 'In review' ? 'block' : 'none'; ?>">In review</span>
                      <span class="approved_status" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'block' : 'none'; ?>">Approved</span>
                      <span class="rejected_status" style="display: <?php echo isset($file_url) && $file_status === 'Rejected' ? 'block' : 'none'; ?>">Rejected</span>
                      <span class="not_uploaded" style="display: <?php echo empty($file_url) && $doc_required == '0' ? 'block' : 'none'; ?>">Yet to upload</span>
                      <span class="not_required" style="display: <?php  echo $doc_required == '1' ? 'block' : 'none'; ?>">Not Applicable</span>                      </td>
                      <?php if( !empty($comment_data)) { ?>    
                      <td>
                        <div style="display: flex;">  
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                          <i class="fa-regular fa-comment"></i>
                          </span>
                          <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
                          <i class="fa-solid fa-comment-medical"></i>
                          </span>
                        </div> 
                      </td>
                      <?php } ?>
                  </tr> 
<?php } ?>
              </tbody>
                        </table>
                    </div>
                </div>



                <h5 class="mt-3 mb-3 title_text"><b>Payroll Additional Documents</b></h5>
                        <div class="row">
              <div class="col-md-12 table-responsive">
                        <table class="table com_doc_display">
              <thead>
                  <tr>
                      <th style="text-align: left;">Documents</th>
                      <th>Files </th>
                      <th class="hideshow_onupload" >Status</th>
                      <th class="hideshow_onupload" >Comments</th>
                  </tr>
              </thead>
              <tbody>
                          <?php foreach ($payroll_additional_docs as $key => $value) {
                          $doc_type_id = $value->doc_type_id;
                          $doc_key =  $value->doc_key;
                          $doc_label = $value->doc_label;
                ?>
                     <tr>
                
                     <?php 

                $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                          
                $file_url = $result[0]->uploded_documents;
                $file_name = $result[0]->local_path_filename;
                $doc_id = $result[0]->id ;
                $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
               ?> 
                     <?php 
                 
                      ?>
                      <td class="first_column">
                      <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                      </td>
                      <td>
                      <?php if($file_url == ''){
                         
                       ?>
                          <label class="custom-file-upload" >
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                          </label>
                          <label class="file_remove_label" style="display:none;">
           <span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                          </label>
                      <?php }else{ 
                         $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                         $file_status = $status_data[0]->doc_status;
                        ?>
                          <label class="custom-file-upload" style="display:none;">
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                          </label>
                          <label class="file_remove_label" >
           <span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="color: initial; display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                          </label>
                          <?php } ?>
                      </td>
                      <td  class="hideshow_onupload" >
                       <span class="progress_status" style="display: <?php echo isset($file_url) && $file_status === 'In review' ? 'block' : 'none'; ?>">In review</span>
                      <span class="approved_status" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'block' : 'none'; ?>">Approved</span>
                      <span class="rejected_status" style="display: <?php echo isset($file_url) && $file_status === 'Rejected' ? 'block' : 'none'; ?>">Rejected</span>
                      <span class="not_uploaded" style="display: <?php echo empty($file_url) ? 'block' : 'none'; ?>">Yet to upload</span> 
                      </td>
                      <?php if( !empty($comment_data)) { ?>    
                      <td>
                        <div style="display: flex;"> 
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                          <i class="fa-regular fa-comment"></i>
                          </span>
                          <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
                          <i class="fa-solid fa-comment-medical"></i>
                          </span>
                        </div> 
                      </td>
                      <?php } ?>
                  </tr> 
<?php } ?>
              </tbody>
                        </table>
                    </div>
                </div>


                
<?php } else {?>
                  
                <h5 class="mt-3 mb-3 title_text"><b>
                        <?php
                        if($is_last_agrrement) {
                            echo "Payroll Tax Return 941’s for quarters- 2021: Q1-Q3";
                        }else{
                            echo "Quarterly Payroll Tax Return 941’s for Eligible Quarters – 2020: Q1-Q4 & 2021: Q1-Q3";
                        }
                        ?>
                        </b></h5>
                        <div class="row">
              <div class="col-md-12 table-responsive">  
                        <!-- For OPS user table Start -->
                        <table class="table com_doc_display">
              <thead>
                  <tr>
                  <th style="text-align: left;">Quarters </th>
                  
                      <th>Files </th>
                      <th>Status</th>
                      <th>Comments</th>
                  </tr>
              </thead>
              <tbody>
              <?php foreach ($quat_payroll_docs as $key => $value) {
                $doc_type_id = $value->doc_type_id;
                $doc_key =  $value->doc_key;
                $doc_label = $value->doc_label;

    ?>
                  <tr>
                
                  <?php 
                
                   $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                  $file_url = $result[0]->uploded_documents;
                   $file_name = $result[0]->local_path_filename;
                   $doc_id = $result[0]->id ;
                   $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                   $doc_required_mapping_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_required_mapping WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                   $doc_required = empty($doc_required_mapping_data) ? '0' : $doc_required_mapping_data[0]->required_flag;
                  ?> 
                      <td class="first_column">
                      <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                      </td>
                      <?php if($file_url != ''){
                          $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                          $file_status = $status_data[0]->doc_status;
                       ?>
                      <td style="text-align: center;">
                     
                      <label class="file_remove_label" >
                      <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && get_current_user_id() == '$test' && $file_status === 'Rejected' ? 'block' : 'none'; ?>">cancel</span>  </span>
                          </label>
                      </td>
                      <td style="text-align: center;">
                         <select name="change_document_status" id="change_erc_document_status" class="change_doc" <?php echo isset($file_url) &&  $file_status != 'In review' && get_current_user_id() != '$test' ? 'disabled' : ''; ?> >
           <option value="">- Select Status -</option>
           <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>">In review</option>
           <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
           <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>"  data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                          </select>
                      </td>
                      <td style="text-align: center;">
                      <?php if( !empty($comment_data)) { ?>     
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>
                      <?php } ?>  
                      <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
                      <i class="fa-solid fa-comment-medical"></i>
                        </span>
                      </td>
                      <?php }else {?>
                        
                        <td>
                      <?php if($file_url == ''&& get_current_user_id() == '$test'){
                         
                       ?>
                          <label class="custom-file-upload"  style= "<?php if($doc_required == '1') {  echo 'filter: grayscale(100%); pointer-events: none;' ;}?>">
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                          </label>
                          <label class="file_remove_label" style="display:none;">
           <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                          </label>
                      <?php } ?>
                      </td>
                          <td style="text-align: center;" style= "<?php if($doc_required == '1') {  echo 'filter: grayscale(100%); pointer-events: none;' ;}?>">
                          <span class="not_uploaded" style="display: <?php echo  $doc_required == '0' ? 'block' : 'none'; ?>">Yet to upload</span>
                          <span class="not_required" style="display: <?php  echo $doc_required == '1' ? 'block' : 'none'; ?>">Not Applicable</span> </td>
                          <td style="text-align: center;"><?php if( !empty($comment_data)) { ?>     
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>
                      <?php } ?></td>
                   <?php   } ?>
                  </tr>
                        <?php } ?>
              </tbody>
                        </table>
                         </div>
                      </div>
                   
                <h5 class="mt-3 mb-3 title_text"><b>
                        <?php
                        if($is_last_agrrement) {
                            echo "Payroll Registers for quarters- 2021: Q1-Q3";
                        }else{
                            echo "Payroll Registers for all Eligible Quarters – 2020: Q1-Q4 & 2021: Q1-Q3";
                        }
                        ?>
                        </b></h5>
                        <div class="row">
              <div class="col-md-12 table-responsive">  
                        <!-- For OPS user table Start -->
                        <table class="table com_doc_display">
              <thead>
                  <tr>
                  <th style="text-align: left;">Quarters</th>
                  
                      <th>Files </th>
                      <th>Status</th>
                      <th>Comments</th>
                  </tr>
              </thead>
              <tbody>
              <?php foreach ($payroll_register_docs as $key => $value) {
                $doc_type_id = $value->doc_type_id;
                $doc_key =  $value->doc_key;
                $doc_label = $value->doc_label;

    ?>
                  <tr>
                
                  <?php 
                
                   $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                  $file_url = $result[0]->uploded_documents;
                   $file_name = $result[0]->local_path_filename;
                   $doc_id = $result[0]->id ;
                   $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                   $doc_required_mapping_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_required_mapping WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                   $doc_required = empty($doc_required_mapping_data) ? '0' : $doc_required_mapping_data[0]->required_flag;
                  ?> 
                      <td class="first_column">
                      <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                      </td>
                      <?php if($file_url != ''){
                          $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                          $file_status = $status_data[0]->doc_status;
                       ?>
                      <td style="text-align: center;">
                     
                      <label class="file_remove_label" >
                      <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && get_current_user_id() == '$test' && $file_status === 'Rejected' ? 'block' : 'none'; ?>">cancel</span>  </span>
                          </label>
                      </td>
                      <td style="text-align: center;">
                         <select name="change_document_status" id="change_erc_document_status" class="change_doc" <?php echo isset($file_url) &&  $file_status != 'In review' && get_current_user_id() != '$test' ? 'disabled' : ''; ?> >
           <option value="">- Select Status -</option>
           <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>">In review</option>
           <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
           <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>"  data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                          </select>
                      </td>
                      <td style="text-align: center;">
                      <?php if( !empty($comment_data)) { ?>     
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>
                      <?php } ?>     
                      <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
                      <i class="fa-solid fa-comment-medical"></i>
                        </span>
                      </td>
                      <?php }else {?>
                        <td>
                      <?php if($file_url == '' && get_current_user_id() == '$test'){
                         
                       ?>
                          <label class="custom-file-upload" style= "<?php if($doc_required == '1') {  echo 'filter: grayscale(100%); pointer-events: none;' ;}?>">
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                          </label>
                          <label class="file_remove_label" style="display:none;">
           <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                          </label>
                      <?php } ?>
                      </td>
                          <td style="text-align: center;"><span class="not_uploaded" style="display: <?php echo  $doc_required == '0' ? 'block' : 'none'; ?>">Yet to upload</span>
                      <span class="not_required" style="display: <?php  echo $doc_required == '1' ? 'block' : 'none'; ?>">Not Applicable</span> </td>
                          <td style="text-align: center;"><?php if( !empty($comment_data)) { ?>     
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>
                      <?php } ?></td>
                   <?php   } ?>
                  </tr>
                        <?php } ?>
              </tbody>
                        </table>
                         </div>
                      </div>
                   
                <h5 class="mt-3 mb-3 title_text"><b>
                        <?php
                        if($is_last_agrrement) {
                            echo "Health Care Expenses per employee per payroll for 2021: Q1-Q3 (if applicable)";
                        }else{
                            echo "Total Health Care Expenses per Employee per Payroll for 2020: Q1-Q4 & 2021: Q1-Q3";
                        }
                        ?>
                        </b></h5>
                        <div class="row">
              <div class="col-md-12 table-responsive">  
                        <!-- For OPS user table Start -->
                        <table class="table com_doc_display">
              <thead>
                  <tr>
                  <th style="text-align: left;">Quarters </th>
                  
                      <th>Files </th>
                      <th>Status</th>
                      <th>Comments</th>
                  </tr>
              </thead>
              <tbody>
              <?php foreach ($payroll_healthcare_docs as $key => $value) {
                $doc_type_id = $value->doc_type_id;
                $doc_key =  $value->doc_key;
                $doc_label = $value->doc_label;
              
    ?>
                  <tr>
                
                  <?php 
                
                   $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                   $file_url = $result[0]->uploded_documents;
                   $file_name = $result[0]->local_path_filename;
                   $doc_id = $result[0]->id ;
                   $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                   $doc_required_mapping_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_required_mapping WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                   $doc_required = empty($doc_required_mapping_data) ? '0' : $doc_required_mapping_data[0]->required_flag;
                  ?> 
                      <td class="first_column">
                      <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                      </td>
                      <?php if($file_url != ''){
                          $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                           
                          $file_status = $status_data[0]->doc_status;
                       ?>
                      <td style="text-align: center;">
                     
                      <label class="file_remove_label" >
                      <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && get_current_user_id() == '$test' && $file_status === 'Rejected' ? 'block' : 'none'; ?>">cancel</span>  </span>
                          </label>
                      </td>
                      <td style="text-align: center;">
                         <select name="change_document_status" id="change_erc_document_status" class="change_doc" <?php echo isset($file_url) &&  $file_status != 'In review' && get_current_user_id() != '$test' ? 'disabled' : ''; ?> >
           <option value="">- Select Status -</option>
           <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>">In review</option>
           <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
           <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>"  data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                          </select>
                      </td>
                      <td style="text-align: center;">
                      <?php if( !empty($comment_data)) { ?>     
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>
                      <?php } ?>      
                      <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
                      <i class="fa-solid fa-comment-medical"></i>
                        </span> 
                      </td>
                      <?php }else {?>
                        <td>
                      <?php if($file_url == '' && get_current_user_id() == '$test'){
                         
                       ?>
                          <label class="custom-file-upload" style= "<?php if($doc_required == '1') {  echo 'filter: grayscale(100%); pointer-events: none;' ;}?>">
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                          </label>
                          <label class="file_remove_label" style="display:none;">
           <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                          </label>
                      <?php } ?>
                      </td>
                          <td style="text-align: center;"><span class="not_uploaded" style="display: <?php echo  $doc_required == '0' ? 'block' : 'none'; ?>">Yet to upload</span>
                      <span class="not_required" style="display: <?php  echo $doc_required == '1' ? 'block' : 'none'; ?>">Not Applicable</span> </td>
                          <td style="text-align: center;"><?php if( !empty($comment_data)) { ?>     
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>
                      <?php } ?></td>
                   <?php   } ?>
                  </tr>
                        <?php } ?>
              </tbody>
                        </table>
                         </div>
                      </div>

                      <h5 class="mt-3 mb-3 title_text"><b>Payroll Additional Documents</b></h5>
                        <div class="row">
              <div class="col-md-12 table-responsive">  
                        <!-- For OPS user table Start -->
                        <table class="table com_doc_display">
              <thead>
                  <tr>
                  <th style="text-align: left;">Documents</th>
                  
                      <th>Files</th>
                      <th>Status</th>
                      <th>Comments</th>
                  </tr>
              </thead>
              <tbody>
              <?php foreach ($payroll_additional_docs as $key => $value) {
                $doc_type_id = $value->doc_type_id;
                $doc_key =  $value->doc_key;
                $doc_label = $value->doc_label;

    ?>
                  <tr>
                
                  <?php 
                
                   $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                  $file_url = $result[0]->uploded_documents;
                   $file_name = $result[0]->local_path_filename;
                   $doc_id = $result[0]->id ;
                   $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                  ?> 
                      <td class="first_column">
                      <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                      </td>
                      <?php if($file_url != ''){
                          $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                          $file_status = $status_data[0]->doc_status;
                       ?>
                      <td style="text-align: center;">
                     
                      <label class="file_remove_label" >
                      <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo isset($file_url) && get_current_user_id() == '$test' && $file_status === 'Rejected' ? 'block' : 'none'; ?>">cancel</span>  </span>
                          </label>
                      </td>
                      <td style="text-align: center;">
                         <select name="change_document_status" id="change_erc_document_status" class="change_doc"  <?php echo isset($file_url) &&  $file_status != 'In review'&& get_current_user_id() != '$test' ? 'disabled' : ''; ?> >
           <option value="">- Select Status -</option>
           <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>">In review</option>
           <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
           <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>"  data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                          </select>
                      </td>
                      <td style="text-align: center;">
                      <?php if( !empty($comment_data)) { ?>     
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>
                      <?php } ?>      
                      <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
                      <i class="fa-solid fa-comment-medical"></i>
                        </span>
                      </td>
                      <?php }else {?>
                        <td>
                      <?php if($file_url == '' && get_current_user_id() == '$test' ){
                         
                       ?>
                          <label class="custom-file-upload" >
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                          </label>
                          <label class="file_remove_label" style="display:none;">
           <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                          </label>
                      <?php } ?>
                      </td>
                          <td style="text-align: center;">
                          <span class="not_uploaded" >Yet to upload</span></td>
                          <td style="text-align: center;"><?php if( !empty($comment_data)) { ?>     
                          <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>
                      <?php } ?></td>
                   <?php   } ?>
                  </tr>
                        <?php } ?>
              </tbody>
                        </table>
                         </div>
                      </div>
                   
                        <!-- For OPS user table END -->

               <?php   }?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>          


                      </div>    
                    </div>
                    <div class="tab-pane active erc-project-scroll" id="erc-docs" role="tabpanel" aria-labelledby="erc-docs">
                      <div class="white_card_body erc-docs">
                          <!-- For OPS user table Start -->
                          <h5 class="mt-3 mb-3 title_text"><b>ERC Documents</b></h5>
                          <div class="main_content_iner">
    <div class="container-fluid p-0">
        <div class="row justify-content-center mb-4">
            <div class="col-lg-12">
                <div class="white_card">
                    <div class="white_card_body">
                        <?php
                        $product_found = 1;
                        if($product_found)
                        {
                        global $wpdb;
                        $parent_folder = 'Erc Documents';
                        $form_id = 3;
                        if($is_last_agrrement) {
                            $company_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `doc_key` IN ('ppp_2021','quarterly_gross_receipt_csv','erc_additional_field1','erc_additional_field2') AND `form_id` = '" . $form_id . "' "));
                        }
                        else{
                            $company_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `form_id` = %d", $form_id));
                        }
                        $test = doc_master_User;
                        ?>
                        <?php
                        $userdata = get_user_by('id', get_current_user_id());
                        $user_roles = $userdata->roles;
                        if (in_array("lead", $user_roles) || in_array("lead_associate", $user_roles)) {
              ?>
              <div class="table-responsive">
                  <table class="table  com_doc_display">
                      <thead>
                      <tr>
                          <th style="text-align: left;">Documents</th>
                          <th>Did your company apply for PPP Loan in 2020 and / or 2021?</th>
                          <th>Files</th>
                          <th class="hideshow_onupload">Status</th>
                          <th class="hideshow_onupload">Comments</th>
                      </tr>
                      </thead>
                      <tbody>
                      <?php
                      $erc_doc_inc = 0;
                      foreach ($company_docs as $key => $value) {
                          $erc_doc_inc++;
                          $doc_type_id = $value->doc_type_id;
                          $doc_key = $value->doc_key;
                          $doc_label = $value->doc_label;
                          if($is_last_agrrement) {
                              if($doc_type_id == 29){
                                  $doc_label = "PPP Forgiveness Form 3508 for 2021 (if applicable)";
                              }
                              if($doc_type_id == 30){
                                  $doc_label = "1. Quarterly GROSS RECEIPTS for each calendar quarter in 2019 & 2021( use template)<br>
2. Name and ownership % of the owner/s of the business";
                              }
                          }
                          ?>
                          <tr>

           <?php

           $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));

           $file_url = $result[0]->uploded_documents;
           $file_name = $result[0]->local_path_filename;

           $doc_id = $result[0]->id;

           $deleted_result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '1' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));

           $deleted_doc_id = $deleted_result[0]->id;

           $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
           $doc_required_mapping_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_required_mapping WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
           $doc_required = empty($doc_required_mapping_data) ? '0' : $doc_required_mapping_data[0]->required_flag;

           ?>
           <?php

           ?>
           <td class="first_column" style="width:31%;">
               <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
           </td>

           <td class="text-center" style="width:25%;">
               <?php
               if ($key == 0 || $key == 1) {
                   ?>
                   <input type="radio" id=""
            name="payroll_doc_section_required_<?php echo $doc_type_id; ?>"
            class="doc_section_required"
            data-index_key="<?php echo $key . 'yes' ?>"
            data-doc_type_id="<?php echo $doc_type_id; ?>"
            value="Yes" <?php echo (isset($doc_required) && $doc_required === '0') ? 'checked' : ''; ?>
            data-doc_id="<?php echo $doc_id ?>" data-section="section4">
                     <label for="html">Yes</label>
                     <input type="radio" id=""
              name="payroll_doc_section_required_<?php echo $doc_type_id; ?>"
              class="doc_section_required"
              data-index_key="<?php echo $key . 'no' ?>"
              data-doc_type_id="<?php echo $doc_type_id; ?>"
              value="No" <?php echo (isset($doc_required) && $doc_required === '1') ? 'checked' : ''; ?>
              data-doc_id="<?php echo $doc_id ?>"
              data-section="section4">
                     <label for="css">No</label>
                   <?php
               }
               ?>
           </td>
           <td>
               <?php if ($file_url == '') {

                   ?>

                   <?php

                   if ($doc_type_id == 30) {
                            $update_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_required_mapping WHERE lead_id = '$lead_id' AND required_flag = 2 AND doc_type_id = 0 ORDER BY `id` DESC limit 1; "));
                            if ($update_data) {
             $sdgr_data = $wpdb->get_row("SELECT sdgr_input_file FROM {$wpdb->prefix}sdgr_calculator WHERE lead_id = '$lead_id' ORDER BY `id` DESC limit 1; ");
             if (!empty($sdgr_data)) {
                 if ($sdgr_data->sdgr_input_file != '') {
                     $sdgr_input_file = str_replace("/opt/bitnami/wordpress/portal/", "", $sdgr_data->sdgr_input_file);
                     $sdgr_input_file_url = get_site_url() . "/" . $sdgr_input_file;
                     ?>
                     <label style="display: block; text-align: center;">
                         <a href="<?php echo $sdgr_input_file_url; ?> "
                            style='background: #55915a 0% 0% no-repeat padding-box;box-shadow: 0px 0px 5px #0000001a;border-radius: 10px;color: #fff;padding: 4px 5px;'>SDGR
                             &amp; Owner's Information</a>
                     </label>
                     <?php
                 }
             } else {
                 if ($doc_key == 'quarterly_gross_receipt_csv') { ?>
                     <label style="display: block; text-align: center;">
                         <a target="_blank" href="<?php echo admin_url("admin.php?page=company_ownership_search&lead_id=$lead_id"); ?>"
                            style='background: #55915a 0% 0% no-repeat padding-box;box-shadow: 0px 0px 5px #0000001a;border-radius: 10px;color: #fff;padding: 4px 5px;'>SDGR
                             &amp; Owner's Information</a>
                     </label>
                 <?php }
             }
                            } else {
             if ($doc_key == 'quarterly_gross_receipt_csv') { ?>
                 <label style="display: block; text-align: center;">
                     <a target="_blank" href="<?php echo admin_url("admin.php?page=company_ownership_search&lead_id=$lead_id"); ?>"
                        style='background: #55915a 0% 0% no-repeat padding-box;box-shadow: 0px 0px 5px #0000001a;border-radius: 10px;color: #fff;padding: 4px 5px;'>SDGR
                         &amp; Owner's Information</a>
                 </label>
             <?php }
                            }
                   } else {
                            if ($doc_key == 'quarterly_gross_receipt_csv') { ?>
             <label style="display: block; text-align: center;">
                 <a target="_blank" href="<?php echo admin_url("admin.php?page=company_ownership_search&lead_id=$lead_id"); ?>"
                    style='background: #55915a 0% 0% no-repeat padding-box;box-shadow: 0px 0px 5px #0000001a;border-radius: 10px;color: #fff;padding: 4px 5px;'>SDGR
                     &amp; Owner's Information</a>
             </label>
                            <?php }
                   }
                   if ($doc_key != 'quarterly_gross_receipt_csv') { ?>
                            <label class="custom-file-upload"
                style="<?php if ($doc_required == '1') {
                    echo 'filter: grayscale(100%); pointer-events: none;';
                } ?>">
             <span class="material-symbols-outlined">upload_file</span>
             <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?>
                    name="<?php echo $doc_key; ?>"
                    data-parent_folder="<?php echo $parent_folder; ?>"
                    data-doc_type_id="<?php echo $doc_type_id; ?>"
                    data-file_type="<?php if ($doc_key == 'quarterly_gross_receipt_csv') {
                        echo 'excel';
                    } ?>">Upload
                            </label>
                   <?php } ?>
                   <label class="file_remove_label 4" style="display:none;">
                            <span class="material-symbols-outlined"
               style="color: initial;  ">download_done</span> <a
                 class="text_overflow_ellipse" href=""
                 target="_blank"><span class="custom_file">File1.pdf</a>
                            &nbsp;<span class="material-symbols-outlined cancel_icon"
                     style="color: initial;">cancel</span></span>
                   </label>
                   <?php

                   $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$deleted_doc_id' ORDER BY `id` DESC limit 1; "));
                   $file_status = $status_data[0]->doc_status;
               } else {
                   $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                   $file_status = $status_data[0]->doc_status;

                   ?>
                   <label class="custom-file-upload" style="display:none;">
                            <span class="material-symbols-outlined">upload_file</span>
                            <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?>
                name="<?php echo $doc_key; ?>"
                data-parent_folder="<?php echo $parent_folder; ?>"
                data-doc_type_id="<?php echo $doc_type_id; ?>">Upload
                   </label>
                   <label class="file_remove_label 1">
                            <span class="material-symbols-outlined"
               style="color: initial;  ">download_done</span> <a
                 class="text_overflow_ellipse"
                 href="<?php echo $file_url; ?>" target="_blank"><span
                     class="custom_file"><?php echo $file_name; ?></a>
                            &nbsp;<span class="material-symbols-outlined cancel_icon"
                     data-doc_id="<?php echo $doc_id; ?>"
                     data-folder_name="<?php echo $parent_folder; ?>"
                     data-lead_id="<?php echo $lead_id; ?>"
                     data-doc_key="<?php echo $doc_key; ?>"
                     data-file_name="<?php echo $file_url; ?>"
                     data-doc_type_id="<?php echo $doc_type_id; ?>"
                     style="color: initial; display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                   </label>
               <?php } ?>
           </td>
           <td class="hideshow_onupload">
               <?php if ($doc_type_id == 30 && $file_status != 'Rejected') {
                   $update_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_required_mapping WHERE lead_id = '$lead_id' AND required_flag = 2 AND doc_type_id = 0 ORDER BY `id` DESC limit 1; "));
                   if ($update_data) {
                            echo '<span class="approved_status" style="display: block">Details Updated</span>';
                            $detail_updated = true;
                   }
               }
               if (!isset($detail_updated) || ($doc_type_id != 30)) {
                   ?>
                   <span class="progress_status"
           style="display: <?php echo isset($file_url) && $file_status === 'In review' ? 'block' : 'none'; ?>">In review</span>
                   <span class="approved_status"
           style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'block' : 'none'; ?>">Approved</span>


                   <span class="rejected_status"
           style="display: <?php echo $file_status === 'Rejected' ? 'block' : 'none'; ?>">Rejected</span>
                   <?php if ($file_status != 'Rejected') { ?>
                            <span class="not_uploaded"
               style="display: <?php echo empty($file_url) && $doc_required == '0' ? 'block' : 'none'; ?>">Yet to upload</span>
                            <span class="not_required"
               style="display: <?php echo $doc_required == '1' ? 'block' : 'none'; ?>">Not Applicable</span>
                   <?php }
               } ?>
           </td>
           <?php if (!empty($comment_data)) { ?>
               <td>
                   <div style="display: flex;">
                          <span class="view_erc_comment" data-toggle="modal"
             data-target="#exampleModal_ercdoc_view"
             data-doc_type_id="<?php echo $doc_type_id; ?>" title="View">
                          <i class="fa-regular fa-comment"></i>
                          </span>
                            <span class="add_erc_comment_btn" data-toggle="modal"
               data-target="#exampleModal_otherdoc_adding_comment"
               data-doc_id="<?php echo $doc_id; ?>"
               data-doc_type_id="<?php echo $doc_type_id; ?>"
               title="Add">
                          <i class="fa-solid fa-comment-medical"></i>
                          </span>
                   </div>
               </td>
           <?php } ?>
                          </tr>
                      <?php } ?>
                      </tbody>
                  </table>
              </div>


                        <?php } else { ?>

              <!-- For OPS user table Start -->
              <div class="table-responsive">
                  <table class="table com_doc_display">
                      <thead>
                      <tr>
                          <th style="text-align: left;">Documents</th>
                          <th style="text-align: center;">Files</th>
                          <th style="text-align: center;">Status</th>
                          <th style="text-align: center;">Comments</th>
                      </tr>
                      </thead>
                      <tbody>
                      <?php foreach ($company_docs as $key => $value) {
                          $doc_type_id = $value->doc_type_id;
                          $doc_key = $value->doc_key;
                          $doc_label = $value->doc_label;
                          if($is_last_agrrement) {
                              if($doc_type_id == 29){
                                  $doc_label = "PPP Forgiveness Form 3508 for 2021 (if applicable)";
                              }
                              if($doc_type_id == 30){
                                  $doc_label = "1. Quarterly GROSS RECEIPTS for each calendar quarter in 2019 & 2021( use template)<br>
2. Name and ownership % of the owner/s of the business";
                              }
                          }
                          ?>
                          <tr>
           <?php
           $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
           $file_url = $result[0]->uploded_documents;

           $file_name = $result[0]->local_path_filename;
           $sdgr_output_file = '';
           if (in_array('echeck_staff', $user_roles) || in_array('echeck_admin', $user_roles) || in_array('master_ops', $user_roles)) {
               $file_url = '';
               $sdgr_output = $wpdb->get_row("SELECT sdgr_output_file FROM $sdgr_calculator_table WHERE lead_id = '$lead_id'");
               if (!empty($sdgr_output)) {
                   $sdgr_output_file = str_replace('/opt/bitnami/wordpress/portal', get_site_url(), $sdgr_output->sdgr_output_file);
               }
           }
           $file_name = $result[0]->local_path_filename;
           $doc_id = $result[0]->id;
           $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
           $doc_required_mapping_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_required_mapping WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
           $doc_required = empty($doc_required_mapping_data) ? '0' : $doc_required_mapping_data[0]->required_flag;

           ?>
           <td class="first_column">
               <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
           </td>
           <?php if ($file_url != '' || $sdgr_output_file != '') {
               if (in_array('echeck_staff', $user_roles) || in_array('echeck_admin', $user_roles) || in_array('master_ops', $user_roles)) {
                   $file_url_link = $sdgr_output_file;
                   $files_name = 'SDGR & Owner Info';
               } else {
                   $file_url_link = $file_url;
                   $files_name = $file_name;
               }
               $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
               $file_status = $status_data[0]->doc_status;
               ?>
               <td style="text-align: center;">

                   <!-- <label class="file_remove_label 2" > -->
                   <?php if (in_array('echeck_staff', $user_roles) || in_array('echeck_admin', $user_roles) || in_array('master_ops', $user_roles)) {
                            $sdgr_form_link = admin_url("admin.php?page=company_ownership_search&lead_id=$lead_id");
                            if ($file_name != '' && $doc_key == 'quarterly_gross_receipt_csv') {
             ?>
             <a target="_blank" href='javascript:void(0)'
                style='background: #55915a 0% 0% no-repeat padding-box;box-shadow: 0px 0px 5px #0000001a;border-radius: 10px;color: #fff;padding: 4px 5px;'
                class='edit_download_sdgr btn' title='Edit/Dowload SDGR'
                data-file-link="<?php echo $file_url_link; ?>"
                data-edit-sdgr="<?php echo $sdgr_form_link; ?>"><?php echo $files_name; ?></a>
                            <?php } else {
             if ($doc_key == 'quarterly_gross_receipt_csv') {
                 ?>
                 <a class=""
                    href="<?php echo admin_url("admin.php?page=company_ownership_search&lead_id=$lead_id"); ?>"
                    target="_blank"
                    style='background: #55915a 0% 0% no-repeat padding-box;box-shadow: 0px 0px 5px #0000001a;border-radius: 10px;color: #fff;padding: 4px 5px;'><span
                             class="custom_file">SDGR & Owner Info</a>
                 <?php
             }
                            }
                   } else { ?>
                            <a class="text_overflow_ellipse"
            href="<?php echo $file_url_link; ?>" target="_blank"><span
                     class="custom_file"><?php echo $file_name; ?></a>
                   <?php } ?>
                   <span class="material-symbols-outlined">download_done</span> <a
             class="text_overflow_ellipse" style="display: block;"
             href="<?php echo $file_url; ?>" target="_blank"
             style='background: #55915a 0% 0% no-repeat padding-box;box-shadow: 0px 0px 5px #0000001a;border-radius: 10px;color: #fff;padding: 4px 5px;'><span
                 class="custom_file">SDGR & Owner Info</a> &nbsp;<span
             class="material-symbols-outlined cancel_icon"
             data-doc_id="<?php echo $doc_id; ?>"
             data-folder_name="<?php echo $parent_folder; ?>"
             data-lead_id="<?php echo $lead_id; ?>"
             data-doc_key="<?php echo $doc_key; ?>"
             data-file_name="<?php echo $file_url; ?>"
             data-doc_type_id="<?php echo $doc_type_id; ?>"
             style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
               </td>
               <?php if (in_array('echeck_staff', $user_roles) || in_array('echeck_admin', $user_roles) || in_array('master_ops', $user_roles)) {
                   $sel_disabled = '';
               } else {
                   $sel_disabled = 'disabled';
               }
               ?>
               <!--Make the drop-down active for User ID 44084 for all the sections and all the documents-->
               <td style="text-align: center;">
                   <select name="change_document_status" id="change_erc_document_status"
             class="change_doc" <?php echo isset($file_url) && $file_status != 'In review' && get_current_user_id() != $test ? $sel_disabled : ''; ?> >
                            <option value="">- Select Status -</option>
                            <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?>
                 data-doc_id="<?php echo $doc_id; ?>"
                 data-doc_type_id="<?php echo $doc_type_id; ?>">In review
                            </option>
                            <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?>
                 data-doc_id="<?php echo $doc_id; ?>"
                 data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
                            <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?>
                 data-doc_id="<?php echo $doc_id; ?>"
                 data-doc_type_id="<?php echo $doc_type_id; ?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                   </select>
               </td>
               <td style="text-align: center;">
                   <?php if (!empty($comment_data)) { ?>
                            <span class="view_erc_comment" data-toggle="modal"
               data-target="#exampleModal_ercdoc_view"
               data-doc_type_id="<?php echo $doc_type_id; ?>"
               title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>
                   <?php } ?>
                   <span class="add_erc_comment_btn" data-toggle="modal"
           data-target="#exampleModal_otherdoc_adding_comment"
           data-doc_id="<?php echo $doc_id; ?>"
           data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
                      <i class="fa-solid fa-comment-medical"></i>
                        </span>
               </td>
           <?php } else { ?>
               <td>
                   <?php if ($file_url == '' && get_current_user_id() == $test) {

                            ?>

                            <?php
                            if ($doc_key == 'quarterly_gross_receipt_csv') { ?>
             <label style="display: block; text-align: center;">
                 <a target="_blank" href="<?php echo admin_url("admin.php?page=company_ownership_search&lead_id=$lead_id"); ?>"
                    style='background: #55915a 0% 0% no-repeat padding-box;box-shadow: 0px 0px 5px #0000001a;border-radius: 10px;color: #fff;padding: 4px 5px;'>SDGR
                     &amp; Owner's Information</a>
             </label>
                            <?php }
                            if ($doc_key != 'quarterly_gross_receipt_csv') { ?>
             <label class="custom-file-upload"
                    style="<?php if ($doc_required == '1') {
                        echo 'filter: grayscale(100%); pointer-events: none;';
                    } ?>">
                 <span class="material-symbols-outlined">upload_file</span>
                 <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?>
                        name="<?php echo $doc_key; ?>"
                        data-parent_folder="<?php echo $parent_folder; ?>"
                        data-doc_type_id="<?php echo $doc_type_id; ?>"
                        data-file_type="<?php if ($doc_key == 'quarterly_gross_receipt_csv') {
                            echo 'excel';
                        } ?>">Upload
             </label>
                            <?php } ?>
                            <label class="file_remove_label 3" style="display:none;">
             <span class="material-symbols-outlined"
                   style="color: initial;  ">download_done</span> <a
                     class="text_overflow_ellipse" href=""
                     target="_blank"><span class="custom_file">File1.pdf</a>
             &nbsp;<span class="material-symbols-outlined cancel_icon"
                         style="color: initial;">cancel</span></span>
                            </label>
                   <?php } ?>
               </td>
               <td style="text-align: center;">
                   <?php if ($doc_key == 'quarterly_gross_receipt_csv') { ?>
                            <a target="_blank" href="<?php echo admin_url("admin.php?page=company_ownership_search&lead_id=$lead_id"); ?>"
            style='background: #55915a 0% 0% no-repeat padding-box;box-shadow: 0px 0px 5px #0000001a;border-radius: 10px;color: #fff;padding: 4px 5px;'>SDGR
             &amp; Owner's Information</a>
                   <?php } else { ?>
                            <span class="not_uploaded"
               style="display: <?php echo $doc_required == '0' ? 'block' : 'none'; ?>">Yet to upload</span>
                   <?php } ?>
                   <span class="not_required"
           style="display: <?php echo $doc_required == '1' ? 'block' : 'none'; ?>">Not Applicable</span>
               </td>
               <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                            <span class="view_erc_comment" data-toggle="modal"
               data-target="#exampleModal_ercdoc_view"
               data-doc_type_id="<?php echo $doc_type_id; ?>"
               title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>
                   <?php } ?> </td>
               <!-- <td style="text-align: center;"><span class="not_uploaded" style="display: <?php echo $doc_required == '0' ? 'block' : 'none'; ?>">Yet to upload</span>
                      <span class="not_required" style="display: <?php echo $doc_required == '1' ? 'block' : 'none'; ?>">Not Applicable</span></td>
                          <td style="text-align: center;"><?php if (!empty($comment_data)) { ?>
                            <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id; ?>" title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>
                      <?php } ?> </td> -->
           <?php } ?>
                          </tr>
                      <?php } ?>
                      </tbody>
                  </table>
              </div>
              <!-- For OPS user table END -->

                        <?php } ?>
                        <?php }else{ ?>
                        <div class="row align-items-center mb-4 search-impact-days">
              <p style="text-align: center;"><?php echo ERC_MESSAGE; ?></p>
                        </div>
                        <?php } ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

                      </div>    
                    </div>
                    <div class="tab-pane erc-project-scroll" id="other-docs" role="tabpanel" aria-labelledby="other-docs">
                      <div class="white_card_body other-docs">
                          <!-- For OPS user table Start -->
                          <h5 class="mt-3 mb-3 title_text"><b>Other Documents</b></h5>
                          <div class="main_content_iner">
    <div class="container-fluid p-0">
        <div class="row justify-content-center mb-4">
            <div class="col-lg-12">
                <div class="white_card">
                    <?php
                        global $wpdb;
                        $parent_folder = 'Other Documents';
                        $form_id = 4;
                if($is_last_agrrement) {
                    $company_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `doc_key` IN ('business_tax_returns_2019','business_tax_returns_2021','other_additional_field1','other_additional_field2') AND `form_id` = '" . $form_id . "' "));
                }else{
                    $company_docs = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_mapping_table where `form_id` = %d",$form_id));
                }
              $test = doc_master_User;
                    ?>
                    <div class="white_card_body">
                          <?php 
          $userdata = get_user_by( 'id', get_current_user_id() );
          $user_roles=$userdata->roles;
        if (in_array("lead", $user_roles ) || in_array("lead_associate", $user_roles)) {?> 
         <div class="table-responsive">
                        <table class="table com_doc_display">
              <thead>
                  <tr>
                      <th style="text-align: left;">Documents</th>
                      <th>Files </th>
                      <th class="hideshow_onupload" >Status</th>
                      <th class="hideshow_onupload" >Comments</th>
                  </tr>
              </thead>
              <tbody>
                          <?php foreach ($company_docs as $key => $value) {
                          $doc_type_id = $value->doc_type_id;
                          $doc_key =  $value->doc_key;
                          $doc_label = $value->doc_label;
                          if($is_last_agrrement) {
                              if($doc_type_id == 33){
                                  $doc_label = "2019-Business Tax Returns-Filed Federal Tax Return 1120, 1120-S, or 1065";
                              }
                              if($doc_type_id == 35){
                                  $doc_label = "2021-Business Tax Return-Filed Federal Tax Return 1120, 1120-S, or 1065";
                              }
                          }
                ?>
                     <tr>
                
                     <?php 

                $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                          
                $file_url = $result[0]->uploded_documents;
                $file_name = $result[0]->local_path_filename;
                $doc_id = $result[0]->id ;
                $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
               ?> 
                     <?php 
                 
                      ?>
                      <td class="first_column">
                      <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                      </td>
                      <td>
                      <?php if($file_url == ''){
                         
                       ?>
                          <label class="custom-file-upload" >
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                          </label>
                          <label class="file_remove_label" style="display:none;">
           <span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                          </label>
                      <?php }else{ 
                         $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                         $file_status = $status_data[0]->doc_status;
                        ?>
                          <label class="custom-file-upload" style="display:none;">
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>">Upload
                          </label>
                          <label class="file_remove_label" >
           <span class="material-symbols-outlined" style="color: initial;  ">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="color: initial; display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'none' : 'block'; ?>">cancel</span>  </span>
                          </label>
                          <?php } ?>
                      </td>
                      <td  class="hideshow_onupload" >
                       <span class="progress_status" style="display: <?php echo isset($file_url) && $file_status === 'In review' ? 'block' : 'none'; ?>">In review</span>
                      <span class="approved_status" style="display: <?php echo isset($file_url) && $file_status === 'Approved' ? 'block' : 'none'; ?>">Approved</span>
                      <span class="rejected_status" style="display: <?php echo isset($file_url) && $file_status === 'Rejected' ? 'block' : 'none'; ?>">Rejected</span>
                      <span class="not_uploaded" style="display: <?php echo empty($file_url) ? 'block' : 'none'; ?>">Yet to upload</span> 
                      </td>
                      <?php if( !empty($comment_data)) { ?>    
                        <td>
                          <div style="display: flex;">
                            <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                            <i class="fa-regular fa-comment"></i>
                            </span>
                            <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
                            <i class="fa-solid fa-comment-medical"></i>
                            </span>
                          </div> 
                      </td>
                      <?php } ?>
                  </tr> 
<?php } ?>
              </tbody>
                        </table>
         </div>
<?php } else {?>
                    <!-- For OPS user table Start -->
                        <div class="table-responsive">
                        <table class="table com_doc_display">
              <thead>
                  <tr>
                  <th style="text-align: left;">Documents</th>
                      <th>Files</th>
                      <th>Status</th>
                      <th>Comments</th>
                  </tr>
              </thead>
              <tbody>
              <?php foreach ($company_docs as $key => $value) {
                $doc_type_id = $value->doc_type_id;
                $doc_key =  $value->doc_key;
                $doc_label = $value->doc_label;
                  if($is_last_agrrement) {
                      if($doc_type_id == 33){
                          $doc_label = "2019-Business Tax Returns-Filed Federal Tax Return 1120, 1120-S, or 1065";
                      }
                      if($doc_type_id == 35){
                          $doc_label = "2021-Business Tax Return-Filed Federal Tax Return 1120, 1120-S, or 1065";
                      }
                  }
    ?>
                  <tr>
                
                  <?php 
                
                   $result = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_table  WHERE `lead_id` = '$lead_id' AND `deleted` = '0' AND `doc_type_id` = '$doc_type_id' ORDER BY `id` DESC limit 1;"));
                  $file_url = $result[0]->uploded_documents;
                   $file_name = $result[0]->local_path_filename;
                   $doc_id = $result[0]->id ;
                   $comment_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM  $doc_uplod_notes WHERE doc_type_id = '$doc_type_id' AND lead_id = '$lead_id' ;"));
                  ?> 
                      <td class="first_column">
                      <label for="" class="questions_Q"><?php echo $doc_label; ?></label>
                      </td>
                      <?php if($file_url != ''){
                          $status_data = $wpdb->get_results($wpdb->prepare("SELECT * FROM $doc_uplod_status WHERE doc_id = '$doc_id' ORDER BY `id` DESC limit 1; "));
                          $file_status = $status_data[0]->doc_status;
                       ?>
                      <td style="text-align: center;">
                     
                      <label class="file_remove_label" >
                      <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="<?php echo $file_url;?>" target="_blank"><span class="custom_file"><?php echo $file_name;?></a> &nbsp;<span class="material-symbols-outlined cancel_icon" data-doc_id="<?php echo $doc_id; ?>" data-folder_name="<?php echo $parent_folder; ?>" data-lead_id="<?php echo $lead_id; ?>"  data-doc_key="<?php echo $doc_key; ?>" data-file_name="<?php echo $file_url; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" style="display: <?php echo (isset($file_url) && get_current_user_id() == $test && $file_status === 'Rejected') || (isset($file_url) && get_current_user_id() == $test && $file_status === 'In review') ? 'block' : 'none'; ?>">cancel</span>  </span>
                          </label>
                      </td>
                      <td style="text-align: center;">
                         <select name="change_document_status" id="change_erc_document_status" class="change_doc"  <?php echo isset($file_url) &&  $file_status != 'In review' && get_current_user_id() != $test  ? 'disabled' : ''; ?> >
           <option value="">- Select Status -</option>
           <option value="In review" <?php echo isset($file_url) && $file_status === 'In review' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>">In review</option>
           <option value="Approved" <?php echo isset($file_url) && $file_status === 'Approved' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>" data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Approved' ? 'Approved' : 'Approve'; ?></option>
           <option value="Rejected" <?php echo isset($file_url) && $file_status === 'Rejected' ? 'selected' : ''; ?> data-doc_id = "<?php echo $doc_id ;?>"  data-doc_type_id = "<?php echo $doc_type_id ;?>"><?php echo isset($file_url) && $file_status === 'Rejected' ? 'Rejected' : 'Reject'; ?></option>
                          </select>
                      </td>
                      <td style="text-align: center;">
                      <?php if( !empty($comment_data)) { ?> 
                        <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span>      
                      <?php } ?>     
                      <span class="add_erc_comment_btn" data-toggle="modal" data-target="#exampleModal_otherdoc_adding_comment" data-doc_id="<?php echo $doc_id; ?>" data-doc_type_id="<?php echo $doc_type_id; ?>" title="Add">
                      <i class="fa-solid fa-comment-medical"></i>
                        </span> 
                      </td>
                      <?php }else {?>
                        <td>
                      <?php if($file_url == ''){
                         
                       ?>
                          <label class="custom-file-upload" >
                          <span class="material-symbols-outlined">upload_file</span> <input type="file" class="erc_custom_button" data-lead_id=<?php echo $lead_id;?> name="<?php echo $doc_key; ?>" data-parent_folder="<?php echo $parent_folder; ?>" data-doc_type_id="<?php echo $doc_type_id ;?>" data-file_type="<?php  if ($doc_key == 'ein') { echo 'word';  } ?>">Upload
                          </label>
                          <label class="file_remove_label" style="display:none;">
           <span class="material-symbols-outlined">download_done</span> <a class="text_overflow_ellipse" href="" target="_blank"><span class="custom_file">File1.pdf</a> &nbsp;<span class="material-symbols-outlined cancel_icon" style="color: initial;">cancel</span></span>
                          </label>
                      <?php } ?>
                      </td>
                          <td style="text-align: center;"><span class="not_uploaded" >Yet to upload</span></td>
                          <td style="text-align: center;"><?php if( !empty($comment_data)) { ?>     
                            <span class="view_erc_comment" data-toggle="modal" data-target="#exampleModal_ercdoc_view" data-doc_type_id="<?php echo $doc_type_id ; ?>" title="View">
                        <i class="fa-regular fa-comment"></i>
                        </span> 
                      <?php } ?></td>
                   <?php   } ?>
                  </tr>
                        <?php } ?>
              </tbody>
                        </table>
                        </div>
                        <!-- For OPS user table END -->

               <?php   }?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>          
<!-- Modal -->
<div class="modal fade" id="exampleModal_ercdoc_view" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Comments</h5>
        <button type="button" class="comm-close" id="close-doc-com" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="max-height:200px; overflow-y:auto;">
        <input type="hidden" name="view_erc_comment_id" class="view_erc_comment_id" id="view_erc_comment_id">
        <span class="comment_username" style="font-size:14px">User Name</span>
        <p class="mt-2">consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        <span class="comment_date" style="float: right; font-size:12px">Comment Date:- 05-12-2023</span>
        <br>
      </div>
      <div class="modal-footer justify-content-center">
        <button type="button" class="btn btn-warning">View Log</button>
      </div>
    </div>
  </div>
</div>

<!-- Modal -->
<div class="modal fade" id="exampleModal_otherdoc_adding_comment" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">Comments</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
      <div class="row">
          <div class="col-md-12">
      <input type="hidden" name="doc_id_comment" id="doc_id_comment" class="doc_id_comment">
      <input type="hidden" name="doc_type_id_comment" id="doc_type_id_comment" class="doc_type_id_comment">
        <textarea name="comment" id="other-comment-textarea" data-doc_id = "<?php echo $result[0]->doc_id ;?>"  maxlength="100" placeholder="Only 100 characters are allowed" style="width: 100%;height: 150px;"></textarea>
      </div>
      </div>
      </div>
      <div class="modal-footer justify-content-center">
        <button type="button" id="submit-erc-comment-btn" class="btn btn-primary">Submit</button>
      </div>
    </div>
  </div>
</div>
                      </div>    
                    </div>
                  </div>
                </div>
                